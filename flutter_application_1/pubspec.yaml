name: limefocus
description: "LimeFocus - 专注时间管理应用，帮助您提升专注力和工作效率"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.3+9

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0

  # State Management
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5

  # UI Components
  cupertino_icons: ^1.0.8
  carousel_slider: ^5.0.0
  flutter_dominant_color_container: ^1.0.0
  palette_generator: ^0.3.3+6
  circular_countdown_timer: ^0.2.4
  calendar_date_picker2: ^2.0.0
  table_calendar: ^3.0.9

  # Audio
  just_audio: ^0.9.43

  # Networking
  http: ^1.3.0
  dio: ^5.4.0
  connectivity_plus: ^6.1.4  # 更新到包含隐私清单的版本
  url_launcher: ^6.1.14

  # 文件操作
  path_provider: ^2.1.5
  share_plus: ^10.1.1  # 更新到包含隐私清单的版本
  file_selector: ^1.0.1  # 替换 file_picker
  image_picker: ^0.8.9  # 使用较稳定的旧版本

  # Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Utils
  freezed_annotation: ^2.4.1
  json_annotation: ^4.8.1
  fl_chart: ^0.70.2
  package_info_plus: ^8.3.0
  image: ^4.1.7  # 图片处理
  device_info_plus: ^11.1.0  # 更新到包含隐私清单的版本
  sign_in_with_apple: ^4.3.0  # 苹果登录 - 使用稳定版本
  in_app_purchase: ^3.1.11  # Apple内购
  wakelock_plus: ^1.3.2
  flutter_local_notifications: ^19.2.1
  timezone: ^0.10.1
  path: ^1.8.3  # 添加path依赖
  uuid: ^4.5.1  # 添加uuid依赖

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

  # Linting
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.8
  riverpod_generator: ^2.4.0
  freezed: ^2.4.7
  json_serializable: ^6.7.1
  hive_generator: ^2.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/images/
    - assets/lock-images/
  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# 依赖覆盖，解决版本冲突
dependency_overrides:
  dio: ^5.4.0
