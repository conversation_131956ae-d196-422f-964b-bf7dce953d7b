#!/bin/bash

echo "🎯 最终测试验证"
echo "================"

# 检查当前目录
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ 请在Flutter项目根目录运行此脚本"
    exit 1
fi

echo "📋 验证修复效果..."

# 1. 检查核心文件存在
echo "📁 检查核心文件..."
REQUIRED_FILES=(
    "lib/core/services/apple_subscription_service.dart"
    "lib/features/development/screens/apple_debug_screen.dart"
    "lib/features/subscription/screens/subscription_screen.dart"
    "ios/Runner/Runner.entitlements"
)

ALL_FILES_EXIST=true
for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file 不存在"
        ALL_FILES_EXIST=false
    fi
done

# 2. 检查iOS配置
echo ""
echo "🔧 检查iOS配置..."

# Bundle ID
if grep -q "com.arborflame.limefocus" ios/Runner.xcodeproj/project.pbxproj; then
    echo "✅ Bundle ID配置正确"
else
    echo "❌ Bundle ID配置错误"
    ALL_FILES_EXIST=false
fi

# entitlements配置
if grep -q "CODE_SIGN_ENTITLEMENTS" ios/Runner.xcodeproj/project.pbxproj; then
    echo "✅ entitlements配置已添加"
else
    echo "❌ entitlements配置缺失"
    ALL_FILES_EXIST=false
fi

# 3. 代码质量检查
echo ""
echo "🔍 代码质量检查..."
if flutter analyze lib/core/services/apple_subscription_service.dart --no-fatal-infos > /dev/null 2>&1; then
    echo "✅ Apple订阅服务代码无错误"
else
    echo "❌ Apple订阅服务代码有错误"
    ALL_FILES_EXIST=false
fi

# 4. 构建测试
echo ""
echo "🔨 构建测试..."
if flutter build ios --no-codesign > /dev/null 2>&1; then
    echo "✅ iOS构建成功"
else
    echo "❌ iOS构建失败"
    ALL_FILES_EXIST=false
fi

# 5. 总结
echo ""
echo "📊 验证总结:"
echo "============"

if [ "$ALL_FILES_EXIST" = true ]; then
    echo "🎉 所有检查通过！"
    echo ""
    echo "✅ 修复完成的问题："
    echo "   - Apple订阅服务初始化逻辑"
    echo "   - iOS项目配置和entitlements"
    echo "   - 代码质量和构建问题"
    echo ""
    echo "🚀 下一步操作："
    echo "   1. 运行: flutter run --release"
    echo "   2. 进入: 个人中心 → 开发者工具 → Apple功能调试"
    echo "   3. 检查调试信息和测试功能"
    echo "   4. 测试订阅页面和Apple登录"
    echo ""
    echo "📚 参考文档："
    echo "   - docs/immediate_testing_guide.md"
    echo "   - docs/apple_subscription_troubleshooting.md"
else
    echo "❌ 仍有问题需要解决"
    echo ""
    echo "🔧 建议操作："
    echo "   1. 检查上述失败的项目"
    echo "   2. 重新运行修复脚本"
    echo "   3. 查看详细错误信息"
fi

echo ""
echo "📞 如需帮助，请查看文档或收集以下信息："
echo "   - 设备信息（iOS版本、型号）"
echo "   - Xcode控制台日志"
echo "   - Apple功能调试工具输出"
