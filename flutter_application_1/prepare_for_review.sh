#!/bin/bash

echo "🍎 Apple审核准备检查"
echo "===================="

# 检查当前目录
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ 请在Flutter项目根目录运行此脚本"
    exit 1
fi

echo "📋 检查审核准备状态..."

# 1. 检查沙盒配置
echo ""
echo "🧪 沙盒配置检查..."

if [ -f "ios/Runner/Runner.entitlements" ]; then
    echo "✅ entitlements文件存在"
    
    if grep -q "com.apple.developer.storekit.sandbox" ios/Runner/Runner.entitlements; then
        echo "✅ 沙盒配置已添加"
    else
        echo "❌ 沙盒配置缺失"
    fi
    
    if grep -q "com.apple.developer.in-app-payments" ios/Runner/Runner.entitlements; then
        echo "✅ 内购权限已配置"
    else
        echo "❌ 内购权限缺失"
    fi
else
    echo "❌ entitlements文件不存在"
fi

# 2. 检查Bundle ID
echo ""
echo "🆔 Bundle ID检查..."
if grep -q "com.arborflame.limefocus" ios/Runner.xcodeproj/project.pbxproj; then
    echo "✅ Bundle ID配置正确: com.arborflame.limefocus"
else
    echo "❌ Bundle ID配置错误"
fi

# 3. 检查产品ID配置
echo ""
echo "📦 产品ID检查..."
PRODUCT_IDS=("LemiVip001" "LimeVip_quarter" "LimeVip_yearly" "LimeVip_AYear")
for product_id in "${PRODUCT_IDS[@]}"; do
    if grep -q "$product_id" lib/core/services/apple_subscription_service.dart; then
        echo "✅ $product_id 已配置"
    else
        echo "❌ $product_id 未找到"
    fi
done

# 4. 检查沙盒配置文件
echo ""
echo "⚙️ 沙盒配置文件检查..."
if [ -f "lib/core/config/sandbox_config.dart" ]; then
    echo "✅ 沙盒配置文件存在"
else
    echo "❌ 沙盒配置文件缺失"
fi

# 5. 检查发布配置
echo ""
echo "🚀 发布配置检查..."
if [ -f "lib/core/config/release_config.dart" ]; then
    echo "✅ 发布配置文件存在"
    
    if grep -q "enableAppleSubscription.*true" lib/core/config/release_config.dart; then
        echo "✅ Apple订阅功能已启用"
    else
        echo "❌ Apple订阅功能未启用"
    fi
else
    echo "❌ 发布配置文件缺失"
fi

# 6. 代码质量检查
echo ""
echo "🔍 代码质量检查..."
echo "正在运行代码分析..."
if flutter analyze lib/core/services/apple_subscription_service.dart lib/core/config/sandbox_config.dart --no-fatal-infos > /dev/null 2>&1; then
    echo "✅ 核心代码无错误"
else
    echo "❌ 代码存在问题，请检查"
fi

# 7. 构建测试
echo ""
echo "🔨 构建测试..."
echo "正在清理项目..."
flutter clean > /dev/null 2>&1

echo "正在获取依赖..."
flutter pub get > /dev/null 2>&1

echo "正在测试Release构建..."
if flutter build ios --release --no-codesign > /dev/null 2>&1; then
    echo "✅ Release构建成功"
else
    echo "❌ Release构建失败"
fi

# 8. 生成审核信息
echo ""
echo "📝 生成审核信息..."

cat > review_info.txt << EOF
LimeFocus Apple审核信息
======================

应用信息：
- 应用名称: LimeFocus
- Bundle ID: com.arborflame.limefocus
- 版本: $(grep "version:" pubspec.yaml | cut -d' ' -f2)
- 构建时间: $(date)

沙盒测试账号：
- 邮箱: <EMAIL>
- 密码: [请在App Store Connect中设置]
- 地区: 中国
- 说明: 专门为审核创建的沙盒测试账号

产品配置：
- LemiVip001: 月度会员 ¥6
- LimeVip_quarter: 季度会员 ¥12  
- LimeVip_yearly: 年度会员 ¥28
- LimeVip_AYear: 一年备考包 ¥18

测试说明：
1. 订阅功能与应用账号系统独立
2. 用户可在未登录状态下购买订阅
3. 订阅状态通过Apple StoreKit验证
4. 请使用提供的沙盒测试账号测试

测试路径：
个人中心 → 订阅管理 → 选择订阅计划 → 立即订阅

注意事项：
- 必须在真机上测试
- 首次购买时使用沙盒测试账号登录
- 沙盒环境下购买不产生真实费用

技术支持：
- 邮箱: <EMAIL>
- 文档: docs/apple_sandbox_testing_guide.md
EOF

echo "✅ 审核信息已生成: review_info.txt"

# 9. 检查清单总结
echo ""
echo "📋 审核准备清单："
echo "=================="
echo "□ App Store Connect中创建沙盒测试账号"
echo "□ 所有产品在App Store Connect中已配置"
echo "□ 产品价格已设置（中国区）"
echo "□ 在真机上完成完整测试"
echo "□ 测试购买和恢复购买流程"
echo "□ 测试各种错误情况"
echo "□ 准备审核备注和测试账号信息"
echo "□ 构建并上传到App Store Connect"

echo ""
echo "🎯 下一步操作："
echo "1. 在App Store Connect中创建沙盒测试账号"
echo "2. 在真机上进行完整测试"
echo "3. 使用 review_info.txt 中的信息填写审核备注"
echo "4. 构建并上传应用到App Store Connect"

echo ""
echo "📖 详细指导请查看: docs/apple_sandbox_testing_guide.md"
