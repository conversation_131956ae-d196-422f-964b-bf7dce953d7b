# LimeFocus v1.0.1 发布指南

## 版本信息
- **版本号**: 1.0.1+6
- **发布分支**: release/v1.0.1
- **构建状态**: ✅ 已完成
- **发布日期**: 2025-06-18

## 版本更新内容

### 🎯 主要功能调整
- **隐藏个人信息功能**: 暂时移除"我的"页面中的个人信息板块和账户管理功能
- **UI层面隐藏**: 保留代码结构，便于后续版本恢复功能

### 🐛 问题修复
- **修复"我的"页面底部颜色问题**: 使用LayoutBuilder确保内容填满整个可用空间
- **优化日程页面日历滑动**: 修复日历滑动切换月份功能，确保状态正确同步

### 🔧 技术改进
- 优化页面布局结构，提升用户体验
- 增强日历组件的交互反馈

## Xcode Archive 和上传步骤

### 前置条件检查
- [x] 版本号已更新到 1.0.1+6
- [x] 发布分支已创建并推送
- [x] Flutter Release构建已完成
- [x] Xcode项目已打开

### 详细操作步骤

#### 1. 在Xcode中准备Archive
1. **确认设备选择**
   - 在Xcode顶部工具栏，确保选择了 "Any iOS Device (arm64)"
   - 不要选择模拟器

2. **检查签名配置**
   - 选择 Runner 项目
   - 在 "Signing & Capabilities" 标签页
   - 确认 Team 设置为 "G5N2P69H35"
   - 确认 Bundle Identifier 为 "com.arborflame.limefocus"

3. **检查构建配置**
   - 在 Scheme 选择器中，确认选择了 "Runner"
   - 点击 Scheme 旁边的下拉箭头，选择 "Edit Scheme"
   - 在 "Archive" 标签页，确认 Build Configuration 设置为 "Release"

#### 2. 创建Archive
1. **开始Archive**
   - 菜单栏选择 Product → Archive
   - 等待Archive过程完成（通常需要几分钟）

2. **Archive完成后**
   - Xcode会自动打开 Organizer 窗口
   - 在左侧列表中找到刚创建的Archive
   - 确认版本号显示为 1.0.1 (6)

#### 3. 上传到App Store Connect
1. **分发Archive**
   - 在Organizer中选择刚创建的Archive
   - 点击 "Distribute App" 按钮

2. **选择分发方式**
   - 选择 "App Store Connect"
   - 点击 "Next"

3. **选择分发选项**
   - 选择 "Upload"
   - 点击 "Next"

4. **配置选项**
   - 保持默认设置（通常包括符号表上传等）
   - 点击 "Next"

5. **重新签名**
   - 选择自动管理签名
   - 点击 "Next"

6. **最终确认**
   - 检查所有信息无误
   - 点击 "Upload"

#### 4. 验证上传
1. **等待处理**
   - 上传完成后，等待Apple处理（通常5-30分钟）

2. **检查App Store Connect**
   - 登录 [App Store Connect](https://appstoreconnect.apple.com)
   - 进入 LimeFocus 应用页面
   - 在 "TestFlight" 或 "App Store" 标签页查看新构建

## 后续步骤
当前有两个反馈的问题，需要你仔细准确定位位置，分析问题原因，制定修复优化方案，然后制定计划一步步完成，最后需要检查
### TestFlight 内测
1. **配置测试信息**
   - 在App Store Connect中添加测试说明
   - 设置测试用户组

2. **提交审核**
   - 提交TestFlight审核
   - 等待Apple审核通过

### App Store 正式发布
1. **创建新版本**
   - 在App Store Connect中创建1.0.1版本
   - 选择刚上传的构建

2. **更新应用信息**
   - 更新版本说明
   - 检查应用截图和描述

3. **提交审核**
   - 提交App Store审核
   - 等待Apple审核结果

## 注意事项

### 重要提醒
- ⚠️ 确保在真实设备上测试过应用功能
- ⚠️ 检查所有隐藏功能确实不可访问
- ⚠️ 验证订阅功能在发布环境中正常工作
- ⚠️ 确认应用图标和启动页正确显示

### 常见问题
1. **Archive失败**
   - 检查代码签名配置
   - 确认选择了正确的设备类型

2. **上传失败**
   - 检查网络连接
   - 确认Apple Developer账号状态

3. **处理时间过长**
   - Apple服务器处理时间可能较长
   - 耐心等待，通常不超过1小时

## 版本发布检查清单

- [ ] Xcode Archive创建成功
- [ ] 上传到App Store Connect成功
- [ ] TestFlight构建出现在后台
- [ ] 内测用户可以下载测试
- [ ] 所有核心功能正常工作
- [ ] 订阅功能测试通过
- [ ] 准备App Store审核材料

---

**发布负责人**: AI Assistant  
**发布时间**: 2025-06-18  
**下次版本**: v1.0.2 (计划恢复个人信息功能)
