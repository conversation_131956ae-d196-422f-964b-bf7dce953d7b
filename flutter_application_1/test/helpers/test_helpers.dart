// LimeFocus 测试辅助工具
// 提供测试中常用的工具函数和模拟数据

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 创建一个包含ProviderScope的测试应用
Widget createTestApp({
  required Widget child,
  List<Override>? overrides,
}) {
  return ProviderScope(
    overrides: overrides ?? [],
    child: MaterialApp(
      home: child,
    ),
  );
}

/// 创建一个完整的测试应用，包含主题和本地化
Widget createFullTestApp({
  required Widget child,
  List<Override>? overrides,
}) {
  return ProviderScope(
    overrides: overrides ?? [],
    child: MaterialApp(
      title: 'LimeFocus Test',
      theme: ThemeData(
        primarySwatch: Colors.green,
        useMaterial3: true,
      ),
      home: child,
    ),
  );
}

/// 等待所有异步操作完成的辅助函数
Future<void> pumpAndSettleWithTimeout(
  WidgetTester tester, {
  Duration timeout = const Duration(seconds: 10),
}) async {
  await tester.pumpAndSettle(timeout);
}

/// 查找文本的辅助函数，支持部分匹配
Finder findTextContaining(String text) {
  return find.byWidgetPredicate(
    (widget) => widget is Text && widget.data?.contains(text) == true,
  );
}

/// 查找按钮的辅助函数
Finder findButtonWithText(String text) {
  return find.widgetWithText(ElevatedButton, text);
}

/// 模拟点击并等待动画完成
Future<void> tapAndSettle(WidgetTester tester, Finder finder) async {
  await tester.tap(finder);
  await tester.pumpAndSettle();
}

/// 验证Widget是否存在的辅助函数
void expectWidgetExists(Finder finder) {
  expect(finder, findsOneWidget);
}

/// 验证Widget不存在的辅助函数
void expectWidgetNotExists(Finder finder) {
  expect(finder, findsNothing);
}

/// 验证多个Widget存在的辅助函数
void expectWidgetsExist(Finder finder, int count) {
  expect(finder, findsNWidgets(count));
}

/// 模拟测试数据类
class TestData {
  static const String testEmail = '<EMAIL>';
  static const String testPassword = 'TestPassword123';
  static const String testUsername = 'TestUser';

  static const List<String> testSubjects = [
    '数学',
    '英语',
    '物理',
    '化学',
  ];

  static const List<String> testProjects = [
    '期末复习',
    '作业练习',
    '课外阅读',
    '实验报告',
  ];

  static const List<Duration> testFocusDurations = [
    Duration(minutes: 25),
    Duration(minutes: 45),
    Duration(hours: 1),
    Duration(hours: 2),
  ];
}

/// 测试用的Provider覆盖
class TestOverrides {
  /// 创建导航索引Provider的覆盖
  static Override navigationIndexOverride(int initialIndex) {
    return StateProvider<int>((ref) => initialIndex);
  }
}

/// 测试断言辅助函数
class TestAssertions {
  /// 验证文本内容
  static void verifyText(String expectedText) {
    expect(find.text(expectedText), findsOneWidget);
  }

  /// 验证文本包含特定内容
  static void verifyTextContains(String partialText) {
    expect(findTextContaining(partialText), findsOneWidget);
  }

  /// 验证按钮存在
  static void verifyButton(String buttonText) {
    expect(findButtonWithText(buttonText), findsOneWidget);
  }

  /// 验证图标存在
  static void verifyIcon(IconData icon) {
    expect(find.byIcon(icon), findsOneWidget);
  }

  /// 验证Widget类型存在
  static void verifyWidgetType<T extends Widget>() {
    expect(find.byType(T), findsOneWidget);
  }

  /// 验证多个相同类型的Widget
  static void verifyWidgetTypeCount<T extends Widget>(int count) {
    expect(find.byType(T), findsNWidgets(count));
  }

  /// 验证底部导航项数量（特殊处理）
  static void verifyBottomNavigationItemCount(int count) {
    expect(find.byType(InkWell), findsAtLeastNWidgets(count));
  }
}

/// 测试场景辅助类
class TestScenarios {
  /// 模拟用户登录场景
  static Future<void> simulateUserLogin(WidgetTester tester) async {
    // 这里可以添加具体的登录模拟逻辑
    await tester.pumpAndSettle();
  }

  /// 模拟创建专注会话场景
  static Future<void> simulateCreateFocusSession(WidgetTester tester) async {
    // 这里可以添加具体的专注会话创建逻辑
    await tester.pumpAndSettle();
  }

  /// 模拟导航切换场景
  static Future<void> simulateNavigationSwitch(
    WidgetTester tester,
    int targetIndex,
  ) async {
    final inkWells = find.byType(InkWell);
    if (inkWells.evaluate().length > targetIndex) {
      await tester.tap(inkWells.at(targetIndex));
      await tester.pumpAndSettle();
    }
  }
}
