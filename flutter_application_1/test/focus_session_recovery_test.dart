import 'package:flutter_test/flutter_test.dart';
import 'package:limefocus/core/providers/focus_session_recovery_provider.dart';
import 'package:limefocus/core/models/focus_session.dart';
import 'package:limefocus/core/services/focus_session_service.dart';

/// 阶段4专注会话恢复测试
/// 验证自动状态恢复功能
void main() {
  group('专注会话恢复Provider测试', () {
    test('FocusRecoveryState枚举应该包含所有必要状态', () {
      // 验证所有恢复状态都存在
      expect(FocusRecoveryState.checking, isNotNull);
      expect(FocusRecoveryState.none, isNotNull);
      expect(FocusRecoveryState.found, isNotNull);
      expect(FocusRecoveryState.recovered, isNotNull);
      expect(FocusRecoveryState.error, isNotNull);
    });

    test('FocusRecoveryData应该正确处理状态变化', () {
      // 创建初始数据
      const initialData = FocusRecoveryData(state: FocusRecoveryState.checking);
      expect(initialData.state, equals(FocusRecoveryState.checking));
      expect(initialData.session, isNull);
      expect(initialData.error, isNull);

      // 测试copyWith方法
      final updatedData = initialData.copyWith(
        state: FocusRecoveryState.found,
        session: FocusSession(
          id: 'test_session',
          subjectId: 'math',
          projectId: 'homework',
          startTime: DateTime.now(),
          isCountdown: false,
          isPaused: false,
          pausedTime: null,
          pausedDurationSeconds: 0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );

      expect(updatedData.state, equals(FocusRecoveryState.found));
      expect(updatedData.session, isNotNull);
      expect(updatedData.session!.id, equals('test_session'));
    });

    test('FocusRecoveryData应该正确处理错误状态', () {
      const errorData = FocusRecoveryData(
        state: FocusRecoveryState.error,
        error: 'Test error message',
      );

      expect(errorData.state, equals(FocusRecoveryState.error));
      expect(errorData.error, equals('Test error message'));
      expect(errorData.session, isNull);
    });
  });

  group('专注会话恢复逻辑测试', () {
    test('应该能正确识别有效会话', () {
      // 创建有效会话
      final validSession = FocusSession(
        id: 'valid_session',
        subjectId: 'math',
        projectId: 'homework',
        startTime: DateTime.now().subtract(const Duration(minutes: 30)),
        isCountdown: false,
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
        updatedAt: DateTime.now().subtract(const Duration(minutes: 30)),
      );

      // 验证会话有效性
      expect(validSession.isValid, isTrue);
      expect(validSession.currentElapsedSeconds, greaterThan(1700)); // 约30分钟
    });

    test('应该能正确识别无效会话', () {
      // 创建过期会话（超过24小时）
      final expiredSession = FocusSession(
        id: 'expired_session',
        subjectId: 'math',
        projectId: 'homework',
        startTime: DateTime.now().subtract(const Duration(hours: 25)),
        isCountdown: false,
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: DateTime.now().subtract(const Duration(hours: 25)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 25)),
      );

      // 验证会话无效
      expect(expiredSession.isValid, isFalse);
    });

    test('应该能正确识别已完成的倒计时会话', () {
      // 创建已完成的倒计时会话
      final completedSession = FocusSession(
        id: 'completed_session',
        subjectId: 'math',
        projectId: 'homework',
        startTime: DateTime.now().subtract(const Duration(minutes: 30)),
        isCountdown: true,
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        countdownDurationSeconds: 1500, // 25分钟
        createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
        updatedAt: DateTime.now().subtract(const Duration(minutes: 30)),
      );

      // 验证倒计时已完成
      expect(completedSession.isCountdown, isTrue);
      expect(completedSession.isCountdownCompleted, isTrue);
      expect(completedSession.remainingSeconds, lessThanOrEqualTo(0));
    });

    test('应该能正确处理暂停状态的会话', () {
      // 创建暂停的会话
      final pausedSession = FocusSession(
        id: 'paused_session',
        subjectId: 'math',
        projectId: 'homework',
        startTime: DateTime.now().subtract(const Duration(minutes: 20)),
        isCountdown: false,
        isPaused: true,
        pausedTime: DateTime.now().subtract(const Duration(minutes: 10)),
        pausedDurationSeconds: 600, // 10分钟暂停时间
        createdAt: DateTime.now().subtract(const Duration(minutes: 20)),
        updatedAt: DateTime.now().subtract(const Duration(minutes: 10)),
      );

      // 验证暂停状态
      expect(pausedSession.isPaused, isTrue);
      expect(pausedSession.pausedTime, isNotNull);
      expect(pausedSession.pausedDurationSeconds, equals(600));
      
      // 验证实际专注时间（应该约为10分钟，因为暂停了10分钟）
      final actualFocusTime = pausedSession.currentElapsedSeconds;
      expect(actualFocusTime, greaterThan(590)); // 约10分钟
      expect(actualFocusTime, lessThan(610));
    });
  });

  group('恢复场景测试', () {
    test('场景1：正计时会话恢复', () {
      final session = FocusSession(
        id: 'forward_session',
        subjectId: 'math',
        projectId: 'homework',
        startTime: DateTime.now().subtract(const Duration(minutes: 45)),
        isCountdown: false,
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: DateTime.now().subtract(const Duration(minutes: 45)),
        updatedAt: DateTime.now().subtract(const Duration(minutes: 45)),
      );

      // 验证正计时会话状态
      expect(session.isCountdown, isFalse);
      expect(session.isPaused, isFalse);
      expect(session.isValid, isTrue);
      
      // 验证已过时间约为45分钟
      final elapsedSeconds = session.currentElapsedSeconds;
      expect(elapsedSeconds, greaterThan(2690)); // 约45分钟
      expect(elapsedSeconds, lessThan(2710));
    });

    test('场景2：倒计时会话恢复', () {
      final session = FocusSession(
        id: 'countdown_session',
        subjectId: 'math',
        projectId: 'homework',
        startTime: DateTime.now().subtract(const Duration(minutes: 15)),
        isCountdown: true,
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        countdownDurationSeconds: 1500, // 25分钟
        createdAt: DateTime.now().subtract(const Duration(minutes: 15)),
        updatedAt: DateTime.now().subtract(const Duration(minutes: 15)),
      );

      // 验证倒计时会话状态
      expect(session.isCountdown, isTrue);
      expect(session.isPaused, isFalse);
      expect(session.isValid, isTrue);
      expect(session.isCountdownCompleted, isFalse);
      
      // 验证剩余时间约为10分钟
      final remainingSeconds = session.remainingSeconds;
      expect(remainingSeconds, greaterThan(590)); // 约10分钟
      expect(remainingSeconds, lessThan(610));
    });

    test('场景3：暂停会话恢复', () {
      final session = FocusSession(
        id: 'paused_recovery_session',
        subjectId: 'math',
        projectId: 'homework',
        startTime: DateTime.now().subtract(const Duration(minutes: 30)),
        isCountdown: false,
        isPaused: true,
        pausedTime: DateTime.now().subtract(const Duration(minutes: 15)),
        pausedDurationSeconds: 900, // 15分钟暂停时间
        createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
        updatedAt: DateTime.now().subtract(const Duration(minutes: 15)),
      );

      // 验证暂停会话恢复
      expect(session.isPaused, isTrue);
      expect(session.isValid, isTrue);
      
      // 验证实际专注时间（应该约为15分钟，因为暂停了15分钟）
      final actualFocusTime = session.currentElapsedSeconds;
      expect(actualFocusTime, greaterThan(890)); // 约15分钟
      expect(actualFocusTime, lessThan(910));
    });
  });

  group('边界情况测试', () {
    test('应该正确处理空会话', () {
      // 测试空会话的处理
      const recoveryData = FocusRecoveryData(
        state: FocusRecoveryState.none,
        session: null,
      );

      expect(recoveryData.state, equals(FocusRecoveryState.none));
      expect(recoveryData.session, isNull);
    });

    test('应该正确处理超长会话', () {
      // 创建超过6小时的会话
      final longSession = FocusSession(
        id: 'long_session',
        subjectId: 'math',
        projectId: 'homework',
        startTime: DateTime.now().subtract(const Duration(hours: 7)),
        isCountdown: false,
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: DateTime.now().subtract(const Duration(hours: 7)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 7)),
      );

      // 验证超长会话被标记为无效
      expect(longSession.isValid, isFalse);
    });

    test('应该正确处理数据不一致的会话', () {
      // 创建数据不一致的会话（开始时间在未来）
      final inconsistentSession = FocusSession(
        id: 'inconsistent_session',
        subjectId: 'math',
        projectId: 'homework',
        startTime: DateTime.now().add(const Duration(minutes: 10)), // 未来时间
        isCountdown: false,
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // 验证数据不一致的会话
      expect(inconsistentSession.currentElapsedSeconds, lessThanOrEqualTo(0));
    });
  });
}
