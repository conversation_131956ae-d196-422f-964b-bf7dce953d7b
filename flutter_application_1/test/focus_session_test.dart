import 'package:flutter_test/flutter_test.dart';
import 'package:limefocus/core/models/focus_session.dart';
import 'package:limefocus/core/services/focus_session_service.dart';

/// 专注会话基础设施测试
/// 验证阶段1的持久化机制是否正常工作
void main() {
  group('FocusSession基础功能测试', () {
    test('应该能创建FocusSession实例', () {
      final now = DateTime.now();
      final session = FocusSession(
        id: 'test_session_123',
        subjectId: 'subject_1',
        projectId: 'project_1',
        startTime: now,
        isCountdown: true,
        countdownDurationSeconds: 1500,
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: now,
        updatedAt: now,
      );

      expect(session.id, equals('test_session_123'));
      expect(session.subjectId, equals('subject_1'));
      expect(session.projectId, equals('project_1'));
      expect(session.isCountdown, isTrue);
      expect(session.countdownDurationSeconds, equals(1500));
    });
  });

  group('FocusSessionService基础测试', () {
    test('应该能创建新的专注会话', () {
      final service = FocusSessionService();
      final session = service.createFocusSession(
        subjectId: 'subject_1',
        projectId: 'project_1',
        isCountdown: true,
        countdownDurationSeconds: 1500,
      );

      expect(session.id, isNotEmpty);
      expect(session.subjectId, equals('subject_1'));
      expect(session.projectId, equals('project_1'));
      expect(session.isCountdown, isTrue);
      expect(session.countdownDurationSeconds, equals(1500));
    });
  });
}
