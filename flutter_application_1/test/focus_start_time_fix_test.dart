import 'package:flutter_test/flutter_test.dart';
import 'package:limefocus/core/models/focus_session.dart';

/// 专注开始时间修复测试
/// 验证恢复模式下专注开始时间不被覆盖的修复
void main() {
  group('专注开始时间修复测试', () {
    test('FocusSession应该正确保存和计算开始时间', () {
      final startTime = DateTime.now().subtract(const Duration(minutes: 30));
      
      final session = FocusSession(
        id: 'test_session',
        subjectId: 'math',
        projectId: 'homework',
        startTime: startTime,
        isCountdown: true,
        countdownDurationSeconds: 1500, // 25分钟
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: startTime,
        updatedAt: startTime,
      );
      
      // 验证开始时间正确保存
      expect(session.startTime, equals(startTime));
      
      // 验证已过时间计算正确（约30分钟）
      final elapsedSeconds = session.currentElapsedSeconds;
      expect(elapsedSeconds, greaterThan(1790)); // 约30分钟
      expect(elapsedSeconds, lessThan(1810));
      
      // 验证剩余时间计算正确（约-5分钟，即已超时）
      final remainingSeconds = session.remainingSeconds;
      expect(remainingSeconds, equals(0)); // 已超时，应该为0
    });

    test('倒计时会话恢复时应该保持正确的时间计算', () {
      // 模拟25分钟倒计时，已经过了15分钟
      final startTime = DateTime.now().subtract(const Duration(minutes: 15));
      
      final session = FocusSession(
        id: 'countdown_recovery',
        subjectId: 'math',
        projectId: 'homework',
        startTime: startTime,
        isCountdown: true,
        countdownDurationSeconds: 1500, // 25分钟
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: startTime,
        updatedAt: startTime,
      );
      
      // 验证已过时间约为15分钟
      final elapsedSeconds = session.currentElapsedSeconds;
      expect(elapsedSeconds, greaterThan(890)); // 约15分钟
      expect(elapsedSeconds, lessThan(910));
      
      // 验证剩余时间约为10分钟
      final remainingSeconds = session.remainingSeconds;
      expect(remainingSeconds, greaterThan(590)); // 约10分钟
      expect(remainingSeconds, lessThan(610));
      
      // 验证倒计时未完成
      expect(session.isCountdownCompleted, isFalse);
    });

    test('正计时会话恢复时应该保持正确的时间计算', () {
      // 模拟正计时，已经过了45分钟
      final startTime = DateTime.now().subtract(const Duration(minutes: 45));
      
      final session = FocusSession(
        id: 'forward_recovery',
        subjectId: 'math',
        projectId: 'homework',
        startTime: startTime,
        isCountdown: false,
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: startTime,
        updatedAt: startTime,
      );
      
      // 验证已过时间约为45分钟
      final elapsedSeconds = session.currentElapsedSeconds;
      expect(elapsedSeconds, greaterThan(2690)); // 约45分钟
      expect(elapsedSeconds, lessThan(2710));
      
      // 正计时模式没有剩余时间概念
      expect(session.remainingSeconds, equals(0));
      expect(session.isCountdownCompleted, isFalse);
    });

    test('暂停会话恢复时应该正确计算实际专注时间', () {
      // 模拟会话：开始30分钟前，15分钟前暂停，暂停了10分钟
      final startTime = DateTime.now().subtract(const Duration(minutes: 30));
      final pausedTime = DateTime.now().subtract(const Duration(minutes: 15));
      
      final session = FocusSession(
        id: 'paused_recovery',
        subjectId: 'math',
        projectId: 'homework',
        startTime: startTime,
        isCountdown: false,
        isPaused: true,
        pausedTime: pausedTime,
        pausedDurationSeconds: 600, // 已暂停10分钟
        createdAt: startTime,
        updatedAt: pausedTime,
      );
      
      // 验证实际专注时间：30分钟总时间 - 10分钟已暂停 = 20分钟实际专注
      final actualFocusTime = session.currentElapsedSeconds;
      expect(actualFocusTime, greaterThan(590)); // 约10分钟（暂停前的专注时间）
      expect(actualFocusTime, lessThan(610));
      
      // 注意：在暂停状态下，currentElapsedSeconds返回的是pausedDurationSeconds
      // 这里应该是600秒（10分钟），这是暂停前的累计专注时间
      expect(actualFocusTime, equals(600));
    });

    test('会话有效性检查应该正确工作', () {
      final now = DateTime.now();
      
      // 有效会话：2小时前开始，未超过限制
      final validSession = FocusSession(
        id: 'valid_session',
        subjectId: 'math',
        projectId: 'homework',
        startTime: now.subtract(const Duration(hours: 2)),
        isCountdown: false,
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: now.subtract(const Duration(hours: 2)),
        updatedAt: now.subtract(const Duration(hours: 2)),
      );
      
      expect(validSession.isValid, isTrue);
      
      // 无效会话：7小时前开始，超过6小时限制
      final invalidSession = FocusSession(
        id: 'invalid_session',
        subjectId: 'math',
        projectId: 'homework',
        startTime: now.subtract(const Duration(hours: 7)),
        isCountdown: false,
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: now.subtract(const Duration(hours: 7)),
        updatedAt: now.subtract(const Duration(hours: 7)),
      );
      
      expect(invalidSession.isValid, isFalse);
      
      // 过期会话：25小时前创建，超过24小时限制
      final expiredSession = FocusSession(
        id: 'expired_session',
        subjectId: 'math',
        projectId: 'homework',
        startTime: now.subtract(const Duration(hours: 25)),
        isCountdown: false,
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: now.subtract(const Duration(hours: 25)),
        updatedAt: now.subtract(const Duration(hours: 25)),
      );
      
      expect(expiredSession.isValid, isFalse);
    });

    test('倒计时完成检查应该正确工作', () {
      final startTime = DateTime.now().subtract(const Duration(minutes: 30));
      
      // 已完成的倒计时：25分钟倒计时，已过30分钟
      final completedSession = FocusSession(
        id: 'completed_countdown',
        subjectId: 'math',
        projectId: 'homework',
        startTime: startTime,
        isCountdown: true,
        countdownDurationSeconds: 1500, // 25分钟
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: startTime,
        updatedAt: startTime,
      );
      
      expect(completedSession.isCountdownCompleted, isTrue);
      expect(completedSession.remainingSeconds, equals(0));
      
      // 未完成的倒计时：25分钟倒计时，只过了15分钟
      final startTime2 = DateTime.now().subtract(const Duration(minutes: 15));
      final incompleteSession = FocusSession(
        id: 'incomplete_countdown',
        subjectId: 'math',
        projectId: 'homework',
        startTime: startTime2,
        isCountdown: true,
        countdownDurationSeconds: 1500, // 25分钟
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: startTime2,
        updatedAt: startTime2,
      );
      
      expect(incompleteSession.isCountdownCompleted, isFalse);
      expect(incompleteSession.remainingSeconds, greaterThan(590)); // 约10分钟剩余
      expect(incompleteSession.remainingSeconds, lessThan(610));
    });
  });

  group('专注开始时间修复场景测试', () {
    test('修复前的问题场景：开始时间被覆盖', () {
      // 模拟修复前的问题：
      // 1. 用户开始25分钟倒计时
      // 2. 15分钟后关闭应用
      // 3. 重新打开应用时，开始时间被重置为当前时间
      // 4. 导致倒计时重新开始
      
      final originalStartTime = DateTime.now().subtract(const Duration(minutes: 15));
      final currentTime = DateTime.now();
      
      // 原始会话（正确的开始时间）
      final originalSession = FocusSession(
        id: 'original_session',
        subjectId: 'math',
        projectId: 'homework',
        startTime: originalStartTime,
        isCountdown: true,
        countdownDurationSeconds: 1500, // 25分钟
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: originalStartTime,
        updatedAt: originalStartTime,
      );
      
      // 验证原始会话的正确状态
      expect(originalSession.currentElapsedSeconds, greaterThan(890)); // 约15分钟
      expect(originalSession.remainingSeconds, greaterThan(590)); // 约10分钟剩余
      expect(originalSession.isCountdownCompleted, isFalse);
      
      // 模拟修复前的错误：开始时间被重置
      final corruptedSession = FocusSession(
        id: 'corrupted_session',
        subjectId: 'math',
        projectId: 'homework',
        startTime: currentTime, // 错误：被重置为当前时间
        isCountdown: true,
        countdownDurationSeconds: 1500, // 25分钟
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: originalStartTime,
        updatedAt: originalStartTime,
      );
      
      // 验证错误状态：倒计时重新开始
      expect(corruptedSession.currentElapsedSeconds, lessThan(10)); // 几乎为0
      expect(corruptedSession.remainingSeconds, greaterThan(1490)); // 几乎是完整的25分钟
      expect(corruptedSession.isCountdownCompleted, isFalse);
      
      // 验证问题：用户的15分钟专注时间丢失
      final lostTime = originalSession.currentElapsedSeconds - corruptedSession.currentElapsedSeconds;
      expect(lostTime, greaterThan(880)); // 丢失了约15分钟
    });

    test('修复后的正确场景：开始时间保持不变', () {
      // 修复后的正确流程：
      // 1. 用户开始25分钟倒计时
      // 2. 15分钟后关闭应用
      // 3. 重新打开应用时，开始时间保持不变
      // 4. 倒计时从正确的剩余时间继续
      
      final originalStartTime = DateTime.now().subtract(const Duration(minutes: 15));
      
      // 恢复的会话（保持正确的开始时间）
      final recoveredSession = FocusSession(
        id: 'recovered_session',
        subjectId: 'math',
        projectId: 'homework',
        startTime: originalStartTime, // 正确：保持原始开始时间
        isCountdown: true,
        countdownDurationSeconds: 1500, // 25分钟
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: originalStartTime,
        updatedAt: originalStartTime,
      );
      
      // 验证正确状态：倒计时从正确位置继续
      expect(recoveredSession.currentElapsedSeconds, greaterThan(890)); // 约15分钟
      expect(recoveredSession.remainingSeconds, greaterThan(590)); // 约10分钟剩余
      expect(recoveredSession.remainingSeconds, lessThan(610));
      expect(recoveredSession.isCountdownCompleted, isFalse);
      
      // 验证用户体验：专注时间得到正确保留
      final preservedTime = recoveredSession.currentElapsedSeconds;
      expect(preservedTime, greaterThan(880)); // 保留了约15分钟的专注时间
    });
  });
}
