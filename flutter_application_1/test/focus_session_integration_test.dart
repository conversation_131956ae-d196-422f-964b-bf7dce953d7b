import 'package:flutter_test/flutter_test.dart';
import 'package:limefocus/core/services/focus_session_service.dart';

/// 阶段2集成测试
/// 验证专注会话服务的集成功能
void main() {
  // 初始化Flutter绑定
  TestWidgetsFlutterBinding.ensureInitialized();
  group('FocusSessionService集成测试', () {
    late FocusSessionService service;

    setUp(() {
      service = FocusSessionService();
    });

    tearDown(() async {
      // 清理测试数据
      await service.clearFocusSession();
    });

    test('应该能完整的创建、保存、获取、清理专注会话', () async {
      // 1. 创建专注会话
      final session = service.createFocusSession(
        subjectId: 'test_subject',
        projectId: 'test_project',
        isCountdown: true,
        countdownDurationSeconds: 1500,
      );

      expect(session.id, isNotEmpty);
      expect(session.subjectId, equals('test_subject'));
      expect(session.projectId, equals('test_project'));

      // 2. 保存专注会话
      await service.saveFocusSession(session);

      // 3. 获取专注会话
      final retrievedSession = await service.getCurrentFocusSession();
      expect(retrievedSession, isNotNull);
      expect(retrievedSession!.id, equals(session.id));
      expect(retrievedSession.subjectId, equals(session.subjectId));
      expect(retrievedSession.projectId, equals(session.projectId));

      // 4. 验证会话存在
      final hasSession = await service.hasFocusSession();
      expect(hasSession, isTrue);

      // 5. 清理专注会话
      await service.clearFocusSession();

      // 6. 验证会话已清理
      final clearedSession = await service.getCurrentFocusSession();
      expect(clearedSession, isNull);

      final hasSessionAfterClear = await service.hasFocusSession();
      expect(hasSessionAfterClear, isFalse);
    });

    test('应该能正确处理暂停和恢复操作', () async {
      // 创建并保存会话
      final session = service.createFocusSession(
        subjectId: 'test_subject',
        projectId: 'test_project',
        isCountdown: false,
      );
      await service.saveFocusSession(session);

      // 暂停会话
      final pausedSession = await service.pauseFocusSession();
      expect(pausedSession, isNotNull);
      expect(pausedSession!.isPaused, isTrue);
      expect(pausedSession.pausedTime, isNotNull);

      // 获取暂停后的会话
      final retrievedPausedSession = await service.getCurrentFocusSession();
      expect(retrievedPausedSession!.isPaused, isTrue);

      // 恢复会话
      final resumedSession = await service.resumeFocusSession();
      expect(resumedSession, isNotNull);
      expect(resumedSession!.isPaused, isFalse);
      expect(resumedSession.pausedTime, isNull);

      // 获取恢复后的会话
      final retrievedResumedSession = await service.getCurrentFocusSession();
      expect(retrievedResumedSession!.isPaused, isFalse);
    });

    test('应该能正确验证会话完整性', () async {
      // 创建并保存正常会话
      final session = service.createFocusSession(
        subjectId: 'test_subject',
        projectId: 'test_project',
        isCountdown: true,
        countdownDurationSeconds: 1500,
      );
      await service.saveFocusSession(session);

      // 验证完整性
      final isValid = await service.validateSessionIntegrity();
      expect(isValid, isTrue);
    });

    test('应该能获取会话统计信息', () async {
      // 没有会话时的统计
      final emptyStats = await service.getSessionStats();
      expect(emptyStats['hasSession'], isFalse);

      // 创建并保存会话
      final session = service.createFocusSession(
        subjectId: 'test_subject',
        projectId: 'test_project',
        isCountdown: true,
        countdownDurationSeconds: 1500,
      );
      await service.saveFocusSession(session);

      // 有会话时的统计
      final stats = await service.getSessionStats();
      expect(stats['hasSession'], isTrue);
      expect(stats['sessionId'], equals(session.id));
      expect(stats['isCountdown'], isTrue);
      expect(stats['isPaused'], isFalse);
      expect(stats['isValid'], isTrue);
    });

    test('应该能处理不存在的会话操作', () async {
      // 尝试获取不存在的会话
      final session = await service.getCurrentFocusSession();
      expect(session, isNull);

      // 尝试暂停不存在的会话
      final pausedSession = await service.pauseFocusSession();
      expect(pausedSession, isNull);

      // 尝试恢复不存在的会话
      final resumedSession = await service.resumeFocusSession();
      expect(resumedSession, isNull);

      // 清理不存在的会话应该不报错
      await service.clearFocusSession();
    });
  });
}
