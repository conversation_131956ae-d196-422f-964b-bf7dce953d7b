import 'package:flutter_test/flutter_test.dart';
import 'package:limefocus/core/models/subject_project.dart';

/// 专注完成后进度更新修复测试
/// 验证专注完成后项目进度能正确更新到项目列表
void main() {
  group('专注完成后进度更新修复测试', () {
    test('专注时间模式项目进度计算应该正确', () {
      // 创建一个专注时间模式的项目
      final project = Project(
        id: 'focus_time_project',
        name: '专注时间项目',
        subjectId: 'math',
        startDate: DateTime.now().subtract(const Duration(days: 30)),
        endDate: DateTime.now().add(const Duration(days: 30)),
        isTrackingEnabled: true,
        trackingMode: ProgressTrackingMode.focusTime,
        totalFocusHours: 100.0, // 目标100小时
        currentFocusHours: 50.0, // 当前50小时
        progress: 0.5, // 50%进度
      );
      
      // 模拟专注2小时后的更新
      const focusHours = 2.0;
      final newCurrentFocusHours = (project.currentFocusHours ?? 0) + focusHours;
      final newProgress = (newCurrentFocusHours / project.totalFocusHours!).clamp(0.0, 1.0);
      
      final updatedProject = project.copyWith(
        currentFocusHours: newCurrentFocusHours,
        progress: newProgress,
      );
      
      // 验证更新后的数据
      expect(updatedProject.currentFocusHours, equals(52.0)); // 50 + 2
      expect(updatedProject.progress, equals(0.52)); // 52/100
      expect(updatedProject.progress, greaterThan(project.progress)); // 进度增加
    });

    test('自定义模式项目进度计算应该正确', () {
      // 创建一个自定义模式的项目
      final project = Project(
        id: 'custom_project',
        name: '自定义项目',
        subjectId: 'math',
        startDate: DateTime.now().subtract(const Duration(days: 30)),
        endDate: DateTime.now().add(const Duration(days: 30)),
        isTrackingEnabled: true,
        trackingMode: ProgressTrackingMode.custom,
        customUnit: '页',
        targetValue: 100, // 目标100页
        currentCustomValue: 30, // 当前30页
        progress: 0.3, // 30%进度
      );
      
      // 模拟用户调整进度到35页
      const newCustomValue = 35;
      final newProgress = (newCustomValue / project.targetValue!).clamp(0.0, 1.0);
      
      final updatedProject = project.copyWith(
        currentCustomValue: newCustomValue,
        progress: newProgress,
      );
      
      // 验证更新后的数据
      expect(updatedProject.currentCustomValue, equals(35)); // 30 -> 35
      expect(updatedProject.progress, equals(0.35)); // 35/100
      expect(updatedProject.progress, greaterThan(project.progress)); // 进度增加
    });

    test('项目进度达到100%时应该正确处理', () {
      // 创建一个接近完成的项目
      final project = Project(
        id: 'almost_complete_project',
        name: '即将完成的项目',
        subjectId: 'math',
        startDate: DateTime.now().subtract(const Duration(days: 30)),
        endDate: DateTime.now().add(const Duration(days: 30)),
        isTrackingEnabled: true,
        trackingMode: ProgressTrackingMode.focusTime,
        totalFocusHours: 50.0, // 目标50小时
        currentFocusHours: 48.0, // 当前48小时
        progress: 0.96, // 96%进度
      );
      
      // 模拟专注3小时后（超过目标）
      const focusHours = 3.0;
      final newCurrentFocusHours = (project.currentFocusHours ?? 0) + focusHours;
      final newProgress = (newCurrentFocusHours / project.totalFocusHours!).clamp(0.0, 1.0);
      
      final updatedProject = project.copyWith(
        currentFocusHours: newCurrentFocusHours,
        progress: newProgress,
      );
      
      // 验证更新后的数据
      expect(updatedProject.currentFocusHours, equals(51.0)); // 48 + 3
      expect(updatedProject.progress, equals(1.0)); // 被clamp到1.0
      expect(updatedProject.progress, equals(1.0)); // 100%完成
    });

    test('项目进度不应该超过100%', () {
      // 测试各种超过100%的情况
      final testCases = [
        // [currentHours, totalHours, expectedProgress]
        [60.0, 50.0, 1.0], // 120% -> 100%
        [100.0, 50.0, 1.0], // 200% -> 100%
        [75.0, 50.0, 1.0], // 150% -> 100%
      ];
      
      for (final testCase in testCases) {
        final currentHours = testCase[0] as double;
        final totalHours = testCase[1] as double;
        final expectedProgress = testCase[2] as double;
        
        final progress = (currentHours / totalHours).clamp(0.0, 1.0);
        expect(progress, equals(expectedProgress));
        expect(progress, lessThanOrEqualTo(1.0));
      }
    });

    test('项目进度不应该为负数', () {
      // 测试边界情况
      final testCases = [
        // [currentValue, targetValue, expectedProgress]
        [0, 100, 0.0], // 0% 
        [-5, 100, 0.0], // 负数 -> 0%
        [0, 0, 0.0], // 目标为0 -> 0%
      ];
      
      for (final testCase in testCases) {
        final currentValue = testCase[0] as int;
        final targetValue = testCase[1] as int;
        final expectedProgress = testCase[2] as double;
        
        final progress = targetValue > 0 ? 
            (currentValue / targetValue).clamp(0.0, 1.0) : 0.0;
        expect(progress, equals(expectedProgress));
        expect(progress, greaterThanOrEqualTo(0.0));
      }
    });
  });

  group('Riverpod状态更新逻辑测试', () {
    test('项目列表更新逻辑应该正确', () {
      // 模拟当前项目列表
      final project1 = Project(
        id: 'project1',
        name: '项目1',
        subjectId: 'math',
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 30)),
        progress: 0.3,
      );
      
      final project2 = Project(
        id: 'project2',
        name: '项目2',
        subjectId: 'english',
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 30)),
        progress: 0.5,
      );
      
      final currentProjects = [project1, project2];
      
      // 模拟更新project1的进度
      final updatedProject1 = project1.copyWith(progress: 0.6);
      
      // 模拟_updateRiverpodProjectState的逻辑
      final updatedProjects = currentProjects.map((project) {
        if (project.id == updatedProject1.id) {
          return updatedProject1;
        }
        return project;
      }).toList();
      
      // 验证更新结果
      expect(updatedProjects.length, equals(2));
      expect(updatedProjects[0].id, equals('project1'));
      expect(updatedProjects[0].progress, equals(0.6)); // 已更新
      expect(updatedProjects[1].id, equals('project2'));
      expect(updatedProjects[1].progress, equals(0.5)); // 未变化
    });

    test('当前项目状态更新逻辑应该正确', () {
      // 模拟当前选中的项目
      final currentProject = Project(
        id: 'current_project',
        name: '当前项目',
        subjectId: 'math',
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 30)),
        progress: 0.4,
      );
      
      // 模拟更新后的项目
      final updatedProject = currentProject.copyWith(progress: 0.7);
      
      // 验证是否需要更新当前项目状态
      final shouldUpdateCurrentProject = currentProject.id == updatedProject.id;
      expect(shouldUpdateCurrentProject, isTrue);
      
      // 验证更新后的当前项目
      if (shouldUpdateCurrentProject) {
        expect(updatedProject.progress, equals(0.7));
        expect(updatedProject.id, equals(currentProject.id));
      }
    });
  });

  group('修复前后对比测试', () {
    test('修复前的问题：项目列表显示过期数据', () {
      // 模拟修复前的问题场景：
      // 1. 项目列表显示的是Riverpod中的旧数据
      // 2. Hive中的数据已更新，但Riverpod状态未同步
      
      // Riverpod中的旧数据
      final riverpodProject = Project(
        id: 'test_project',
        name: '测试项目',
        subjectId: 'math',
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 30)),
        progress: 0.3, // 旧进度
        currentFocusHours: 15.0, // 旧专注时间
      );
      
      // Hive中的新数据（专注完成后更新）
      final hiveProject = riverpodProject.copyWith(
        progress: 0.5, // 新进度
        currentFocusHours: 25.0, // 新专注时间
      );
      
      // 验证问题：数据不一致
      expect(riverpodProject.progress, isNot(equals(hiveProject.progress)));
      expect(riverpodProject.currentFocusHours, isNot(equals(hiveProject.currentFocusHours)));
      
      // 这就是用户看到的问题：项目列表显示旧进度，项目详情显示新进度
      final listDisplayProgress = riverpodProject.progress; // 项目列表显示
      final detailDisplayProgress = hiveProject.progress; // 项目详情显示
      
      expect(listDisplayProgress, lessThan(detailDisplayProgress));
    });

    test('修复后的正确场景：数据实时同步', () {
      // 修复后的正确流程：
      // 1. 专注完成后更新Hive数据
      // 2. 同时同步更新Riverpod状态
      // 3. 项目列表立即显示最新数据
      
      // 初始状态
      final initialProject = Project(
        id: 'sync_project',
        name: '同步项目',
        subjectId: 'math',
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 30)),
        progress: 0.2,
        currentFocusHours: 10.0,
      );
      
      // 专注完成后的更新
      const focusHours = 5.0;
      final newCurrentFocusHours = (initialProject.currentFocusHours ?? 0) + focusHours;
      final newProgress = 0.4; // 假设计算后的新进度
      
      final updatedProject = initialProject.copyWith(
        progress: newProgress,
        currentFocusHours: newCurrentFocusHours,
      );
      
      // 模拟同步更新：Hive和Riverpod都更新为相同数据
      final hiveData = updatedProject;
      final riverpodData = updatedProject;
      
      // 验证修复效果：数据一致
      expect(hiveData.progress, equals(riverpodData.progress));
      expect(hiveData.currentFocusHours, equals(riverpodData.currentFocusHours));
      
      // 用户体验：项目列表和项目详情显示相同的最新数据
      final listDisplayProgress = riverpodData.progress; // 项目列表显示
      final detailDisplayProgress = hiveData.progress; // 项目详情显示
      
      expect(listDisplayProgress, equals(detailDisplayProgress));
      expect(listDisplayProgress, equals(newProgress));
    });
  });

  group('边界情况测试', () {
    test('项目不存在于列表中时应该安全处理', () {
      // 模拟项目列表
      final project1 = Project(
        id: 'existing_project',
        name: '存在的项目',
        subjectId: 'math',
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 30)),
        progress: 0.3,
      );
      
      final currentProjects = [project1];
      
      // 尝试更新一个不存在的项目
      final nonExistentProject = Project(
        id: 'non_existent_project',
        name: '不存在的项目',
        subjectId: 'math',
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 30)),
        progress: 0.5,
      );
      
      // 模拟更新逻辑
      final updatedProjects = currentProjects.map((project) {
        if (project.id == nonExistentProject.id) {
          return nonExistentProject;
        }
        return project;
      }).toList();
      
      // 验证：列表应该保持不变
      expect(updatedProjects.length, equals(1));
      expect(updatedProjects[0].id, equals('existing_project'));
      expect(updatedProjects[0].progress, equals(0.3)); // 未变化
    });

    test('空项目列表时应该安全处理', () {
      // 模拟空项目列表
      final List<Project> currentProjects = [];
      
      // 尝试更新项目
      final updatedProject = Project(
        id: 'some_project',
        name: '某个项目',
        subjectId: 'math',
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 30)),
        progress: 0.5,
      );
      
      // 模拟更新逻辑
      final updatedProjects = currentProjects.map((project) {
        if (project.id == updatedProject.id) {
          return updatedProject;
        }
        return project;
      }).toList();
      
      // 验证：列表应该保持为空
      expect(updatedProjects.isEmpty, isTrue);
    });
  });
}
