// LimeFocus 底部导航栏 Widget 测试
// 测试底部导航栏的功能和交互

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:limefocus/shared/widgets/bottom_navigation.dart';

void main() {
  group('BottomNavigation Widget Tests', () {
    testWidgets('BottomNavigation should display 5 items', (WidgetTester tester) async {
      int selectedIndex = 0;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            bottomNavigationBar: BottomNavigation(
              currentIndex: selectedIndex,
              onTap: (index) {
                selectedIndex = index;
              },
            ),
          ),
        ),
      );

      // 验证自定义底部导航栏存在
      expect(find.byType(BottomNavigation), findsOneWidget);

      // 验证有5个导航项（通过InkWell计数）
      expect(find.byType(InkWell), findsNWidgets(5));
    });

    testWidgets('BottomNavigation should handle tap events', (WidgetTester tester) async {
      int selectedIndex = 0;

      await tester.pumpWidget(
        StatefulBuilder(
          builder: (context, setState) {
            return MaterialApp(
              home: Scaffold(
                bottomNavigationBar: BottomNavigation(
                  currentIndex: selectedIndex,
                  onTap: (index) {
                    setState(() {
                      selectedIndex = index;
                    });
                  },
                ),
              ),
            );
          },
        ),
      );

      // 初始状态应该是第一个项目被选中
      expect(selectedIndex, 0);

      // 点击第二个导航项
      await tester.tap(find.byType(InkWell).at(1));
      await tester.pump();

      // 验证选中状态更新
      expect(selectedIndex, 1);
    });

    testWidgets('BottomNavigation should show correct icons', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            bottomNavigationBar: BottomNavigation(
              currentIndex: 0,
              onTap: (index) {},
            ),
          ),
        ),
      );

      // 验证导航栏图标存在（根据实际图标调整）
      expect(find.byType(Icon), findsAtLeastNWidgets(5));
    });

    testWidgets('BottomNavigation should highlight selected item', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            bottomNavigationBar: BottomNavigation(
              currentIndex: 2, // 选中第三个项目
              onTap: (index) {},
            ),
          ),
        ),
      );

      final bottomNav = tester.widget<BottomNavigation>(
        find.byType(BottomNavigation),
      );

      // 验证当前选中的索引
      expect(bottomNav.currentIndex, 2);
    });
  });
}
