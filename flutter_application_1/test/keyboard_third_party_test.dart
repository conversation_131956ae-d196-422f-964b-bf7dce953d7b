import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:limefocus/shared/utils/keyboard_avoidance_utils.dart';

/// 第三方键盘适配测试
/// 验证键盘高度检测和适配逻辑的正确性
void main() {
  group('第三方键盘适配测试', () {
    testWidgets('键盘高度检测 - 正常系统键盘', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // 模拟正常的系统键盘高度
              return MediaQuery(
                data: const MediaQueryData(
                  viewInsets: EdgeInsets.only(bottom: 300),
                ),
                child: Builder(
                  builder: (context) {
                    final height = KeyboardAvoidanceUtils.getKeyboardHeight(context);
                    expect(height, equals(300));
                    return const Scaffold();
                  },
                ),
              );
            },
          ),
        ),
      );
    });

    testWidgets('键盘高度检测 - 异常小的第三方键盘高度', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // 模拟第三方键盘报告的异常小高度
              return MediaQuery(
                data: const MediaQueryData(
                  viewInsets: EdgeInsets.only(bottom: 50), // 异常小的高度
                ),
                child: Builder(
                  builder: (context) {
                    final height = KeyboardAvoidanceUtils.getKeyboardHeight(context);
                    // 应该返回默认最小高度而不是异常小的高度
                    expect(height, equals(250));
                    return const Scaffold();
                  },
                ),
              );
            },
          ),
        ),
      );
    });

    testWidgets('键盘高度检测 - 键盘未显示', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // 模拟键盘未显示
              return MediaQuery(
                data: const MediaQueryData(
                  viewInsets: EdgeInsets.zero,
                ),
                child: Builder(
                  builder: (context) {
                    final height = KeyboardAvoidanceUtils.getKeyboardHeight(context);
                    expect(height, equals(0));
                    return const Scaffold();
                  },
                ),
              );
            },
          ),
        ),
      );
    });

    testWidgets('安全底部弹窗高度计算 - 第三方键盘', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // 模拟第三方键盘的异常高度
              return MediaQuery(
                data: const MediaQueryData(
                  size: Size(375, 812), // iPhone 13 尺寸
                  viewInsets: EdgeInsets.only(bottom: 80), // 异常小的键盘高度
                  padding: EdgeInsets.only(bottom: 34), // 安全区域
                ),
                child: Builder(
                  builder: (context) {
                    final height = KeyboardAvoidanceUtils.getSafeBottomSheetHeight(context);

                    // 验证计算逻辑：
                    // 屏幕高度: 812
                    // 调整后键盘高度: 250 + 20 = 270 (第三方键盘额外边距)
                    // 安全区域: 34
                    // 顶部偏移: 100
                    // 可用高度: 812 - 270 - 34 - 100 = 408
                    // 最大高度: 812 * 0.9 = 730.8
                    // 应该返回: 408 (可用高度小于最大高度)

                    expect(height, closeTo(408, 5)); // 允许小的误差
                    return const Scaffold();
                  },
                ),
              );
            },
          ),
        ),
      );
    });

    testWidgets('键盘可见性检测', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              return MediaQuery(
                data: const MediaQueryData(
                  viewInsets: EdgeInsets.only(bottom: 300),
                ),
                child: Builder(
                  builder: (context) {
                    final isVisible = KeyboardAvoidanceUtils.isKeyboardVisible(context);
                    expect(isVisible, isTrue);
                    return const Scaffold();
                  },
                ),
              );
            },
          ),
        ),
      );
    });
  });
}
