// LimeFocus 应用流程集成测试
// 测试应用的主要用户流程

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:limefocus/main.dart';
import 'package:limefocus/shared/widgets/bottom_navigation.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('App Flow Integration Tests', () {
    testWidgets('Complete app navigation flow', (WidgetTester tester) async {
      // 构建完整应用
      await tester.pumpWidget(const ProviderScope(child: MyApp()));
      await pumpAndSettleWithTimeout(tester);

      // 验证应用启动成功
      TestAssertions.verifyWidgetType<MaterialApp>();

      // 验证主屏幕加载
      TestAssertions.verifyWidgetType<Scaffold>();

      // 验证底部导航栏存在
      TestAssertions.verifyWidgetType<BottomNavigation>();
      TestAssertions.verifyBottomNavigationItemCount(5);
    });

    testWidgets('Navigation between all screens', (WidgetTester tester) async {
      await tester.pumpWidget(const ProviderScope(child: MyApp()));
      await pumpAndSettleWithTimeout(tester);

      // 测试导航到每个页面
      for (int i = 0; i < 5; i++) {
        await TestScenarios.simulateNavigationSwitch(tester, i);

        // 验证导航成功（通过检查BottomNavigation组件存在）
        expect(find.byType(BottomNavigation), findsOneWidget);
      }
    });

    testWidgets('App should handle rapid navigation switches', (WidgetTester tester) async {
      await tester.pumpWidget(const ProviderScope(child: MyApp()));
      await pumpAndSettleWithTimeout(tester);

      // 快速切换导航
      await TestScenarios.simulateNavigationSwitch(tester, 1);
      await TestScenarios.simulateNavigationSwitch(tester, 3);
      await TestScenarios.simulateNavigationSwitch(tester, 0);
      await TestScenarios.simulateNavigationSwitch(tester, 4);
      await TestScenarios.simulateNavigationSwitch(tester, 2);

      // 验证最终状态（导航组件仍然存在）
      expect(find.byType(BottomNavigation), findsOneWidget);
    });

    testWidgets('App should maintain state during navigation', (WidgetTester tester) async {
      await tester.pumpWidget(const ProviderScope(child: MyApp()));
      await pumpAndSettleWithTimeout(tester);

      // 验证初始状态
      expect(find.byType(BottomNavigation), findsOneWidget);

      // 导航到其他页面再返回
      await TestScenarios.simulateNavigationSwitch(tester, 2);
      await TestScenarios.simulateNavigationSwitch(tester, 0);

      // 验证状态保持（导航组件仍然存在）
      expect(find.byType(BottomNavigation), findsOneWidget);
    });
  });

  group('Error Handling Tests', () {
    testWidgets('App should handle provider errors gracefully', (WidgetTester tester) async {
      // 使用错误的Provider覆盖来测试错误处理
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            // 可以在这里添加会导致错误的Provider覆盖
          ],
          child: const MyApp(),
        ),
      );

      // 等待错误处理完成
      await pumpAndSettleWithTimeout(tester);

      // 验证应用仍然可以正常显示
      TestAssertions.verifyWidgetType<MaterialApp>();
    });

    testWidgets('App should handle navigation errors', (WidgetTester tester) async {
      await tester.pumpWidget(const ProviderScope(child: MyApp()));
      await pumpAndSettleWithTimeout(tester);

      // 尝试导航到无效索引（这应该被正确处理）
      try {
        await TestScenarios.simulateNavigationSwitch(tester, 10);
      } catch (e) {
        // 预期会有错误，但应用应该保持稳定
      }

      // 验证应用仍然正常工作
      TestAssertions.verifyWidgetType<BottomNavigation>();
    });
  });

  group('Performance Tests', () {
    testWidgets('App should start within reasonable time', (WidgetTester tester) async {
      final stopwatch = Stopwatch()..start();

      await tester.pumpWidget(const ProviderScope(child: MyApp()));
      await tester.pumpAndSettle();

      stopwatch.stop();

      // 验证启动时间合理（这里设置为5秒，实际可以根据需要调整）
      expect(stopwatch.elapsedMilliseconds, lessThan(5000));

      // 验证应用正常加载
      TestAssertions.verifyWidgetType<MaterialApp>();
    });

    testWidgets('Navigation should be responsive', (WidgetTester tester) async {
      await tester.pumpWidget(const ProviderScope(child: MyApp()));
      await pumpAndSettleWithTimeout(tester);

      // 测试导航响应时间
      for (int i = 0; i < 5; i++) {
        final stopwatch = Stopwatch()..start();

        await TestScenarios.simulateNavigationSwitch(tester, i);

        stopwatch.stop();

        // 验证导航响应时间合理（1秒内）
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      }
    });
  });

  group('Accessibility Tests', () {
    testWidgets('App should have proper semantics', (WidgetTester tester) async {
      await tester.pumpWidget(const ProviderScope(child: MyApp()));
      await pumpAndSettleWithTimeout(tester);

      // 验证语义标签存在
      expect(tester.getSemantics(find.byType(BottomNavigation)), isNotNull);
    });

    testWidgets('Navigation should be accessible', (WidgetTester tester) async {
      await tester.pumpWidget(const ProviderScope(child: MyApp()));
      await pumpAndSettleWithTimeout(tester);

      // 验证导航项可访问
      final navigationItems = find.byType(InkWell);
      expect(navigationItems, findsAtLeastNWidgets(5));

      // 验证导航组件存在
      expect(find.byType(BottomNavigation), findsOneWidget);
    });
  });
}
