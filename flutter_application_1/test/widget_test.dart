// LimeFocus 应用的 Widget 测试
// 测试应用的基本功能和UI组件

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:limefocus/main.dart';
import 'package:limefocus/shared/widgets/bottom_navigation.dart';

void main() {
  group('LimeFocus App Tests', () {
    testWidgets('App should build without errors', (WidgetTester tester) async {
      // 构建应用并触发一帧
      await tester.pumpWidget(const ProviderScope(child: MyApp()));

      // 等待异步操作完成
      await tester.pumpAndSettle();

      // 验证应用成功构建
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('MainScreen should display bottom navigation', (WidgetTester tester) async {
      // 构建主屏幕
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: MainScreen(),
          ),
        ),
      );

      // 等待异步操作完成
      await tester.pumpAndSettle();

      // 验证自定义底部导航栏存在
      expect(find.byType(BottomNavigation), findsOneWidget);

      // 验证有5个导航项（通过InkWell计数）
      expect(find.byType(InkWell), findsAtLeastNWidgets(5));
    });

    testWidgets('Bottom navigation should switch screens', (WidgetTester tester) async {
      // 构建主屏幕
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: MainScreen(),
          ),
        ),
      );

      // 等待异步操作完成
      await tester.pumpAndSettle();

      // 点击第二个导航项（专注页面）
      final inkWells = find.byType(InkWell);
      if (inkWells.evaluate().length >= 2) {
        await tester.tap(inkWells.at(1));
        await tester.pumpAndSettle();
      }

      // 验证页面切换成功（底部导航仍然存在）
      expect(find.byType(BottomNavigation), findsOneWidget);
    });
  });
}
