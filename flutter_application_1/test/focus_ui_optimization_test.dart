import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:limefocus/features/focus/screens/focus_screen.dart';
import 'package:limefocus/core/models/subject_project.dart';

/// 专注界面UI优化测试
/// 验证问题2的修复效果：移除返回按钮、添加倒计时显示、调整取消时间限制
void main() {
  group('专注界面UI优化测试', () {
    late Subject testSubject;
    late Project testProject;

    setUp(() {
      testSubject = Subject(
        id: 'test-subject-id',
        name: '测试科目',
        color: Colors.blue.value,
      );

      testProject = Project(
        id: 'test-project-id',
        name: '测试项目',
        subjectId: testSubject.id,
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 30)),
        isArchived: false,
        isTrackingEnabled: false,
        trackingMode: ProgressTrackingMode.focusTime,
        focusedHours: 0,
        currentFocusHours: 0,
        progress: 0,
      );
    });

    testWidgets('专注页面应该不显示左上角返回按钮', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: FocusScreen(
              isCountdown: true,
              countdownMinutes: 25,
              subject: testSubject,
              project: testProject,
            ),
          ),
        ),
      );

      await tester.pump();

      // 验证不存在返回按钮
      expect(find.byIcon(Icons.arrow_back_ios), findsNothing);

      // 验证存在占位空间（SizedBox）
      expect(find.byType(SizedBox), findsWidgets);
    });

    testWidgets('60秒内应该显示取消按钮，暂停按钮禁用', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: FocusScreen(
              isCountdown: true,
              countdownMinutes: 25,
              subject: testSubject,
              project: testProject,
            ),
          ),
        ),
      );

      await tester.pump();

      // 验证存在取消按钮
      expect(find.text('取消'), findsOneWidget);

      // 验证存在暂停按钮
      expect(find.text('暂停'), findsOneWidget);

      // 验证暂停按钮存在（60秒内应该是禁用状态，但仍然显示）
      // 注意：在60秒内，暂停按钮应该是禁用状态
      final pauseButtonFinder = find.text('暂停');
      expect(pauseButtonFinder, findsOneWidget);

      // 验证不存在倒计时显示
      expect(find.textContaining('s)'), findsNothing);
    });

    testWidgets('取消按钮和暂停按钮应该横向对齐', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: FocusScreen(
              isCountdown: true,
              countdownMinutes: 25,
              subject: testSubject,
              project: testProject,
            ),
          ),
        ),
      );

      await tester.pump();

      // 查找底部控制区域，应该包含取消按钮和暂停按钮
      expect(find.text('取消'), findsOneWidget);
      expect(find.text('暂停'), findsOneWidget);

      // 验证两个按钮都存在且在同一行
      final cancelButton = find.text('取消');
      final pauseButton = find.text('暂停');

      expect(cancelButton, findsOneWidget);
      expect(pauseButton, findsOneWidget);

      // 验证按钮的垂直位置相近（在同一行）
      final cancelRect = tester.getRect(cancelButton);
      final pauseRect = tester.getRect(pauseButton);

      // 两个按钮的中心Y坐标应该相近（允许一些误差）
      expect((cancelRect.center.dy - pauseRect.center.dy).abs(), lessThan(50));

      // 验证不存在倒计时显示
      expect(find.textContaining('s)'), findsNothing);
    });

    testWidgets('正计时模式应该显示长按结束按钮', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: FocusScreen(
              isCountdown: false, // 正计时模式
              subject: testSubject,
              project: testProject,
            ),
          ),
        ),
      );

      await tester.pump();

      // 等待60秒后，应该显示长按结束按钮
      await tester.pump(const Duration(seconds: 61));

      // 验证存在结束按钮
      expect(find.text('结束'), findsOneWidget);
    });
  });
}
