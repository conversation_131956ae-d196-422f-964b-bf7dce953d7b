// LimeFocus Providers 单元测试
// 测试Riverpod状态管理相关功能

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:limefocus/main.dart';

void main() {
  group('Providers Tests', () {
    test('navigationIndexProvider should have initial value 0', () {
      final container = ProviderContainer();
      addTearDown(container.dispose);

      final navigationIndex = container.read(navigationIndexProvider);
      expect(navigationIndex, 0);
    });

    test('navigationIndexProvider should update value', () {
      final container = ProviderContainer();
      addTearDown(container.dispose);

      // 初始值应该是0
      expect(container.read(navigationIndexProvider), 0);

      // 更新值
      container.read(navigationIndexProvider.notifier).state = 2;
      expect(container.read(navigationIndexProvider), 2);

      // 再次更新
      container.read(navigationIndexProvider.notifier).state = 4;
      expect(container.read(navigationIndexProvider), 4);
    });

    test('navigationIndexProvider should handle boundary values', () {
      final container = ProviderContainer();
      addTearDown(container.dispose);

      // 测试边界值
      container.read(navigationIndexProvider.notifier).state = 0;
      expect(container.read(navigationIndexProvider), 0);

      container.read(navigationIndexProvider.notifier).state = 4;
      expect(container.read(navigationIndexProvider), 4);
    });
  });
}
