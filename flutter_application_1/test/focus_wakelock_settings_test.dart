import 'package:flutter_test/flutter_test.dart';
import 'package:limefocus/core/services/preferences_service.dart';

/// 屏幕常亮设置持久化测试
/// 验证屏幕常亮设置的保存和读取功能
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('屏幕常亮设置持久化测试', () {
    late PreferencesService preferencesService;

    setUp(() {
      preferencesService = PreferencesService();
    });

    test('应该能保存和读取屏幕常亮设置', () async {
      // 测试保存true
      await preferencesService.saveKeepScreenOnSetting(true);
      bool result = await preferencesService.getKeepScreenOnSetting();
      expect(result, isTrue);

      // 测试保存false
      await preferencesService.saveKeepScreenOnSetting(false);
      result = await preferencesService.getKeepScreenOnSetting();
      expect(result, isFalse);

      // 再次测试保存true
      await preferencesService.saveKeepScreenOnSetting(true);
      result = await preferencesService.getKeepScreenOnSetting();
      expect(result, isTrue);
    });

    test('默认值应该为true', () async {
      // 在没有保存过设置的情况下，应该返回默认值true
      // 注意：这个测试可能会受到其他测试的影响，因为SharedPreferences是持久的
      final result = await preferencesService.getKeepScreenOnSetting();
      expect(result, isTrue); // 默认值应该是true
    });

    test('应该能正确处理多次保存', () async {
      // 多次保存相同值
      await preferencesService.saveKeepScreenOnSetting(true);
      await preferencesService.saveKeepScreenOnSetting(true);
      await preferencesService.saveKeepScreenOnSetting(true);

      bool result = await preferencesService.getKeepScreenOnSetting();
      expect(result, isTrue);

      // 多次保存不同值
      await preferencesService.saveKeepScreenOnSetting(false);
      await preferencesService.saveKeepScreenOnSetting(true);
      await preferencesService.saveKeepScreenOnSetting(false);

      result = await preferencesService.getKeepScreenOnSetting();
      expect(result, isFalse); // 应该是最后保存的值
    });
  });

  group('屏幕常亮设置键名测试', () {
    test('键名应该正确定义', () {
      // 验证键名常量存在且不为空
      expect(PreferencesService.keepScreenOnKey, isNotNull);
      expect(PreferencesService.keepScreenOnKey, isNotEmpty);
      expect(PreferencesService.keepScreenOnKey, equals('keep_screen_on'));
    });
  });

  group('屏幕常亮设置边界情况测试', () {
    late PreferencesService preferencesService;

    setUp(() {
      preferencesService = PreferencesService();
    });

    test('应该能处理快速连续的保存操作', () async {
      // 快速连续保存
      final futures = <Future<void>>[];
      for (int i = 0; i < 10; i++) {
        futures.add(preferencesService.saveKeepScreenOnSetting(i % 2 == 0));
      }

      // 等待所有保存操作完成
      await Future.wait(futures);

      // 验证最终结果
      final result = await preferencesService.getKeepScreenOnSetting();
      expect(result, isA<bool>()); // 应该是布尔值
    });

    test('应该能处理异常情况', () async {
      // 这个测试主要验证方法不会抛出异常
      try {
        await preferencesService.saveKeepScreenOnSetting(true);
        await preferencesService.getKeepScreenOnSetting();
        // 如果没有抛出异常，测试通过
        expect(true, isTrue);
      } catch (e) {
        // 如果抛出异常，测试失败
        fail('屏幕常亮设置操作不应该抛出异常: $e');
      }
    });
  });

  group('屏幕常亮设置与其他设置的隔离测试', () {
    late PreferencesService preferencesService;

    setUp(() {
      preferencesService = PreferencesService();
    });

    test('屏幕常亮设置不应该影响其他设置', () async {
      // 保存屏幕常亮设置
      await preferencesService.saveKeepScreenOnSetting(false);

      // 保存其他设置
      await preferencesService.saveLastSubjectId('test_subject');
      await preferencesService.saveLastProjectId('test_project');

      // 验证屏幕常亮设置没有被影响
      final keepScreenOn = await preferencesService.getKeepScreenOnSetting();
      expect(keepScreenOn, isFalse);

      // 验证其他设置也正常
      final subjectId = await preferencesService.getLastSubjectId();
      final projectId = await preferencesService.getLastProjectId();
      expect(subjectId, equals('test_subject'));
      expect(projectId, equals('test_project'));
    });

    test('其他设置不应该影响屏幕常亮设置', () async {
      // 先保存屏幕常亮设置
      await preferencesService.saveKeepScreenOnSetting(true);

      // 保存和修改其他设置
      await preferencesService.saveLastSubjectId('subject1');
      await preferencesService.saveLastSubjectId('subject2');
      await preferencesService.saveLastProjectId('project1');
      await preferencesService.saveLastProjectId('project2');

      // 验证屏幕常亮设置没有被影响
      final keepScreenOn = await preferencesService.getKeepScreenOnSetting();
      expect(keepScreenOn, isTrue);
    });
  });

  group('屏幕常亮设置实际使用场景测试', () {
    late PreferencesService preferencesService;

    setUp(() {
      preferencesService = PreferencesService();
    });

    test('模拟用户首次使用场景', () async {
      // 模拟用户首次打开专注页面，应该使用默认值
      final defaultValue = await preferencesService.getKeepScreenOnSetting();
      expect(defaultValue, isTrue); // 默认应该开启
    });

    test('模拟用户关闭屏幕常亮场景', () async {
      // 用户关闭屏幕常亮
      await preferencesService.saveKeepScreenOnSetting(false);

      // 下次打开应该记住用户的选择
      final userChoice = await preferencesService.getKeepScreenOnSetting();
      expect(userChoice, isFalse);
    });

    test('模拟用户重新开启屏幕常亮场景', () async {
      // 用户先关闭
      await preferencesService.saveKeepScreenOnSetting(false);
      expect(await preferencesService.getKeepScreenOnSetting(), isFalse);

      // 然后重新开启
      await preferencesService.saveKeepScreenOnSetting(true);
      expect(await preferencesService.getKeepScreenOnSetting(), isTrue);
    });

    test('模拟用户多次切换场景', () async {
      // 模拟用户在多个专注会话中切换设置
      final testSequence = [true, false, true, false, false, true];

      for (final value in testSequence) {
        await preferencesService.saveKeepScreenOnSetting(value);
        final saved = await preferencesService.getKeepScreenOnSetting();
        expect(saved, equals(value));
      }

      // 最终值应该是最后设置的值
      final finalValue = await preferencesService.getKeepScreenOnSetting();
      expect(finalValue, equals(testSequence.last));
    });
  });

  group('屏幕常亮设置性能测试', () {
    late PreferencesService preferencesService;

    setUp(() {
      preferencesService = PreferencesService();
    });

    test('保存操作应该在合理时间内完成', () async {
      final stopwatch = Stopwatch()..start();

      await preferencesService.saveKeepScreenOnSetting(true);

      stopwatch.stop();

      // 保存操作应该在1秒内完成
      expect(stopwatch.elapsedMilliseconds, lessThan(1000));
    });

    test('读取操作应该在合理时间内完成', () async {
      // 先保存一个值
      await preferencesService.saveKeepScreenOnSetting(true);

      final stopwatch = Stopwatch()..start();

      await preferencesService.getKeepScreenOnSetting();

      stopwatch.stop();

      // 读取操作应该在1秒内完成
      expect(stopwatch.elapsedMilliseconds, lessThan(1000));
    });
  });
}
