import 'package:flutter_test/flutter_test.dart';
import 'package:limefocus/core/models/focus_session.dart';

/// 倒计时恢复修复测试
/// 验证倒计时模式下应用重启后的恢复功能
void main() {
  group('倒计时恢复修复测试', () {
    test('倒计时会话应该正确计算剩余时间', () {
      // 模拟25分钟倒计时，已经过了15分钟
      final startTime = DateTime.now().subtract(const Duration(minutes: 15));

      final session = FocusSession(
        id: 'countdown_session',
        subjectId: 'math',
        projectId: 'homework',
        startTime: startTime,
        isCountdown: true,
        countdownDurationSeconds: 1500, // 25分钟
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: startTime,
        updatedAt: startTime,
      );

      // 验证已过时间约为15分钟
      final elapsedSeconds = session.currentElapsedSeconds;
      expect(elapsedSeconds, greaterThan(890)); // 约15分钟
      expect(elapsedSeconds, lessThan(910));

      // 验证剩余时间约为10分钟
      final remainingSeconds = session.remainingSeconds;
      expect(remainingSeconds, greaterThan(590)); // 约10分钟
      expect(remainingSeconds, lessThan(610));

      // 验证倒计时未完成
      expect(session.isCountdownCompleted, isFalse);
    });

    test('倒计时会话恢复时应该保持正确的开始时间', () {
      // 模拟用户15分钟前开始的倒计时
      final originalStartTime = DateTime.now().subtract(const Duration(minutes: 15));

      final session = FocusSession(
        id: 'recovery_session',
        subjectId: 'math',
        projectId: 'homework',
        startTime: originalStartTime,
        isCountdown: true,
        countdownDurationSeconds: 1500, // 25分钟
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: originalStartTime,
        updatedAt: originalStartTime,
      );

      // 验证开始时间没有被修改
      expect(session.startTime, equals(originalStartTime));

      // 验证基于正确开始时间的计算
      final now = DateTime.now();
      final expectedElapsed = now.difference(originalStartTime).inSeconds;
      final actualElapsed = session.currentElapsedSeconds;

      // 允许1秒的误差
      expect((actualElapsed - expectedElapsed).abs(), lessThan(2));
    });

    test('暂停的倒计时会话应该正确恢复', () {
      // 模拟倒计时：20分钟前开始，10分钟前暂停，已专注10分钟
      final startTime = DateTime.now().subtract(const Duration(minutes: 20));
      final pausedTime = DateTime.now().subtract(const Duration(minutes: 10));

      final session = FocusSession(
        id: 'paused_countdown',
        subjectId: 'math',
        projectId: 'homework',
        startTime: startTime,
        isCountdown: true,
        countdownDurationSeconds: 1500, // 25分钟
        isPaused: true,
        pausedTime: pausedTime,
        pausedDurationSeconds: 600, // 暂停前已专注10分钟
        createdAt: startTime,
        updatedAt: pausedTime,
      );

      // 验证暂停状态下的时间计算
      expect(session.isPaused, isTrue);
      expect(session.currentElapsedSeconds, equals(600)); // 10分钟

      // 验证剩余时间：25分钟 - 10分钟 = 15分钟
      final remainingSeconds = session.remainingSeconds;
      expect(remainingSeconds, equals(900)); // 15分钟

      // 验证倒计时未完成
      expect(session.isCountdownCompleted, isFalse);
    });

    test('已完成的倒计时会话应该被正确识别', () {
      // 模拟30分钟前开始的25分钟倒计时（已超时）
      final startTime = DateTime.now().subtract(const Duration(minutes: 30));

      final session = FocusSession(
        id: 'completed_countdown',
        subjectId: 'math',
        projectId: 'homework',
        startTime: startTime,
        isCountdown: true,
        countdownDurationSeconds: 1500, // 25分钟
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: startTime,
        updatedAt: startTime,
      );

      // 验证倒计时已完成
      expect(session.isCountdownCompleted, isTrue);
      expect(session.remainingSeconds, equals(0));

      // 验证已过时间超过倒计时时长
      expect(session.currentElapsedSeconds, greaterThan(1500));
    });

    test('倒计时会话有效性检查应该正确', () {
      final now = DateTime.now();

      // 有效的倒计时会话：2小时前开始
      final validSession = FocusSession(
        id: 'valid_countdown',
        subjectId: 'math',
        projectId: 'homework',
        startTime: now.subtract(const Duration(hours: 2)),
        isCountdown: true,
        countdownDurationSeconds: 1500,
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: now.subtract(const Duration(hours: 2)),
        updatedAt: now.subtract(const Duration(hours: 2)),
      );

      expect(validSession.isValid, isTrue);

      // 无效的倒计时会话：7小时前开始（超过6小时限制）
      final invalidSession = FocusSession(
        id: 'invalid_countdown',
        subjectId: 'math',
        projectId: 'homework',
        startTime: now.subtract(const Duration(hours: 7)),
        isCountdown: true,
        countdownDurationSeconds: 1500,
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: now.subtract(const Duration(hours: 7)),
        updatedAt: now.subtract(const Duration(hours: 7)),
      );

      expect(invalidSession.isValid, isFalse);
    });
  });

  group('倒计时恢复修复场景测试', () {
    test('修复前的问题：倒计时重置', () {
      // 模拟修复前的问题场景：
      // 1. 用户开始25分钟倒计时
      // 2. 15分钟后关闭应用
      // 3. 重新打开应用时，倒计时从25分钟重新开始

      final originalStartTime = DateTime.now().subtract(const Duration(minutes: 15));

      // 原始会话（正确状态）
      final originalSession = FocusSession(
        id: 'original_countdown',
        subjectId: 'math',
        projectId: 'homework',
        startTime: originalStartTime,
        isCountdown: true,
        countdownDurationSeconds: 1500, // 25分钟
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: originalStartTime,
        updatedAt: originalStartTime,
      );

      // 验证原始状态：应该剩余约10分钟
      expect(originalSession.remainingSeconds, greaterThan(590));
      expect(originalSession.remainingSeconds, lessThan(610));
      expect(originalSession.isCountdownCompleted, isFalse);

      // 模拟修复前的错误：倒计时器重置为25分钟
      const resetCountdownSeconds = 1500; // 错误：重置为完整时长

      // 验证问题：用户丢失了15分钟的专注时间
      final lostTime = resetCountdownSeconds - originalSession.remainingSeconds;
      expect(lostTime, greaterThan(880)); // 丢失了约15分钟
    });

    test('修复后的正确场景：倒计时从剩余时间继续', () {
      // 修复后的正确流程：
      // 1. 用户开始25分钟倒计时
      // 2. 15分钟后关闭应用
      // 3. 重新打开应用时，倒计时从剩余10分钟继续

      final originalStartTime = DateTime.now().subtract(const Duration(minutes: 15));

      // 恢复的会话（保持正确状态）
      final recoveredSession = FocusSession(
        id: 'recovered_countdown',
        subjectId: 'math',
        projectId: 'homework',
        startTime: originalStartTime, // 保持原始开始时间
        isCountdown: true,
        countdownDurationSeconds: 1500, // 25分钟
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: originalStartTime,
        updatedAt: originalStartTime,
      );

      // 验证正确恢复：倒计时从正确的剩余时间继续
      final remainingSeconds = recoveredSession.remainingSeconds;
      expect(remainingSeconds, greaterThan(590)); // 约10分钟剩余
      expect(remainingSeconds, lessThan(610));
      expect(recoveredSession.isCountdownCompleted, isFalse);

      // 验证用户体验：专注时间得到正确保留
      final preservedTime = recoveredSession.currentElapsedSeconds;
      expect(preservedTime, greaterThan(880)); // 保留了约15分钟的专注时间
    });

    test('修复关键点：CircularCountDownTimer控制器设置', () {
      // 验证修复的关键逻辑：
      // 1. autoStart在恢复模式下应该为false
      // 2. controller.restart()应该设置正确的剩余时间
      // 3. controller.start()或controller.pause()应该根据会话状态调用

      final startTime = DateTime.now().subtract(const Duration(minutes: 15));

      final session = FocusSession(
        id: 'controller_test',
        subjectId: 'math',
        projectId: 'homework',
        startTime: startTime,
        isCountdown: true,
        countdownDurationSeconds: 1500, // 25分钟
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: startTime,
        updatedAt: startTime,
      );

      final remainingSeconds = session.remainingSeconds;

      // 验证修复逻辑的输入参数
      expect(remainingSeconds, greaterThan(590)); // 约10分钟
      expect(remainingSeconds, lessThan(610));
      expect(session.isPaused, isFalse); // 未暂停状态

      // 模拟修复后的控制器设置逻辑：
      // controller.restart(duration: remainingSeconds)
      // if (!session.isPaused) controller.start()

      // 验证这些参数是正确的
      expect(remainingSeconds, isPositive);
      expect(remainingSeconds, lessThan(1500)); // 小于总时长
    });

    test('暂停状态的倒计时恢复', () {
      // 测试暂停状态下的倒计时恢复
      final startTime = DateTime.now().subtract(const Duration(minutes: 20));
      final pausedTime = DateTime.now().subtract(const Duration(minutes: 10));

      final pausedSession = FocusSession(
        id: 'paused_recovery',
        subjectId: 'math',
        projectId: 'homework',
        startTime: startTime,
        isCountdown: true,
        countdownDurationSeconds: 1500, // 25分钟
        isPaused: true,
        pausedTime: pausedTime,
        pausedDurationSeconds: 600, // 暂停前专注10分钟
        createdAt: startTime,
        updatedAt: pausedTime,
      );

      // 验证暂停状态的恢复参数
      expect(pausedSession.isPaused, isTrue);
      expect(pausedSession.remainingSeconds, equals(900)); // 15分钟剩余

      // 模拟修复后的暂停状态恢复逻辑：
      // controller.restart(duration: 900)
      // controller.pause() // 因为session.isPaused为true

      // 验证暂停状态恢复的正确性
      expect(pausedSession.remainingSeconds, isPositive);
      expect(pausedSession.remainingSeconds, equals(900));
    });
  });

  group('倒计时恢复边界情况测试', () {
    test('倒计时刚开始就关闭应用', () {
      // 倒计时刚开始（30秒前）就关闭应用
      final startTime = DateTime.now().subtract(const Duration(seconds: 30));

      final session = FocusSession(
        id: 'just_started',
        subjectId: 'math',
        projectId: 'homework',
        startTime: startTime,
        isCountdown: true,
        countdownDurationSeconds: 1500, // 25分钟
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: startTime,
        updatedAt: startTime,
      );

      // 验证时间计算的正确性：
      // 总时长1500秒，已过30秒，剩余应该是1470秒
      final elapsedSeconds = session.currentElapsedSeconds;
      final remainingSeconds = session.remainingSeconds;

      // 验证已过时间约为30秒（允许1秒误差）
      expect(elapsedSeconds, greaterThanOrEqualTo(29));
      expect(elapsedSeconds, lessThanOrEqualTo(31));

      // 验证剩余时间约为1470秒（允许1秒误差）
      expect(remainingSeconds, greaterThanOrEqualTo(1469));
      expect(remainingSeconds, lessThanOrEqualTo(1471));

      // 验证总时长计算正确
      expect(elapsedSeconds + remainingSeconds, equals(1500));
      expect(session.isCountdownCompleted, isFalse);
    });

    test('倒计时即将完成时关闭应用', () {
      // 倒计时还剩1分钟时关闭应用
      final startTime = DateTime.now().subtract(const Duration(minutes: 24));

      final session = FocusSession(
        id: 'almost_done',
        subjectId: 'math',
        projectId: 'homework',
        startTime: startTime,
        isCountdown: true,
        countdownDurationSeconds: 1500, // 25分钟
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: startTime,
        updatedAt: startTime,
      );

      // 验证剩余时间约为1分钟
      final remainingSeconds = session.remainingSeconds;
      expect(remainingSeconds, greaterThan(50)); // 约1分钟
      expect(remainingSeconds, lessThan(70));
      expect(session.isCountdownCompleted, isFalse);
    });

    test('倒计时刚好完成时关闭应用', () {
      // 倒计时刚好完成时关闭应用
      final startTime = DateTime.now().subtract(const Duration(minutes: 25));

      final session = FocusSession(
        id: 'just_completed',
        subjectId: 'math',
        projectId: 'homework',
        startTime: startTime,
        isCountdown: true,
        countdownDurationSeconds: 1500, // 25分钟
        isPaused: false,
        pausedTime: null,
        pausedDurationSeconds: 0,
        createdAt: startTime,
        updatedAt: startTime,
      );

      // 验证倒计时已完成
      expect(session.isCountdownCompleted, isTrue);
      expect(session.remainingSeconds, equals(0));
    });
  });
}
