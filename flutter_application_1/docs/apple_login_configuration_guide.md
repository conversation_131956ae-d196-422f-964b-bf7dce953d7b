# Apple登录配置完整指南

## 🔍 当前问题分析

### 错误信息
```
[core] Authorization failed: Error Domain=AKAuthenticationError Code=-7003 "(null)" UserInfo={AKClientBundleID=com.arborflame.limefocus}
SignInWithAppleAuthorizationException(AuthorizationErrorCode.canceled, The operation couldn't be completed. (com.apple.AuthenticationServices.AuthorizationError error 1001.))
```

### 错误代码含义
- **AKAuthenticationError Code=-7003**: Apple认证服务配置错误
- **AuthorizationError 1001**: 用户取消授权或权限配置问题

## 🛠️ 解决步骤

### 步骤1: 检查Apple Developer配置

#### 1.1 验证App ID配置
1. **登录Apple Developer Console**
   - 访问: https://developer.apple.com/account/
   - 进入"Certificates, Identifiers & Profiles"

2. **检查App ID**
   - 查找Bundle ID: `com.arborflame.limefocus`
   - 确认"Sign In with Apple"功能已启用
   - 如果未启用，点击编辑并启用该功能

#### 1.2 检查Provisioning Profile
1. **开发证书**
   - 确认有效的iOS开发证书
   - 证书应包含"Sign In with Apple"权限

2. **Provisioning Profile**
   - 确认Profile包含"Sign In with Apple"权限
   - 重新生成并下载Profile（如果需要）

### 步骤2: 检查Xcode项目配置

#### 2.1 检查Signing & Capabilities
1. **打开Xcode项目**
   ```bash
   open ios/Runner.xcworkspace
   ```

2. **检查Capabilities**
   - 选择Runner项目 → Signing & Capabilities
   - 确认"Sign In with Apple"已添加
   - 如果没有，点击"+ Capability"添加

#### 2.2 检查Entitlements文件
1. **查看entitlements文件**
   - 文件位置: `ios/Runner/Runner.entitlements`
   - 应包含以下内容:
   ```xml
   <key>com.apple.developer.applesignin</key>
   <array>
       <string>Default</string>
   </array>
   ```

### 步骤3: 设备和账号配置

#### 3.1 设备Apple ID检查
1. **确认设备登录Apple ID**
   - 设置 → 登录iPhone
   - 确保使用有效的Apple ID

2. **检查双重认证**
   - Apple ID必须启用双重认证
   - 设置 → Apple ID → 密码与安全性

#### 3.2 沙盒测试账号（推荐）
1. **创建沙盒测试账号**
   - Apple Developer Console → Users and Access → Sandbox Testers
   - 创建专用测试账号

2. **在设备上配置**
   - 设置 → App Store → 沙盒账户
   - 登录测试账号

### 步骤4: 代码配置验证

#### 4.1 检查权限请求
```dart
// 确认请求的权限范围正确
final credential = await SignInWithApple.getAppleIDCredential(
  scopes: [
    AppleIDAuthorizationScopes.email,
    AppleIDAuthorizationScopes.fullName,
  ],
);
```

#### 4.2 添加错误处理增强
```dart
try {
  final credential = await SignInWithApple.getAppleIDCredential(
    scopes: [
      AppleIDAuthorizationScopes.email,
      AppleIDAuthorizationScopes.fullName,
    ],
  );
} on SignInWithAppleAuthorizationException catch (e) {
  switch (e.code) {
    case AuthorizationErrorCode.canceled:
      throw Exception('用户取消了Apple登录');
    case AuthorizationErrorCode.failed:
      throw Exception('Apple登录失败，请检查网络连接');
    case AuthorizationErrorCode.invalidResponse:
      throw Exception('Apple服务器响应无效');
    case AuthorizationErrorCode.notHandled:
      throw Exception('Apple登录请求未被处理');
    case AuthorizationErrorCode.unknown:
    default:
      throw Exception('Apple登录遇到未知错误');
  }
}
```

### 步骤5: 测试验证

#### 5.1 基础连接测试
1. **检查网络连接**
   - 确保设备连接到互联网
   - 尝试访问其他Apple服务

2. **重启设备**
   - 有时需要重启设备来刷新Apple服务连接

#### 5.2 逐步测试
1. **简单测试**
   - 先测试不带scope的请求
   - 再逐步添加email和fullName权限

2. **日志分析**
   - 观察详细的错误日志
   - 记录每个步骤的结果

## 🚀 立即执行的修复步骤

### 优先级1: 检查Apple Developer配置
1. **登录Apple Developer Console**
2. **验证Bundle ID和Sign In with Apple配置**
3. **重新生成Provisioning Profile**

### 优先级2: 检查Xcode配置
1. **打开Xcode项目**
2. **验证Signing & Capabilities**
3. **检查entitlements文件**

### 优先级3: 设备配置
1. **确认Apple ID登录状态**
2. **考虑使用沙盒测试账号**
3. **重启设备**

## 📋 配置检查清单

### Apple Developer Console
- [ ] Bundle ID存在且正确
- [ ] Sign In with Apple功能已启用
- [ ] 开发证书有效
- [ ] Provisioning Profile包含Apple登录权限

### Xcode项目
- [ ] Signing & Capabilities中有Sign In with Apple
- [ ] entitlements文件配置正确
- [ ] Bundle ID匹配
- [ ] Team配置正确

### 设备配置
- [ ] 设备已登录Apple ID
- [ ] Apple ID启用双重认证
- [ ] 网络连接正常
- [ ] 考虑使用沙盒测试账号

### 代码配置
- [ ] 权限请求正确
- [ ] 错误处理完善
- [ ] 日志输出详细

## 🎯 预期结果

修复后应该看到：
1. **Apple登录弹窗正常显示**
2. **用户可以选择Apple ID**
3. **成功获取用户凭据**
4. **后端API调用成功**

## 📞 如果问题仍然存在

### 高级排查
1. **检查系统日志**
   - 使用Xcode的Console查看系统日志
   - 搜索Apple认证相关错误

2. **联系Apple支持**
   - 如果配置都正确但仍然失败
   - 可能是Apple服务端问题

3. **临时解决方案**
   - 暂时禁用Apple登录
   - 使用其他登录方式
   - 等待Apple服务恢复

---

**下一步：请按照优先级顺序检查上述配置，特别是Apple Developer Console中的配置！**
