# 工程规范文档

## 一、Git分支管理规范

### 1.1 分支策略

采用Gitflow工作流模式，主要包含以下分支：

- **main**: 主分支，存放正式发布的版本
- **develop**: 开发分支，最新的开发进度
- **feature/xxx**: 特性分支，用于开发新功能
- **release/x.x.x**: 发布分支，用于版本发布前的测试和修复
- **hotfix/xxx**: 热修复分支，用于修复线上问题

### 1.2 分支命名规范

- feature分支：`feature/功能模块名-功能描述`
- release分支：`release/版本号`
- hotfix分支：`hotfix/问题描述`

### 1.3 工作流程

1. 从develop分支创建feature分支进行功能开发
2. 功能开发完成后，合并回develop分支
3. 版本发布前，从develop分支创建release分支
4. release分支测试通过后，同时合并到main和develop分支
5. 线上问题从main分支创建hotfix分支，修复后同时合并到main和develop分支

## 二、架构设计规范

### 2.1 架构模式

采用MVVM（Model-View-ViewModel）架构模式：

- **Model**: 数据模型层，位于`lib/models`目录
- **View**: 视图层，位于`lib/screens`和`lib/components`目录
- **ViewModel**: 视图模型层，使用Riverpod进行状态管理

### 2.2 目录结构

```
lib/
├── api/          # API接口
├── components/   # 可复用组件
├── models/       # 数据模型
├── screens/      # 页面
├── services/     # 业务服务
├── utils/        # 工具类
└── main.dart     # 入口文件
```

### 2.3 依赖注入

使用Riverpod进行依赖注入和状态管理：

- 所有的全局状态使用Provider管理
- 异步操作使用FutureProvider或StreamProvider
- 复杂状态使用StateNotifier和StateNotifierProvider

## 三、编码规范

### 3.1 命名规范

- **类名**: 使用大驼峰命名法（PascalCase）
- **变量和方法**: 使用小驼峰命名法（camelCase）
- **常量**: 使用大写下划线（UPPER_SNAKE_CASE）
- **私有成员**: 使用下划线前缀（_privateVariable）

### 3.2 文件命名

- 所有文件名使用小写下划线命名法（snake_case）
- 组件文件名应与组件类名对应（小写下划线形式）

### 3.3 代码格式

- 使用2个空格缩进
- 大括号采用K&R风格
- 每行代码不超过80个字符
- 使用dart format格式化代码

### 3.4 注释规范

- 类和公共方法必须添加文档注释（///）
- 复杂逻辑需要添加行内注释（//）
- 临时代码使用TODO注释标记

### 3.5 异常处理

- 使用try-catch捕获可预见的异常
- 在API层统一处理网络异常
- 使用日志记录异常信息

## 四、性能优化准则

### 4.1 UI性能

- 合理使用const构造函数
- 避免在build方法中进行复杂计算
- 使用ListView.builder代替ListView
- 图片资源进行适当压缩

### 4.2 状态管理

- 避免频繁的状态更新
- 合理划分状态粒度
- 及时释放不需要的监听器

### 4.3 资源管理

- 及时释放音频、视频等媒体资源
- 使用懒加载加载大型资源
- 合理使用缓存机制

## 五、测试规范

### 5.1 单元测试

- 业务逻辑必须编写单元测试
- 测试覆盖率要求达到80%以上
- 使用mock处理外部依赖

### 5.2 集成测试

- 核心功能流程必须编写集成测试
- 使用flutter_test进行Widget测试
- 模拟各种异常场景

## 六、发布规范

### 6.1 版本号管理

采用语义化版本号（Semantic Versioning）：

- 主版本号：不兼容的API修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

### 6.2 发布流程

1. 确保所有测试通过
2. 更新版本号和更新日志
3. 在release分支进行最后测试
4. 合并到main分支并打tag
5. 执行发布流程

## 七、安全规范

### 7.1 数据安全

- 敏感数据必须加密存储
- 使用安全的加密算法
- 避免明文传输敏感信息

### 7.2 代码安全

- 避免硬编码敏感信息
- 使用环境变量存储配置信息
- 定期更新依赖包版本

## 八、文档规范

### 8.1 文档要求

- README.md：项目说明、环境要求、启动方式
- API文档：接口说明、参数说明、返回值说明
- 架构文档：系统架构、模块说明、数据流转
- 更新日志：版本更新内容、Breaking Changes

### 8.2 文档管理

- 文档必须及时更新
- 采用Markdown格式编写
- 文档版本与代码版本保持一致