# Hive本地存储与Riverpod状态管理架构说明

## 1. 架构概述

本项目采用了Hive作为本地存储解决方案，Riverpod作为状态管理框架，两者结合实现了高效的数据持久化和响应式UI更新。整体架构遵循了清晰的分层设计原则：

```
应用层（UI组件）
    ↑↓
状态管理层（Riverpod Providers）
    ↑↓
数据访问层（Repositories）
    ↑↓
持久化层（Hive Storage）
```

## 2. Hive本地存储

### 2.1 概述

Hive是一个轻量级、高性能的NoSQL数据库，专为Flutter应用设计。在本项目中，Hive用于持久化存储用户数据，包括科目、项目等信息。

### 2.2 数据模型

所有数据模型都使用`@HiveType`注解进行标记，并通过`build_runner`生成适配器代码：

```dart
@HiveType(typeId: 3)
@JsonSerializable()
class Subject {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String name;
  
  // ...
}
```

### 2.3 初始化流程

```dart
// 在main.dart中初始化Hive
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化Hive
  try {
    final hiveService = HiveService();
    await hiveService.initHive();
    debugPrint('Hive 初始化成功');
  } catch (e) {
    debugPrint('Hive 初始化失败: $e');
  }
  
  // 使用ProviderScope包装应用，启用Riverpod状态管理
  runApp(const ProviderScope(child: MyApp()));
}
```

### 2.4 HiveService

HiveService负责Hive的初始化和提供对各个Repository的访问：

```dart
class HiveService {
  late SubjectRepository subjectRepository;
  
  Future<void> initHive() async {
    // 初始化Hive
    await Hive.initFlutter();
    
    // 注册适配器
    Hive.registerAdapter(SubjectAdapter());
    Hive.registerAdapter(ProjectAdapter());
    // ...
    
    // 初始化仓库
    subjectRepository = SubjectRepository();
    await subjectRepository.init();
  }
}
```

### 2.5 Repository模式

每种数据类型都有对应的Repository类，负责该类型数据的CRUD操作：

```dart
class SubjectRepository {
  static const String subjectBoxName = 'subjects';
  static const String projectBoxName = 'projects';
  
  late Box<Subject> _subjectBox;
  late Box<Project> _projectBox;
  
  // 初始化
  Future<void> init() async {
    // 打开Hive盒子
  }
  
  // 获取所有科目
  List<Subject> getAllSubjects() {
    return _subjectBox.values.toList();
  }
  
  // 删除科目（同时删除关联的项目）
  Future<void> deleteSubject(String id) async {
    await _subjectBox.delete(id);
    
    // 删除关联的项目
    final projectsToDelete = _projectBox.values
        .where((project) => project.subjectId == id)
        .toList();
        
    for (var project in projectsToDelete) {
      await _projectBox.delete(project.id);
    }
  }
  
  // 其他CRUD操作...
}
```

## 3. Riverpod状态管理

### 3.1 概述

Riverpod是一个声明式状态管理框架，提供了可测试、可组合的状态管理解决方案。在本项目中，Riverpod用于管理UI状态并响应数据变化。

### 3.2 状态定义

使用`freezed`库定义不可变状态类：

```dart
@freezed
class SubjectState with _$SubjectState {
  const factory SubjectState({
    @Default([]) List<Subject> subjects,
    @Default([]) List<Project> projects,
    Subject? currentSubject,
    Project? currentProject,
    @Default(0) int currentTabIndex,
    @Default(false) bool isLoading,
    String? error,
  }) = _SubjectState;

  factory SubjectState.fromJson(Map<String, dynamic> json) =>
      _$SubjectStateFromJson(json);
}
```

### 3.3 状态提供者

```dart
// 科目状态Provider
final subjectStateProvider = StateNotifierProvider<SubjectStateNotifier, SubjectState>(
  (ref) => SubjectStateNotifier(),
);
```

### 3.4 状态管理类

```dart
class SubjectStateNotifier extends StateNotifier<SubjectState> {
  SubjectStateNotifier() : super(const SubjectState());

  // 设置加载状态
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  // 删除科目
  void deleteSubject(String subjectId) {
    final updatedSubjects = state.subjects.where((subject) => subject.id != subjectId).toList();
    final updatedProjects = state.projects.where((project) => project.subjectId != subjectId).toList();
    
    // 如果当前选中的科目被删除，则重置当前科目
    Subject? newCurrentSubject = state.currentSubject;
    Project? newCurrentProject = state.currentProject;
    
    if (state.currentSubject?.id == subjectId) {
      newCurrentSubject = updatedSubjects.isNotEmpty ? updatedSubjects.first : null;
      newCurrentProject = null;
    }
    
    // 如果当前选中的项目属于被删除的科目，则重置当前项目
    if (state.currentProject?.subjectId == subjectId) {
      newCurrentProject = null;
    }
    
    state = state.copyWith(
      subjects: updatedSubjects,
      projects: updatedProjects,
      currentSubject: newCurrentSubject,
      currentProject: newCurrentProject,
    );
  }
  
  // 其他状态更新方法...
}
```

## 4. UI与状态的连接

### 4.1 ConsumerWidget/ConsumerStatefulWidget

使用Riverpod提供的Consumer组件连接UI和状态：

```dart
class SubjectProjectCardRiverpod extends ConsumerStatefulWidget {
  const SubjectProjectCardRiverpod({super.key});

  @override
  ConsumerState<SubjectProjectCardRiverpod> createState() => _SubjectProjectCardRiverpodState();
}

class _SubjectProjectCardRiverpodState extends ConsumerState<SubjectProjectCardRiverpod> with TickerProviderStateMixin {
  // ...
  
  @override
  Widget build(BuildContext context) {
    // 监听科目状态
    final subjectState = ref.watch(subjectStateProvider);
    final subjects = subjectState.subjects;
    final projects = subjectState.projects;
    
    // ...
  }
}
```

### 4.2 状态读取与更新

```dart
// 读取状态
final subjectState = ref.watch(subjectStateProvider);

// 更新状态
ref.read(subjectStateProvider.notifier).setCurrentProject(project);
```

## 5. 数据流程

### 5.1 数据加载流程

1. UI组件初始化时，调用HiveService加载数据
2. 从Repository获取数据
3. 更新Riverpod状态
4. UI自动响应状态变化并更新

```dart
Future<void> _loadData() async {
  try {
    // 确保Hive服务已初始化
    await _hiveService.initHive();
    
    // 获取所有科目
    final subjects = _hiveService.subjectRepository.getAllSubjects();
    // 获取所有项目
    final projects = _hiveService.subjectRepository.getAllProjects();
    
    // 更新Riverpod状态
    ref.read(subjectStateProvider.notifier).setSubjects(subjects);
    ref.read(subjectStateProvider.notifier).setProjects(projects);
    
    // 创建TabController
    _updateTabController();
  } catch (e) {
    print('加载数据出错: $e');
    ref.read(subjectStateProvider.notifier).setError('加载数据失败: $e');
  }
}
```

### 5.2 数据修改流程

1. UI触发修改操作
2. 调用Repository保存数据到Hive
3. 更新Riverpod状态
4. UI自动响应状态变化并更新

```dart
// 删除科目示例
onDeleteSubject: (subject) {
  // 确认删除科目
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('确认删除'),
      content: Text('确定要删除科目"${subject.name}"吗？这将同时删除该科目下的所有项目。'),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('取消'),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context);
            // 显示加载指示器
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('正在删除科目...')),
            );
            
            // 实现删除科目功能
            _hiveService.subjectRepository.deleteSubject(subject.id).then((_) {
              // 更新Riverpod状态
              ref.read(subjectStateProvider.notifier).deleteSubject(subject.id);
              // 更新TabController
              _updateTabController();
              // 显示成功消息
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('科目删除成功')),
              );
            }).catchError((error) {
              // 显示错误消息
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('删除失败: $error')),
              );
            });
          },
          child: const Text('删除'),
        ),
      ],
    ),
  );
}
```

## 6. 最佳实践

### 6.1 Hive最佳实践

1. **类型安全**：始终使用类型化的Box，如`Box<Subject>`而非`Box`
2. **关系处理**：在Repository层处理数据关系，如删除科目时同时删除关联的项目
3. **错误处理**：对所有Hive操作进行try-catch处理，确保应用稳定性
4. **懒加载**：按需打开Box，避免不必要的资源消耗
5. **关闭资源**：不再使用时关闭Box，释放资源

### 6.2 Riverpod最佳实践

1. **不可变状态**：使用freezed库确保状态不可变性，避免意外修改
2. **细粒度更新**：设计合适粒度的状态更新方法，避免不必要的重建
3. **状态隔离**：不同功能模块使用独立的Provider，避免状态耦合
4. **选择性监听**：使用select方法只监听状态的特定部分，优化性能
5. **错误处理**：在状态中包含错误信息，统一处理错误展示

### 6.3 UI更新最佳实践

1. **TabController更新**：在状态变化后及时更新TabController，确保UI一致性
2. **加载状态**：显示加载指示器，提升用户体验
3. **错误反馈**：使用SnackBar等组件提供操作反馈
4. **确认对话框**：对重要操作（如删除）使用确认对话框，防止误操作

## 7. 注意事项

1. **状态同步**：确保Hive存储和Riverpod状态保持同步，避免数据不一致
2. **性能考虑**：大量数据操作时考虑使用批处理或异步处理，避免UI卡顿
3. **版本迁移**：设计合理的Hive适配器版本管理策略，支持数据模型升级
4. **依赖注入**：考虑使用依赖注入管理服务实例，提高可测试性
5. **错误恢复**：实现数据错误恢复机制，确保应用在数据异常时仍能正常工作