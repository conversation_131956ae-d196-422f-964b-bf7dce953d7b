# 样式优化指南

## 概述

为了统一项目中的样式定义，我们进行了以下优化：

1. 统一了主题和常量文件，保留 `lib/shared/theme/constants.dart` 和 `lib/shared/theme/app_theme.dart`
2. 添加了更多常用的颜色、透明度和装饰样式常量
3. 使用 `withAlpha` 替代已弃用的 `withOpacity` 方法

## 硬编码样式优化

### 颜色替换

将硬编码的颜色替换为常量：

```dart
// 替换这些硬编码颜色
Colors.white
Colors.black
Colors.grey
Colors.green
Colors.red
Colors.blue
Colors.amber
Colors.purple
Colors.orange
Colors.transparent

// 使用这些常量
AppColors.white
AppColors.black
AppColors.grey
AppColors.green
AppColors.red
AppColors.blue
AppColors.amber
AppColors.purple
AppColors.orange
AppColors.transparent
```

### 透明度替换

将 `withOpacity` 方法替换为 `withAlpha` 方法：

```dart
// 替换这些用法
Colors.white.withOpacity(0.1)
Colors.black.withOpacity(0.2)
AppColors.primary.withOpacity(0.5)

// 使用这些常量或方法
AppColors.white10
AppColors.black20
AppColors.primary50
// 或者
AppColors.whiteWithAlpha(AppColors.alpha10)
AppColors.blackWithAlpha(AppColors.alpha20)
AppColors.primaryWithAlpha(AppColors.alpha50)
```

### 尺寸替换

将硬编码的尺寸替换为常量：

```dart
// 替换这些硬编码尺寸
BorderRadius.circular(4)
BorderRadius.circular(8)
BorderRadius.circular(16)
BorderRadius.circular(24)

// 使用这些常量
BorderRadius.circular(AppSizes.radiusSmall)
BorderRadius.circular(AppSizes.radiusMedium)
BorderRadius.circular(AppSizes.radiusLarge)
BorderRadius.circular(AppSizes.radiusXLarge)
```

```dart
// 替换这些硬编码内边距
EdgeInsets.all(8)
EdgeInsets.all(16)
EdgeInsets.all(24)
EdgeInsets.symmetric(horizontal: 16, vertical: 8)

// 使用这些常量
EdgeInsets.all(AppSizes.paddingSmall)
EdgeInsets.all(AppSizes.paddingMedium)
EdgeInsets.all(AppSizes.paddingLarge)
EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium, vertical: AppSizes.paddingSmall)
```

### 文本样式替换

将硬编码的文本样式替换为常量：

```dart
// 替换这些硬编码文本样式
TextStyle(
  fontSize: 16,
  fontWeight: FontWeight.bold,
  color: Colors.black,
)

// 使用这些常量
AppTextStyles.headline3
// 或者
AppTextStyles.bodyLarge.copyWith(fontWeight: FontWeight.bold)
```

### 阴影替换

将硬编码的阴影替换为常量：

```dart
// 替换这些硬编码阴影
BoxShadow(
  color: Colors.black.withOpacity(0.1),
  offset: Offset(0, 2),
  blurRadius: 4,
)

// 使用这些常量
AppShadows.low
```

## 装饰样式替换

将硬编码的装饰样式替换为预定义的装饰样式：

```dart
// 替换这种用法
BoxDecoration(
  color: Colors.white,
  borderRadius: BorderRadius.circular(16),
  boxShadow: [
    BoxShadow(
      color: Colors.black.withOpacity(0.1),
      offset: Offset(0, 2),
      blurRadius: 4,
    ),
  ],
)

// 使用这种用法
AppDecorations.card()
// 或者
AppDecorations.cardNoBorder(
  color: AppColors.white,
  borderRadius: AppSizes.radiusLarge,
)
```

## 动画时长替换

将硬编码的动画时长替换为常量：

```dart
// 替换这些硬编码动画时长
Duration(milliseconds: 300)
Duration(milliseconds: 500)
Duration(milliseconds: 150)

// 使用这些常量
AppConstants.defaultAnimationDuration
AppConstants.longAnimationDuration
AppConstants.shortAnimationDuration
```

## 优化步骤

1. 使用 IDE 的全局搜索功能，查找硬编码的颜色、尺寸和样式
2. 按照上述指南，将硬编码的样式替换为常量
3. 优先替换频繁使用的组件和页面
4. 完成替换后，运行应用并检查所有页面，确保没有视觉问题

## 注意事项

1. 在替换过程中，确保保持原有的视觉效果不变
2. 如果需要特定的样式，可以使用 `copyWith` 方法在预定义样式的基础上进行修改
3. 如果常量库中没有合适的常量，可以在 `constants.dart` 中添加新的常量
