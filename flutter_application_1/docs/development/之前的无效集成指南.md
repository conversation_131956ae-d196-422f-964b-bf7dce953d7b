<!-- # 前后端集成测试指南
# 无效的文档，后端进行了更新了
本文档提供了前后端集成测试的详细指南，帮助开发人员确保系统各组件之间的正确交互。

## 准备工作

### 环境设置

1. **后端环境**
   - 确保后端服务器在测试环境中运行
   - 设置环境变量：`NODE_ENV=test`
   - 启动命令：`npm run start:test`

2. **前端环境**
   - 确保前端应用配置为连接测试环境API
   - 设置环境变量：`REACT_APP_API_URL=http://localhost:3001/api`
   - 启动命令：`npm run start:test`

3. **数据库**
   - 确保测试数据库已准备好
   - 运行数据库迁移：`npm run db:migrate:test`
   - 加载测试数据：`npm run db:seed:test`

4. **测试工具**
   - 安装Postman或类似工具用于API测试
   - 安装浏览器开发者工具用于前端调试

### 测试账号

- **普通用户**
  - 手机号：`13800138000`
  - 密码：`Password123!`
  - 验证码：在测试环境中，验证码会在API响应中返回

- **高级用户**
  - 手机号：`13800138001`
  - 密码：`Password123!`
  - 会员类型：`premium`

- **测试用苹果账号**
  - 身份令牌：`test_apple_id_123`（测试环境专用）

## 测试流程

### 1. 认证功能测试

#### 1.1 发送验证码

**API**: `POST /api/auth/send-verification-code`

**请求体**:
```json
{
  "phone": "13800138000"
}
```

**预期结果**:
- 状态码：200
- 响应包含验证码（仅测试环境）
- 前端显示验证码发送成功提示

**测试步骤**:
1. 在前端登录页面输入手机号
2. 点击"获取验证码"按钮
3. 验证前端显示倒计时
4. 检查后端日志确认验证码生成
5. 验证响应中包含验证码（测试环境）

#### 1.2 验证验证码

**API**: `POST /api/auth/verify-code`

**请求体**:
```json
{
  "phone": "13800138000",
  "code": "123456"  // 使用实际收到的验证码
}
```

**预期结果**:
- 状态码：200
- 响应表明验证成功
- 前端显示验证成功提示

**测试步骤**:
1. 在前端验证码输入框中输入收到的验证码
2. 点击"验证"按钮
3. 验证前端显示验证成功提示
4. 检查后端日志确认验证成功

#### 1.3 注册

**API**: `POST /api/auth/register`

**请求体**:
```json
{
  "phone": "13800138002",  // 使用新手机号
  "verificationCode": "123456",  // 使用实际收到的验证码
  "password": "Password123!",
  "username": "testuser",
  "nickname": "测试用户"
}
```

**预期结果**:
- 状态码：201
- 响应包含用户信息和令牌
- 前端自动登录并跳转到主页

**测试步骤**:
1. 在前端注册页面填写注册信息
2. 点击"注册"按钮
3. 验证前端显示注册成功提示并自动登录
4. 检查后端日志确认用户创建成功
5. 验证数据库中存在新用户记录

#### 1.4 密码登录

**API**: `POST /api/auth/login`

**请求体**:
```json
{
  "phone": "13800138000",
  "password": "Password123!",
  "rememberMe": true
}
```

**预期结果**:
- 状态码：200
- 响应包含用户信息和令牌
- 前端跳转到主页
- 本地存储中保存令牌

**测试步骤**:
1. 在前端登录页面输入手机号和密码
2. 勾选"记住我"选项
3. 点击"登录"按钮
4. 验证前端显示登录成功提示并跳转到主页
5. 检查后端日志确认登录成功
6. 验证本地存储中保存了令牌

#### 1.5 验证码登录

**API**: `POST /api/auth/login`

**请求体**:
```json
{
  "phone": "13800138000",
  "code": "123456",  // 使用实际收到的验证码
  "rememberMe": true
}
```

**预期结果**:
- 状态码：200
- 响应包含用户信息和令牌
- 前端跳转到主页
- 本地存储中保存令牌

**测试步骤**:
1. 在前端登录页面输入手机号
2. 获取并输入验证码
3. 勾选"记住我"选项
4. 点击"登录"按钮
5. 验证前端显示登录成功提示并跳转到主页
6. 检查后端日志确认登录成功
7. 验证本地存储中保存了令牌

#### 1.6 苹果登录

**API**: `POST /api/auth/apple-login`

**请求体**:
```json
{
  "identityToken": "test_apple_id_123",  // 测试环境专用
  "rememberMe": true
}
```

**预期结果**:
- 状态码：200
- 响应包含用户信息和令牌
- 前端跳转到主页
- 本地存储中保存令牌

**测试步骤**:
1. 在前端登录页面点击"苹果登录"按钮
2. 模拟苹果登录流程（测试环境）
3. 验证前端显示登录成功提示并跳转到主页
4. 检查后端日志确认登录成功
5. 验证本地存储中保存了令牌

#### 1.7 刷新令牌

**API**: `POST /api/auth/refresh-token`

**请求体**:
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."  // 使用实际的刷新令牌
}
```

**预期结果**:
- 状态码：200
- 响应包含新的访问令牌和刷新令牌
- 前端更新本地存储中的令牌

**测试步骤**:
1. 模拟访问令牌过期场景
2. 验证前端自动使用刷新令牌获取新的访问令牌
3. 检查后端日志确认令牌刷新成功
4. 验证本地存储中的令牌已更新
5. 验证用户无需重新登录即可继续使用应用

### 2. 用户功能测试

#### 2.1 获取用户信息

**API**: `GET /api/user/profile`

**预期结果**:
- 状态码：200
- 响应包含用户详细信息
- 前端显示用户信息

**测试步骤**:
1. 登录后访问个人资料页面
2. 验证前端显示正确的用户信息
3. 检查后端日志确认请求成功
4. 验证显示的信息与数据库中的用户记录一致

#### 2.2 更新用户信息

**API**: `PUT /api/user/profile`

**请求体**:
```json
{
  "nickname": "新昵称",
  "preferences": {
    "theme": "dark"
  }
}
```

**预期结果**:
- 状态码：200
- 响应包含更新后的用户信息
- 前端显示更新后的用户信息

**测试步骤**:
1. 登录后访问个人资料页面
2. 修改昵称和主题偏好
3. 点击"保存"按钮
4. 验证前端显示更新成功提示
5. 检查后端日志确认更新成功
6. 验证数据库中的用户记录已更新
7. 刷新页面后验证显示的是更新后的信息

### 3. 备份功能测试

#### 3.1 创建备份

**API**: `POST /api/backup/create`

**请求体**:
```json
{
  "data": {
    "tasks": [...],
    "settings": {...}
  },
  "description": "测试备份"
}
```

**预期结果**:
- 状态码：201
- 响应包含备份ID和创建时间
- 前端显示备份成功提示

**测试步骤**:
1. 登录后访问备份页面
2. 点击"创建备份"按钮
3. 输入备份描述
4. 点击"确认"按钮
5. 验证前端显示备份成功提示
6. 检查后端日志确认备份创建成功
7. 验证数据库中存在新的备份记录

#### 3.2 获取备份列表

**API**: `GET /api/backup/list`

**预期结果**:
- 状态码：200
- 响应包含备份列表
- 前端显示备份列表

**测试步骤**:
1. 登录后访问备份页面
2. 验证前端显示备份列表
3. 检查后端日志确认请求成功
4. 验证显示的备份列表与数据库中的记录一致

#### 3.3 获取备份详情

**API**: `GET /api/backup/{backupId}`

**预期结果**:
- 状态码：200
- 响应包含备份详细信息
- 前端显示备份详情

**测试步骤**:
1. 登录后访问备份页面
2. 点击某个备份的"详情"按钮
3. 验证前端显示备份详情
4. 检查后端日志确认请求成功
5. 验证显示的备份详情与数据库中的记录一致

#### 3.4 恢复备份

**API**: `POST /api/backup/{backupId}/restore`

**预期结果**:
- 状态码：200
- 响应表明恢复成功
- 前端显示恢复成功提示
- 应用状态恢复到备份时的状态

**测试步骤**:
1. 登录后访问备份页面
2. 点击某个备份的"恢复"按钮
3. 确认恢复操作
4. 验证前端显示恢复成功提示
5. 检查后端日志确认恢复成功
6. 验证应用状态已恢复到备份时的状态

### 4. 错误处理测试

#### 4.1 无效验证码

**API**: `POST /api/auth/verify-code`

**请求体**:
```json
{
  "phone": "13800138000",
  "code": "000000"  // 使用错误的验证码
}
```

**预期结果**:
- 状态码：400
- 响应包含错误信息
- 前端显示验证码无效提示

**测试步骤**:
1. 在前端验证码输入框中输入错误的验证码
2. 点击"验证"按钮
3. 验证前端显示验证码无效提示
4. 检查后端日志确认验证失败

#### 4.2 无效登录凭证

**API**: `POST /api/auth/login`

**请求体**:
```json
{
  "phone": "13800138000",
  "password": "WrongPassword123!"  // 使用错误的密码
}
```

**预期结果**:
- 状态码：401
- 响应包含错误信息
- 前端显示账号或密码错误提示

**测试步骤**:
1. 在前端登录页面输入正确的手机号和错误的密码
2. 点击"登录"按钮
3. 验证前端显示账号或密码错误提示
4. 检查后端日志确认登录失败

#### 4.3 未授权访问

**API**: `GET /api/user/profile`

**请求头**: 无令牌或无效令牌

**预期结果**:
- 状态码：401
- 响应包含错误信息
- 前端重定向到登录页面

**测试步骤**:
1. 清除本地存储中的令牌
2. 尝试访问需要认证的页面
3. 验证前端重定向到登录页面
4. 检查后端日志确认未授权访问

#### 4.4 请求频率限制

**API**: `POST /api/auth/send-verification-code`

**请求体**:
```json
{
  "phone": "13800138000"
}
```

**预期结果**:
- 多次请求后状态码：429
- 响应包含错误信息和重试时间
- 前端显示请求过于频繁提示

**测试步骤**:
1. 在短时间内多次请求发送验证码
2. 验证前端显示请求过于频繁提示
3. 检查后端日志确认请求被限制
4. 等待限制时间过后再次请求，验证可以正常发送

## 端到端测试场景

### 场景1：新用户注册并登录

1. 访问注册页面
2. 输入手机号并获取验证码
3. 输入验证码、密码和用户信息
4. 提交注册表单
5. 验证注册成功并自动登录
6. 验证跳转到主页
7. 退出登录
8. 使用新注册的账号和密码登录
9. 验证登录成功并跳转到主页

### 场景2：忘记密码并重置

1. 访问登录页面
2. 点击"忘记密码"链接
3. 输入手机号并获取验证码
4. 输入验证码和新密码
5. 提交重置密码表单
6. 验证密码重置成功
7. 使用新密码登录
8. 验证登录成功并跳转到主页

### 场景3：创建和恢复备份

1. 登录系统
2. 创建一些测试数据
3. 访问备份页面
4. 创建新备份
5. 验证备份创建成功
6. 修改或删除一些数据
7. 恢复之前创建的备份
8. 验证数据已恢复到备份时的状态

### 场景4：会话管理

1. 登录系统并勾选"记住我"
2. 关闭浏览器
3. 重新打开应用
4. 验证用户仍然处于登录状态
5. 模拟访问令牌过期
6. 验证系统自动使用刷新令牌获取新的访问令牌
7. 验证用户无需重新登录即可继续使用应用
8. 退出登录
9. 验证所有令牌已被清除

## 测试工具使用指南

### Postman

1. 导入测试集合：`docs/postman/focus-app-api.json`
2. 设置环境变量：
   - `API_URL`: `http://localhost:3001/api`
   - `ACCESS_TOKEN`: 登录后获取的访问令牌
   - `REFRESH_TOKEN`: 登录后获取的刷新令牌

3. 运行测试集合：
   - 单个请求：选择请求并点击"Send"
   - 整个集合：右键点击集合并选择"Run Collection"

### 浏览器开发者工具

1. 打开开发者工具：
   - Chrome: F12 或 Ctrl+Shift+I
   - Firefox: F12 或 Ctrl+Shift+I
   - Safari: Cmd+Option+I

2. 网络请求监控：
   - 切换到"Network"标签
   - 筛选XHR请求：点击"XHR"
   - 查看请求详情：点击请求名称

3. 本地存储检查：
   - 切换到"Application"标签（Chrome）或"Storage"标签（Firefox）
   - 展开"Local Storage"
   - 查看存储的令牌和用户信息

## 常见问题与解决方案

### 1. 验证码无法发送

**可能原因**:
- 后端服务未启动
- 请求频率限制
- 手机号格式不正确

**解决方案**:
- 检查后端服务是否正常运行
- 等待限制时间过后再次尝试
- 确保手机号格式正确（11位数字）

### 2. 登录失败

**可能原因**:
- 账号或密码错误
- 验证码无效或已过期
- 用户账号被禁用

**解决方案**:
- 检查账号和密码是否正确
- 重新获取验证码
- 联系管理员解除账号禁用

### 3. 令牌刷新失败

**可能原因**:
- 刷新令牌已过期
- 刷新令牌无效
- 用户账号状态变更

**解决方案**:
- 重新登录获取新的令牌
- 检查刷新令牌是否正确
- 检查用户账号状态

### 4. 备份创建失败

**可能原因**:
- 数据格式不正确
- 数据过大
- 服务器存储空间不足

**解决方案**:
- 检查数据格式是否符合要求
- 减少数据量
- 联系管理员增加存储空间

## 测试报告模板

```markdown
# 集成测试报告

## 测试信息
- 测试日期：YYYY-MM-DD
- 测试环境：[测试环境URL]
- 测试人员：[姓名]
- 测试版本：[版本号]

## 测试结果摘要
- 总测试用例数：XX
- 通过数：XX
- 失败数：XX
- 跳过数：XX
- 通过率：XX%

## 测试用例详情

| ID | 测试用例 | 预期结果 | 实际结果 | 状态 | 备注 |
|----|---------|---------|---------|------|------|
| 1  | 发送验证码 | 验证码发送成功 | 验证码发送成功 | 通过 |  |
| 2  | 验证验证码 | 验证码验证成功 | 验证码验证成功 | 通过 |  |
| ...| ... | ... | ... | ... | ... |

## 问题与建议

### 问题1
- 描述：[问题描述]
- 重现步骤：[重现步骤]
- 严重程度：[高/中/低]
- 状态：[待修复/已修复/已关闭]

### 建议1
- 描述：[建议描述]
- 优先级：[高/中/低]
- 状态：[待实施/已实施/已拒绝]

## 结论与下一步计划
[总结测试结果，提出下一步测试计划]
```

## 结论

本集成测试指南提供了全面的测试流程和方法，帮助开发人员确保前后端系统的正确交互。通过遵循本指南进行测试，可以提前发现并解决潜在问题，提高系统的稳定性和用户体验。 -->
