# 前后端集成测试计划

本文档提供了前后端集成测试的详细计划，包括测试环境准备、测试用例和测试流程。

## 目录

1. [测试环境准备](#测试环境准备)
2. [测试用例](#测试用例)
3. [测试流程](#测试流程)
4. [测试报告](#测试报告)
5. [问题跟踪](#问题跟踪)

## 测试环境准备

### 后端环境

1. 确保MongoDB已启动
2. 重置测试数据：
   ```bash
   node scripts/reset-test-data.js
   ```
3. 启动测试环境：
   ```bash
   NODE_ENV=test node dist/server.js
   ```

### 前端环境

1. 配置API基础URL为 `http://localhost:3000/api`
2. 确保前端应用已启动并可以访问

### 测试工具

1. API测试工具（如Postman、Insomnia或自定义脚本）
2. 浏览器开发者工具
3. 网络监控工具

## 测试用例

### 1. 用户认证测试

| 测试ID | 测试名称 | 测试步骤 | 预期结果 |
|--------|---------|---------|---------|
| AUTH-01 | 用户登录 | 1. 使用有效凭据登录<br>2. 检查响应 | 1. 返回200状态码<br>2. 返回访问令牌、刷新令牌和用户信息 |
| AUTH-02 | 无效登录 | 1. 使用无效凭据登录<br>2. 检查响应 | 1. 返回401状态码<br>2. 返回错误消息 |
| AUTH-03 | 刷新令牌 | 1. 使用有效刷新令牌<br>2. 检查响应 | 1. 返回200状态码<br>2. 返回新的访问令牌、刷新令牌和用户信息 |
| AUTH-04 | 无效刷新令牌 | 1. 使用无效刷新令牌<br>2. 检查响应 | 1. 返回401状态码<br>2. 返回错误消息 |
| AUTH-05 | 令牌过期处理 | 1. 使用过期的访问令牌<br>2. 使用刷新令牌获取新令牌<br>3. 使用新令牌访问API | 1. 返回401状态码<br>2. 成功获取新令牌<br>3. 成功访问API |

### 2. 用户信息测试

| 测试ID | 测试名称 | 测试步骤 | 预期结果 |
|--------|---------|---------|---------|
| USER-01 | 获取用户信息 | 1. 使用有效令牌获取用户信息<br>2. 检查响应 | 1. 返回200状态码<br>2. 返回用户详细信息 |
| USER-02 | 更新用户信息 | 1. 使用有效令牌更新用户信息<br>2. 检查响应<br>3. 获取用户信息验证更新 | 1. 返回200状态码<br>2. 返回更新后的用户信息<br>3. 信息已更新 |
| USER-03 | 更新用户密码 | 1. 使用有效令牌更新密码<br>2. 检查响应<br>3. 使用新密码登录 | 1. 返回200状态码<br>2. 返回成功消息<br>3. 登录成功 |

### 3. 备份功能测试

| 测试ID | 测试名称 | 测试步骤 | 预期结果 |
|--------|---------|---------|---------|
| BACKUP-01 | 获取备份列表 | 1. 使用有效令牌获取备份列表<br>2. 检查响应 | 1. 返回200状态码<br>2. 返回备份列表和分页信息 |
| BACKUP-02 | 创建备份 | 1. 使用有效令牌创建备份<br>2. 检查响应<br>3. 获取备份列表验证 | 1. 返回201状态码<br>2. 返回新创建的备份信息<br>3. 备份已添加到列表 |
| BACKUP-03 | 获取单个备份 | 1. 使用有效令牌获取单个备份<br>2. 检查响应 | 1. 返回200状态码<br>2. 返回备份详细信息 |
| BACKUP-04 | 更新备份 | 1. 使用有效令牌更新备份<br>2. 检查响应<br>3. 获取备份验证更新 | 1. 返回200状态码<br>2. 返回更新后的备份信息<br>3. 信息已更新 |
| BACKUP-05 | 删除备份 | 1. 使用有效令牌删除备份<br>2. 检查响应<br>3. 获取备份列表验证 | 1. 返回200状态码<br>2. 返回成功消息<br>3. 备份已从列表中移除 |
| BACKUP-06 | 获取备份配额 | 1. 使用有效令牌获取备份配额<br>2. 检查响应 | 1. 返回200状态码<br>2. 返回配额信息 |

### 4. 订阅功能测试

| 测试ID | 测试名称 | 测试步骤 | 预期结果 |
|--------|---------|---------|---------|
| SUB-01 | 获取价格计划 | 1. 使用有效令牌获取价格计划<br>2. 检查响应 | 1. 返回200状态码<br>2. 返回价格计划列表 |
| SUB-02 | 获取当前订阅 | 1. 使用有效令牌获取当前订阅<br>2. 检查响应 | 1. 返回200状态码<br>2. 返回订阅信息或404状态码 |
| SUB-03 | 获取订阅历史 | 1. 使用有效令牌获取订阅历史<br>2. 检查响应 | 1. 返回200状态码<br>2. 返回订阅历史和分页信息 |

### 5. 错误处理测试

| 测试ID | 测试名称 | 测试步骤 | 预期结果 |
|--------|---------|---------|---------|
| ERR-01 | 无效路径 | 1. 访问不存在的API路径<br>2. 检查响应 | 1. 返回404状态码<br>2. 返回错误消息 |
| ERR-02 | 无效请求 | 1. 发送格式错误的请求<br>2. 检查响应 | 1. 返回400状态码<br>2. 返回错误消息和详细错误信息 |
| ERR-03 | 速率限制 | 1. 在短时间内多次请求同一端点<br>2. 检查响应 | 1. 返回429状态码<br>2. 返回错误消息和重试时间 |

## 测试流程

### 1. 准备阶段

1. 启动后端测试环境
2. 重置测试数据
3. 准备测试工具和脚本

### 2. 执行阶段

1. 按照测试用例顺序执行测试
2. 记录每个测试的结果
3. 对于失败的测试，记录详细错误信息

### 3. 验证阶段

1. 验证所有测试用例的结果
2. 检查前端UI是否正确显示API返回的数据
3. 验证错误处理是否符合预期

## 测试报告

测试报告应包含以下内容：

1. 测试环境信息
2. 测试执行日期和时间
3. 测试用例执行结果汇总
4. 失败测试的详细信息
5. 发现的问题和建议

## 问题跟踪

对于测试中发现的问题，应记录以下信息：

1. 问题ID
2. 问题描述
3. 复现步骤
4. 预期结果和实际结果
5. 问题严重程度
6. 问题状态（新建、进行中、已解决、已关闭）

## 集成测试检查清单

- [ ] 所有API端点都能正确响应
- [ ] 所有响应格式符合预期
- [ ] 认证流程工作正常
- [ ] 错误处理符合预期
- [ ] 前端UI正确显示API返回的数据
- [ ] 速率限制处理正确
- [ ] 分页功能工作正常
- [ ] 文件上传功能工作正常

---

如有任何问题，请联系后端开发团队。
