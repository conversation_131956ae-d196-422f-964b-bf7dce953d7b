# 分支管理策略

本文档描述了项目的分支管理策略，旨在规范开发流程，提高代码质量和团队协作效率。

## 分支类型

我们的项目使用以下类型的分支：

### 1. 主分支 (`main` 或 `master`)

- **用途**：包含稳定、可发布的代码
- **特点**：
  - 随时可以部署到生产环境
  - 所有代码都经过充分测试
  - 通过标签（tag）标记版本号
- **命名规范**：`main` 或 `master`
- **保护措施**：
  - 禁止直接提交
  - 只能通过合并请求（Pull Request）更新
  - 需要代码审查和CI测试通过

### 2. 开发分支 (`develop`)

- **用途**：包含最新的开发代码
- **特点**：
  - 集成所有已完成的功能
  - 可能包含尚未发布的功能
  - 相对稳定，但可能存在一些问题
- **命名规范**：`develop`
- **保护措施**：
  - 禁止直接提交（除非是小修改）
  - 主要通过合并功能分支更新
  - 需要基本的CI测试通过

### 3. 功能分支 (`feature/*`)

- **用途**：开发新功能
- **特点**：
  - 从`develop`分支创建
  - 完成后合并回`develop`分支
  - 一个分支只开发一个功能
- **命名规范**：`feature/功能名称`，例如：`feature/user-authentication`
- **生命周期**：
  - 创建：开始开发新功能时
  - 合并：功能完成并通过测试后
  - 删除：合并到`develop`分支后

### 4. 发布分支 (`release/*`)

- **用途**：准备版本发布
- **特点**：
  - 从`develop`分支创建
  - 只进行bug修复和文档更新
  - 完成后同时合并到`main`和`develop`分支
- **命名规范**：`release/版本号`，例如：`release/1.0.0`
- **生命周期**：
  - 创建：准备发布新版本时
  - 合并：版本准备就绪后
  - 删除：合并到`main`和`develop`分支后

### 5. 修复分支 (`hotfix/*`)

- **用途**：修复生产环境中的问题
- **特点**：
  - 从`main`分支创建
  - 只修复特定问题
  - 完成后同时合并到`main`和`develop`分支
- **命名规范**：`hotfix/问题描述`，例如：`hotfix/login-crash`
- **生命周期**：
  - 创建：发现生产环境问题时
  - 合并：问题修复并测试通过后
  - 删除：合并到`main`和`develop`分支后

### 6. 存档分支 (`archived/*`)

- **用途**：存档不再活跃开发但可能将来有用的代码
- **特点**：
  - 不会被合并到其他分支
  - 仅用于参考和存档
- **命名规范**：`archived/功能名称-日期`，例如：`archived/audio-feature-20250508`
- **生命周期**：长期保留，除非确定永远不会使用

## 工作流程

### 1. 功能开发流程

1. 从`develop`分支创建功能分支
   ```bash
   git checkout develop
   git pull
   git checkout -b feature/new-feature
   ```

2. 在功能分支上进行开发
   ```bash
   # 进行代码修改
   git add .
   git commit -m "实现新功能的某个部分"
   ```

3. 定期将`develop`分支合并到功能分支，保持同步
   ```bash
   git checkout feature/new-feature
   git merge develop
   # 解决可能的冲突
   ```

4. 功能完成后，创建Pull Request将功能分支合并到`develop`分支
   - 进行代码审查
   - 运行自动化测试
   - 解决反馈问题

5. 合并完成后，删除功能分支
   ```bash
   git branch -d feature/new-feature
   ```

### 2. 版本发布流程

1. 从`develop`分支创建发布分支
   ```bash
   git checkout develop
   git pull
   git checkout -b release/1.0.0
   ```

2. 在发布分支上进行最终调整和bug修复
   ```bash
   # 进行必要的修改
   git add .
   git commit -m "修复发布前的问题"
   ```

3. 测试发布分支，确保所有功能正常工作

4. 创建Pull Request将发布分支合并到`main`分支
   - 进行最终代码审查
   - 运行完整的测试套件

5. 在`main`分支上创建版本标签
   ```bash
   git checkout main
   git pull
   git tag -a v1.0.0 -m "Version 1.0.0"
   git push origin v1.0.0
   ```

6. 将发布分支也合并回`develop`分支
   ```bash
   git checkout develop
   git merge --no-ff release/1.0.0
   git push origin develop
   ```

7. 删除发布分支
   ```bash
   git branch -d release/1.0.0
   ```

### 3. 热修复流程

1. 从`main`分支创建修复分支
   ```bash
   git checkout main
   git pull
   git checkout -b hotfix/critical-bug
   ```

2. 在修复分支上修复问题
   ```bash
   # 进行必要的修改
   git add .
   git commit -m "修复关键问题"
   ```

3. 测试修复，确保问题已解决

4. 创建Pull Request将修复分支合并到`main`分支
   - 进行代码审查
   - 运行必要的测试

5. 在`main`分支上创建新的版本标签
   ```bash
   git checkout main
   git pull
   git tag -a v1.0.1 -m "Hotfix: Version 1.0.1"
   git push origin v1.0.1
   ```

6. 将修复分支也合并回`develop`分支
   ```bash
   git checkout develop
   git merge --no-ff hotfix/critical-bug
   git push origin develop
   ```

7. 删除修复分支
   ```bash
   git branch -d hotfix/critical-bug
   ```

## 最佳实践

1. **频繁提交**：每个提交应该是一个逻辑上完整的变更，便于代码审查和回滚

2. **有意义的提交信息**：提交信息应该清晰描述变更内容和原因

3. **保持分支同步**：定期将`develop`分支合并到功能分支，减少合并冲突

4. **功能分支生命周期**：功能分支应该尽量短暂，避免长时间不合并

5. **代码审查**：所有合并到`develop`和`main`的代码都应该经过审查

6. **自动化测试**：利用CI/CD工具自动运行测试，确保代码质量

7. **分支命名**：遵循命名规范，使分支用途一目了然

## 工具和集成

1. **Git工具**：使用Git命令行或图形界面工具（如SourceTree、GitKraken）

2. **代码托管平台**：使用GitHub、GitLab或Bitbucket等平台

3. **CI/CD集成**：使用GitHub Actions、GitLab CI或Jenkins等工具

4. **代码审查工具**：使用平台提供的Pull Request功能进行代码审查

## 常见问题解答

**Q: 什么时候应该创建功能分支？**
A: 当开始开发一个新功能，且该功能需要多次提交才能完成时。

**Q: 如何处理长期开发的功能？**
A: 将大功能拆分为多个小功能，分别创建功能分支，逐个完成并合并。

**Q: 如何处理紧急修复？**
A: 使用`hotfix`分支从`main`分支创建，修复后合并回`main`和`develop`。

**Q: 什么情况下应该使用存档分支？**
A: 当某个功能开发暂停，但代码可能在将来有用时，可以创建存档分支保存。

## 结论

遵循本文档描述的分支管理策略，可以帮助团队更有效地协作，提高代码质量，减少发布风险。随着项目的发展，我们可能会调整策略以适应新的需求和挑战。

最后更新：2025年5月8日
