# 功能分支状态文档

本文档记录了项目中各功能分支的状态、内容和整理情况，以便团队成员了解和参考。

## 分支整理概述

在项目开发过程中，我们创建了多个功能分支来开发不同的功能模块。为了保持代码库的整洁和可维护性，我们进行了分支整理工作，将有用的功能模块代码提取并存档，以便将来参考和集成。

整理日期：2025年5月8日

## 功能分支状态

### 1. 模拟考试功能 (`feature/ebbinghaus-memory`)

**状态**：已存档，暂不集成到主开发分支

**描述**：实现艾宾浩斯记忆曲线的模拟考试功能，包括考试模板创建、编辑、考试实例管理等功能。

**主要文件**：
- `lib/features/exam/models/exam_template_models.dart`
- `lib/features/exam/providers/exam_state.dart`
- `lib/features/exam/screens/exam_home_screen.dart`
- `lib/features/exam/screens/exam_template_create_new_screen.dart`
- `lib/features/exam/services/exam_template_service.dart`

**存档位置**：`archived_features/exam/`

**未来计划**：计划在v1.2版本中集成此功能，需要进行UI适配和功能优化。

### 2. 每日复盘功能 (`feature/daily-review`)

**状态**：已存档，暂不集成到主开发分支

**描述**：实现每日学习和专注情况的复盘功能，帮助用户回顾和总结每日学习成果。

**主要文件**：
- `lib/features/review/screens/daily_review_screen.dart`
- `lib/features/review/widgets/review_summary_card.dart`
- `lib/core/models/daily_review.dart`
- `lib/core/repositories/daily_review_repository.dart`

**存档位置**：`archived_features/daily-review/`

**未来计划**：计划在v1.3版本中集成此功能，需要与数据分析模块进行整合。

### 3. 紫色主题方案 (`feature/color-scheme-purple`)

**状态**：已存档，可作为可选主题

**描述**：实现紫色为主色调的应用主题方案。

**主要文件**：
- `lib/shared/theme/constants.dart`（紫色主题版本）

**存档位置**：`archived_features/color-schemes/purple_theme_constants.dart`

**未来计划**：计划在v1.1版本中实现主题切换功能，将此作为可选主题之一。

### 4. 绿色主题方案 (`feature/color-scheme-green`)

**状态**：已存档，可作为可选主题

**描述**：实现绿色为主色调的应用主题方案。

**主要文件**：
- `lib/shared/theme/constants.dart`（绿色主题版本）

**存档位置**：`archived_features/color-schemes/green_theme_constants.dart`

**未来计划**：计划在v1.1版本中实现主题切换功能，将此作为可选主题之一。

### 5. 场景音频功能 (`feature/audio-scene`)

**状态**：部分代码已在主分支，但功能未完成

**描述**：实现专注场景的音频播放功能，提供白噪音等音频场景。

**主要文件**：
- `lib/features/audio/screens/scene_selection_screen.dart`
- `lib/core/services/audio_service.dart`

**未来计划**：计划在v1.1版本中完成此功能的开发。

## 分支管理策略

为了更好地管理代码和功能开发，我们采用以下分支管理策略：

1. **主分支**：`main` 或 `master`，只包含稳定、可发布的代码
2. **开发分支**：`develop`，包含最新的开发代码
3. **功能分支**：`feature/*`，从 `develop` 分支创建，用于开发新功能
4. **发布分支**：`release/*`，从 `develop` 分支创建，用于准备发布
5. **修复分支**：`hotfix/*`，从 `main` 分支创建，用于修复生产环境中的问题
6. **存档分支**：`archived/*`，用于存档不再活跃开发但可能将来有用的代码

## 如何使用存档的功能代码

如果需要参考或集成存档的功能代码，请按照以下步骤操作：

1. 查看对应功能的存档目录，了解功能结构和主要文件
2. 创建新的功能分支：`git checkout -b feature/new-feature develop`
3. 参考存档代码，根据当前项目结构进行适配和优化
4. 完成功能开发后，通过Pull Request将功能合并到`develop`分支

**注意**：直接复制存档代码可能会导致兼容性问题，请确保进行必要的调整和测试。

## 文档维护

本文档将随着项目的发展和分支的变化而更新。如有任何问题或建议，请联系项目管理员。

最后更新：2025年5月8日
