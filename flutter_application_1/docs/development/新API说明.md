# 专注应用API参考文档

本文档提供了专注应用后端API的详细说明，用于前端开发人员进行集成。

## 基本信息

- **基础URL**:
  - 生产环境：`https://arborflame.com/api`
  - 开发环境：`http://localhost:3000/api`
- **认证方式**: Bearer <PERSON>ken
- **内容类型**: `application/json`
- **响应格式**:
  ```json
  {
    "status": "success" | "error",
    "message": "操作成功/失败信息",
    "data": {...} | null,
    "timestamp": 1625123456789,
    "path": "/api/users"
  }
  ```

## 目录

1. [认证API](#认证api)
   - [发送邮箱验证码](#发送邮箱验证码)
   - [验证邮箱验证码](#验证邮箱验证码)
   - [邮箱注册](#邮箱注册)
   - [邮箱验证码登录](#邮箱验证码登录)
   - [邮箱密码登录](#邮箱密码登录)
   - [忘记密码](#忘记密码发送重置验证码)
   - [重置密码](#重置密码)
   - [Apple登录](#apple登录)
   - [刷新令牌](#刷新令牌)
2. [用户API](#用户api)
   - [获取当前用户信息](#获取当前用户信息)
   - [更新用户信息](#更新用户信息)
   - [更新用户密码](#更新用户密码)
   - [注销账号](#注销账号)

## 认证API

### 发送邮箱验证码 ⭐ 推荐使用

发送验证码到指定邮箱（已修复安全漏洞）。

> **重要提醒**：请使用此接口发送邮箱验证码，不要使用 `/api/auth/send-verification-code`（该接口仅支持手机号）

**请求**:
```
POST /api/email-auth/send-verification-code?purpose={purpose}
```

**查询参数**:
- `purpose` (可选): 验证码用途
  - `login`: 登录（默认值，要求邮箱已注册）
  - `register`: 注册（要求邮箱未注册）
  - `reset_password`: 重置密码（要求邮箱已注册）

**请求体**:
```json
{
  "email": "<EMAIL>"
}
```

**请求示例**:
```bash
# 登录验证码（邮箱必须已注册）
POST /api/email-auth/send-verification-code?purpose=login

# 注册验证码（邮箱必须未注册）
POST /api/email-auth/send-verification-code?purpose=register

# 重置密码验证码（邮箱必须已注册）
POST /api/email-auth/send-verification-code?purpose=reset_password
```

**成功响应**:
```json
{
  "status": "success",
  "message": "验证码已发送到您的邮箱",
  "data": null
}
```

**错误响应**:
```json
{
  "status": "error",
  "message": "该邮箱尚未注册，请先注册账号",
  "data": null
}
```

**安全特性**:
- ✅ 登录时验证邮箱是否已注册
- ✅ 注册时验证邮箱是否未注册
- ✅ 防止向任意邮箱发送验证码
- ✅ 防止垃圾邮件攻击和隐私泄露

### ⚠️ 废弃接口：发送手机验证码

**接口**: `POST /api/auth/send-verification-code`

**状态**: 已废弃用于邮箱验证码

**说明**: 此接口仅支持手机号验证码，如果传递邮箱参数会返回错误。请使用 `/api/email-auth/send-verification-code` 发送邮箱验证码。

**错误响应示例**:
```json
{
  "status": "error",
  "message": "此接口仅支持手机验证码，请使用 /api/email-auth/send-verification-code 发送邮箱验证码"
}
```

### 验证邮箱验证码

验证邮箱验证码是否有效。

**请求**:
```
POST /api/email-auth/verify-code
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "code": "123456"
}
```

**响应**:
```json
{
  "status": "success",
  "message": "验证码验证成功",
  "data": {
    "valid": true
  }
}
```

### 邮箱注册

使用邮箱和验证码注册新用户。

**请求**:
```
POST /api/email-auth/register
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "verificationCode": "123456",
  "password": "Password123",
  "username": "username",
  "nickname": "用户昵称",
  "deviceInfo": {
    "type": "mobile",
    "os": "iOS",
    "version": "15.0"
  }
}
```

**响应**:
```json
{
  "status": "success",
  "message": "注册成功",
  "data": {
    "user": {
      "id": "60d21b4667d0d8992e610c85",
      "email": "<EMAIL>",
      "username": "username",
      "nickname": "用户昵称",
      "authMethods": {
        "email": true
      },
      "membershipType": "free",
      "preferences": {
        "theme": "system",
        "language": "zh-CN",
        "notifications": true
      },
      "settings": {
        "notifications": true,
        "focusReminders": true,
        "autoBackup": true
      },
      "createdAt": "2023-06-22T12:00:00Z"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
  }
}
```

### 邮箱验证码登录

使用邮箱和验证码登录。

**请求**:
```
POST /api/email-auth/login-with-code
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "code": "123456",
  "rememberMe": true,
  "deviceInfo": {
    "type": "mobile",
    "os": "iOS",
    "version": "15.0"
  }
}
```

**响应**:
```json
{
  "status": "success",
  "message": "登录成功",
  "data": {
    "user": {
      "id": "60d21b4667d0d8992e610c85",
      "email": "<EMAIL>",
      "username": "username",
      "nickname": "用户昵称",
      "avatar": null,
      "authMethods": {
        "email": true
      },
      "membershipType": "free",
      "membershipExpires": null,
      "preferences": {
        "theme": "system",
        "language": "zh-CN",
        "notifications": true
      },
      "settings": {
        "notifications": true,
        "focusReminders": true,
        "autoBackup": true
      },
      "createdAt": "2023-06-22T12:00:00Z"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
  }
}
```

### 邮箱密码登录

使用邮箱和密码登录。

**请求**:
```
POST /api/email-auth/login-with-password
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "Password123",
  "rememberMe": true,
  "deviceInfo": {
    "type": "mobile",
    "os": "iOS",
    "version": "15.0"
  }
}
```

**响应**:
```json
{
  "status": "success",
  "message": "登录成功",
  "data": {
    "user": {
      "id": "60d21b4667d0d8992e610c85",
      "email": "<EMAIL>",
      "username": "username",
      "nickname": "用户昵称",
      "avatar": null,
      "authMethods": {
        "email": true
      },
      "membershipType": "free",
      "membershipExpires": null,
      "preferences": {
        "theme": "system",
        "language": "zh-CN",
        "notifications": true
      },
      "settings": {
        "notifications": true,
        "focusReminders": true,
        "autoBackup": true
      },
      "createdAt": "2023-06-22T12:00:00Z"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
  }
}
```

### 忘记密码（发送重置验证码）

发送密码重置验证码到指定邮箱。

**请求**:
```
POST /api/email-auth/forgot-password
```

**请求体**:
```json
{
  "email": "<EMAIL>"
}
```

**响应**:
```json
{
  "status": "success",
  "message": "密码重置验证码已发送，请查收邮件",
  "data": null
}
```

### 重置密码

使用验证码重置密码。

**请求**:
```
POST /api/email-auth/reset-password
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "code": "123456",
  "password": "NewPassword123"
}
```

**响应**:
```json
{
  "status": "success",
  "message": "密码重置成功，请使用新密码登录",
  "data": null
}
```

### Apple登录

使用Apple ID登录。

**请求**:
```
POST /api/auth/apple-login
```

**请求体**:
```json
{
  "identityToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "rememberMe": true,
  "deviceInfo": {
    "type": "mobile",
    "os": "iOS",
    "version": "15.0"
  }
}
```

**响应**:
```json
{
  "status": "success",
  "message": "登录成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "60d21b4667d0d8992e610c85",
      "username": "username",
      "nickname": "用户昵称",
      "avatar": null,
      "accountStatus": "active",
      "membershipType": "free",
      "membershipExpires": null,
      "preferences": {
        "theme": "system",
        "language": "zh-CN",
        "notifications": true
      },
      "settings": {
        "notifications": true,
        "focusReminders": true,
        "autoBackup": true
      },
      "createdAt": "2023-06-22T12:00:00Z",
      "lastLoginAt": "2023-06-22T12:00:00Z"
    }
  }
}
```

### 刷新令牌

使用刷新令牌获取新的访问令牌。

**请求**:
```
POST /api/auth/refresh-token
```

**请求体**:
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**响应**:
```json
{
  "status": "success",
  "message": "令牌刷新成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

## 用户API

### 获取当前用户信息

获取当前登录用户的信息。

**请求**:
```
GET /api/users/me
```

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:
```json
{
  "status": "success",
  "message": "获取用户信息成功",
  "data": {
    "id": "60d21b4667d0d8992e610c85",
    "email": "<EMAIL>",
    "username": "username",
    "nickname": "用户昵称",
    "avatar": null,
    "authMethods": {
      "email": true,
      "apple": false
    },
    "membershipType": "free",
    "membershipExpires": null,
    "preferences": {
      "theme": "system",
      "language": "zh-CN",
      "notifications": true
    },
    "settings": {
      "notifications": true,
      "focusReminders": true,
      "autoBackup": true
    },
    "createdAt": "2023-06-22T12:00:00Z",
    "lastLoginAt": "2023-06-22T12:00:00Z",
    "lastActiveAt": "2023-06-22T12:00:00Z"
  }
}
```

### 更新用户信息

更新当前登录用户的信息。

**请求**:
```
PUT /api/users/me
```

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**请求体**:
```json
{
  "username": "new_username",
  "nickname": "新昵称",
  "preferences.theme": "dark",
  "preferences.language": "en-US",
  "settings.notifications": false
}
```

**响应**:
```json
{
  "status": "success",
  "message": "用户信息更新成功",
  "data": {
    "id": "60d21b4667d0d8992e610c85",
    "email": "<EMAIL>",
    "username": "new_username",
    "nickname": "新昵称",
    "avatar": null,
    "authMethods": {
      "email": true,
      "apple": false
    },
    "membershipType": "free",
    "membershipExpires": null,
    "preferences": {
      "theme": "dark",
      "language": "en-US",
      "notifications": true
    },
    "settings": {
      "notifications": false,
      "focusReminders": true,
      "autoBackup": true
    },
    "createdAt": "2023-06-22T12:00:00Z",
    "lastLoginAt": "2023-06-22T12:00:00Z",
    "lastActiveAt": "2023-06-22T12:00:00Z"
  }
}
```

### 更新用户密码

更新当前登录用户的密码。

**请求**:
```
PUT /api/users/me/password
```

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**请求体**:
```json
{
  "currentPassword": "OldPassword123",
  "newPassword": "NewPassword123",
  "confirmPassword": "NewPassword123"
}
```

**响应**:
```json
{
  "status": "success",
  "message": "密码更新成功",
  "data": null
}
```

### 注销账号

永久删除当前用户账号及其所有相关数据。此操作不可逆转。

**请求**:
```
DELETE /api/users/me
```

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**请求体**:
```json
{
  "password": "UserPassword123",
  "confirmText": "确认注销"
}
```

**响应**:
```json
{
  "status": "success",
  "message": "账号注销成功",
  "data": null
}
```

**错误响应示例**:
```json
{
  "status": "error",
  "message": "密码验证失败",
  "code": 400,
  "errors": {
    "password": "密码不正确"
  }
}
```

**注意事项**:
- 此操作将永久删除用户账号及其所有相关数据
- 需要提供正确的密码进行身份验证
- 需要输入确认文本"确认注销"以防止误操作
- 操作成功后，用户的访问令牌将立即失效
- 已删除的账号无法恢复

## 订阅API

### 获取价格计划列表

获取所有可用的价格计划。

**请求**:
```
GET /api/subscriptions/plans
```

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:
```json
{
  "status": "success",
  "message": "获取价格计划成功",
  "data": [
    {
      "id": "60d21b4667d0d8992e610c85",
      "planId": "monthly",
      "name": "月度会员",
      "description": "每月订阅，自动续费",
      "price": 18,
      "currency": "CNY",
      "type": "monthly",
      "durationMonths": 1,
      "features": ["无限使用", "数据同步", "高级统计"],
      "isActive": true,
      "appleProductId": "com.example.app.monthly",
      "createdAt": "2023-06-22T12:00:00Z"
    },
    {
      "id": "60d21b4667d0d8992e610c86",
      "planId": "yearly",
      "name": "年度会员",
      "description": "每年订阅，自动续费，享受8折优惠",
      "price": 168,
      "currency": "CNY",
      "type": "yearly",
      "durationMonths": 12,
      "features": ["无限使用", "数据同步", "高级统计", "优先支持"],
      "isActive": true,
      "appleProductId": "com.example.app.yearly",
      "createdAt": "2023-06-22T12:00:00Z"
    }
  ]
}
```

### 获取当前活跃订阅

获取当前用户的活跃订阅信息。

**请求**:
```
GET /api/subscriptions/active
```

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:
```json
{
  "status": "success",
  "message": "获取订阅信息成功",
  "data": {
    "id": "60d21b4667d0d8992e610c87",
    "planId": "monthly",
    "type": "monthly",
    "status": "active",
    "startDate": "2023-06-22T12:00:00Z",
    "endDate": "2023-07-22T12:00:00Z",
    "autoRenew": true,
    "paymentMethod": "apple",
    "platform": "ios",
    "createdAt": "2023-06-22T12:00:00Z"
  }
}
```

### 处理Apple支付

处理来自Apple的支付收据。

**请求**:
```
POST /api/subscriptions/apple
```

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**请求体**:
```json
{
  "receiptData": "MIIT0AYJKoZIhvcNAQcCoIITwTCCE70CAQExCzAJBgUrDgMCGgUAMIIDcQYJKoZIhvcNAQcBoIIDYgSCA14...",
  "deviceInfo": {
    "type": "mobile",
    "os": "iOS",
    "version": "15.0"
  }
}
```

**响应**:
```json
{
  "status": "success",
  "message": "订阅创建成功",
  "data": {
    "id": "60d21b4667d0d8992e610c87",
    "planId": "monthly",
    "type": "monthly",
    "status": "active",
    "startDate": "2023-06-22T12:00:00Z",
    "endDate": "2023-07-22T12:00:00Z",
    "autoRenew": true
  }
}
```

## 错误响应

所有API错误都会返回一个标准格式的错误响应：

```json
{
  "status": "error",
  "message": "错误消息",
  "code": 400,
  "errors": {
    "field1": "字段1的错误信息",
    "field2": "字段2的错误信息"
  },
  "timestamp": 1625123456789,
  "path": "/api/endpoint"
}
```

### 常见错误代码

- `400` - 请求参数错误
- `401` - 未认证或认证失败
- `403` - 权限不足
- `404` - 资源不存在
- `409` - 资源冲突
- `429` - 请求过于频繁
- `500` - 服务器内部错误
