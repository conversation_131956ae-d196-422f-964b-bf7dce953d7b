# 存档功能使用指南

本文档提供了关于如何使用和集成存档功能代码的详细指导，帮助开发人员在未来需要时能够正确地参考和集成这些功能。

## 存档功能概述

在项目开发过程中，我们开发了一些功能，但由于各种原因（如优先级调整、功能暂缓等）暂时不集成到主开发分支。为了保留这些功能代码以便将来参考和使用，我们将它们存档在专门的目录中。

主要存档功能包括：

1. **模拟考试功能**：基于艾宾浩斯记忆曲线的考试系统
2. **每日复盘功能**：用户每日学习和专注情况的回顾与总结
3. **色彩方案**：紫色和绿色主题方案
4. **场景音频功能**：专注场景的音频播放（部分代码已在主分支）

## 存档位置

所有存档功能代码都位于项目根目录下的 `archived_features` 目录中，按功能模块组织：

```
archived_features/
├── exam/                  # 模拟考试功能
├── daily-review/          # 每日复盘功能
└── color-schemes/         # 色彩方案
    ├── purple_theme_constants.dart
    └── green_theme_constants.dart
```

## 如何使用存档功能

### 1. 模拟考试功能

#### 功能描述

模拟考试功能基于艾宾浩斯记忆曲线原理，帮助用户创建考试模板、管理考试实例，并根据记忆曲线安排复习计划。

#### 主要组件

- **考试模板**：定义考试的基本结构和题目
- **考试实例**：基于模板创建的具体考试
- **复习计划**：根据记忆曲线生成的复习时间表

#### 集成步骤

1. **创建数据模型**：
   - 复制 `archived_features/exam/models` 目录到 `lib/features/exam/models`
   - 确保模型类与当前项目的数据结构兼容

2. **实现存储层**：
   - 创建 Hive 适配器：`lib/core/models/adapters/exam_template_adapter.dart`
   - 创建仓库类：`lib/core/repositories/exam_repository.dart`

3. **实现业务逻辑**：
   - 复制 `archived_features/exam/providers` 和 `archived_features/exam/services` 到对应目录
   - 调整代码以适应当前项目的状态管理方式

4. **实现UI**：
   - 复制 `archived_features/exam/screens` 和 `archived_features/exam/widgets` 到对应目录
   - 根据当前项目的UI风格进行调整

5. **添加路由**：
   - 在 `lib/core/routes/app_routes.dart` 中添加考试相关路由

6. **集成到导航**：
   - 在适当的位置（如"其他"标签页）添加考试功能入口

### 2. 每日复盘功能

#### 功能描述

每日复盘功能帮助用户回顾和总结每日的学习和专注情况，包括专注时间分布、完成的任务、达成的目标等。

#### 主要组件

- **复盘记录**：包含每日学习和专注的详细数据
- **复盘总结**：对一天学习情况的文字总结
- **复盘分析**：基于数据的学习效果分析

#### 集成步骤

1. **创建数据模型**：
   - 复制 `archived_features/daily-review/daily_review.dart` 到 `lib/core/models/`
   - 复制 `archived_features/daily-review/daily_review_adapter.dart` 到 `lib/core/models/adapters/`

2. **实现存储层**：
   - 复制 `archived_features/daily-review/daily_review_repository.dart` 到 `lib/core/repositories/`

3. **实现UI**：
   - 复制 `archived_features/daily-review/screens` 和 `archived_features/daily-review/widgets` 到对应目录
   - 根据当前项目的UI风格进行调整

4. **添加路由**：
   - 在 `lib/core/routes/app_routes.dart` 中添加复盘相关路由

5. **集成到导航**：
   - 在适当的位置（如"数据"标签页）添加复盘功能入口

### 3. 色彩方案

#### 功能描述

项目支持多种色彩方案，包括默认的浅绿色、紫色和绿色主题。

#### 集成步骤

1. **创建主题管理器**：
   - 在 `lib/shared/theme/` 目录下创建 `theme_manager.dart`
   - 实现主题切换和持久化功能

2. **创建主题方案**：
   - 在 `lib/shared/theme/schemes/` 目录下创建各个主题方案
   - 使用存档的 `purple_theme_constants.dart` 和 `green_theme_constants.dart` 作为参考

3. **实现主题切换UI**：
   - 在设置页面添加主题选择选项
   - 创建主题预览组件

4. **集成到应用**：
   - 修改 `main.dart`，使用主题管理器提供的当前主题

### 4. 场景音频功能

#### 功能描述

场景音频功能提供各种专注场景的白噪音，如咖啡厅、雨声、森林等，帮助用户更好地进入专注状态。

#### 主要组件

- **场景选择**：提供各种音频场景供用户选择
- **音频播放**：控制音频的播放、暂停、音量等
- **定时器集成**：与专注计时器集成，在专注结束时停止音频

#### 集成步骤

1. **准备音频资源**：
   - 在 `assets/audio/` 目录下添加各种场景的音频文件
   - 更新 `pubspec.yaml` 文件，添加音频资源

2. **实现音频服务**：
   - 参考 `lib/core/services/audio_service.dart`（已在主分支）
   - 完善音频播放、暂停、音量控制等功能

3. **实现场景选择UI**：
   - 参考 `lib/features/audio/screens/scene_selection_screen.dart`（已在主分支）
   - 完善场景列表、预览和选择功能

4. **集成到专注功能**：
   - 在专注设置中添加场景选择选项
   - 将音频播放与专注计时器同步

## 注意事项

1. **代码兼容性**：存档代码可能基于项目的早期版本，集成时需要确保与当前代码兼容。

2. **依赖管理**：检查存档功能是否需要额外的依赖，如需要，更新 `pubspec.yaml` 文件。

3. **测试**：集成后进行充分测试，确保功能正常工作且不影响现有功能。

4. **性能考虑**：评估集成新功能对应用性能的影响，必要时进行优化。

5. **UI一致性**：确保集成的功能UI风格与应用其他部分保持一致。

## 开发流程

集成存档功能时，建议遵循以下开发流程：

1. **创建功能分支**：从 `develop` 分支创建新的功能分支
   ```bash
   git checkout develop
   git checkout -b feature/integrate-exam
   ```

2. **分阶段实现**：按照上述集成步骤分阶段实现功能

3. **代码审查**：完成实现后，创建Pull Request进行代码审查

4. **测试**：进行单元测试和集成测试，确保功能正常

5. **合并**：测试通过后，将功能分支合并到 `develop` 分支

## 结论

存档功能代码是项目的宝贵资产，通过本文档的指导，开发人员可以在需要时正确地参考和集成这些功能，避免重复开发工作，提高开发效率。

随着项目的发展，我们会不断更新和完善存档功能，本文档也将相应更新。

最后更新：2025年5月8日
