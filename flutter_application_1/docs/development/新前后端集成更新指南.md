# 前端集成指南

本文档提供了与后端API集成的详细指南，包括认证、用户管理、数据同步等功能。

## 目录

1. [环境准备](#环境准备)
2. [API认证](#api认证)
3. [API响应格式](#api响应格式)
4. [常用API端点](#常用api端点)
5. [错误处理](#错误处理)
6. [测试数据](#测试数据)
7. [集成测试步骤](#集成测试步骤)
8. [常见问题](#常见问题)

## 环境准备

### 后端测试环境

后端测试环境已经配置好，可以通过以下步骤启动：

1. 确保MongoDB已启动
2. 重置测试数据：
   ```bash
   node scripts/reset-test-data.js
   ```
3. 启动测试环境：
   ```bash
   NODE_ENV=test node dist/server.js
   ```

测试环境将在 http://localhost:3000 上运行，API基础路径为 http://localhost:3000/api。

### 测试账号

以下是可用的测试账号：

| 账号类型 | 邮箱 | 密码 | 说明 |
|---------|------|------|------|
| 普通用户 | <EMAIL> | Password123! | 免费会员账号 |
| 高级用户 | <EMAIL> | Password123! | 付费会员账号 |

## API认证

### 登录注册方式

后端支持以下登录注册方式：

1. **邮箱+验证码+密码注册**：用户通过邮箱接收验证码验证身份，然后设置密码完成注册。
2. **邮箱+密码登录**：用户通过邮箱和密码进行登录（主要登录方式）。
3. **邮箱+验证码登录**：用户通过邮箱和验证码进行登录（辅助登录方式，适用于忘记密码的情况）。
4. **苹果登录**：iOS用户可以使用苹果账号进行登录，如果是新用户则自动注册。

### 登录获取令牌

**邮箱密码登录**:
```javascript
const response = await axios.post('http://localhost:3000/api/email-auth/login-with-password', {
  email: '<EMAIL>',
  password: 'Password123!',
  rememberMe: true, // 可选，默认为 false
  deviceInfo: {
    type: 'desktop',
    os: 'Windows',
    browser: 'Chrome',
    version: '96.0'
  }
});

// 获取访问令牌
const accessToken = response.data.data.tokens.accessToken;
const refreshToken = response.data.data.tokens.refreshToken;
```

**邮箱验证码登录**:
```javascript
// 先发送验证码
await axios.post('http://localhost:3000/api/email-auth/send-verification-code?purpose=login', {
  email: '<EMAIL>'
});

// 然后使用验证码登录
const response = await axios.post('http://localhost:3000/api/email-auth/login-with-code', {
  email: '<EMAIL>',
  code: '123456', // 在测试环境中，可以从响应中获取验证码
  rememberMe: true,
  deviceInfo: {
    type: 'mobile',
    os: 'iOS',
    browser: 'Safari',
    version: '15.0'
  }
});

// 获取访问令牌
const accessToken = response.data.data.tokens.accessToken;
const refreshToken = response.data.data.tokens.refreshToken;
```

**苹果登录**:
```javascript
const response = await axios.post('http://localhost:3000/api/auth/apple-login', {
  identityToken: 'apple_identity_token', // 从苹果登录SDK获取
  rememberMe: true,
  deviceInfo: {
    type: 'mobile',
    os: 'iOS',
    browser: 'Safari',
    version: '15.0'
  }
});

// 获取访问令牌
const accessToken = response.data.data.accessToken;
```

### 使用令牌访问API

```javascript
const response = await axios.get('http://localhost:3000/api/users/me', {
  headers: {
    Authorization: `Bearer ${accessToken}`
  }
});
```

### 密码重置流程

**发送密码重置验证码**:
```javascript
const response = await axios.post('http://localhost:3000/api/email-auth/forgot-password', {
  email: '<EMAIL>'
});

// 检查响应
if (response.data.status === 'success') {
  console.log('密码重置验证码已发送');
}
```

**重置密码**:
```javascript
const response = await axios.post('http://localhost:3000/api/email-auth/reset-password', {
  email: '<EMAIL>',
  code: '123456', // 用户收到的验证码
  password: 'NewPassword123!'
});

// 检查响应
if (response.data.status === 'success') {
  console.log('密码重置成功，请使用新密码登录');
}
```

### 令牌刷新

```javascript
const response = await axios.post('http://localhost:3000/api/auth/refresh-token', {
  refreshToken: refreshToken,
  deviceInfo: {
    type: 'desktop',
    os: 'Windows',
    browser: 'Chrome',
    version: '96.0'
  }
});

// 获取新的访问令牌和用户信息
const { accessToken, refreshToken: newRefreshToken, user } = response.data.data;

// 或者单独获取
const newAccessToken = response.data.data.accessToken;
const newRefreshToken = response.data.data.refreshToken;
const userData = response.data.data.user;
```

> **注意**:
> 1. 刷新令牌端点同时返回新的访问令牌、刷新令牌和基本用户信息。
> 2. 系统同时支持 `/api/auth/refresh-token` 和 `/api/auth/refresh` 两个刷新令牌端点，它们的功能完全相同。

## API响应格式

所有API响应都遵循统一的格式：

### 成功响应

```json
{
  "status": "success",
  "data": {
    // 响应数据
  },
  "message": "操作成功",
  "timestamp": 1746801234567,
  "path": "/api/users/me"
}
```

### 错误响应

```json
{
  "status": "error",
  "message": "错误消息",
  "errors": {
    // 详细错误信息
  },
  "timestamp": 1746801234567,
  "path": "/api/users/me"
}
```

### 分页响应

```json
{
  "status": "success",
  "data": {
    "items": [
      // 数据项数组
    ],
    "pagination": {
      "total": 100,
      "page": 1,
      "limit": 10,
      "pages": 10,
      "hasNext": true,
      "hasPrev": false,
      "nextPage": 2,
      "prevPage": null,
      "startIndex": 1,
      "endIndex": 10
    }
  },
  "message": "获取成功",
  "timestamp": 1746801234567,
  "path": "/api/backups"
}
```

## 常用API端点

### 用户相关

| 端点 | 方法 | 描述 | 认证 |
|------|------|------|------|
| /api/email-auth/login-with-password | POST | 邮箱密码登录 | 否 |
| /api/email-auth/login-with-code | POST | 邮箱验证码登录 | 否 |
| /api/email-auth/register | POST | 邮箱注册 | 否 |
| /api/email-auth/send-verification-code?purpose=xxx | POST | 发送邮箱验证码 | 否 |
| /api/email-auth/verify-code | POST | 验证邮箱验证码 | 否 |
| /api/email-auth/forgot-password | POST | 发送密码重置验证码 | 否 |
| /api/email-auth/reset-password | POST | 重置密码 | 否 |
| /api/auth/apple-login | POST | 苹果登录 | 否 |
| /api/auth/refresh-token | POST | 刷新令牌 | 否 |
| /api/users/me | GET | 获取当前用户信息 | 是 |
| /api/users/me | PUT | 更新用户信息 | 是 |

### 备份相关

| 端点 | 方法 | 描述 | 认证 |
|------|------|------|------|
| /api/backups | GET | 获取备份列表 | 是 |
| /api/backups | POST | 创建备份 | 是 |
| /api/backups/:id | GET | 获取单个备份 | 是 |
| /api/backups/:id | PUT | 更新备份 | 是 |
| /api/backups/:id | DELETE | 删除备份 | 是 |
| /api/backups/quota | GET | 获取备份配额 | 是 |

### 订阅相关

| 端点 | 方法 | 描述 | 认证 |
|------|------|------|------|
| /api/subscriptions/plans | GET | 获取价格计划 | 是 |
| /api/subscriptions/active | GET | 获取当前订阅 | 是 |
| /api/subscriptions/history | GET | 获取订阅历史 | 是 |

## 错误处理

### 错误类型

| 状态码 | 错误类型 | 描述 |
|-------|---------|------|
| 400 | ValidationError | 输入验证失败 |
| 401 | AuthenticationError | 认证失败 |
| 403 | AuthorizationError | 授权失败 |
| 404 | NotFoundError | 资源不存在 |
| 429 | RateLimitError | 请求过于频繁 |
| 500 | InternalServerError | 服务器内部错误 |

### 错误处理示例

```javascript
try {
  const response = await axios.get('http://localhost:3000/api/users/me', {
    headers: {
      Authorization: `Bearer ${accessToken}`
    }
  });

  // 处理成功响应
  const userData = response.data.data;
} catch (error) {
  if (error.response) {
    const { status, data } = error.response;

    if (status === 401) {
      // 处理认证错误
      console.error('认证失败，请重新登录');
    } else if (status === 400) {
      // 处理验证错误
      console.error('输入验证失败:', data.message);
      if (data.errors) {
        console.error('详细错误:', data.errors);
      }
    } else {
      // 处理其他错误
      console.error(`请求失败 (${status}):`, data.message);
    }
  } else if (error.request) {
    // 请求已发送但没有收到响应
    console.error('没有收到服务器响应');
  } else {
    // 请求配置错误
    console.error('请求错误:', error.message);
  }
}
```

## 测试数据

测试环境中已经预置了以下测试数据：

### 用户

- 普通用户：13800138000 / Password123!
- 高级用户：13800138001 / Password123!

### 备份

每个用户都有预置的备份数据，可以通过 `/api/backups` 接口获取。

### 订阅

- 价格计划：通过 `/api/subscriptions/plans` 接口获取
- 13800138001 用户有一个活跃的订阅

## 集成测试步骤

### 1. 基本流程测试

1. 用户登录
2. 获取用户信息
3. 获取备份列表
4. 创建新备份
5. 获取单个备份
6. 更新备份
7. 删除备份

### 2. 订阅流程测试

1. 用户登录
2. 获取价格计划
3. 获取当前订阅
4. 获取订阅历史

### 3. 错误处理测试

1. 使用错误的凭据登录
2. 使用无效的令牌访问API
3. 访问不存在的资源
4. 提交无效的数据

## 常见问题

### Q: 如何处理令牌过期？

A: 当访问令牌过期时，API将返回401状态码。此时，应使用刷新令牌获取新的访问令牌：

```javascript
async function handleTokenExpiration(error, refreshToken) {
  if (error.response && error.response.status === 401) {
    try {
      const response = await axios.post('http://localhost:3000/api/auth/refresh', {
        refreshToken: refreshToken
      });

      // 获取新的令牌和用户信息
      const { accessToken, refreshToken: newRefreshToken, user } = response.data.data;

      // 更新本地存储的令牌和用户信息
      localStorage.setItem('accessToken', accessToken);
      localStorage.setItem('refreshToken', newRefreshToken);

      // 返回新的访问令牌
      return accessToken;
    } catch (refreshError) {
      // 刷新令牌也过期，需要重新登录
      return null;
    }
  }

  throw error;
}
```

### Q: 如何处理分页数据？

A: 分页数据可以通过以下方式处理：

```javascript
async function fetchPaginatedData(url, token, page = 1, limit = 10) {
  const response = await axios.get(`${url}?page=${page}&limit=${limit}`, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });

  const { items, pagination } = response.data.data;

  return {
    items,
    pagination,
    hasMore: pagination.hasNext,
    nextPage: pagination.nextPage
  };
}
```

### Q: 如何处理文件上传？

A: 文件上传可以使用FormData：

```javascript
async function uploadFile(file, token) {
  const formData = new FormData();
  formData.append('file', file);

  const response = await axios.post('http://localhost:3000/api/upload', formData, {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'multipart/form-data'
    }
  });

  return response.data.data;
}
```

### Q: 如何处理速率限制？

A: 当请求过于频繁时，API会返回429状态码。此时，应等待一段时间后再重试：

```javascript
async function handleRateLimit(error) {
  if (error.response && error.response.status === 429) {
    // 获取重试时间（秒）
    const retryAfter = error.response.data.errors?.retryAfter || 60;

    console.warn(`请求过于频繁，请在${retryAfter}秒后重试`);

    // 可以选择等待后自动重试
    return new Promise(resolve => {
      setTimeout(() => {
        console.log('重试请求...');
        resolve();
      }, retryAfter * 1000);
    });
  }

  throw error;
}
```

> **注意**: 不同的API端点有不同的速率限制，请参考API文档中的说明。

---

如有任何问题，请联系后端开发团队。
