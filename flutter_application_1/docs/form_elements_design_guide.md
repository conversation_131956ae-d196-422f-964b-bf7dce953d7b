# 表单元素设计规范

本文档提供了应用中表单元素的设计规范和使用指南，确保所有表单元素在整个应用中保持一致的外观和行为。

## 1. 输入框规范

### 1.1 尺寸规范

输入框有四种标准高度，根据使用场景选择合适的尺寸：

| 类型 | 高度 | 使用场景 |
|------|------|----------|
| 大型输入框 | 48px | 用于表单中的主要输入框，如登录表单、注册表单等 |
| 中型输入框 | 40px | 用于一般场景的输入框，如搜索框、筛选框等 |
| 小型输入框 | 36px | 用于紧凑布局中的输入框，如对话框中的输入框、内嵌表单等 |
| 紧凑型输入框 | 32px | 用于空间有限的场景，如表格中的输入框、工具栏中的输入框等 |

输入框宽度应根据内容类型和可用空间选择合适的尺寸：

| 类型 | 宽度 | 使用场景 |
|------|------|----------|
| 大型输入框宽度 | 240px | 用于长文本输入，如描述、地址等 |
| 中型输入框宽度 | 180px | 用于一般文本输入，如姓名、标题等 |
| 小型输入框宽度 | 120px | 用于短文本或数字输入，如年龄、数量等 |
| 紧凑型输入框宽度 | 80px | 用于极短文本或数字输入，如邮编、时间等 |

### 1.2 样式规范

输入框有三种标准圆角大小：

| 类型 | 圆角 | 使用场景 |
|------|------|----------|
| 大型圆角 | 12px | 用于强调的输入框，如登录表单、注册表单等 |
| 中型圆角 | 8px | 用于标准输入框，如一般表单、搜索框等 |
| 小型圆角 | 4px | 用于紧凑型输入框，如表格中的输入框、工具栏中的输入框等 |

输入框边框样式：

| 状态 | 边框颜色 | 边框宽度 |
|------|----------|----------|
| 默认状态 | AppColors.border | 1.0px |
| 聚焦状态 | AppColors.primary | 1.5px |
| 错误状态 | AppColors.error | 1.0px |

输入框内边距：

| 类型 | 水平内边距 | 垂直内边距 |
|------|------------|------------|
| 大型内边距 | 16px | 12px |
| 中型内边距 | 12px | 8px |
| 小型内边距 | 8px | 6px |

### 1.3 文本样式

输入框相关文本样式：

| 类型 | 字体大小 | 字重 | 颜色 |
|------|----------|------|------|
| 输入框文本 | 14px | Medium (500) | AppColors.text |
| 输入框提示文本 | 14px | Normal (400) | AppColors.textPlaceholder |
| 输入框标签 | 14px | Medium (500) | AppColors.textSecondary |
| 输入框辅助文本 | 12px | Normal (400) | AppColors.textTertiary |
| 输入框错误文本 | 12px | Normal (400) | AppColors.error |

## 2. 特殊输入框规范

### 2.1 带单位的输入框

带单位的输入框用于输入带有单位的数值，如时间、数量等。

- 容器高度：36px（小型输入框）
- 容器宽度：120px（小型输入框宽度）
- 容器圆角：8px（中型圆角）
- 容器背景色：白色
- 容器边框：1px AppColors.border
- 输入区域与单位区域之间使用1px宽的分隔线，颜色为AppColors.divider
- 输入区域占比：2/3
- 单位区域占比：1/3
- 输入区域文本居中对齐
- 单位区域文本居中对齐
- 单位文本样式：12px，Normal (400)，AppColors.textSecondary

### 2.2 选择器

选择器用于从预定义的选项中选择一个或多个值，如下拉选择、日期选择等。

- 容器高度：36px（小型输入框）
- 容器圆角：8px（中型圆角）
- 容器背景色：白色
- 容器边框：1px AppColors.border
- 内边距：水平12px，垂直6px
- 文本样式：14px，Normal (400)，AppColors.textSecondary
- 图标大小：16px
- 图标颜色：AppColors.textSecondary
- 图标与文本间距：4px

### 2.3 选择芯片

选择芯片用于多选场景，如标签选择、筛选条件选择等。

- 容器高度：28px
- 容器圆角：16px
- 未选中状态背景色：透明
- 选中状态背景色：AppColors.primary10
- 未选中状态边框：1px AppColors.border
- 选中状态边框：1px AppColors.primary
- 未选中状态文本颜色：AppColors.textSecondary
- 选中状态文本颜色：AppColors.primary
- 文本样式：13px
- 内边距：水平8px，垂直0px
- 芯片间距：8px

## 3. 使用指南

### 3.1 输入框使用指南

- 在表单中，使用标准的输入框样式，包括标签、输入框和可选的辅助文本
- 输入框标签应简洁明了，表达输入内容的类型
- 输入框提示文本应提供输入示例或格式说明
- 输入框辅助文本应提供额外的说明或限制条件
- 输入框错误文本应明确指出错误原因和修复方法
- 必填字段应在标签后添加"*"号标记
- 禁用状态的输入框应使用灰色背景和文本，并禁止交互

### 3.2 特殊输入框使用指南

- 带单位的输入框适用于需要输入带单位的数值，如时间、数量等
- 选择器适用于从预定义的选项中选择一个值，如下拉选择、日期选择等
- 选择芯片适用于多选场景，如标签选择、筛选条件选择等

### 3.3 表单布局指南

- 表单元素应垂直排列，每个元素占据一行
- 表单元素之间的间距应为16px
- 相关的表单元素可以分组，组之间的间距应为24px
- 表单标题应使用AppTextStyles.headline3样式
- 表单按钮应放置在表单底部，与表单元素保持一致的对齐方式
- 提交按钮应使用主要按钮样式，取消按钮应使用次要按钮样式

## 4. 代码示例

### 4.1 标准输入框

```dart
TextField(
  controller: textController,
  style: AppInputStyles.inputTextStyle,
  decoration: AppInputStyles.inputDecoration(
    hintText: '请输入内容',
    helperText: '辅助说明文本',
  ),
)
```

### 4.2 带单位的输入框

```dart
Container(
  width: AppInputStyles.inputWidthSmall,
  height: AppInputStyles.inputHeightSmall,
  decoration: AppInputStyles.inputWithUnitDecoration(),
  child: Row(
    children: [
      Expanded(
        flex: 2,
        child: TextField(
          controller: valueController,
          keyboardType: TextInputType.number,
          textAlign: TextAlign.center,
          style: AppInputStyles.inputTextStyle,
          decoration: AppInputStyles.inputDecorationNoBorder(
            hintText: '10',
          ),
        ),
      ),
      Container(
        width: 1,
        height: 20,
        color: AppColors.divider,
      ),
      Expanded(
        flex: 1,
        child: Container(
          alignment: Alignment.center,
          child: Text(
            '小时',
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
        ),
      ),
    ],
  ),
)
```

### 4.3 选择器

```dart
InkWell(
  onTap: () {
    // 显示选择器
  },
  child: Container(
    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
    decoration: AppInputStyles.selectorDecoration(),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          selectedValue,
          style: const TextStyle(
            fontSize: 14,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(width: 4),
        const Icon(
          Icons.keyboard_arrow_down,
          size: 16,
          color: AppColors.textSecondary,
        ),
      ],
    ),
  ),
)
```

### 4.4 选择芯片

```dart
Wrap(
  spacing: 8,
  runSpacing: 8,
  children: options.map((option) {
    final isSelected = selectedOptions.contains(option);
    return ChoiceChip(
      label: Text(option),
      selected: isSelected,
      selectedColor: AppColors.primary10,
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isSelected ? AppColors.primary : AppColors.border,
          width: 1,
        ),
      ),
      labelStyle: TextStyle(
        fontSize: 13,
        color: isSelected ? AppColors.primary : AppColors.textSecondary,
        fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
      ),
      onSelected: (selected) {
        // 处理选择事件
      },
    );
  }).toList(),
)
```
