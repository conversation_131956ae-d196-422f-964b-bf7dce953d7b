# LimeFocus 订阅产品信息获取问题诊断指南

## 🔍 问题现状分析

### 当前问题
- 无法获取App Store Connect中配置的订阅产品信息
- 产品ID：LemiVip001, LimeVip_quarter, LimeVip_yearly
- 应用在模拟器和真机上都无法加载真实产品信息

### 代码实现状态
✅ **已完成的部分**：
- Apple订阅服务类已实现
- 产品查询逻辑已编写
- 错误处理和日志记录完善
- 模拟数据作为fallback机制

❌ **问题所在**：
- App Store Connect配置可能不完整
- 产品状态可能不正确
- Bundle ID可能不匹配
- 沙盒环境配置问题

## 📋 详细诊断清单

### 1. App Store Connect配置检查

#### 1.1 基础配置验证
- [ ] **应用状态**：确认应用在App Store Connect中状态为"准备提交"或"等待审核"
- [ ] **Bundle ID匹配**：确认Bundle ID为`com.arborflame.limefocus`
- [ ] **协议签署**：确认已签署付费应用协议（Paid Applications Agreement）
- [ ] **税务信息**：确认已填写税务信息
- [ ] **银行信息**：确认已填写银行账户信息

#### 1.2 订阅产品配置检查
```
产品ID检查清单：
□ LemiVip001 - 月度订阅
□ LimeVip_quarter - 季度订阅  
□ LimeVip_yearly - 年度订阅

每个产品需要检查：
□ 产品状态：必须是"准备提交"
□ 价格设置：已设置中国区价格
□ 本地化信息：已添加中文描述
□ 订阅群组：已分配到订阅群组
□ 审核信息：已填写审核说明
```

#### 1.3 订阅群组配置
- [ ] **群组创建**：确认已创建订阅群组
- [ ] **群组状态**：群组状态为"准备提交"
- [ ] **产品关联**：所有产品都已关联到群组
- [ ] **本地化**：群组已添加本地化信息

### 2. 技术实现检查

#### 2.1 Bundle ID验证
```bash
# 检查iOS项目配置
cat ios/Runner/Info.plist | grep -A1 CFBundleIdentifier
# 应该显示：com.arborflame.limefocus
```

#### 2.2 产品ID验证
```dart
// 当前配置的产品ID
static const String _monthlySubscriptionId = 'LemiVip001';
static const String _quarterlySubscriptionId = 'LimeVip_quarter';
static const String _yearlySubscriptionId = 'LimeVip_yearly';
```

#### 2.3 权限配置检查
```xml
<!-- ios/Runner/Info.plist 应包含 -->
<key>SKAdNetworkItems</key>
<array>
    <!-- StoreKit配置 -->
</array>
```

### 3. 环境配置问题

#### 3.1 开发环境限制
- **模拟器限制**：iOS模拟器不支持真实的App Store连接
- **沙盒环境**：需要使用沙盒测试账号
- **网络环境**：需要稳定的网络连接到App Store

#### 3.2 测试账号配置
- [ ] **沙盒测试账号**：已在App Store Connect中创建
- [ ] **设备配置**：测试设备已退出生产环境Apple ID
- [ ] **区域设置**：测试账号区域与产品销售区域匹配

## 🛠️ 解决方案

### 方案1：App Store Connect配置修复

#### 步骤1：验证基础配置
1. 登录 [App Store Connect](https://appstoreconnect.apple.com)
2. 进入"我的App" → "LimeFocus"
3. 检查应用状态和基本信息
4. 确认Bundle ID为`com.arborflame.limefocus`

#### 步骤2：配置订阅产品
1. 进入"功能" → "App内购买项目"
2. 点击"+"创建新的订阅产品
3. 按以下配置创建三个产品：

```
产品1：
- 类型：自动续费订阅
- 产品ID：LemiVip001
- 订阅群组：LimeFocus Premium
- 价格：¥6.00/月
- 显示名称：LimeFocus 高级版（月度）

产品2：
- 类型：自动续费订阅
- 产品ID：LimeVip_quarter
- 订阅群组：LimeFocus Premium
- 价格：¥12.00/季度
- 显示名称：LimeFocus 高级版（季度）

产品3：
- 类型：自动续费订阅
- 产品ID：LimeVip_yearly
- 订阅群组：LimeFocus Premium
- 价格：¥18.00/年
- 显示名称：LimeFocus 高级版（年度）
```

#### 步骤3：设置产品状态
1. 每个产品都需要设置为"准备提交"状态
2. 添加本地化信息（中文）
3. 填写审核信息和截图

### 方案2：代码优化和调试

#### 增强错误诊断
```dart
// 在 _loadProducts 方法中添加更详细的诊断
Future<void> _loadProducts() async {
  try {
    // 检查网络连接
    debugPrint('🔍 开始产品诊断...');
    debugPrint('📱 平台: ${Platform.operatingSystem}');
    debugPrint('🆔 Bundle ID: ${await _getBundleId()}');
    debugPrint('🌐 网络状态: ${await _checkNetworkStatus()}');
    
    final Set<String> productIds = {
      _monthlySubscriptionId,
      _quarterlySubscriptionId,
      _yearlySubscriptionId,
    };

    debugPrint('🔍 查询产品ID: ${productIds.join(", ")}');
    
    // 添加更长的超时时间
    final ProductDetailsResponse response = await _inAppPurchase
        .queryProductDetails(productIds)
        .timeout(
          const Duration(seconds: 30), // 增加到30秒
          onTimeout: () {
            debugPrint('⏰ 产品查询超时（30秒）');
            return ProductDetailsResponse(
              productDetails: [],
              notFoundIDs: productIds.toList(),
              error: null,
            );
          },
        );

    // 详细的错误分析
    if (response.error != null) {
      _analyzeError(response.error!);
      return;
    }

    // 成功情况的详细日志
    _logSuccessfulResponse(response);
    
  } catch (e) {
    debugPrint('❌ 产品加载异常: $e');
    debugPrint('📍 异常类型: ${e.runtimeType}');
    if (e is PlatformException) {
      debugPrint('🔧 平台异常代码: ${e.code}');
      debugPrint('💬 平台异常消息: ${e.message}');
    }
  }
}

void _analyzeError(IAPError error) {
  debugPrint('❌ 产品查询错误详细分析:');
  debugPrint('  错误代码: ${error.code}');
  debugPrint('  错误来源: ${error.source}');
  debugPrint('  错误消息: ${error.message}');
  debugPrint('  错误详情: ${error.details}');
  
  // 根据错误代码提供具体建议
  switch (error.code) {
    case 'storekit_duplicate_product_object':
      debugPrint('💡 建议: 产品ID重复，检查是否多次查询同一产品');
      break;
    case 'storekit_invalid_product_identifier':
      debugPrint('💡 建议: 产品ID无效，检查App Store Connect配置');
      break;
    case 'storekit_no_products_available':
      debugPrint('💡 建议: 无可用产品，检查产品状态和区域设置');
      break;
    default:
      debugPrint('💡 建议: 检查网络连接和App Store Connect配置');
  }
}
```

### 方案3：分步测试策略

#### 阶段1：基础连接测试
```dart
// 添加基础连接测试方法
Future<bool> testStoreConnection() async {
  try {
    debugPrint('🧪 测试App Store连接...');
    
    // 检查StoreKit可用性
    final bool available = await InAppPurchase.instance.isAvailable();
    debugPrint('📱 StoreKit可用性: $available');
    
    if (!available) {
      debugPrint('❌ StoreKit不可用，可能原因:');
      debugPrint('  • 在模拟器上运行');
      debugPrint('  • 设备限制');
      debugPrint('  • 网络问题');
      return false;
    }
    
    // 测试简单查询
    final response = await InAppPurchase.instance
        .queryProductDetails({'com.example.test'})
        .timeout(Duration(seconds: 10));
        
    debugPrint('🔍 测试查询响应: ${response.error?.message ?? "成功"}');
    return true;
    
  } catch (e) {
    debugPrint('❌ 连接测试失败: $e');
    return false;
  }
}
```

#### 阶段2：逐个产品测试
```dart
// 逐个测试产品ID
Future<void> testIndividualProducts() async {
  final productIds = [
    'LemiVip001',
    'LimeVip_quarter', 
    'LimeVip_yearly'
  ];
  
  for (String productId in productIds) {
    debugPrint('🧪 测试产品: $productId');
    
    final response = await InAppPurchase.instance
        .queryProductDetails({productId})
        .timeout(Duration(seconds: 15));
        
    if (response.productDetails.isNotEmpty) {
      debugPrint('✅ $productId: 找到产品');
      final product = response.productDetails.first;
      debugPrint('  标题: ${product.title}');
      debugPrint('  价格: ${product.price}');
    } else {
      debugPrint('❌ $productId: 未找到');
      if (response.notFoundIDs.contains(productId)) {
        debugPrint('  原因: 产品ID不存在或配置错误');
      }
    }
  }
}
```

## 🚀 立即行动计划

### 今天（1小时）
1. **检查App Store Connect配置**（30分钟）
   - 验证应用状态
   - 检查产品配置
   - 确认协议和税务信息

2. **代码诊断增强**（30分钟）
   - 添加详细错误分析
   - 实现分步测试
   - 增加超时时间

### 明天（2小时）
1. **真机测试**（1小时）
   - 使用真机运行应用
   - 创建沙盒测试账号
   - 测试产品查询

2. **问题修复**（1小时）
   - 根据测试结果修复配置
   - 优化错误处理
   - 验证修复效果

## 📞 如果问题仍然存在

### 联系Apple支持
- 开发者技术支持：https://developer.apple.com/support/
- App Store Connect支持：通过开发者账号提交技术支持请求

### 社区资源
- Apple开发者论坛：https://developer.apple.com/forums/
- Stack Overflow：搜索"iOS in-app purchase product not found"

### 备用方案
如果短期内无法解决，可以：
1. 使用模拟数据继续开发其他功能
2. 实现本地付费状态管理
3. 准备App Store审核时再解决订阅问题
