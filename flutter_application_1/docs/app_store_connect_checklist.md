# App Store Connect配置检查清单

## 🏪 **App Store Connect配置状态检查**

### 基本信息
- **应用名称**: LimeFocus
- **Bundle ID**: com.arborflame.limefocus
- **开发团队**: G5N2P69H35

### 📦 **订阅产品配置**

#### 产品1：月度订阅
- **产品ID**: `LemiVip001`
- **产品类型**: 自动续订订阅
- **价格**: ¥6.00/月
- **状态**: 需要检查是否为"准备提交"

#### 产品2：季度订阅
- **产品ID**: `LimeVip_quarter`
- **产品类型**: 自动续订订阅
- **价格**: ¥12.00/季度
- **状态**: 需要检查是否为"准备提交"

#### 产品3：年度订阅
- **产品ID**: `LimeVip_yearly`
- **产品类型**: 自动续订订阅
- **价格**: ¥18.00/年
- **状态**: 需要检查是否为"准备提交"

---

## ✅ **配置检查清单**

### 1. App Store Connect基本配置
- [ ] 应用已在App Store Connect中创建
- [ ] Bundle ID匹配：com.arborflame.limefocus
- [ ] 开发团队正确：G5N2P69H35
- [ ] 应用状态为"准备提交"或"开发中"

### 2. 订阅产品配置
- [ ] 三个订阅产品已创建
- [ ] 产品ID拼写正确（区分大小写）
- [ ] 产品类型设置为"自动续订订阅"
- [ ] 价格已设置且正确
- [ ] 产品状态为"准备提交"（不是"元数据丢失"）

### 3. 订阅组配置
- [ ] 创建了订阅组
- [ ] 所有产品都在同一个订阅组中
- [ ] 订阅组名称已设置
- [ ] 订阅级别已正确配置（月度 < 季度 < 年度）

### 4. 本地化信息
- [ ] 产品显示名称已设置
- [ ] 产品描述已填写
- [ ] 至少配置了中文（简体）本地化

### 5. 审核信息
- [ ] 审核备注已填写
- [ ] 截图已上传（如果需要）
- [ ] 审核联系信息已提供

---

## 🔍 **"元数据丢失"问题解决**

如果产品状态显示"元数据丢失"，请检查：

### 必填信息检查
1. **产品显示名称**
   - 中文（简体）：LimeFocus 高级版（月度/季度/年度）
   - 英文：LimeFocus Premium (Monthly/Quarterly/Yearly)

2. **产品描述**
   - 详细描述订阅包含的功能
   - 说明订阅的好处和价值

3. **订阅组信息**
   - 订阅组显示名称
   - 订阅组描述

4. **价格和可用性**
   - 确保在中国大陆地区可用
   - 价格设置正确

### 修复步骤
1. 进入App Store Connect
2. 选择你的应用
3. 进入"功能" → "App内购买项目"
4. 逐个检查每个产品的配置
5. 补充缺失的信息
6. 保存并等待状态更新

---

## 🧪 **沙盒测试配置**

### 测试账号设置
1. **创建沙盒测试账号**
   - 进入"用户和访问" → "沙盒测试员"
   - 创建测试账号（使用不同的邮箱）
   - 记录测试账号信息

2. **设备配置**
   - 在iOS设备上退出Apple ID
   - 不要在设置中预先登录沙盒账号
   - 只在购买时输入沙盒账号

### 测试流程
1. 在真机上运行应用
2. 进入订阅页面
3. 点击购买按钮
4. 在弹出的登录界面输入沙盒测试账号
5. 完成购买流程

---

## 📊 **状态监控**

### 产品状态说明
- **准备提交**: ✅ 可以在沙盒环境中测试
- **等待审核**: ✅ 可以在沙盒环境中测试
- **被拒绝**: ❌ 需要修复问题后重新提交
- **元数据丢失**: ❌ 需要补充必填信息

### 检查方法
1. 定期登录App Store Connect
2. 查看产品状态变化
3. 如有问题，及时修复

---

## 🚨 **常见问题解决**

### 问题1：产品一直显示"元数据丢失"
**解决方案**：
1. 检查所有必填字段是否完整
2. 确保本地化信息完整
3. 检查订阅组配置
4. 联系Apple支持

### 问题2：沙盒测试无法购买
**解决方案**：
1. 确保使用真机测试
2. 检查沙盒测试账号状态
3. 确保产品状态为"准备提交"
4. 重启设备和应用

### 问题3：产品加载失败
**解决方案**：
1. 等待24小时让配置生效
2. 检查网络连接
3. 验证Bundle ID匹配
4. 使用诊断工具检查

---

## 📞 **获取帮助**

### Apple支持资源
- **开发者文档**: https://developer.apple.com/in-app-purchase/
- **App Store Connect帮助**: https://help.apple.com/app-store-connect/
- **开发者论坛**: https://developer.apple.com/forums/

### 联系方式
- **技术支持**: <EMAIL>
- **开发者支持**: 通过Apple Developer网站提交工单

---

## 📝 **配置完成后的验证**

完成所有配置后，请使用以下工具验证：

1. **订阅功能诊断工具**
   - 个人中心 → 开发者工具 → 订阅功能诊断
   - 查看产品加载状态
   - 进行购买测试

2. **Apple功能调试工具**
   - 个人中心 → 开发者工具 → Apple功能调试
   - 检查服务初始化状态
   - 查看详细错误信息

3. **真机测试**
   - 在iPhone 13上运行应用
   - 测试完整的购买流程
   - 验证订阅状态更新

按照这个清单逐项检查，应该能够解决App Store Connect配置问题。
