# TestFlight 部署完整指南

## 📋 构建前准备清单

### 1. 代码准备
- [ ] 确认所有功能正常工作
- [ ] 运行 `flutter analyze` 确保无严重错误
- [ ] 运行 `flutter test` 确保测试通过
- [ ] 检查版本号和构建号是否正确递增

### 2. 证书和配置文件检查
- [ ] 确认开发者账号状态正常
- [ ] 检查Distribution证书有效期
- [ ] 确认App Store Provisioning Profile最新
- [ ] 验证Bundle ID配置正确

### 3. App Store Connect配置
- [ ] 应用信息完整填写
- [ ] 订阅产品配置正确
- [ ] 测试账号创建完成
- [ ] 审核备注已填写

## 🔧 构建步骤

### Step 1: 环境检查
```bash
# 检查Flutter环境
flutter doctor -v

# 检查iOS工具链
xcodebuild -version

# 清理项目
flutter clean
flutter pub get
```

### Step 2: 版本号管理
编辑 `pubspec.yaml`:
```yaml
version: 1.0.0+1  # 格式：版本号+构建号
```

### Step 3: Release构建
```bash
# 构建iOS Release版本
flutter build ios --release

# 或者使用Xcode构建
open ios/Runner.xcworkspace
```

### Step 4: Xcode配置
1. 打开 `ios/Runner.xcworkspace`
2. 选择 `Runner` 项目
3. 在 `Signing & Capabilities` 中：
   - 选择正确的Team
   - 确认Bundle Identifier
   - 选择Distribution证书
4. 在 `General` 中确认版本信息

### Step 5: Archive构建
1. 在Xcode中选择 `Product` > `Archive`
2. 等待构建完成
3. 在Organizer中选择刚构建的Archive

### Step 6: 上传到App Store Connect
1. 点击 `Distribute App`
2. 选择 `App Store Connect`
3. 选择 `Upload`
4. 确认配置信息
5. 点击 `Upload` 开始上传

## 📱 TestFlight 测试计划

### 内部测试阶段（1-2天）
**目标：** 验证基础功能和订阅流程

**测试重点：**
- [ ] 应用启动和基础导航
- [ ] 专注计时功能
- [ ] 目标和项目创建
- [ ] 订阅购买流程
- [ ] 数据分析页面

**测试设备：**
- iPhone 13 (iOS 17.x)
- iPhone 12 (iOS 16.x)
- iPhone SE (iOS 15.x)

### 外部测试阶段（3-5天）
**目标：** 收集用户反馈，验证用户体验

**测试用户：** 10-20名外部测试者

**反馈收集：**
- 使用TestFlight内置反馈功能
- 创建反馈表单收集详细意见
- 定期与测试用户沟通

## 🔍 测试检查清单

### 功能测试
- [ ] 用户注册/登录流程
- [ ] 专注计时（正计时/倒计时）
- [ ] 目标管理（创建/编辑/删除）
- [ ] 科目和项目管理
- [ ] 数据分析和图表显示
- [ ] 订阅购买和恢复
- [ ] 设置页面功能

### 性能测试
- [ ] 应用启动时间 < 3秒
- [ ] 页面切换流畅度
- [ ] 内存使用合理
- [ ] 电池消耗正常

### 兼容性测试
- [ ] 不同iOS版本兼容性
- [ ] 不同设备尺寸适配
- [ ] 横竖屏切换
- [ ] 深色模式支持

## 📝 问题追踪模板

### Bug报告格式
```
标题：[功能模块] 简短描述

设备信息：
- 设备型号：
- iOS版本：
- 应用版本：

重现步骤：
1.
2.
3.

预期结果：

实际结果：

截图/录屏：

严重程度：[高/中/低]
```

## 🚀 发布时间线

### 第1天：构建上传
- 上午：最终代码检查和构建
- 下午：上传到TestFlight

### 第2-3天：内部测试
- 核心功能验证
- 订阅流程测试
- 关键Bug修复

### 第4-8天：外部测试
- 邀请外部测试用户
- 收集反馈和建议
- 优化用户体验

### 第9-10天：最终准备
- 修复测试中发现的问题
- 准备正式版本提交
- 完善App Store页面信息

## 📞 联系信息

**技术支持：** <EMAIL>
**开发团队：** LimeFocus Development Team
**紧急联系：** 通过App Store Connect消息中心

## 🚀 快速操作指南

### 一键构建脚本使用
```bash
# 给脚本执行权限
chmod +x scripts/build_and_upload.sh

# 使用当前版本号构建
./scripts/build_and_upload.sh

# 指定新版本号构建
./scripts/build_and_upload.sh 1.0.1 2
```

### App Store Connect 配置检查
1. **应用信息完整性**
   - 应用名称：LimeFocus
   - 副标题：专注学习，高效备考
   - 关键词：专注,学习,备考,时间管理,效率
   - 描述：详细功能介绍
   - 截图：5张iPhone截图 + 可选iPad截图

2. **订阅产品状态**
   - LemiVip001 (月度) - 已配置
   - LimeVip_quarter (季度) - 已配置
   - LimeVip_yearly (年度) - 已配置
   - LimeVip_AYear (一年备考包) - 已配置

3. **测试账号信息**
   - 沙盒测试账号：<EMAIL>
   - 密码：TestLime123
   - 确保账号在App Store Connect中已创建

### TestFlight 邀请流程
1. **内部测试组**
   - 添加开发团队成员邮箱
   - 设置为自动分发新构建版本

2. **外部测试组**
   - 创建"LimeFocus Beta测试"组
   - 添加外部测试用户邮箱
   - 设置测试说明和反馈指南

### 常见问题解决

**Q: 构建失败，提示证书问题**
A: 检查开发者账号状态，更新Provisioning Profile

**Q: 上传到App Store Connect失败**
A: 确认Bundle ID正确，检查应用专用密码设置

**Q: TestFlight中看不到新版本**
A: 等待苹果处理（通常5-30分钟），检查构建状态

**Q: 测试用户无法安装**
A: 确认邀请邮箱正确，检查设备兼容性

### 监控和分析
- **App Store Connect Analytics** - 下载量、崩溃率
- **TestFlight反馈** - 用户反馈和截图
- **Xcode Organizer** - 崩溃日志和性能数据

---

**注意事项：**
1. 每次构建前确保版本号递增
2. 保持TestFlight测试版本与最终发布版本一致
3. 及时响应测试用户反馈
4. 记录所有问题和解决方案
5. 构建前运行自动化脚本检查
6. 保持与苹果审核指南的合规性
