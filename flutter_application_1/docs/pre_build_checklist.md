# LimeFocus 构建前检查清单

## 📋 构建前必检项目

### ✅ 代码质量检查

#### 代码分析
- [ ] 运行 `flutter analyze --no-fatal-infos` 无严重错误
- [ ] 修复所有 warning 级别问题
- [ ] 检查 info 级别问题，修复重要的

#### 测试验证
- [ ] 运行 `flutter test` 所有测试通过
- [ ] 核心功能手动测试通过
- [ ] 订阅功能在沙盒环境测试正常

#### 代码规范
- [ ] 移除所有 `print()` 语句，使用 `debugPrint()`
- [ ] 移除调试代码和注释
- [ ] 确保没有硬编码的测试数据
- [ ] 检查敏感信息是否正确处理

### ✅ 版本信息检查

#### 版本号管理
- [ ] `pubspec.yaml` 中版本号正确递增
- [ ] 版本号格式：`major.minor.patch+build`
- [ ] 构建号大于上一个版本
- [ ] iOS项目中版本号与pubspec.yaml一致

#### 更新日志
- [ ] 准备版本更新说明
- [ ] 记录新功能和改进
- [ ] 记录Bug修复
- [ ] 准备用户可见的更新内容

### ✅ 配置文件检查

#### 应用配置
- [ ] Bundle ID正确：`com.arborflame.limefocus`
- [ ] 应用名称正确：`LimeFocus`
- [ ] 应用图标文件完整
- [ ] 启动页配置正确

#### 权限配置
- [ ] 检查 `Info.plist` 权限描述
- [ ] 移除不必要的权限请求
- [ ] 确认隐私权限说明准确

#### 环境配置
- [ ] 生产环境API地址正确
- [ ] 移除开发环境配置
- [ ] 确认第三方服务密钥正确

### ✅ 功能完整性检查

#### 核心功能
- [ ] 用户注册/登录流程正常
- [ ] 专注计时功能完整
- [ ] 目标管理功能正常
- [ ] 科目项目管理正常
- [ ] 数据分析功能正常
- [ ] 设置页面功能完整

#### 订阅功能
- [ ] 订阅产品配置正确
- [ ] 购买流程测试通过
- [ ] 恢复购买功能正常
- [ ] 订阅状态检查正确
- [ ] 功能解锁逻辑正确

#### 界面适配
- [ ] 不同设备尺寸适配正常
- [ ] 深色模式支持（如有）
- [ ] 横竖屏切换正常
- [ ] 键盘弹出适配正常

### ✅ 性能优化检查

#### 启动性能
- [ ] 冷启动时间 < 3秒
- [ ] 热启动时间 < 1秒
- [ ] 启动页显示正常
- [ ] 初始化流程优化

#### 运行性能
- [ ] 页面切换流畅
- [ ] 列表滚动流畅
- [ ] 动画效果流畅
- [ ] 内存使用合理

#### 资源优化
- [ ] 图片资源压缩优化
- [ ] 字体文件大小合理
- [ ] 移除未使用的资源
- [ ] 代码混淆配置正确

### ✅ 安全性检查

#### 数据安全
- [ ] 敏感数据加密存储
- [ ] 网络传输使用HTTPS
- [ ] API密钥安全处理
- [ ] 用户数据隐私保护

#### 代码安全
- [ ] 移除调试信息
- [ ] 代码混淆启用
- [ ] 防逆向工程措施
- [ ] 第三方库安全检查

### ✅ 合规性检查

#### App Store指南
- [ ] 遵循App Store审核指南
- [ ] 内容分级设置正确
- [ ] 年龄限制标识准确
- [ ] 避免违规内容

#### 隐私合规
- [ ] 隐私政策链接正确
- [ ] 数据收集说明准确
- [ ] 用户同意流程完整
- [ ] GDPR合规（如适用）

#### 订阅合规
- [ ] 订阅条款清晰
- [ ] 自动续费说明明确
- [ ] 取消订阅流程清楚
- [ ] 价格显示准确

### ✅ 文档准备

#### 提交材料
- [ ] 应用描述完整
- [ ] 关键词优化
- [ ] 应用截图准备
- [ ] 预览视频制作（可选）

#### 审核信息
- [ ] 审核备注准备
- [ ] 测试账号信息
- [ ] 联系信息更新
- [ ] 技术支持信息

### ✅ 构建环境检查

#### 开发环境
- [ ] Flutter版本稳定
- [ ] Xcode版本最新
- [ ] 依赖包版本稳定
- [ ] 构建工具正常

#### 证书配置
- [ ] 开发者证书有效
- [ ] Provisioning Profile最新
- [ ] 签名配置正确
- [ ] 推送证书配置（如需要）

## 🚀 构建执行步骤

### 1. 最终代码检查
```bash
# 代码分析
flutter analyze --no-fatal-infos

# 运行测试
flutter test

# 清理项目
flutter clean
flutter pub get
```

### 2. 版本号更新
```bash
# 编辑 pubspec.yaml
# 更新版本号，例如：1.0.0+1 -> 1.0.1+2
```

### 3. 构建Release版本
```bash
# 使用自动化脚本
chmod +x scripts/build_and_upload.sh
./scripts/build_and_upload.sh 1.0.1 2

# 或手动构建
flutter build ios --release
```

### 4. Xcode Archive
```bash
# 打开Xcode workspace
open ios/Runner.xcworkspace

# 在Xcode中执行Archive
# Product -> Archive
```

### 5. 上传到App Store Connect
```bash
# 在Xcode Organizer中
# 选择Archive -> Distribute App -> App Store Connect -> Upload
```

## ⚠️ 常见问题预防

### 构建失败
- 检查Flutter和Xcode版本兼容性
- 确认所有依赖包版本稳定
- 清理缓存重新构建

### 签名问题
- 更新Provisioning Profile
- 检查证书有效期
- 确认Bundle ID配置

### 上传失败
- 检查网络连接
- 确认Apple ID权限
- 验证应用专用密码

### 审核被拒
- 仔细阅读审核指南
- 准备详细的审核备注
- 确保功能完整可用

## 📞 紧急联系

如果在构建过程中遇到问题：

**技术支持：** <EMAIL>  
**开发团队：** LimeFocus Development Team  
**Apple开发者支持：** https://developer.apple.com/support/

---

**检查完成确认：**

- [ ] 所有检查项目已完成
- [ ] 构建环境准备就绪
- [ ] 相关文档已准备
- [ ] 团队成员已通知

**检查人员：** _______________  
**检查日期：** _______________  
**版本信息：** _______________

---

**准备就绪，开始构建！** 🚀
