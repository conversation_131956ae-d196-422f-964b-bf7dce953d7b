# iOS部署问题解决指南

## 🔍 当前问题

**错误信息：**
```
Could not run build/ios/iphoneos/Runner.app on 00008110-00022D500131801E.
Try launching Xcode and selecting "Product > Run" to fix the problem:
  open ios/Runner.xcworkspace
```

**设备ID：** `00008110-00022D500131801E` (LEVIL)

## 🛠️ 解决步骤

### 步骤1：在Xcode中检查配置

#### 1.1 打开项目
- ✅ Xcode已自动打开 `ios/Runner.xcworkspace`

#### 1.2 检查设备连接
1. **确认设备显示**
   - 在Xcode顶部工具栏查看设备选择器
   - 应该显示"LEVIL"或设备名称
   - 如果显示"Unavailable"，需要解决连接问题

2. **检查设备状态**
   - 设备应该显示为"Connected"
   - 如果显示其他状态，需要处理

#### 1.3 检查签名配置
1. **选择Runner项目**
   - 在左侧项目导航器中点击"Runner"

2. **检查Signing & Capabilities**
   - 选择"Signing & Capabilities"标签
   - 确认以下配置：
     - **Team:** G5N2P69H35 (已配置)
     - **Bundle Identifier:** com.arborflame.limefocus
     - **Provisioning Profile:** 应该显示有效的配置文件

3. **解决签名问题**
   - 如果显示错误，点击"Try Again"
   - 或者取消勾选"Automatically manage signing"，然后重新勾选

### 步骤2：设备信任问题解决

#### 2.1 检查设备信任
1. **在iOS设备上**
   - 打开"设置" > "通用" > "VPN与设备管理"
   - 查找开发者证书（G5N2P69H35）
   - 如果显示"未受信任"，点击信任

2. **重新连接设备**
   - 断开USB连接
   - 重新连接设备
   - 在设备上点击"信任此电脑"

#### 2.2 清除设备缓存
1. **在Xcode中**
   - 选择"Window" > "Devices and Simulators"
   - 找到LEVIL设备
   - 右键点击 > "Unpair Device"
   - 重新连接设备并配对

### 步骤3：在Xcode中运行

#### 3.1 选择正确的目标
1. **设备选择**
   - 在Xcode顶部选择"LEVIL"设备
   - 不要选择模拟器

2. **Scheme选择**
   - 确保选择的是"Runner"
   - 构建配置应该是"Debug"

#### 3.2 执行Product > Run
1. **开始运行**
   - 点击菜单"Product" > "Run"
   - 或者按快捷键 ⌘ + R
   - 或者点击播放按钮 ▶️

2. **观察构建过程**
   - 查看构建日志
   - 注意任何错误或警告

### 步骤4：常见问题解决

#### 4.1 如果显示"Device Locked"
1. **解锁设备**
   - 确保iOS设备已解锁
   - 输入密码或使用Face ID/Touch ID

2. **保持设备唤醒**
   - 在部署过程中保持设备屏幕亮起

#### 4.2 如果显示签名错误
1. **重新生成证书**
   - 在Xcode中选择"Preferences" > "Accounts"
   - 选择Apple ID账号
   - 点击"Download Manual Profiles"

2. **清理并重建**
   - 在Xcode中选择"Product" > "Clean Build Folder"
   - 重新运行"Product" > "Run"

#### 4.3 如果显示Provisioning Profile错误
1. **检查Bundle ID**
   - 确认Bundle ID为：com.arborflame.limefocus
   - 确认在Apple Developer中已注册

2. **更新Provisioning Profile**
   - 登录Apple Developer网站
   - 更新或重新生成Provisioning Profile

### 步骤5：返回Flutter

#### 5.1 Xcode运行成功后
1. **确认应用启动**
   - 应用应该在设备上正常启动
   - 可以看到LimeFocus的启动画面

2. **关闭Xcode中的应用**
   - 在Xcode中停止运行
   - 或者在设备上关闭应用

#### 5.2 重新使用Flutter
```bash
# 重新尝试Flutter运行
flutter run --debug
```

## 🔧 预防措施

### 定期维护
1. **保持证书更新**
   - 定期检查开发者证书有效期
   - 及时更新Provisioning Profile

2. **设备管理**
   - 定期清理设备缓存
   - 保持设备与开发环境的信任关系

### 最佳实践
1. **使用Xcode进行首次部署**
   - 新设备或新项目首次部署建议使用Xcode
   - 确保所有配置正确后再使用Flutter

2. **保持环境一致**
   - 确保Xcode、Flutter、iOS版本兼容
   - 定期更新开发工具

## 📋 检查清单

### 设备检查
- [ ] 设备已解锁
- [ ] 设备已信任此电脑
- [ ] 设备在Xcode中显示为Connected

### 签名检查
- [ ] Team配置正确 (G5N2P69H35)
- [ ] Bundle ID正确 (com.arborflame.limefocus)
- [ ] Provisioning Profile有效
- [ ] 开发者证书已信任

### Xcode检查
- [ ] 项目在Xcode中可以正常运行
- [ ] 没有构建错误或警告
- [ ] 应用可以在设备上启动

### Flutter检查
- [ ] Xcode运行成功后Flutter可以正常部署
- [ ] 设备在flutter devices中显示
- [ ] flutter run命令执行成功

## 🚀 成功标志

当看到以下情况时，表示问题已解决：

1. **Xcode中**
   - ✅ 设备显示为"Connected"
   - ✅ Product > Run成功执行
   - ✅ 应用在设备上正常启动

2. **Flutter中**
   - ✅ `flutter run`命令成功执行
   - ✅ 应用正常启动并显示日志
   - ✅ 热重载功能正常工作

## 📞 如果问题仍然存在

### 高级解决方案
1. **重置开发环境**
   - 清理Xcode缓存：`~/Library/Developer/Xcode/DerivedData`
   - 重新安装Provisioning Profile
   - 重新配对设备

2. **联系支持**
   - Apple开发者支持
   - Flutter社区支持
   - 项目技术支持：<EMAIL>

---

**当前状态：** Xcode已打开，请按照上述步骤在Xcode中运行应用，然后返回Flutter继续开发。
