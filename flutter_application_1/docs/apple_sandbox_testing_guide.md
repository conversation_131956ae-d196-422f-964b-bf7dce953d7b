# Apple沙盒测试完整指南

## 📋 概述

本文档提供LimeFocus应用Apple内购和订阅功能的沙盒测试完整指南，包括准备工作、测试流程和审核准备。

## 🎯 沙盒测试准备

### 1. App Store Connect配置

#### 1.1 创建沙盒测试账号
1. 登录 [App Store Connect](https://appstoreconnect.apple.com)
2. 进入 **用户和访问** → **沙盒测试员**
3. 点击 **+** 创建新的测试账号
4. 填写信息：
   - **邮箱**: `<EMAIL>`（或其他可用邮箱）
   - **密码**: 设置强密码
   - **名字**: LimeFocus
   - **姓氏**: Tester
   - **地区**: 中国

#### 1.2 验证产品配置
确保以下产品已在App Store Connect中正确配置：
- `LemiVip001` - 月度会员 ¥6
- `LimeVip_quarter` - 季度会员 ¥12
- `LimeVip_yearly` - 年度会员 ¥28
- `LimeVip_AYear` - 一年备考包 ¥18

### 2. 代码配置检查

#### 2.1 entitlements文件
确保 `ios/Runner/Runner.entitlements` 包含：
```xml
<key>com.apple.developer.storekit.sandbox</key>
<true/>
<key>com.apple.developer.in-app-payments</key>
<array/>
```

#### 2.2 Bundle ID匹配
确认Bundle ID为：`com.arborflame.limefocus`

#### 2.3 沙盒配置
代码中已集成 `SandboxConfig` 类，自动处理沙盒环境。

## 🧪 沙盒测试流程

### 1. 准备测试设备

#### 1.1 设备要求
- **必须使用真机**（模拟器不支持内购）
- iOS 12.0 或更高版本
- 网络连接正常

#### 1.2 设备设置
1. 在设备上退出当前Apple ID：
   - 设置 → Apple ID → 退出登录
2. **不要**在App Store中登录沙盒账号
3. 确保设备没有购买限制

### 2. 测试步骤

#### 2.1 安装应用
```bash
flutter run --release
```

#### 2.2 测试购买流程
1. 打开应用，进入 **个人中心** → **订阅管理**
2. 选择任意订阅产品
3. 点击 **立即订阅**
4. 系统会弹出登录提示，选择 **使用现有Apple ID**
5. 输入沙盒测试账号和密码
6. 确认购买（沙盒环境不会产生真实费用）
7. 验证订阅状态是否正确更新

#### 2.3 测试恢复购买
1. 在订阅界面点击 **恢复购买**
2. 验证之前的购买是否正确恢复

#### 2.4 测试错误情况
1. 取消购买流程
2. 网络断开时的处理
3. 重复购买的处理

### 3. 调试工具使用

#### 3.1 Apple功能调试
1. 进入 **个人中心** → **开发者工具** → **Apple功能调试**
2. 查看详细的调试信息
3. 使用测试按钮验证各项功能

#### 3.2 日志查看
在Xcode控制台查看详细日志：
```
[SANDBOX] 开始初始化Apple订阅服务
[SANDBOX] 当前环境: Debug (Sandbox)
[SANDBOX] 沙盒模式: true
```

## 📝 审核准备

### 1. 审核账号准备

#### 1.1 提供给Apple的账号
- **类型**: 沙盒测试账号
- **状态**: 新创建的账号（未进行过购买）
- **邮箱**: `<EMAIL>`
- **密码**: [在审核备注中提供]
- **地区**: 中国

#### 1.2 账号说明
```
审核测试账号信息：
邮箱：<EMAIL>
密码：[密码]
地区：中国
说明：这是专门为审核创建的沙盒测试账号，未进行过任何购买操作。
```

### 2. 审核备注模板

```
LimeFocus - 专注学习应用

测试账号信息：
- 沙盒测试账号：<EMAIL>
- 密码：[密码]
- 地区：中国

测试说明：
1. 本应用的订阅功能与后端账号系统独立，直接通过Apple内购实现
2. 用户可以在未登录应用账号的情况下直接购买订阅
3. 订阅状态通过Apple StoreKit验证，确保安全可靠
4. 请使用提供的沙盒测试账号进行内购功能测试

功能测试路径：
个人中心 → 订阅管理 → 选择订阅计划 → 立即订阅

注意事项：
- 请在真机上测试（模拟器不支持内购）
- 首次购买时会提示登录Apple ID，请使用提供的测试账号
- 沙盒环境下的购买不会产生真实费用
```

## 🔧 代码清理策略

### 1. 沙盒相关代码保留

**保留的代码**（审核和生产都需要）：
- `SandboxConfig` 类
- entitlements中的沙盒配置
- Apple订阅服务的沙盒日志

**原因**：
- 沙盒配置在审核时必需
- 生产环境下会自动切换到生产模式
- Apple要求应用支持沙盒测试

### 2. 开发调试代码清理

**需要清理的代码**（提交审核前）：
- 开发者工具页面（通过 `ReleaseConfig` 控制）
- 测试数据生成器
- 网络诊断工具
- 详细的调试日志

**清理时机**：
- 构建Release版本时自动隐藏
- 不需要手动删除代码

### 3. 构建配置

#### 3.1 Debug构建（开发测试）
```bash
flutter run --debug
# 显示所有开发工具和调试功能
```

#### 3.2 Release构建（审核提交）
```bash
flutter build ios --release
# 自动隐藏开发工具，保留沙盒支持
```

## ⚠️ 重要注意事项

### 1. 沙盒测试限制
- 只能在真机上测试
- 必须使用专门的沙盒测试账号
- 不能使用真实Apple ID
- 订阅在沙盒环境下会加速过期

### 2. 审核要求
- 提供可用的沙盒测试账号
- 确保购买流程完整可用
- 处理各种错误情况
- 遵循Apple内购指南

### 3. 生产发布
- 沙盒配置会自动切换到生产模式
- 不需要修改代码
- 确保App Store Connect中的产品已上线

## 🆘 常见问题

### Q1: 产品加载失败
**解决方案**：
1. 检查Bundle ID是否匹配
2. 确认产品在App Store Connect中已配置
3. 等待24小时让配置生效

### Q2: 购买流程无响应
**解决方案**：
1. 确保在真机上测试
2. 检查网络连接
3. 重启应用和设备

### Q3: 沙盒账号登录失败
**解决方案**：
1. 确认账号信息正确
2. 检查账号是否被锁定
3. 在设备设置中完全退出Apple ID

## 📞 技术支持

如遇到问题，请查看：
1. Apple功能调试工具
2. Xcode控制台日志
3. App Store Connect帮助文档
