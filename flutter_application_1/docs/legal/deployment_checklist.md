# LimeFocus 法律文档部署检查清单

## 📋 部署前检查

### 文档准备
- [ ] `terms_of_service.html` - 使用条款文档已准备
- [ ] `privacy_policy.html` - 隐私政策文档已准备
- [ ] `index.html` - 导航页面已准备
- [ ] 所有文档中的联系邮箱已确认：<EMAIL>
- [ ] 域名信息已确认：arborflame.com

### 服务器配置
- [ ] 阿里云服务器已配置
- [ ] Web服务器（Nginx/Apache）已安装
- [ ] SSL证书已配置（HTTPS必需）
- [ ] 域名DNS解析已配置

## 🚀 部署步骤

### 1. 文件上传
```bash
# 建议的服务器目录结构
/var/www/arborflame.com/
├── index.html          # 导航页面
├── terms.html          # 使用条款（重命名自 terms_of_service.html）
└── privacy.html        # 隐私政策（重命名自 privacy_policy.html）
```

### 2. 文件重命名
- [ ] `terms_of_service.html` → `terms.html`
- [ ] `privacy_policy.html` → `privacy.html`
- [ ] `index.html` 保持不变

### 3. 权限设置
```bash
# 设置正确的文件权限
chmod 644 *.html
chown www-data:www-data *.html  # 根据您的服务器配置调整
```

## 🔗 URL测试

部署完成后，请测试以下URL：

### 必需的URL（用于应用内跳转）
- [ ] https://arborflame.com/terms.html
- [ ] https://arborflame.com/privacy.html

### 可选的URL
- [ ] https://arborflame.com/ 或 https://arborflame.com/index.html

## 📱 前端集成

部署完成后，需要更新Flutter应用中的URL：

### 1. 更新注册表单中的链接
文件：`lib/features/auth/widgets/register_form.dart`
```dart
// 当前代码
Navigator.pushNamed(context, '/terms');
Navigator.pushNamed(context, '/privacy');

// 需要改为
_launchURL('https://arborflame.com/terms.html');
_launchURL('https://arborflame.com/privacy.html');
```

### 2. 添加URL启动功能
需要添加 `url_launcher` 依赖并实现URL启动功能。

### 3. 更新路由（如果使用WebView）
如果选择使用WebView显示：
- [ ] 更新 `lib/features/legal/screens/terms_screen.dart`
- [ ] 更新 `lib/features/legal/screens/privacy_screen.dart`

## ✅ 测试验证

### 功能测试
- [ ] 在浏览器中访问所有URL
- [ ] 检查页面在移动设备上的显示效果
- [ ] 验证HTTPS证书有效
- [ ] 测试页面加载速度

### 内容检查
- [ ] 所有联系信息正确
- [ ] 日期和版本号正确
- [ ] 文档内容完整无误
- [ ] 中文显示正常

### iOS集成测试
- [ ] 在iOS设备上测试链接跳转
- [ ] 验证Safari中页面显示正常
- [ ] 测试从应用内跳转到网页

## 📋 iOS App Store提交检查

### App Store Connect配置
- [ ] 在App Store Connect中填写隐私政策URL：https://arborflame.com/privacy.html
- [ ] 确认应用内所有法律文档链接正常工作
- [ ] 验证隐私政策内容与应用实际行为一致

### 审核准备
- [ ] 确保文档内容符合iOS审核指南
- [ ] 验证所有第三方服务都已在隐私政策中说明
- [ ] 确认订阅相关条款完整

## 🔄 维护计划

### 定期检查（每月）
- [ ] 检查网站可访问性
- [ ] 验证SSL证书有效期
- [ ] 检查服务器日志

### 内容更新（按需）
- [ ] 应用功能更新时同步更新文档
- [ ] 法律法规变化时及时调整
- [ ] 用户反馈问题时及时修正

## 📞 应急联系

如果部署过程中遇到问题：

### 技术支持
- 阿里云技术支持
- 域名服务商支持

### 法律咨询
- 建议咨询专业律师确认文档合规性
- 关注相关法律法规更新

## 📝 部署完成确认

部署完成后，请确认：
- [ ] 所有URL正常访问
- [ ] HTTPS证书有效
- [ ] 页面内容显示正确
- [ ] 移动端适配良好
- [ ] 联系邮箱可正常接收邮件

**部署完成时间：** ___________  
**部署人员：** ___________  
**验证人员：** ___________
