# LimeFocus 前端集成指导 - 法律文档链接

## 📋 概述

静态法律文档已成功部署到服务器，现在需要在Flutter应用中集成这些链接。

## 🔗 可用的URL

### 正式链接（用于生产环境）
- **使用条款**: https://arborflame.com/terms
- **隐私政策**: https://arborflame.com/privacy
- **主页**: https://arborflame.com/

### 特点
- ✅ 支持HTTPS（iOS要求）
- ✅ 移动端适配
- ✅ 快速加载
- ✅ 中文内容
- ✅ 符合iOS审核要求

## 📱 Flutter集成方案

### 方案一：使用url_launcher（推荐）

#### 1. 添加依赖
在 `pubspec.yaml` 中添加：
```yaml
dependencies:
  url_launcher: ^6.1.14
```

#### 2. 创建URL启动工具类
创建文件 `lib/utils/url_launcher_helper.dart`：
```dart
import 'package:url_launcher/url_launcher.dart';

class UrlLauncherHelper {
  static const String termsUrl = 'https://arborflame.com/terms';
  static const String privacyUrl = 'https://arborflame.com/privacy';
  
  static Future<void> launchTerms() async {
    await _launchUrl(termsUrl);
  }
  
  static Future<void> launchPrivacy() async {
    await _launchUrl(privacyUrl);
  }
  
  static Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(
        uri,
        mode: LaunchMode.externalApplication, // 在外部浏览器中打开
      );
    } else {
      throw 'Could not launch $url';
    }
  }
}
```

#### 3. 在注册页面中使用
修改注册表单中的链接：
```dart
// 在注册页面的条款同意部分
TextButton(
  onPressed: () => UrlLauncherHelper.launchTerms(),
  child: Text('使用条款'),
),

TextButton(
  onPressed: () => UrlLauncherHelper.launchPrivacy(),
  child: Text('隐私政策'),
),
```

#### 4. 在设置页面中使用
在设置页面添加法律文档入口：
```dart
ListTile(
  title: Text('使用条款'),
  trailing: Icon(Icons.open_in_new),
  onTap: () => UrlLauncherHelper.launchTerms(),
),
ListTile(
  title: Text('隐私政策'),
  trailing: Icon(Icons.open_in_new),
  onTap: () => UrlLauncherHelper.launchPrivacy(),
),
```

### 方案二：使用WebView（可选）

如果希望在应用内显示，可以使用WebView：

#### 1. 添加依赖
```yaml
dependencies:
  webview_flutter: ^4.4.1
```

#### 2. 创建WebView页面
```dart
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class LegalDocumentWebView extends StatefulWidget {
  final String url;
  final String title;
  
  const LegalDocumentWebView({
    Key? key,
    required this.url,
    required this.title,
  }) : super(key: key);
  
  @override
  State<LegalDocumentWebView> createState() => _LegalDocumentWebViewState();
}

class _LegalDocumentWebViewState extends State<LegalDocumentWebView> {
  late final WebViewController controller;
  
  @override
  void initState() {
    super.initState();
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(Uri.parse(widget.url));
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        actions: [
          IconButton(
            icon: Icon(Icons.open_in_browser),
            onPressed: () => UrlLauncherHelper._launchUrl(widget.url),
          ),
        ],
      ),
      body: WebViewWidget(controller: controller),
    );
  }
}
```

#### 3. 导航到WebView页面
```dart
// 使用条款
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => LegalDocumentWebView(
      url: 'https://arborflame.com/terms',
      title: '使用条款',
    ),
  ),
);

// 隐私政策
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => LegalDocumentWebView(
      url: 'https://arborflame.com/privacy',
      title: '隐私政策',
    ),
  ),
);
```

## 🍎 iOS配置

### 1. Info.plist配置
在 `ios/Runner/Info.plist` 中添加（如果使用url_launcher）：
```xml
<key>LSApplicationQueriesSchemes</key>
<array>
    <string>https</string>
    <string>http</string>
</array>
```

### 2. App Store Connect配置
在App Store Connect中：
1. 进入应用信息页面
2. 在"隐私政策URL"字段填入：`https://arborflame.com/privacy`
3. 保存更改

## 🧪 测试指南

### 1. 开发环境测试
```dart
// 测试代码示例
void testLegalDocuments() {
  print('测试使用条款链接...');
  UrlLauncherHelper.launchTerms();
  
  print('测试隐私政策链接...');
  UrlLauncherHelper.launchPrivacy();
}
```

### 2. 设备测试清单
- [ ] iOS真机测试链接跳转
- [ ] Android真机测试链接跳转
- [ ] 检查页面在移动浏览器中的显示效果
- [ ] 验证HTTPS连接安全
- [ ] 测试网络异常情况的处理

### 3. 用户体验测试
- [ ] 链接响应速度
- [ ] 页面加载时间
- [ ] 移动端阅读体验
- [ ] 返回应用的流畅性

## 🔧 故障排除

### 常见问题

#### 1. 链接无法打开
```dart
// 添加错误处理
static Future<void> _launchUrl(String url) async {
  try {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      // 显示错误提示
      throw 'Could not launch $url';
    }
  } catch (e) {
    // 记录错误并显示用户友好的提示
    print('Error launching URL: $e');
    // 可以显示一个对话框告知用户
  }
}
```

#### 2. iOS权限问题
确保在Info.plist中正确配置了URL schemes。

#### 3. 网络连接问题
添加网络状态检查：
```dart
import 'package:connectivity_plus/connectivity_plus.dart';

static Future<bool> checkConnectivity() async {
  var connectivityResult = await (Connectivity().checkConnectivity());
  return connectivityResult != ConnectivityResult.none;
}
```

## 📋 部署后检查清单

### 开发团队检查
- [ ] 依赖已添加到pubspec.yaml
- [ ] URL启动工具类已创建
- [ ] 注册页面链接已更新
- [ ] 设置页面链接已添加
- [ ] iOS Info.plist已配置
- [ ] 错误处理已实现

### 测试团队检查
- [ ] iOS真机测试通过
- [ ] Android真机测试通过
- [ ] 网络异常测试通过
- [ ] 用户体验测试通过

### 发布前检查
- [ ] App Store Connect隐私政策URL已配置
- [ ] 所有链接在生产环境正常工作
- [ ] 法律文档内容与应用功能一致

## 📞 技术支持

如果在集成过程中遇到问题，请联系：
- **邮箱**: <EMAIL>
- **文档**: 本项目的docs/条款目录

## 📝 更新日志

- **2025-05-25**: 初始版本，完成HTTPS部署和基础集成指导
- **后续**: 根据实际集成情况更新文档

---

**注意**: 请确保在正式发布前测试所有链接的可访问性和用户体验。n g