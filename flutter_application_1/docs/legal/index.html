<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LimeFocus - 法律文档</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .logo {
            font-size: 36px;
            font-weight: bold;
            color: #27ae60;
            margin-bottom: 20px;
        }
        .subtitle {
            color: #7f8c8d;
            margin-bottom: 40px;
            font-size: 18px;
        }
        .document-links {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 40px;
        }
        .doc-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 30px 20px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            min-width: 200px;
        }
        .doc-card:hover {
            border-color: #27ae60;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .doc-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .doc-description {
            font-size: 14px;
            color: #7f8c8d;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #7f8c8d;
            font-size: 14px;
        }
        .contact-info {
            margin-top: 20px;
        }
        .contact-info a {
            color: #27ae60;
            text-decoration: none;
        }
        .contact-info a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">LimeFocus</div>
        <div class="subtitle">专注学习，成就未来</div>
        
        <div class="document-links">
            <a href="terms.html" class="doc-card">
                <div class="doc-title">使用条款</div>
                <div class="doc-description">
                    了解使用LimeFocus的条款和条件
                </div>
            </a>
            
            <a href="privacy.html" class="doc-card">
                <div class="doc-title">隐私政策</div>
                <div class="doc-description">
                    了解我们如何保护您的隐私
                </div>
            </a>
        </div>
        
        <div class="footer">
            <div class="contact-info">
                <p><strong>联系我们</strong></p>
                <p>邮箱：<a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p>网站：<a href="https://arborflame.com">arborflame.com</a></p>
            </div>
            
            <p style="margin-top: 30px;">
                © 2024 LimeFocus. 保留所有权利。<br>
                个人开发者 | 四川省成都市双流区
            </p>
        </div>
    </div>
</body>
</html>
