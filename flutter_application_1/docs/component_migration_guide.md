# 组件迁移指南

## 概述

为了统一项目中的UI组件和样式，我们进行了以下优化：

1. 更新了 `AppCard` 组件，使其使用 `AppDecorations` 类，并支持多种卡片类型
2. 更新了 `AppButton` 组件，使其使用 `AppColors` 和 `AppSizes` 常量
3. 添加了更多常用的颜色、透明度和装饰样式常量

## 卡片组件迁移

### 替换原生 Card 组件

将所有使用原生 `Card` 组件的地方替换为 `AppCard` 组件：

```dart
// 替换这种用法
Card(
  elevation: 2,
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(16),
  ),
  child: Padding(
    padding: const EdgeInsets.all(16),
    child: Text('卡片内容'),
  ),
)

// 使用这种用法
AppCard(
  child: Text('卡片内容'),
)
```

### 使用不同类型的卡片

`AppCard` 组件现在支持多种类型，可以通过 `type` 参数或工厂方法创建不同类型的卡片：

```dart
// 标准卡片（带边框和阴影）
AppCard(
  title: '标准卡片',
  type: AppCardType.standard,
  child: Text('卡片内容'),
)
// 或者使用工厂方法
AppCardFactory.standard(
  title: '标准卡片',
  child: Text('卡片内容'),
)

// 简单卡片（无边框，只有阴影）
AppCardFactory.simple(
  title: '简单卡片',
  child: Text('卡片内容'),
)

// 扁平卡片（无边框无阴影）
AppCardFactory.flat(
  title: '扁平卡片',
  child: Text('卡片内容'),
)

// 选中卡片（带主题色边框）
AppCardFactory.selected(
  title: '选中卡片',
  child: Text('卡片内容'),
)

// 强调卡片（带主题色背景）
AppCardFactory.accent(
  title: '强调卡片',
  child: Text('卡片内容'),
)
```

## 按钮组件迁移

### 替换原生按钮组件

将所有使用原生按钮组件的地方替换为 `AppButton` 组件：

```dart
// 替换这种用法
ElevatedButton(
  onPressed: () {},
  style: ElevatedButton.styleFrom(
    backgroundColor: AppColors.primary,
    foregroundColor: Colors.white,
  ),
  child: const Text('主要按钮'),
)

// 使用这种用法
AppButton(
  text: '主要按钮',
  type: AppButtonType.primary,
  onPressed: () {},
)
```

### 使用不同类型的按钮

`AppButton` 组件支持多种类型：

```dart
// 主要按钮
AppButton(
  text: '主要按钮',
  type: AppButtonType.primary,
  onPressed: () {},
)

// 次要按钮
AppButton(
  text: '次要按钮',
  type: AppButtonType.secondary,
  onPressed: () {},
)

// 文本按钮
AppButton(
  text: '文本按钮',
  type: AppButtonType.text,
  onPressed: () {},
)

// 轮廓按钮
AppButton(
  text: '轮廓按钮',
  type: AppButtonType.outline,
  onPressed: () {},
)

// 图标按钮
AppButton(
  icon: Icons.add,
  type: AppButtonType.icon,
  onPressed: () {},
)

// 带图标的按钮
AppButton(
  text: '带图标的按钮',
  icon: Icons.add,
  type: AppButtonType.primary,
  onPressed: () {},
)
```

### 使用不同尺寸的按钮

`AppButton` 组件支持多种尺寸：

```dart
// 小按钮
AppButton(
  text: '小按钮',
  size: AppButtonSize.small,
  onPressed: () {},
)

// 中等按钮
AppButton(
  text: '中等按钮',
  size: AppButtonSize.medium,
  onPressed: () {},
)

// 大按钮
AppButton(
  text: '大按钮',
  size: AppButtonSize.large,
  onPressed: () {},
)
```

## 颜色使用迁移

### 替换硬编码颜色

将代码中的硬编码颜色替换为常量：

```dart
// 替换这些硬编码颜色
Colors.white
Colors.black
Colors.grey
Colors.green
Colors.red
Colors.blue
Colors.amber
Colors.purple
Colors.orange
Colors.transparent

// 使用这些常量
AppColors.white
AppColors.black
AppColors.grey
AppColors.green
AppColors.red
AppColors.blue
AppColors.amber
AppColors.purple
AppColors.orange
AppColors.transparent
```

### 替换透明度用法

将 `withOpacity` 方法替换为 `withAlpha` 方法：

```dart
// 替换这些用法
Colors.white.withOpacity(0.1)
Colors.black.withOpacity(0.2)
AppColors.primary.withOpacity(0.5)

// 使用这些常量或方法
AppColors.white10
AppColors.black20
AppColors.primary50
// 或者
AppColors.whiteWithAlpha(AppColors.alpha10)
AppColors.blackWithAlpha(AppColors.alpha20)
AppColors.primaryWithAlpha(AppColors.alpha50)
```

## 装饰样式迁移

### 使用预定义的装饰样式

使用 `AppDecorations` 类中预定义的装饰样式：

```dart
// 替换这种用法
BoxDecoration(
  color: Colors.white,
  borderRadius: BorderRadius.circular(16),
  boxShadow: [
    BoxShadow(
      color: Colors.black.withOpacity(0.1),
      offset: Offset(0, 2),
      blurRadius: 4,
    ),
  ],
)

// 使用这种用法
AppDecorations.card()
// 或者
AppDecorations.cardNoBorder(
  color: AppColors.white,
  borderRadius: AppSizes.radiusLarge,
)
```

## 注意事项

1. 在替换过程中，确保保持原有的视觉效果不变
2. 如果需要特定的样式，可以通过组件的参数进行自定义
3. 完成迁移后，运行应用并检查所有页面，确保没有视觉问题
