# 前端安全修复更新说明

## 概述

后端已修复邮件验证码发送接口的安全漏洞，前端需要相应调整以配合新的安全机制。

## 修复内容

### 1. 安全漏洞修复

**问题描述**：
- 原接口 `/api/email-auth/send-verification-code` 没有验证邮箱是否已注册
- 允许向任意邮箱发送验证码，存在安全风险

**修复方案**：
- 添加邮箱存在性验证
- 根据用途（purpose）进行不同的验证逻辑
- 防止垃圾邮件攻击和隐私泄露

### 2. API 接口变更

#### 发送邮箱验证码接口更新

**接口**: `POST /api/email-auth/send-verification-code`

**新增查询参数**:
```
?purpose={login|register|reset_password}
```

**用途说明**:
- `login`: 登录（默认值，要求邮箱已注册）
- `register`: 注册（要求邮箱未注册）
- `reset_password`: 重置密码（要求邮箱已注册）

## 前端需要的修改

### 1. 发送验证码请求更新

#### 注册流程
```javascript
// 修改前
const response = await fetch('/api/email-auth/send-verification-code', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email })
});

// 修改后
const response = await fetch('/api/email-auth/send-verification-code?purpose=register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email })
});
```

#### 登录流程
```javascript
// 修改前
const response = await fetch('/api/email-auth/send-verification-code', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email })
});

// 修改后（可以省略purpose=login，因为是默认值）
const response = await fetch('/api/email-auth/send-verification-code?purpose=login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email })
});
```

#### 重置密码流程
```javascript
// 修改前
const response = await fetch('/api/email-auth/forgot-password', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email })
});

// 修改后（可以继续使用原接口，或使用新接口）
const response = await fetch('/api/email-auth/send-verification-code?purpose=reset_password', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email })
});
```

### 2. 错误处理更新

#### 新增错误情况处理

```javascript
const sendVerificationCode = async (email, purpose = 'login') => {
  try {
    const response = await fetch(`/api/email-auth/send-verification-code?purpose=${purpose}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email })
    });

    const data = await response.json();

    if (!response.ok) {
      // 处理新的错误情况
      if (purpose === 'login' && data.message.includes('尚未注册')) {
        // 邮箱未注册，引导用户注册
        showError('该邮箱尚未注册，请先注册账号');
        redirectToRegister();
        return;
      }
      
      if (purpose === 'register' && data.message.includes('已被注册')) {
        // 邮箱已注册，引导用户登录
        showError('该邮箱已被注册，请直接登录');
        redirectToLogin();
        return;
      }
      
      if (purpose === 'reset_password' && data.message.includes('尚未注册')) {
        // 邮箱未注册，无法重置密码
        showError('该邮箱尚未注册，无法重置密码');
        return;
      }
      
      // 其他错误
      showError(data.message || '发送验证码失败');
      return;
    }

    // 成功处理
    showSuccess('验证码已发送到您的邮箱');
  } catch (error) {
    showError('网络错误，请稍后重试');
  }
};
```

### 3. 用户体验优化建议

#### 智能流程引导
```javascript
// 在登录页面，如果邮箱未注册，自动引导到注册页面
const handleLoginEmailVerification = async (email) => {
  const result = await sendVerificationCode(email, 'login');
  if (result.error && result.error.includes('尚未注册')) {
    const shouldRegister = confirm('该邮箱尚未注册，是否前往注册页面？');
    if (shouldRegister) {
      navigateToRegister(email); // 预填邮箱
    }
  }
};

// 在注册页面，如果邮箱已注册，自动引导到登录页面
const handleRegisterEmailVerification = async (email) => {
  const result = await sendVerificationCode(email, 'register');
  if (result.error && result.error.includes('已被注册')) {
    const shouldLogin = confirm('该邮箱已被注册，是否前往登录页面？');
    if (shouldLogin) {
      navigateToLogin(email); // 预填邮箱
    }
  }
};
```

#### 错误提示优化
```javascript
const getErrorMessage = (error, purpose) => {
  const messages = {
    login: {
      'unregistered': '该邮箱尚未注册，请先注册账号或检查邮箱是否正确',
      'rate_limit': '发送过于频繁，请稍后重试',
      'invalid_email': '邮箱格式不正确'
    },
    register: {
      'already_registered': '该邮箱已被注册，请直接登录或使用其他邮箱',
      'rate_limit': '发送过于频繁，请稍后重试',
      'invalid_email': '邮箱格式不正确'
    },
    reset_password: {
      'unregistered': '该邮箱尚未注册，无法重置密码',
      'rate_limit': '发送过于频繁，请稍后重试',
      'invalid_email': '邮箱格式不正确'
    }
  };
  
  // 根据错误类型和用途返回合适的提示
  return messages[purpose][error] || '发送验证码失败，请稍后重试';
};
```

## 测试建议

### 1. 功能测试

- [ ] 注册流程：使用未注册邮箱发送验证码（应该成功）
- [ ] 注册流程：使用已注册邮箱发送验证码（应该失败）
- [ ] 登录流程：使用已注册邮箱发送验证码（应该成功）
- [ ] 登录流程：使用未注册邮箱发送验证码（应该失败）
- [ ] 重置密码：使用已注册邮箱发送验证码（应该成功）
- [ ] 重置密码：使用未注册邮箱发送验证码（应该失败）

### 2. 用户体验测试

- [ ] 错误提示是否友好明确
- [ ] 是否有合适的流程引导
- [ ] 页面跳转是否流畅
- [ ] 邮箱预填是否正常工作

### 3. 边界情况测试

- [ ] 无效邮箱格式处理
- [ ] 网络错误处理
- [ ] 频率限制处理
- [ ] 服务器错误处理

## 兼容性说明

### 向后兼容

- 不带 `purpose` 参数的请求默认为 `login` 用途
- 原有的 `/api/email-auth/forgot-password` 接口仍然可用
- 响应格式保持不变

### 建议迁移时间

- **立即修改**：注册流程（添加 `purpose=register`）
- **可选修改**：登录流程（添加 `purpose=login` 或保持默认）
- **可选修改**：重置密码流程（可继续使用原接口）

## 安全收益

修复后的接口提供以下安全保障：

1. **防止垃圾邮件攻击**：无法向任意邮箱发送验证码
2. **防止隐私泄露**：无法通过发送验证码探测邮箱是否存在
3. **提升用户体验**：明确的错误提示和流程引导
4. **减少资源浪费**：避免向无效邮箱发送邮件

## 联系方式

如有疑问或需要技术支持，请联系后端开发团队。
