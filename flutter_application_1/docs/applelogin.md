# Apple登录前端集成指南

## 🎯 概述

本指南提供了与后端Apple登录API集成的完整说明。后端已完全支持Apple官方Sign In with Apple的所有标准字段。

## ✅ 前端设计合理性确认

经过全面分析，**前端发送的所有字段都符合Apple官方规范**：

- ✅ `identityToken` - Apple官方标准字段（必需）
- ✅ `authorizationCode` - Apple官方标准字段（可选）
- ✅ `userIdentifier` - Apple官方标准字段（可选）
- ✅ `email` - Apple官方标准字段（可选，仅首次登录提供）
- ✅ `givenName` - Apple官方标准字段（可选，仅首次登录提供）
- ✅ `familyName` - Apple官方标准字段（可选，仅首次登录提供）

这些字段都来自Apple的`ASAuthorizationAppleIDCredential`对象，是Apple官方标准。

## 🔧 后端修复完成

后端已完成以下修复：

1. **验证器更新** - 支持所有Apple官方标准字段
2. **配置检查修复** - 正确检查APPLE_PRIVATE_KEY
3. **服务逻辑增强** - 正确处理姓名和邮箱信息
4. **P8文件保护** - 确保重新部署时不会丢失关键配置

## 📡 API接口文档

### 端点信息

- **URL**: `POST /api/apple-auth/login`
- **Content-Type**: `application/json`
- **认证**: 无需认证（登录接口）

### 请求格式

#### 标准格式（推荐）

```javascript
{
  // 必需字段
  "identityToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...", // Apple身份令牌
  
  // Apple官方标准字段（可选）
  "authorizationCode": "c1234567890abcdef...", // Apple授权码
  "userIdentifier": "001234.567890abcdef...", // Apple用户标识符
  "email": "<EMAIL>", // 用户邮箱（仅首次登录时提供）
  "givenName": "John", // 用户名字（仅首次登录时提供）
  "familyName": "Doe", // 用户姓氏（仅首次登录时提供）
  
  // 应用特定字段（可选）
  "rememberMe": true, // 是否记住登录状态，默认false
  "deviceInfo": { // 设备信息（可选）
    "type": "mobile",
    "os": "iOS",
    "browser": "Safari",
    "version": "15.0"
  }
}
```

#### 兼容格式（支持嵌套对象）

```javascript
{
  "identityToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "authorizationCode": "c1234567890abcdef...",
  "user": {
    "name": {
      "firstName": "John",
      "lastName": "Doe"
    },
    "email": "<EMAIL>"
  },
  "rememberMe": true,
  "deviceInfo": {
    "type": "mobile",
    "os": "iOS"
  }
}
```

### 响应格式

#### 成功响应 (200)

```javascript
{
  "success": true,
  "message": "登录成功",
  "data": {
    "user": {
      "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
      "appleId": "001234.567890abcdef...",
      "username": "apple_1640000000000",
      "nickname": "John Doe", // 如果提供了姓名信息
      "email": "<EMAIL>", // 如果提供了邮箱信息
      "authMethods": {
        "apple": true,
        "email": true // 如果有邮箱
      },
      "loginMethod": "apple",
      "accountStatus": "active",
      "profile": { // 如果提供了姓名信息
        "firstName": "John",
        "lastName": "Doe",
        "displayName": "John Doe"
      },
      "createdAt": "2023-12-01T10:00:00.000Z",
      "lastLoginAt": "2023-12-01T10:00:00.000Z"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 86400 // 秒
    }
  }
}
```

#### 错误响应 (400/401/500)

```javascript
{
  "success": false,
  "message": "错误描述",
  "error": "ERROR_CODE"
}
```



## 🧪 测试指南

### 1. 开发环境测试

在开发环境中，可以使用任意字符串作为`identityToken`进行测试：

```javascript
const testLoginData = {
    identityToken: "test_token_123",
    authorizationCode: "test_auth_code",
    userIdentifier: "test_user_id",
    email: "<EMAIL>",
    givenName: "Test",
    familyName: "User",
    rememberMe: false
};
```

### 2. 生产环境测试

在生产环境中，必须使用真实的Apple登录获取的数据。

### 3. 验证清单

- [ ] 验证器接受包含所有字段的请求
- [ ] 验证器接受只包含必需字段的请求
- [ ] 用户信息正确保存（姓名、邮箱）
- [ ] 令牌正确生成和返回
- [ ] 重复登录正常工作

## 🚨 注意事项

1. **首次登录特殊性**：Apple只在用户首次登录时提供`email`、`givenName`、`familyName`信息
2. **令牌安全性**：`identityToken`包含敏感信息，请确保HTTPS传输
3. **错误处理**：请妥善处理网络错误和API错误响应
4. **令牌刷新**：访问令牌有过期时间，请实现令牌刷新机制

## 📞 技术支持

如果遇到问题：

1. 检查请求格式是否正确
2. 查看后端日志获取详细错误信息
3. 确认Apple开发者配置是否正确
4. 验证网络连接和HTTPS证书

现在Apple登录已完全支持所有官方标准字段，前端可以直接使用真实的Apple登录数据进行集成！

open ios/Runner.xcworkspace