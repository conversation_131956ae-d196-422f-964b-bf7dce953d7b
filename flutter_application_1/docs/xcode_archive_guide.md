# Xcode Archive 操作指南

## 🎯 当前状态

✅ **Flutter构建已完成** - `build/ios/iphoneos/Runner.app` (90.6MB)  
✅ **iOS配置已修复** - CocoaPods依赖重新安装  
✅ **发布分支已创建** - `release/v1.0.0`  
✅ **Xcode已打开** - `ios/Runner.xcworkspace`  

## 📱 Archive 步骤

### 第一步：Xcode配置检查
1. **确认项目选择**
   - 左侧项目导航器中选择 `Runner` 项目
   - 确保选择的是 `Runner` target

2. **检查签名配置**
   - 点击 `Runner` 项目
   - 选择 `Signing & Capabilities` 标签
   - 确认以下配置：
     - **Team:** 选择正确的开发者团队
     - **Bundle Identifier:** `com.arborflame.limefocus`
     - **Provisioning Profile:** 选择 `Automatic` 或正确的Distribution配置文件

3. **验证版本信息**
   - 在 `General` 标签中确认：
     - **Version:** `1.0.0`
     - **Build:** `1`

### 第二步：选择构建目标
1. **设置构建目标**
   - 在Xcode顶部工具栏中
   - 点击设备选择器（Runner旁边的下拉菜单）
   - 选择 `Any iOS Device (arm64)` 或 `Generic iOS Device`

### 第三步：执行Archive
1. **开始Archive**
   - 菜单栏选择 `Product` > `Archive`
   - 或使用快捷键 `⌘ + Shift + B`

2. **等待构建完成**
   - Archive过程大约需要2-5分钟
   - 可以在底部状态栏查看进度
   - 如果出现错误，检查错误信息并修复

### 第四步：Organizer操作
1. **Archive完成后**
   - Xcode会自动打开 `Organizer` 窗口
   - 如果没有自动打开，选择 `Window` > `Organizer`

2. **选择Archive**
   - 在 `Archives` 标签中
   - 找到刚才创建的Archive（最新的一个）
   - 确认版本号和时间正确

3. **分发应用**
   - 点击 `Distribute App` 按钮
   - 选择分发方式

## 🚀 分发选项

### 选项1：App Store Connect (推荐)
1. **选择分发方式**
   - 选择 `App Store Connect`
   - 点击 `Next`

2. **选择分发方法**
   - 选择 `Upload`
   - 点击 `Next`

3. **配置选项**
   - **Include bitcode:** 取消勾选（Flutter不支持）
   - **Upload your app's symbols:** 保持勾选
   - **Manage Version and Build Number:** 保持勾选
   - 点击 `Next`

4. **重新签名**
   - 选择 `Automatically manage signing`
   - 点击 `Next`

5. **审查和上传**
   - 检查应用信息
   - 点击 `Upload`
   - 等待上传完成

### 选项2：导出IPA文件
1. **选择导出**
   - 选择 `Export`
   - 选择 `App Store`
   - 按照向导完成导出

## ⚠️ 常见问题解决

### 问题1：签名错误
**错误信息：** "No signing certificate found"
**解决方案：**
1. 检查开发者账号状态
2. 在Xcode中登录Apple ID
3. 刷新Provisioning Profiles

### 问题2：Bundle ID冲突
**错误信息：** "Bundle identifier is already in use"
**解决方案：**
1. 确认Bundle ID正确：`com.arborflame.limefocus`
2. 检查App Store Connect中的应用配置

### 问题3：版本号问题
**错误信息：** "Version already exists"
**解决方案：**
1. 增加Build Number
2. 或更新Version Number

### 问题4：Archive失败
**错误信息：** 各种构建错误
**解决方案：**
1. 清理项目：`Product` > `Clean Build Folder`
2. 重新运行Flutter构建：
   ```bash
   flutter clean
   flutter pub get
   flutter build ios --release --no-codesign
   ```

## 📊 上传后验证

### App Store Connect检查
1. **登录App Store Connect**
   - 访问 https://appstoreconnect.apple.com
   - 选择LimeFocus应用

2. **检查构建状态**
   - 进入 `TestFlight` 标签
   - 查看 `iOS构建版本`
   - 等待处理完成（通常5-30分钟）

3. **构建处理状态**
   - **正在处理：** 苹果正在处理上传的构建
   - **可供测试：** 构建已准备好进行TestFlight测试
   - **已拒绝：** 构建被拒绝，需要查看拒绝原因

## 🎯 TestFlight配置

### 内部测试
1. **添加内部测试员**
   - 在TestFlight中点击 `内部测试`
   - 添加开发团队成员邮箱

2. **分发构建**
   - 选择最新的构建版本
   - 点击 `分发给测试员`

### 外部测试
1. **创建测试组**
   - 点击 `外部测试`
   - 创建新的测试组
   - 添加测试用户邮箱

2. **提交审核**
   - 外部测试需要苹果审核
   - 填写测试信息和说明
   - 提交审核（通常24-48小时）

## 📝 完成检查清单

- [ ] Xcode Archive成功完成
- [ ] 上传到App Store Connect成功
- [ ] 构建在App Store Connect中显示
- [ ] 构建状态为"可供测试"
- [ ] TestFlight内部测试配置完成
- [ ] 外部测试组创建（可选）

## 📞 支持信息

**如果遇到问题：**
- **技术支持：** <EMAIL>
- **Apple开发者支持：** https://developer.apple.com/support/
- **App Store Connect帮助：** https://help.apple.com/app-store-connect/

---

## 🎉 成功标志

当看到以下信息时，表示Archive和上传成功：

1. **Xcode Organizer中显示新的Archive**
2. **上传过程无错误信息**
3. **App Store Connect中显示新构建**
4. **邮件通知构建处理完成**

**恭喜！LimeFocus已成功上传到App Store Connect！** 🚀
