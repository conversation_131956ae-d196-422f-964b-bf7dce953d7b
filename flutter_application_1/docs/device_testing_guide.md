# 真机测试完整指导

## 📱 **测试环境准备**

### 1.1 设备要求
- **设备类型**：iPhone 或 iPad（必须是真机，模拟器不支持内购）
- **iOS版本**：建议 iOS 14.0 或更高版本
- **开发者账号**：设备必须添加到你的开发者账号中

### 1.2 设备配置步骤

#### 步骤1：设备设置
```bash
1. 打开设置 → iTunes Store与App Store
2. 点击当前登录的Apple ID
3. 选择"退出登录"
4. ⚠️ 重要：不要在这里登录沙盒测试账号
```

#### 步骤2：开发环境检查
```bash
# 检查设备连接
flutter devices

# 确认Bundle ID
grep -A 1 "PRODUCT_BUNDLE_IDENTIFIER" ios/Runner.xcodeproj/project.pbxproj

# 应该显示：com.arborflame.limefocus
```

#### 步骤3：构建和安装
```bash
# 清理构建缓存
flutter clean
flutter pub get

# 构建并安装到设备
flutter run --release
# 或者使用调试模式
flutter run
```

---

## 🧪 **完整测试流程**

### 2.1 基础功能测试

#### 测试1：应用启动和初始化
```
✅ 检查项：
1. 应用正常启动
2. 没有崩溃或错误
3. 订阅服务正常初始化
4. 控制台显示："Apple订阅服务初始化完成"

🔍 验证方法：
- 查看Xcode控制台日志
- 确认没有错误信息
```

#### 测试2：产品加载测试
```
✅ 检查项：
1. 进入订阅页面
2. 能看到3个订阅产品
3. 产品信息正确显示
4. 价格信息正确

🔍 验证方法：
- 导航到订阅页面
- 检查是否显示：月度¥6、季度¥12、年度¥18
- 确认产品描述和标题正确
```

### 2.2 购买流程测试

#### 测试3：购买流程
```
✅ 检查项：
1. 点击任意订阅产品的"立即订阅"按钮
2. 系统弹出Apple支付界面
3. 提示登录Apple ID
4. 使用沙盒测试账号登录
5. 完成支付流程
6. 应用显示订阅成功

🔍 详细步骤：
1. 点击"立即订阅"按钮
2. 等待Apple支付界面出现
3. 在弹出的登录框中输入沙盒测试账号
4. 确认购买（沙盒环境不会真实扣费）
5. 观察应用反馈

⚠️ 注意事项：
- 首次使用沙盒账号可能需要同意条款
- 支付过程可能需要几秒钟
- 不要在设置中预先登录沙盒账号
```

#### 测试4：订阅状态验证
```
✅ 检查项：
1. 购买成功后订阅状态更新
2. 应用显示"您是高级用户"
3. 高级功能正常解锁
4. 重启应用后状态保持

🔍 验证方法：
- 检查订阅页面状态显示
- 重启应用验证状态持久化
- 检查高级功能是否可用
```

### 2.3 恢复购买测试

#### 测试5：恢复购买功能
```
✅ 检查项：
1. 删除应用并重新安装
2. 点击"恢复购买"按钮
3. 使用相同的沙盒账号登录
4. 订阅状态正确恢复

🔍 详细步骤：
1. 长按应用图标 → 删除应用
2. 重新运行 flutter run
3. 进入订阅页面
4. 点击"恢复购买"
5. 登录相同的沙盒测试账号
6. 验证订阅状态恢复
```

---

## 🔧 **问题排查指南**

### 3.1 常见问题及解决方案

#### 问题1：产品加载失败
```
🚨 症状：订阅页面显示"加载产品失败"或产品列表为空

🔍 可能原因：
1. App Store Connect配置未生效（需要24小时）
2. Bundle ID不匹配
3. 产品ID拼写错误
4. 网络连接问题
5. 沙盒环境问题

✅ 解决方案：
1. 检查Bundle ID是否为：com.arborflame.limefocus
2. 确认产品ID正确：
   - limefocus_premium_monthly
   - limefocus_premium_quarterly  
   - limefocus_premium_yearly
3. 等待24小时让App Store Connect配置生效
4. 检查网络连接
5. 重启设备和应用
```

#### 问题2：购买按钮无反应
```
🚨 症状：点击购买按钮后没有弹出支付界面

🔍 可能原因：
1. 设备限制（家长控制等）
2. 沙盒环境问题
3. Apple ID状态问题

✅ 解决方案：
1. 检查设备的购买限制设置
2. 确保在设置中已退出Apple ID
3. 重启设备
4. 尝试不同的沙盒测试账号
```

#### 问题3：支付失败
```
🚨 症状：支付过程中出现错误或取消

🔍 可能原因：
1. 沙盒账号问题
2. 产品配置问题
3. 网络问题

✅ 解决方案：
1. 重新创建沙盒测试账号
2. 检查产品在App Store Connect中的状态
3. 确保产品已提交审核（沙盒测试不需要通过审核）
4. 检查网络连接稳定性
```

#### 问题4：订阅状态不更新
```
🚨 症状：购买成功但应用中仍显示未订阅

🔍 可能原因：
1. 状态同步延迟
2. 本地缓存问题
3. 收据验证失败

✅ 解决方案：
1. 等待几秒钟让状态同步
2. 重启应用
3. 点击"恢复购买"
4. 清除应用数据重新测试
```

### 3.2 调试工具使用

#### Xcode控制台日志
```bash
# 关键日志信息
✅ 正常日志：
- "Apple订阅服务初始化完成"
- "加载了 3 个产品"
- "开始购买: LimeFocus 高级版"
- "订阅解锁成功"

🚨 错误日志：
- "App Store不可用"
- "加载产品失败"
- "购买错误"
- "验证购买失败"
```

#### 应用内调试页面
```
位置：开发工具 → 订阅功能测试
功能：
- 查看产品加载状态
- 测试购买流程
- 检查订阅详情
- 手动状态切换（测试模式）
```

---

## 📊 **测试结果记录表**

### 4.1 基础功能测试记录
```
□ 应用启动正常
□ 订阅服务初始化成功
□ 产品信息正确加载（3个产品）
□ 价格显示正确（¥6/¥12/¥18）
□ UI界面显示正常
```

### 4.2 购买流程测试记录
```
□ 月度订阅购买测试通过
□ 季度订阅购买测试通过  
□ 年度订阅购买测试通过
□ 购买成功后状态正确更新
□ 错误处理正常工作
```

### 4.3 状态管理测试记录
```
□ 订阅状态正确显示
□ 重启应用后状态保持
□ 恢复购买功能正常
□ 跨设备状态同步（如果测试）
```

### 4.4 异常情况测试记录
```
□ 网络断开时的处理
□ 用户取消购买的处理
□ 支付失败的处理
□ 产品加载失败的处理
```

---

## 🎯 **测试成功标准**

### 必须通过的测试
1. ✅ **产品加载**：能正确显示3个订阅产品
2. ✅ **购买流程**：至少一个产品能成功购买
3. ✅ **状态更新**：购买后订阅状态正确显示
4. ✅ **状态持久化**：重启应用后状态保持
5. ✅ **恢复购买**：删除重装后能恢复状态

### 推荐通过的测试
1. ✅ **全产品测试**：所有3个产品都能正常购买
2. ✅ **错误处理**：各种异常情况处理正常
3. ✅ **用户体验**：界面友好，反馈及时
4. ✅ **性能表现**：加载快速，操作流畅

---

## 📞 **测试支持**

### 遇到问题时的处理步骤
1. **记录详细信息**：截图、错误信息、操作步骤
2. **查看控制台日志**：Xcode中的详细日志
3. **尝试基础解决方案**：重启设备、重新安装应用
4. **检查配置**：Bundle ID、产品ID、沙盒账号
5. **参考问题排查指南**：按照上述指南逐步排查

### 测试完成后的下一步
1. **记录测试结果**：填写测试记录表
2. **整理问题清单**：记录发现的问题和解决方案
3. **准备生产环境**：根据测试结果优化代码
4. **文档更新**：更新用户使用指南

测试过程中如有任何问题，请参考问题排查指南或查看详细的技术文档。
