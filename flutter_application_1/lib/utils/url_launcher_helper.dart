import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

/// URL启动工具类
/// 用于启动外部链接，如法律文档、联系方式等
class UrlLauncherHelper {
  // 法律文档链接
  static const String termsUrl = 'https://arborflame.com/terms';
  static const String privacyUrl = 'https://arborflame.com/privacy';
  static const String websiteUrl = 'https://arborflame.com/';
  
  // 联系方式
  static const String contactEmail = '<EMAIL>';
  
  /// 启动使用条款页面
  static Future<void> launchTerms() async {
    await _launchUrl(termsUrl);
  }
  
  /// 启动隐私政策页面
  static Future<void> launchPrivacy() async {
    await _launchUrl(privacyUrl);
  }
  
  /// 启动官方网站
  static Future<void> launchWebsite() async {
    await _launchUrl(websiteUrl);
  }
  
  /// 启动邮件客户端
  static Future<void> launchEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: contactEmail,
      query: 'subject=LimeFocus反馈',
    );
    await _launchUrl(emailUri.toString());
  }
  
  /// 通用URL启动方法
  static Future<void> _launchUrl(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      
      // 检查是否可以启动URL
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication, // 在外部浏览器中打开
        );
      } else {
        throw 'Could not launch $url';
      }
    } catch (e) {
      // 记录错误
      debugPrint('Error launching URL: $url, Error: $e');
      
      // 可以在这里添加用户友好的错误提示
      // 例如显示SnackBar或对话框
      rethrow;
    }
  }
  
  /// 带错误处理的URL启动方法（用于UI组件）
  static Future<void> launchUrlWithErrorHandling(
    BuildContext context,
    String url, {
    String? errorMessage,
  }) async {
    try {
      await _launchUrl(url);
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              errorMessage ?? '无法打开链接，请检查网络连接',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  /// 启动使用条款（带错误处理）
  static Future<void> launchTermsWithErrorHandling(BuildContext context) async {
    await launchUrlWithErrorHandling(
      context,
      termsUrl,
      errorMessage: '无法打开使用条款页面',
    );
  }
  
  /// 启动隐私政策（带错误处理）
  static Future<void> launchPrivacyWithErrorHandling(BuildContext context) async {
    await launchUrlWithErrorHandling(
      context,
      privacyUrl,
      errorMessage: '无法打开隐私政策页面',
    );
  }
  
  /// 启动邮件（带错误处理）
  static Future<void> launchEmailWithErrorHandling(BuildContext context) async {
    await launchUrlWithErrorHandling(
      context,
      'mailto:$contactEmail?subject=LimeFocus反馈',
      errorMessage: '无法打开邮件客户端',
    );
  }
}
