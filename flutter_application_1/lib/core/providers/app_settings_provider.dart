import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/app_settings_service.dart';
import 'dart:developer' as developer;

/// 应用设置服务提供者
final appSettingsServiceProvider = Provider<AppSettingsService>((ref) {
  return AppSettingsService();
});

/// 首次启动状态提供者
final isFirstLaunchProvider = FutureProvider<bool>((ref) async {
  final appSettingsService = ref.watch(appSettingsServiceProvider);
  return await appSettingsService.isFirstLaunch();
});

/// 引导页完成状态提供者
final isOnboardingCompletedProvider = FutureProvider<bool>((ref) async {
  final appSettingsService = ref.watch(appSettingsServiceProvider);
  return await appSettingsService.isOnboardingCompleted();
});

/// 应用初始化路由提供者
/// 根据需求，不再显示引导页，直接进入主页
final initialRouteProvider = FutureProvider<String>((ref) async {
  final appSettingsService = ref.watch(appSettingsServiceProvider);

  // 检查是否是首次启动
  final isFirstLaunch = await appSettingsService.isFirstLaunch();
  developer.log('是否首次启动: $isFirstLaunch', name: 'AppSettingsProvider');

  if (isFirstLaunch) {
    // 标记应用已启动
    await appSettingsService.markAppLaunched();
    // 自动标记引导页已完成
    await appSettingsService.markOnboardingCompleted();
    developer.log('首次启动，但根据需求直接导航到主页', name: 'AppSettingsProvider');
  }

  // 正常启动，显示主页
  developer.log('正常启动，导航到主页', name: 'AppSettingsProvider');
  return '/';
});
