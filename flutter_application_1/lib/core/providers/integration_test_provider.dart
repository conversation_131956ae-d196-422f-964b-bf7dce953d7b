import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user.dart';
// 云同步服务已在MVP版本中移除
import '../services/subscription_service.dart';
import '../utils/integration_test_helper.dart';
import 'auth_provider.dart';
// 备份功能已在MVP版本中移除

// 集成测试辅助类提供者
final integrationTestHelperProvider = Provider<IntegrationTestHelper>((ref) {
  final authService = ref.watch(authServiceProvider);
  // 备份和云同步服务已在MVP版本中移除
  final subscriptionService = SubscriptionService();

  return IntegrationTestHelper(
    authService: authService,
    subscriptionService: subscriptionService,
  );
});

// 测试状态
enum TestStatus { idle, running, success, failure }

// 测试状态类
class TestState {
  final TestStatus status;
  final String? message;
  final Map<String, dynamic>? data;

  TestState({
    this.status = TestStatus.idle,
    this.message,
    this.data,
  });

  TestState copyWith({
    TestStatus? status,
    String? message,
    Map<String, dynamic>? data,
  }) {
    return TestState(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

// 测试状态提供者
final testStateProvider = StateNotifierProvider<TestStateNotifier, TestState>((ref) {
  return TestStateNotifier(ref.watch(integrationTestHelperProvider));
});

// 测试状态通知器
class TestStateNotifier extends StateNotifier<TestState> {
  final IntegrationTestHelper _testHelper;

  TestStateNotifier(this._testHelper) : super(TestState());

  // 运行所有测试
  Future<void> runAllTests() async {
    state = state.copyWith(status: TestStatus.running, message: '正在初始化测试环境...');

    try {
      // 初始化测试环境
      await _testHelper.initTestEnvironment();

      // 检查是否需要登录
      final isLoggedIn = await _testHelper.authService.isLoggedIn();
      User? user;

      if (!isLoggedIn) {
        // 尝试登录测试账号
        state = state.copyWith(message: '正在登录测试账号...');
        user = await _testHelper.loginTestAccount();

        if (user == null) {
          state = state.copyWith(
            message: '登录测试账号失败，将以未登录状态继续测试',
          );
        } else {
          state = state.copyWith(
            message: '登录测试账号成功',
          );
        }
      } else {
        // 已经登录，获取当前用户
        state = state.copyWith(message: '已经登录，获取当前用户...');
        user = await _testHelper.authService.getCurrentUser();

        if (user == null) {
          state = state.copyWith(
            message: '获取当前用户失败，将以未登录状态继续测试',
          );
        } else {
          state = state.copyWith(
            message: '获取当前用户成功',
          );
        }
      }

      // 创建测试数据
      state = state.copyWith(message: '正在创建测试数据...');
      try {
        await _testHelper.createTestData();
      } catch (e) {
        state = state.copyWith(message: '创建测试数据部分失败，继续测试: $e');
      }

      // 创建测试云备份
      state = state.copyWith(message: '正在创建测试云备份...');
      Map<String, dynamic>? backup;
      try {
        backup = await _testHelper.createTestCloudBackup();
        if (backup == null) {
          state = state.copyWith(message: '创建测试云备份失败，继续测试');
        }
      } catch (e) {
        state = state.copyWith(message: '创建测试云备份失败，继续测试: $e');
      }

      // 测试订阅功能
      state = state.copyWith(message: '正在测试订阅功能...');
      bool subscriptionResult = false;
      try {
        subscriptionResult = await _testHelper.testSubscription();
        if (!subscriptionResult) {
          state = state.copyWith(message: '测试订阅功能失败，继续测试');
        }
      } catch (e) {
        state = state.copyWith(message: '测试订阅功能失败，继续测试: $e');
      }

      // 清理测试环境
      state = state.copyWith(message: '正在清理测试环境...');
      try {
        await _testHelper.cleanupTestEnvironment();
      } catch (e) {
        state = state.copyWith(message: '清理测试环境失败: $e');
      }

      // 测试完成
      state = state.copyWith(
        status: TestStatus.success,
        message: '所有测试完成',
        data: {
          'user': user?.toJson(),
          'backup': backup,
          'subscription': subscriptionResult,
        },
      );
    } catch (e) {
      state = state.copyWith(
        status: TestStatus.failure,
        message: '测试失败: $e',
      );
    }
  }

  // 重置测试状态
  void reset() {
    state = TestState();
  }
}
