import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../repositories/subject_repository.dart';
import '../repositories/focus_record_repository.dart';
import '../repositories/schedule_repository.dart';
import 'hive_service_provider.dart';

/// 科目仓库提供者
final subjectRepositoryProvider = Provider<SubjectRepository>((ref) {
  final hiveService = ref.watch(hiveServiceProvider);
  return hiveService.subjectRepository;
});

/// 专注记录仓库提供者
final focusRecordRepositoryProvider = Provider<FocusRecordRepository>((ref) {
  final hiveService = ref.watch(hiveServiceProvider);
  return hiveService.focusRecordRepository;
});

/// 日程仓库提供者
final scheduleRepositoryProvider = Provider<ScheduleRepository>((ref) {
  final hiveService = ref.watch(hiveServiceProvider);
  return hiveService.scheduleRepository;
});
