import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:limefocus/core/services/enhanced_hive_service.dart';

/// 提供EnhancedHiveService的Provider
final hiveServiceProvider = Provider<EnhancedHiveService>((ref) {
  return EnhancedHiveService();
});

/// Hive初始化状态Provider
/// 在后台静默初始化，不阻塞UI显示
final hiveInitializationProvider = FutureProvider<bool>((ref) async {
  final hiveService = ref.watch(hiveServiceProvider);

  if (!hiveService.isInitialized) {
    try {
      // 在后台初始化Hive
      await hiveService.initHive();
      return true;
    } catch (e) {
      // 初始化失败时记录错误，但不阻塞UI
      debugPrint('Hive初始化失败: $e');
      return false;
    }
  }

  return hiveService.isInitialized;
});

/// Hive数据验证Provider
final hiveDataValidityProvider = FutureProvider<bool>((ref) async {
  final hiveService = ref.watch(hiveServiceProvider);

  if (!hiveService.isInitialized) {
    await hiveService.initHive();
  }

  return await hiveService.validateDataIntegrity();
});
