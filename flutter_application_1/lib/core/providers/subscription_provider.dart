import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/subscription_service.dart';
import '../services/apple_subscription_service.dart';

// 订阅服务提供者
final subscriptionServiceProvider = Provider<SubscriptionService>((ref) {
  return SubscriptionService();
});

// Apple订阅服务提供者
final appleSubscriptionServiceProvider = Provider<AppleSubscriptionService>((ref) {
  final service = AppleSubscriptionService();

  // 当提供者被销毁时释放资源
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

// Apple订阅状态提供者
final appleSubscriptionStatusProvider = FutureProvider<bool>((ref) async {
  final service = ref.watch(appleSubscriptionServiceProvider);
  return await service.isPremiumUser();
});

// Apple订阅产品提供者
final appleSubscriptionProductsProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  final service = ref.watch(appleSubscriptionServiceProvider);
  return await service.getSubscriptionProducts();
});

// 价格计划提供者
final subscriptionPlansProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  final subscriptionService = ref.watch(subscriptionServiceProvider);
  return await subscriptionService.getSubscriptionPlans();
});

// 当前订阅提供者
final currentSubscriptionProvider = FutureProvider<Map<String, dynamic>?>((ref) async {
  final subscriptionService = ref.watch(subscriptionServiceProvider);
  return await subscriptionService.getCurrentSubscription();
});

// 订阅历史提供者
final subscriptionHistoryProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  final subscriptionService = ref.watch(subscriptionServiceProvider);
  return await subscriptionService.getSubscriptionHistory();
});

// 支付记录提供者
final paymentHistoryProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  final subscriptionService = ref.watch(subscriptionServiceProvider);
  return await subscriptionService.getPaymentHistory();
});

// 付费用户状态提供者
final isPremiumUserProvider = FutureProvider<bool>((ref) async {
  final subscriptionService = ref.watch(subscriptionServiceProvider);
  return await subscriptionService.isPremiumUser();
});

// 订阅操作状态
enum SubscriptionOperationStatus { idle, loading, success, error }

// 订阅操作状态
class SubscriptionOperationState {
  final SubscriptionOperationStatus status;
  final String? message;
  final Map<String, dynamic>? data;

  SubscriptionOperationState({
    this.status = SubscriptionOperationStatus.idle,
    this.message,
    this.data,
  });

  SubscriptionOperationState copyWith({
    SubscriptionOperationStatus? status,
    String? message,
    Map<String, dynamic>? data,
  }) {
    return SubscriptionOperationState(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

// 订阅操作提供者
final subscriptionOperationProvider = StateNotifierProvider<SubscriptionOperationNotifier, SubscriptionOperationState>((ref) {
  return SubscriptionOperationNotifier(ref.watch(subscriptionServiceProvider));
});

class SubscriptionOperationNotifier extends StateNotifier<SubscriptionOperationState> {
  final SubscriptionService _subscriptionService;

  SubscriptionOperationNotifier(this._subscriptionService) : super(SubscriptionOperationState());

  // 创建订阅
  Future<void> createSubscription(String planId) async {
    state = state.copyWith(status: SubscriptionOperationStatus.loading, message: '正在创建订阅...');

    try {
      final subscription = await _subscriptionService.createSubscription(planId);
      if (subscription != null) {
        state = state.copyWith(
          status: SubscriptionOperationStatus.success,
          message: '订阅创建成功',
          data: subscription,
        );
      } else {
        state = state.copyWith(
          status: SubscriptionOperationStatus.error,
          message: '订阅创建失败',
        );
      }
    } catch (e) {
      state = state.copyWith(
        status: SubscriptionOperationStatus.error,
        message: '订阅创建失败: $e',
      );
    }
  }

  // 处理苹果支付
  Future<void> processApplePayment(String receipt) async {
    state = state.copyWith(status: SubscriptionOperationStatus.loading, message: '正在处理支付...');

    try {
      final success = await _subscriptionService.processApplePayment(receipt);
      if (success) {
        state = state.copyWith(
          status: SubscriptionOperationStatus.success,
          message: '支付处理成功',
        );
      } else {
        state = state.copyWith(
          status: SubscriptionOperationStatus.error,
          message: '支付处理失败',
        );
      }
    } catch (e) {
      state = state.copyWith(
        status: SubscriptionOperationStatus.error,
        message: '支付处理失败: $e',
      );
    }
  }

  // 取消订阅
  Future<void> cancelSubscription(String subscriptionId, {String? reason}) async {
    state = state.copyWith(status: SubscriptionOperationStatus.loading, message: '正在取消订阅...');

    try {
      final success = await _subscriptionService.cancelSubscription(subscriptionId, reason: reason);
      if (success) {
        state = state.copyWith(
          status: SubscriptionOperationStatus.success,
          message: '订阅已取消',
        );
      } else {
        state = state.copyWith(
          status: SubscriptionOperationStatus.error,
          message: '取消订阅失败',
        );
      }
    } catch (e) {
      state = state.copyWith(
        status: SubscriptionOperationStatus.error,
        message: '取消订阅失败: $e',
      );
    }
  }

  // 重置状态
  void reset() {
    state = SubscriptionOperationState();
  }
}
