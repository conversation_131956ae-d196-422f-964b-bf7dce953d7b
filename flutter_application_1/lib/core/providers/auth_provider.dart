import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user.dart';
import '../services/auth_service.dart';
import '../utils/event_bus.dart';

// 认证服务提供者
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

// 认证状态
enum AuthStatus { initial, authenticated, unauthenticated }

// 认证状态提供者
final authStatusProvider = StateNotifierProvider<AuthStatusNotifier, AuthStatus>((ref) {
  return AuthStatusNotifier(ref.watch(authServiceProvider));
});

class AuthStatusNotifier extends StateNotifier<AuthStatus> {
  final AuthService _authService;

  AuthStatusNotifier(this._authService) : super(AuthStatus.initial) {
    _checkAuthStatus();
    _setupEventListeners();
  }

  // 设置事件监听器
  void _setupEventListeners() {
    // 监听登录成功事件
    eventBus.stream.listen((event) {
      if (event == EventType.authLogin) {
        state = AuthStatus.authenticated;
      } else if (event == EventType.authLogout) {
        state = AuthStatus.unauthenticated;
      }
    });
  }

  // 检查认证状态
  Future<void> _checkAuthStatus() async {
    final isLoggedIn = await _authService.isLoggedIn();
    state = isLoggedIn ? AuthStatus.authenticated : AuthStatus.unauthenticated;
  }

  // 发送验证码
  Future<bool> sendVerificationCode(String email, {String type = 'login'}) async {
    final result = await _authService.sendVerificationCode(email, type: type);

    // 如果发送失败，抛出包含错误信息的异常，让UI层能够显示具体错误
    if (!(result['success'] ?? false)) {
      final errorMessage = result['message'] ?? '发送验证码失败';
      throw Exception(errorMessage);
    }

    return result['success'] ?? false;
  }

  // 验证验证码
  Future<bool> verifyCode(String email, String code) async {
    return await _authService.verifyCode(email, code);
  }

  // 密码登录
  Future<bool> loginWithPassword({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      await _authService.loginWithPassword(
        email: email,
        password: password,
        rememberMe: rememberMe,
      );

      // 检查是否真的登录成功（通过检查令牌）
      final isLoggedIn = await _authService.isLoggedIn();
      if (isLoggedIn) {
        state = AuthStatus.authenticated;
        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  // 验证码登录
  Future<bool> loginWithVerificationCode({
    required String email,
    required String verificationCode,
    bool rememberMe = false,
  }) async {
    final user = await _authService.loginWithVerificationCode(
      email: email,
      verificationCode: verificationCode,
      rememberMe: rememberMe,
    );
    if (user != null) {
      state = AuthStatus.authenticated;
      return true;
    }
    return false;
  }

  // 苹果登录
  Future<bool> loginWithApple({bool rememberMe = true}) async {
    try {
      debugPrint('🍎 AuthProvider: 开始Apple登录流程');

      final user = await _authService.loginWithApple(rememberMe: rememberMe);

      if (user != null) {
        debugPrint('🍎 AuthProvider: Apple登录成功，用户: ${user.email.isNotEmpty ? user.email : user.username}');
        state = AuthStatus.authenticated;
        return true;
      } else {
        debugPrint('🍎 AuthProvider: Apple登录失败，AuthService返回null');
        return false;
      }
    } catch (e, stackTrace) {
      debugPrint('💥 AuthProvider: Apple登录异常');
      debugPrint('  - 错误类型: ${e.runtimeType}');
      debugPrint('  - 错误信息: $e');
      debugPrint('  - 堆栈跟踪: $stackTrace');

      // 重新抛出异常，让UI层能够捕获并显示给用户
      rethrow;
    }
  }

  // 兼容旧版登录方法
  Future<bool> login(String credential, String password, {bool rememberMe = false}) async {
    final user = await _authService.login(credential, password, rememberMe: rememberMe);
    if (user != null) {
      state = AuthStatus.authenticated;
      return true;
    }
    return false;
  }

  // 用户注册
  Future<bool> register({
    required String email,
    required String verificationCode,
    required String password,
    String? nickname,
    String? username,
    bool agreeToTerms = true,
  }) async {
    try {
      final success = await _authService.register(
        email: email,
        verificationCode: verificationCode,
        password: password,
        nickname: nickname,
        username: username,
        agreeToTerms: agreeToTerms,
      );

      // 检查是否真的注册并登录成功（通过检查令牌）
      final isLoggedIn = await _authService.isLoggedIn();
      if (success && isLoggedIn) {
        state = AuthStatus.authenticated;
        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  // 用户登出
  Future<void> logout() async {
    await _authService.logout();
    state = AuthStatus.unauthenticated;
  }

  // 注销账号
  Future<bool> deleteAccount({
    required String password,
    required String confirmText,
  }) async {
    final success = await _authService.deleteAccount(
      password: password,
      confirmText: confirmText,
    );
    if (success) {
      state = AuthStatus.unauthenticated;
    }
    return success;
  }
}

// 当前用户提供者
final currentUserProvider = FutureProvider.autoDispose<User?>((ref) async {
  final authService = ref.watch(authServiceProvider);
  final authStatus = ref.watch(authStatusProvider);

  if (authStatus == AuthStatus.authenticated) {
    try {
      final user = await authService.getCurrentUser();
      if (user != null) {
        return user;
      }

      // 如果获取用户信息失败，但状态是已认证，可能是缓存问题
      // 尝试重新检查认证状态
      final isLoggedIn = await authService.isLoggedIn();
      if (!isLoggedIn) {
        // 如果实际未登录，触发状态检查
        ref.read(authStatusProvider.notifier)._checkAuthStatus();
      }

      return user;
    } catch (e) {
      // 获取用户信息失败，可能需要重新登录
      return null;
    }
  }

  return null;
});
