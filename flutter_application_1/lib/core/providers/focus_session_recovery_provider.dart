import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/focus_session.dart';
import '../services/focus_session_service.dart';

/// 专注会话恢复状态
enum FocusRecoveryState {
  checking,    // 正在检查
  none,        // 没有需要恢复的会话
  found,       // 发现需要恢复的会话
  recovered,   // 已恢复
  error,       // 检查出错
}

/// 专注会话恢复数据
class FocusRecoveryData {
  final FocusRecoveryState state;
  final FocusSession? session;
  final String? error;

  const FocusRecoveryData({
    required this.state,
    this.session,
    this.error,
  });

  FocusRecoveryData copyWith({
    FocusRecoveryState? state,
    FocusSession? session,
    String? error,
  }) {
    return FocusRecoveryData(
      state: state ?? this.state,
      session: session ?? this.session,
      error: error ?? this.error,
    );
  }
}

/// 专注会话恢复管理器
class FocusSessionRecoveryNotifier extends StateNotifier<FocusRecoveryData> {
  final FocusSessionService _focusSessionService;

  FocusSessionRecoveryNotifier(this._focusSessionService)
      : super(const FocusRecoveryData(state: FocusRecoveryState.checking)) {
    _checkForRecovery();
  }

  /// 检查是否有需要恢复的专注会话
  Future<void> _checkForRecovery() async {
    try {
      debugPrint('阶段4：检查是否有需要恢复的专注会话');
      
      // 获取当前专注会话
      final session = await _focusSessionService.getCurrentFocusSession();
      
      if (session == null) {
        debugPrint('阶段4：没有发现专注会话');
        state = state.copyWith(state: FocusRecoveryState.none);
        return;
      }

      // 验证会话有效性
      if (!session.isValid) {
        debugPrint('阶段4：发现无效专注会话，自动清理: ${session.id}');
        await _focusSessionService.clearFocusSession();
        state = state.copyWith(state: FocusRecoveryState.none);
        return;
      }

      // 检查会话是否已完成（倒计时模式）
      if (session.isCountdown && session.isCountdownCompleted) {
        debugPrint('阶段4：发现已完成的倒计时会话，自动清理: ${session.id}');
        await _focusSessionService.clearFocusSession();
        state = state.copyWith(state: FocusRecoveryState.none);
        return;
      }

      debugPrint('阶段4：发现有效的专注会话，准备恢复: ${session.id}');
      debugPrint('  - 科目: ${session.subjectId}');
      debugPrint('  - 项目: ${session.projectId}');
      debugPrint('  - 模式: ${session.isCountdown ? "倒计时" : "正计时"}');
      debugPrint('  - 已过时间: ${session.currentElapsedSeconds}秒');
      debugPrint('  - 是否暂停: ${session.isPaused}');

      state = state.copyWith(
        state: FocusRecoveryState.found,
        session: session,
      );
    } catch (e) {
      debugPrint('阶段4：检查专注会话恢复时出错: $e');
      state = state.copyWith(
        state: FocusRecoveryState.error,
        error: e.toString(),
      );
    }
  }

  /// 标记会话已恢复
  void markAsRecovered() {
    debugPrint('阶段4：专注会话已成功恢复');
    state = state.copyWith(state: FocusRecoveryState.recovered);
  }

  /// 重置状态
  void reset() {
    state = const FocusRecoveryData(state: FocusRecoveryState.checking);
    _checkForRecovery();
  }

  /// 手动清理会话
  Future<void> clearSession() async {
    try {
      await _focusSessionService.clearFocusSession();
      state = state.copyWith(state: FocusRecoveryState.none);
      debugPrint('阶段4：手动清理专注会话完成');
    } catch (e) {
      debugPrint('阶段4：清理专注会话失败: $e');
    }
  }
}

/// 专注会话恢复Provider
final focusSessionRecoveryProvider = StateNotifierProvider<FocusSessionRecoveryNotifier, FocusRecoveryData>((ref) {
  final focusSessionService = FocusSessionService();
  return FocusSessionRecoveryNotifier(focusSessionService);
});

/// 便捷的状态检查Provider
final hasFocusSessionToRecoverProvider = Provider<bool>((ref) {
  final recoveryData = ref.watch(focusSessionRecoveryProvider);
  return recoveryData.state == FocusRecoveryState.found;
});

/// 获取需要恢复的会话Provider
final sessionToRecoverProvider = Provider<FocusSession?>((ref) {
  final recoveryData = ref.watch(focusSessionRecoveryProvider);
  return recoveryData.session;
});
