import 'package:flutter/foundation.dart';

/// 发布配置管理
/// 控制不同环境下功能的显示和隐藏
class ReleaseConfig {
  // 构建模式
  static bool get isDebugMode => kDebugMode;
  static bool get isReleaseMode => kReleaseMode;
  static bool get isProfileMode => kProfileMode;

  // 功能开关
  static bool get showDeveloperTools => isDebugMode;
  static bool get showTestFeatures => isDebugMode;
  static bool get showUnfinishedFeatures => isDebugMode;
  
  // 页面功能开关
  static bool get showMoreFeaturesInHome => isDebugMode;
  static bool get showDeveloperMenu => isDebugMode;
  static bool get showTestDataGenerator => isDebugMode;
  static bool get showNetworkDiagnostics => isDebugMode;
  static bool get showSubscriptionTest => isDebugMode;
  
  // 导航功能开关
  static bool get enableDeveloperRoutes => isDebugMode;
  static bool get enableTestRoutes => isDebugMode;
  
  // 日志和调试
  static bool get enableDetailedLogging => isDebugMode;
  static bool get enablePerformanceMonitoring => isDebugMode;
  
  // 数据和存储
  static bool get enableTestData => isDebugMode;
  static bool get enableDataExport => isDebugMode;
  
  // UI和体验
  static bool get showVersionInfo => true; // 总是显示版本信息
  static bool get enableBetaFeatures => isDebugMode;
  
  // Apple订阅相关
  static bool get enableAppleSubscription => true; // 总是启用
  static bool get enableSubscriptionTest => isDebugMode;
  static bool get enableTestPurchases => isDebugMode;
  
  // 获取当前构建模式描述
  static String get buildModeDescription {
    if (isDebugMode) return 'Debug';
    if (isReleaseMode) return 'Release';
    if (isProfileMode) return 'Profile';
    return 'Unknown';
  }
  
  // 获取功能可用性描述
  static Map<String, bool> get featureFlags => {
    'Developer Tools': showDeveloperTools,
    'Test Features': showTestFeatures,
    'Unfinished Features': showUnfinishedFeatures,
    'More Features in Home': showMoreFeaturesInHome,
    'Developer Menu': showDeveloperMenu,
    'Test Data Generator': showTestDataGenerator,
    'Network Diagnostics': showNetworkDiagnostics,
    'Subscription Test': showSubscriptionTest,
    'Apple Subscription': enableAppleSubscription,
    'Detailed Logging': enableDetailedLogging,
    'Performance Monitoring': enablePerformanceMonitoring,
  };
  
  // 检查功能是否可用
  static bool isFeatureEnabled(String featureName) {
    return featureFlags[featureName] ?? false;
  }
  
  // 开发者信息
  static Map<String, dynamic> get developerInfo => {
    'Build Mode': buildModeDescription,
    'Debug Mode': isDebugMode,
    'Release Mode': isReleaseMode,
    'Profile Mode': isProfileMode,
    'Features Enabled': featureFlags.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList(),
  };
}
