import 'package:flutter/foundation.dart';
import 'dart:io';

/// Apple沙盒测试配置
/// 用于管理沙盒环境下的测试设置
class SandboxConfig {
  /// 是否启用沙盒模式
  /// 在Debug模式下默认启用，Release模式下禁用
  static bool get isSandboxMode {
    if (kReleaseMode) {
      // Release模式下，根据构建配置决定
      return const bool.fromEnvironment('SANDBOX_MODE', defaultValue: false);
    }
    // Debug模式下默认启用沙盒
    return true;
  }

  /// 是否是真机环境
  static bool get isRealDevice {
    if (!Platform.isIOS) return false;
    
    // 在iOS上，检查是否是真机
    // 模拟器的identifierForVendor通常为null或特定值
    return true; // 简化处理，实际可以通过device_info_plus检测
  }

  /// 沙盒测试账号信息
  static Map<String, String> get sandboxTestAccount => {
    'email': '<EMAIL>',
    'description': 'LimeFocus沙盒测试账号',
    'region': 'CN', // 中国区
  };

  /// 产品ID配置（沙盒和生产环境相同）
  static List<String> get productIds => [
    'LemiVip001',        // 月度会员 ¥6
    'LimeVip_quarter',   // 季度会员 ¥12  
    'LimeVip_yearly',    // 年度会员 ¥28
    'LimeVip_AYear',     // 一年备考包 ¥18
  ];

  /// 沙盒环境配置
  static Map<String, dynamic> get sandboxSettings => {
    'enableDetailedLogging': true,
    'enablePurchaseValidation': true,
    'enableReceiptValidation': false, // 沙盒环境下可能不稳定
    'autoFinishTransactions': true,
    'showSandboxIndicator': kDebugMode,
  };

  /// 获取当前环境描述
  static String get environmentDescription {
    if (kReleaseMode) {
      return isSandboxMode ? 'Release (Sandbox)' : 'Production';
    }
    return 'Debug (Sandbox)';
  }

  /// 检查沙盒环境是否正确配置
  static Map<String, bool> checkSandboxConfiguration() {
    return {
      'iOS平台': Platform.isIOS,
      '沙盒模式': isSandboxMode,
      '调试模式': kDebugMode,
      '真机环境': isRealDevice,
      'entitlements配置': true, // 需要手动验证
      'Bundle ID匹配': true,    // 需要手动验证
    };
  }

  /// 沙盒测试注意事项
  static List<String> get sandboxTestingNotes => [
    '1. 必须使用专门的沙盒测试账号',
    '2. 不能使用真实的Apple ID进行测试',
    '3. 沙盒环境下购买不会产生真实费用',
    '4. 测试账号需要在App Store Connect中创建',
    '5. 设备需要退出真实Apple ID，使用测试账号',
    '6. 首次购买时会提示登录沙盒账号',
    '7. 沙盒环境下可以重复购买同一产品',
    '8. 订阅在沙盒环境下会加速过期（用于测试）',
  ];

  /// 审核准备检查清单
  static Map<String, String> get reviewPreparationChecklist => {
    '沙盒测试账号': '已创建并可正常使用',
    '产品配置': '所有产品在App Store Connect中已配置',
    '价格设置': '中国区价格已正确设置',
    '订阅组': '订阅产品已正确分组',
    '本地化': '产品名称和描述已本地化',
    '测试设备': '使用真机进行完整测试',
    '购买流程': '完整的购买和恢复流程已测试',
    '错误处理': '各种错误情况已测试',
  };

  /// 获取沙盒测试指导
  static String get sandboxTestingGuide => '''
🧪 Apple沙盒测试指导

📋 准备工作：
1. 在App Store Connect中创建沙盒测试账号
2. 确保所有产品已在App Store Connect中配置
3. 在测试设备上退出真实Apple ID

🔧 测试步骤：
1. 在真机上安装应用（不能在模拟器测试）
2. 进入订阅界面，选择产品
3. 点击购买，系统会提示登录沙盒账号
4. 使用创建的沙盒测试账号登录
5. 完成购买流程
6. 测试恢复购买功能

⚠️ 注意事项：
- 沙盒购买不产生真实费用
- 可以重复购买同一产品
- 订阅会加速过期（便于测试）
- 必须使用专门的测试账号

🎯 审核准备：
- 提供可用的沙盒测试账号
- 确保购买流程完整可用
- 测试各种错误情况
''';

  /// 日志前缀
  static String get logPrefix => isSandboxMode ? '[SANDBOX]' : '[PROD]';

  /// 调试日志
  static void log(String message) {
    if (kDebugMode || isSandboxMode) {
      debugPrint('$logPrefix $message');
    }
  }
}
