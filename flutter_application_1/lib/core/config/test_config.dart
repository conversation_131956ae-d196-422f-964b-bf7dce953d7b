import 'package:flutter/foundation.dart';

/// 测试配置
/// 用于存储测试环境的配置
class TestConfig {
  /// 是否处于测试模式
  static bool _testMode = const bool.fromEnvironment('TESTING', defaultValue: false);

  /// 获取是否处于测试模式
  static bool get isTestMode => _testMode;

  /// 设置测试模式
  static void setTestMode(bool value) {
    _testMode = value;
    log('测试模式已${value ? '开启' : '关闭'}');
  }

  /// 测试服务器地址
  static String get testServerUrl => 'http://localhost:3000/api';

  /// 测试账号
  static Map<String, String> get testAccount => {
    'username': 'testuser',
    'email': '<EMAIL>',
    'password': 'Password123!',
  };

  /// 测试令牌
  static String get testToken => 'test-token';

  /// 测试刷新令牌
  static String get testRefreshToken => 'test-refresh-token';

  /// 打印测试日志
  static void log(String message) {
    if (isTestMode || kDebugMode) {
      debugPrint('[TEST] $message');
    }
  }
}
