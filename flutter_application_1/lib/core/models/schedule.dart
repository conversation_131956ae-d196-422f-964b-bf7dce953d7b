import 'package:hive/hive.dart';

part 'schedule.g.dart';

@HiveType(typeId: 17)
/// 日程类型枚举
enum ScheduleType {
  @HiveField(0)
  plan,     // 计划（与日期绑定的一次性任务）

  @HiveField(1)
  routine,  // 打卡（循环任务）

  @HiveField(2)
  todo,     // 待办（一次性的与时间无关的事项）
}

@HiveType(typeId: 18)
/// 重复类型枚举
enum RepeatType {
  @HiveField(0)
  daily,    // 每天

  @HiveField(1)
  weekly,   // 每周

  @HiveField(2)
  monthly,  // 每月

  @HiveField(3)
  custom,   // 自定义
}

@HiveType(typeId: 19)
/// 日程状态枚举
enum ScheduleStatus {
  @HiveField(0)
  pending,  // 待完成

  @HiveField(1)
  completed, // 已完成

  @HiveField(2)
  missed,   // 已错过
}

@HiveType(typeId: 20)
/// 日程模型
class Schedule {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String title;

  @HiveField(2)
  final String? description;

  @HiveField(3)
  final ScheduleType type;

  @HiveField(4)
  final DateTime createdAt;

  @HiveField(5)
  final DateTime? date;  // 计划日期（对于计划类型必填）

  @HiveField(6)
  final DateTime? time;  // 具体时间（可选）

  @HiveField(12)
  final DateTime? endDate;  // 结束日期（对于打卡类型，表示打卡结束的日期）

  @HiveField(7)
  final bool isRepeat;  // 是否重复（对于打卡类型）

  @HiveField(8)
  final RepeatType? repeatType;  // 重复类型

  @HiveField(9)
  final List<int>? repeatDays;  // 重复的具体日期（例如每周一、三、五）

  @HiveField(10)
  final ScheduleStatus status;  // 状态

  @HiveField(11)
  final List<DateTime>? completedDates;  // 已完成的日期记录（用于打卡类型）

  Schedule({
    required this.id,
    required this.title,
    this.description,
    required this.type,
    required this.createdAt,
    this.date,
    this.time,
    this.endDate,
    this.isRepeat = false,
    this.repeatType,
    this.repeatDays,
    this.status = ScheduleStatus.pending,
    this.completedDates,
  });

  /// 检查日程是否在指定日期
  bool isOnDate(DateTime checkDate) {
    // 对于计划类型，检查日期是否匹配
    if (type == ScheduleType.plan) {
      if (date == null) return false;
      return date!.year == checkDate.year &&
             date!.month == checkDate.month &&
             date!.day == checkDate.day;
    }

    // 对于待办类型，总是显示
    if (type == ScheduleType.todo) {
      return true;
    }

    // 对于打卡类型，检查是否在重复日期内且在有效期内
    if (type == ScheduleType.routine && isRepeat) {
      // 检查是否在有效期内
      if (date != null) {
        // 检查是否在开始日期之后
        final startDate = DateTime(date!.year, date!.month, date!.day);
        if (checkDate.isBefore(startDate)) return false;
      }

      // 检查是否在结束日期之前
      if (endDate != null) {
        final lastDate = DateTime(endDate!.year, endDate!.month, endDate!.day, 23, 59, 59);
        if (checkDate.isAfter(lastDate)) return false;
      }

      // 检查是否符合重复规则
      if (repeatType == RepeatType.daily) {
        return true;  // 每天都显示
      }

      if (repeatType == RepeatType.weekly && repeatDays != null) {
        // 检查是否是指定的星期几（1-7，其中1是星期一）
        final weekday = checkDate.weekday;
        return repeatDays!.contains(weekday);
      }

      if (repeatType == RepeatType.monthly && repeatDays != null) {
        final day = checkDate.day;

        // 获取当月的最后一天
        final lastDayOfMonth = DateTime(checkDate.year, checkDate.month + 1, 0).day;

        for (final repeatDay in repeatDays!) {
          // 处理正常日期（1-28）
          if (repeatDay > 0 && repeatDay <= 28) {
            if (day == repeatDay) return true;
          }
          // 处理特殊日期：每月最后一天 (-1)
          else if (repeatDay == -1 && day == lastDayOfMonth) {
            return true;
          }
          // 处理特殊日期：倒数第二天 (-2)
          else if (repeatDay == -2 && day == lastDayOfMonth - 1) {
            return true;
          }
          // 处理特殊日期：倒数第三天 (-3)
          else if (repeatDay == -3 && day == lastDayOfMonth - 2) {
            return true;
          }
        }
        return false;
      }
    }

    return false;
  }

  /// 检查日程在指定日期是否已完成
  bool isCompletedOnDate(DateTime checkDate) {
    // 对于非重复任务，直接返回整体状态
    if (!isRepeat) return status == ScheduleStatus.completed;

    // 对于重复任务，只检查完成日期列表，不考虑整体状态
    if (completedDates == null || completedDates!.isEmpty) return false;

    // 检查指定日期是否在完成列表中
    return completedDates!.any((completedDate) =>
      completedDate.year == checkDate.year &&
      completedDate.month == checkDate.month &&
      completedDate.day == checkDate.day
    );
  }

  /// 创建副本
  Schedule copyWith({
    String? id,
    String? title,
    String? description,
    ScheduleType? type,
    DateTime? createdAt,
    DateTime? date,
    DateTime? time,
    DateTime? endDate,
    bool? isRepeat,
    RepeatType? repeatType,
    List<int>? repeatDays,
    ScheduleStatus? status,
    List<DateTime>? completedDates,
  }) {
    return Schedule(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      date: date ?? this.date,
      time: time ?? this.time,
      endDate: endDate ?? this.endDate,
      isRepeat: isRepeat ?? this.isRepeat,
      repeatType: repeatType ?? this.repeatType,
      repeatDays: repeatDays ?? this.repeatDays,
      status: status ?? this.status,
      completedDates: completedDates ?? this.completedDates,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.index,
      'createdAt': createdAt.toIso8601String(),
      'date': date?.toIso8601String(),
      'time': time?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'isRepeat': isRepeat,
      'repeatType': repeatType?.index,
      'repeatDays': repeatDays,
      'status': status.index,
      'completedDates': completedDates?.map((date) => date.toIso8601String()).toList(),
    };
  }
}
