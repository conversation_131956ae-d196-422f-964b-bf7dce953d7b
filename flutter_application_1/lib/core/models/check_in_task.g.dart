// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_in_task.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CheckInTaskAdapter extends TypeAdapter<CheckInTask> {
  @override
  final int typeId = 35;

  @override
  CheckInTask read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CheckInTask(
      id: fields[0] as String,
      name: fields[1] as String,
      description: fields[2] as String,
      color: fields[3] as int,
      startDate: fields[4] as DateTime,
      endDate: fields[5] as DateTime?,
      checkInRecords: (fields[6] as Map?)?.cast<String, bool>(),
      status: fields[7] as CheckInTaskStatus,
      createdAt: fields[8] as DateTime?,
      updatedAt: fields[9] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, CheckInTask obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.color)
      ..writeByte(4)
      ..write(obj.startDate)
      ..writeByte(5)
      ..write(obj.endDate)
      ..writeByte(6)
      ..write(obj.checkInRecords)
      ..writeByte(7)
      ..write(obj.status)
      ..writeByte(8)
      ..write(obj.createdAt)
      ..writeByte(9)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CheckInTaskAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CheckInTaskStatusAdapter extends TypeAdapter<CheckInTaskStatus> {
  @override
  final int typeId = 36;

  @override
  CheckInTaskStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return CheckInTaskStatus.inProgress;
      case 1:
        return CheckInTaskStatus.completed;
      case 2:
        return CheckInTaskStatus.failed;
      case 3:
        return CheckInTaskStatus.paused;
      default:
        return CheckInTaskStatus.inProgress;
    }
  }

  @override
  void write(BinaryWriter writer, CheckInTaskStatus obj) {
    switch (obj) {
      case CheckInTaskStatus.inProgress:
        writer.writeByte(0);
        break;
      case CheckInTaskStatus.completed:
        writer.writeByte(1);
        break;
      case CheckInTaskStatus.failed:
        writer.writeByte(2);
        break;
      case CheckInTaskStatus.paused:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CheckInTaskStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
