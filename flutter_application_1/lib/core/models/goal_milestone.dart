import 'package:hive/hive.dart';

part 'goal_milestone.g.dart';

@HiveType(typeId: 1)
/// 目标模型
class Goal {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final DateTime startDate;

  @HiveField(3)
  final DateTime endDate;

  @HiveField(4)
  final List<Milestone> milestones;

  @HiveField(5)
  final bool isArchived; // 是否归档

  Goal({
    required this.id,
    required this.name,
    required this.startDate,
    required this.endDate,
    this.milestones = const [],
    this.isArchived = false,
  });

  /// 计算目标的进度
  double get progress {
    final totalDuration = endDate.difference(startDate).inDays;
    if (totalDuration <= 0) return 1.0;

    final passedDuration = DateTime.now().difference(startDate).inDays;
    if (passedDuration <= 0) return 0.0;
    if (passedDuration >= totalDuration) return 1.0;

    return passedDuration / totalDuration;
  }

  /// 获取已经过去的天数
  int get passedDays {
    return DateTime.now().difference(startDate).inDays;
  }

  /// 获取剩余的天数
  int get remainingDays {
    return endDate.difference(DateTime.now()).inDays;
  }

  Goal copyWith({
    String? id,
    String? name,
    DateTime? startDate,
    DateTime? endDate,
    List<Milestone>? milestones,
    bool? isArchived,
  }) {
    return Goal(
      id: id ?? this.id,
      name: name ?? this.name,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      milestones: milestones ?? this.milestones,
      isArchived: isArchived ?? this.isArchived,
    );
  }
}

@HiveType(typeId: 2)
/// 里程碑模型
class Milestone {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String goalId; // 关联的目标ID

  @HiveField(3)
  final DateTime date; // 里程碑日期

  Milestone({
    required this.id,
    required this.name,
    required this.goalId,
    required this.date,
  });

  /// 计算里程碑的进度（相对于所属目标）
  double calculateProgress(Goal goal) {
    final totalDuration = goal.endDate.difference(goal.startDate).inDays;
    if (totalDuration <= 0) return 0.0;

    final milestoneDuration = date.difference(goal.startDate).inDays;
    if (milestoneDuration <= 0) return 0.0;
    if (milestoneDuration >= totalDuration) return 1.0;

    return milestoneDuration / totalDuration;
  }

  /// 获取里程碑剩余天数
  int get remainingDays {
    return date.difference(DateTime.now()).inDays;
  }

  Milestone copyWith({
    String? id,
    String? name,
    String? goalId,
    DateTime? date,
  }) {
    return Milestone(
      id: id ?? this.id,
      name: name ?? this.name,
      goalId: goalId ?? this.goalId,
      date: date ?? this.date,
    );
  }
}