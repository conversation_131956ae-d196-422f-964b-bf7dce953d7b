// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subject_project.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SubjectAdapter extends TypeAdapter<Subject> {
  @override
  final int typeId = 3;

  @override
  Subject read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Subject(
      id: fields[0] as String,
      name: fields[1] as String,
      color: fields[2] as int,
    );
  }

  @override
  void write(BinaryWriter writer, Subject obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.color);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SubjectAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProjectAdapter extends TypeAdapter<Project> {
  @override
  final int typeId = 5;

  @override
  Project read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Project(
      id: fields[0] as String,
      name: fields[1] as String,
      subjectId: fields[2] as String,
      startDate: fields[3] as DateTime,
      endDate: fields[4] as DateTime,
      isTrackingEnabled: fields[5] as bool,
      trackingMode: fields[6] as ProgressTrackingMode?,
      totalFocusHours: fields[7] as double?,
      customUnit: fields[8] as String?,
      targetValue: fields[9] as int?,
      currentFocusHours: fields[10] as double?,
      currentCustomValue: fields[11] as int?,
      description: fields[12] as String?,
      progressMode: fields[13] as ProgressMode,
      progress: fields[14] as double,
      targetHours: fields[15] as double,
      focusedHours: fields[16] as double,
      isArchived: fields[17] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, Project obj) {
    writer
      ..writeByte(18)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.subjectId)
      ..writeByte(3)
      ..write(obj.startDate)
      ..writeByte(4)
      ..write(obj.endDate)
      ..writeByte(5)
      ..write(obj.isTrackingEnabled)
      ..writeByte(6)
      ..write(obj.trackingMode)
      ..writeByte(7)
      ..write(obj.totalFocusHours)
      ..writeByte(8)
      ..write(obj.customUnit)
      ..writeByte(9)
      ..write(obj.targetValue)
      ..writeByte(10)
      ..write(obj.currentFocusHours)
      ..writeByte(11)
      ..write(obj.currentCustomValue)
      ..writeByte(12)
      ..write(obj.description)
      ..writeByte(13)
      ..write(obj.progressMode)
      ..writeByte(14)
      ..write(obj.progress)
      ..writeByte(15)
      ..write(obj.targetHours)
      ..writeByte(16)
      ..write(obj.focusedHours)
      ..writeByte(17)
      ..write(obj.isArchived);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProjectAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProgressTrackingModeAdapter extends TypeAdapter<ProgressTrackingMode> {
  @override
  final int typeId = 4;

  @override
  ProgressTrackingMode read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ProgressTrackingMode.focusTime;
      case 1:
        return ProgressTrackingMode.custom;
      default:
        return ProgressTrackingMode.focusTime;
    }
  }

  @override
  void write(BinaryWriter writer, ProgressTrackingMode obj) {
    switch (obj) {
      case ProgressTrackingMode.focusTime:
        writer.writeByte(0);
        break;
      case ProgressTrackingMode.custom:
        writer.writeByte(1);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProgressTrackingModeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProgressModeAdapter extends TypeAdapter<ProgressMode> {
  @override
  final int typeId = 6;

  @override
  ProgressMode read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ProgressMode.focusTime;
      case 1:
        return ProgressMode.custom;
      default:
        return ProgressMode.focusTime;
    }
  }

  @override
  void write(BinaryWriter writer, ProgressMode obj) {
    switch (obj) {
      case ProgressMode.focusTime:
        writer.writeByte(0);
        break;
      case ProgressMode.custom:
        writer.writeByte(1);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProgressModeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Subject _$SubjectFromJson(Map<String, dynamic> json) => Subject(
      id: json['id'] as String,
      name: json['name'] as String,
      color: (json['color'] as num?)?.toInt() ?? 0xFF4CAF50,
    );

Map<String, dynamic> _$SubjectToJson(Subject instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'color': instance.color,
    };

Project _$ProjectFromJson(Map<String, dynamic> json) => Project(
      id: json['id'] as String,
      name: json['name'] as String,
      subjectId: json['subjectId'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      isTrackingEnabled: json['isTrackingEnabled'] as bool? ?? false,
      trackingMode: $enumDecodeNullable(
          _$ProgressTrackingModeEnumMap, json['trackingMode']),
      totalFocusHours: (json['totalFocusHours'] as num?)?.toDouble(),
      customUnit: json['customUnit'] as String?,
      targetValue: (json['targetValue'] as num?)?.toInt(),
      currentFocusHours: (json['currentFocusHours'] as num?)?.toDouble(),
      currentCustomValue: (json['currentCustomValue'] as num?)?.toInt(),
      description: json['description'] as String?,
      progressMode:
          $enumDecodeNullable(_$ProgressModeEnumMap, json['progressMode']) ??
              ProgressMode.focusTime,
      progress: (json['progress'] as num?)?.toDouble() ?? 0.0,
      targetHours: (json['targetHours'] as num?)?.toDouble() ?? 10.0,
      focusedHours: (json['focusedHours'] as num?)?.toDouble() ?? 0.0,
      isArchived: json['isArchived'] as bool? ?? false,
    );

Map<String, dynamic> _$ProjectToJson(Project instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'subjectId': instance.subjectId,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'isTrackingEnabled': instance.isTrackingEnabled,
      'trackingMode': _$ProgressTrackingModeEnumMap[instance.trackingMode],
      'totalFocusHours': instance.totalFocusHours,
      'customUnit': instance.customUnit,
      'targetValue': instance.targetValue,
      'currentFocusHours': instance.currentFocusHours,
      'currentCustomValue': instance.currentCustomValue,
      'description': instance.description,
      'progressMode': _$ProgressModeEnumMap[instance.progressMode]!,
      'progress': instance.progress,
      'targetHours': instance.targetHours,
      'focusedHours': instance.focusedHours,
      'isArchived': instance.isArchived,
    };

const _$ProgressTrackingModeEnumMap = {
  ProgressTrackingMode.focusTime: 'focusTime',
  ProgressTrackingMode.custom: 'custom',
};

const _$ProgressModeEnumMap = {
  ProgressMode.focusTime: 'focusTime',
  ProgressMode.custom: 'custom',
};
