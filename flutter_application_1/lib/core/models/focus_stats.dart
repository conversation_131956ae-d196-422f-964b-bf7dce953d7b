import 'focus_record.dart';

class FocusStats {
  // 默认构造函数
  FocusStats();
  // 总体统计
  int totalDurationSeconds = 0;  // 总专注时长（秒）
  int totalFocusSessions = 0;    // 总专注会话次数
  int completedSessions = 0;     // 完成的专注次数
  int totalInterruptions = 0;    // 总中断次数

  // 按维度统计的专注时长（秒）
  Map<String, int> subjectDurations = {};  // 科目 -> 时长
  Map<String, int> projectDurations = {};  // 项目 -> 时长

  // 按维度统计的专注会话次数
  Map<String, int> subjectSessions = {};   // 科目 -> 会话次数
  Map<String, int> projectSessions = {};   // 项目 -> 会话次数

  // 按维度统计的中断次数
  Map<String, int> subjectInterruptions = {};  // 科目 -> 中断次数
  Map<String, int> projectInterruptions = {};  // 项目 -> 中断次数

  // 按时间周期统计的专注时长（秒）
  Map<String, int> dailyDurations = {};    // 日期 -> 时长
  Map<String, int> weeklyDurations = {};   // 周 -> 时长
  Map<String, int> monthlyDurations = {};  // 月 -> 时长

  // 按时间周期统计的专注会话次数
  Map<String, int> dailySessions = {};     // 日期 -> 会话次数
  Map<String, int> weeklySessions = {};    // 周 -> 会话次数
  Map<String, int> monthlySessions = {};   // 月 -> 会话次数

  // 按时间周期统计的中断次数
  Map<String, int> dailyInterruptions = {};    // 日期 -> 中断次数
  Map<String, int> weeklyInterruptions = {};   // 周 -> 中断次数
  Map<String, int> monthlyInterruptions = {};  // 月 -> 中断次数

  // 按小时统计的专注时长（秒）
  Map<int, int> hourlyDurations = {};      // 小时 -> 时长

  // 计算平均专注时长（秒）
  double get averageDurationSeconds {
    if (totalFocusSessions == 0) return 0;
    return totalDurationSeconds / totalFocusSessions;
  }

  // 计算完成率
  double get completionRate {
    if (totalFocusSessions == 0) return 0;
    return completedSessions / totalFocusSessions;
  }

  // 计算平均中断次数
  double get averageInterruptionsPerSession {
    if (totalFocusSessions == 0) return 0;
    return totalInterruptions / totalFocusSessions;
  }

  // 重置统计数据
  void reset() {
    totalDurationSeconds = 0;
    totalFocusSessions = 0;
    completedSessions = 0;
    totalInterruptions = 0;

    subjectDurations.clear();
    projectDurations.clear();
    subjectSessions.clear();
    projectSessions.clear();
    subjectInterruptions.clear();
    projectInterruptions.clear();

    dailyDurations.clear();
    weeklyDurations.clear();
    monthlyDurations.clear();
    dailySessions.clear();
    weeklySessions.clear();
    monthlySessions.clear();
    dailyInterruptions.clear();
    weeklyInterruptions.clear();
    monthlyInterruptions.clear();

    hourlyDurations.clear();
  }

  // 添加专注记录
  void addRecord(FocusRecord record) {
    // 更新总体统计
    totalDurationSeconds += record.durationSeconds;
    totalFocusSessions++;
    if (record.status == FocusRecordStatus.completed) {
      completedSessions++;
    }
    totalInterruptions += record.interruptionCount;

    // 更新按维度统计
    _updateMapValue(subjectDurations, record.subjectId, record.durationSeconds);
    _updateMapValue(projectDurations, record.projectId, record.durationSeconds);
    _updateMapValue(subjectSessions, record.subjectId, 1);
    _updateMapValue(projectSessions, record.projectId, 1);
    _updateMapValue(subjectInterruptions, record.subjectId, record.interruptionCount);
    _updateMapValue(projectInterruptions, record.projectId, record.interruptionCount);

    // 更新按时间周期统计
    final dateKey = _formatDate(record.startTime);
    final weekKey = '${record.startTime.year}-W${record.weekOfYear}';
    final monthKey = '${record.startTime.year}-${record.startTime.month}';

    _updateMapValue(dailyDurations, dateKey, record.durationSeconds);
    _updateMapValue(weeklyDurations, weekKey, record.durationSeconds);
    _updateMapValue(monthlyDurations, monthKey, record.durationSeconds);

    _updateMapValue(dailySessions, dateKey, 1);
    _updateMapValue(weeklySessions, weekKey, 1);
    _updateMapValue(monthlySessions, monthKey, 1);

    _updateMapValue(dailyInterruptions, dateKey, record.interruptionCount);
    _updateMapValue(weeklyInterruptions, weekKey, record.interruptionCount);
    _updateMapValue(monthlyInterruptions, monthKey, record.interruptionCount);

    // 更新按小时统计
    _updateMapValue(hourlyDurations, record.startTime.hour, record.durationSeconds);
  }

  // 辅助方法：更新Map值
  void _updateMapValue<K>(Map<K, int> map, K key, int value) {
    map[key] = (map[key] ?? 0) + value;
  }

  // 辅助方法：格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // 获取总专注时长（小时）
  double get totalDurationHours => totalDurationSeconds / 3600;

  // 获取指定日期范围内的专注时长（小时）
  double getDurationInRange(DateTime start, DateTime end) {
    int totalSeconds = 0;

    // 遍历日期统计数据
    dailyDurations.forEach((dateStr, seconds) {
      final parts = dateStr.split('-');
      if (parts.length == 3) {
        final date = DateTime(
          int.parse(parts[0]),
          int.parse(parts[1]),
          int.parse(parts[2])
        );

        if (date.isAfter(start.subtract(const Duration(days: 1))) &&
            date.isBefore(end.add(const Duration(days: 1)))) {
          totalSeconds += seconds;
        }
      }
    });

    return totalSeconds / 3600;
  }

  // 获取本周专注时长（小时）
  double getWeeklyDuration() {
    final now = DateTime.now();
    final startOfWeek = DateTime(
      now.year,
      now.month,
      now.day - now.weekday + 1
    );
    return getDurationInRange(startOfWeek, now);
  }

  // 获取本月专注时长（小时）
  double getMonthlyDuration() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    return getDurationInRange(startOfMonth, now);
  }

  // 获取指定科目的专注时长（小时）
  double getSubjectDuration(String subjectId) {
    return (subjectDurations[subjectId] ?? 0) / 3600;
  }

  // 获取指定项目的专注时长（小时）
  double getProjectDuration(String projectId) {
    return (projectDurations[projectId] ?? 0) / 3600;
  }

  // 获取科目专注时长排名
  List<MapEntry<String, int>> getSubjectDurationRanking() {
    final entries = subjectDurations.entries.toList();
    entries.sort((a, b) => b.value.compareTo(a.value));
    return entries;
  }

  // 获取项目专注时长排名
  List<MapEntry<String, int>> getProjectDurationRanking() {
    final entries = projectDurations.entries.toList();
    entries.sort((a, b) => b.value.compareTo(a.value));
    return entries;
  }

  // 获取最佳专注时段（小时）
  int getBestFocusHour() {
    if (hourlyDurations.isEmpty) return -1;

    int bestHour = 0;
    int maxDuration = 0;

    hourlyDurations.forEach((hour, duration) {
      if (duration > maxDuration) {
        maxDuration = duration;
        bestHour = hour;
      }
    });

    return bestHour;
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'totalDurationSeconds': totalDurationSeconds,
      'totalFocusSessions': totalFocusSessions,
      'completedSessions': completedSessions,
      'totalInterruptions': totalInterruptions,
      'subjectDurations': subjectDurations,
      'projectDurations': projectDurations,
      'subjectSessions': subjectSessions,
      'projectSessions': projectSessions,
      'subjectInterruptions': subjectInterruptions,
      'projectInterruptions': projectInterruptions,
      'dailyDurations': dailyDurations,
      'weeklyDurations': weeklyDurations,
      'monthlyDurations': monthlyDurations,
      'dailySessions': dailySessions,
      'weeklySessions': weeklySessions,
      'monthlySessions': monthlySessions,
      'dailyInterruptions': dailyInterruptions,
      'weeklyInterruptions': weeklyInterruptions,
      'monthlyInterruptions': monthlyInterruptions,
      'hourlyDurations': hourlyDurations.map((k, v) => MapEntry(k.toString(), v)),
    };
  }

  // 从JSON创建
  factory FocusStats.fromJson(Map<String, dynamic> json) {
    final stats = FocusStats();

    stats.totalDurationSeconds = json['totalDurationSeconds'] ?? 0;
    stats.totalFocusSessions = json['totalFocusSessions'] ?? 0;
    stats.completedSessions = json['completedSessions'] ?? 0;
    stats.totalInterruptions = json['totalInterruptions'] ?? 0;

    _parseMap(json['subjectDurations'], stats.subjectDurations);
    _parseMap(json['projectDurations'], stats.projectDurations);
    _parseMap(json['subjectSessions'], stats.subjectSessions);
    _parseMap(json['projectSessions'], stats.projectSessions);
    _parseMap(json['subjectInterruptions'], stats.subjectInterruptions);
    _parseMap(json['projectInterruptions'], stats.projectInterruptions);

    _parseMap(json['dailyDurations'], stats.dailyDurations);
    _parseMap(json['weeklyDurations'], stats.weeklyDurations);
    _parseMap(json['monthlyDurations'], stats.monthlyDurations);
    _parseMap(json['dailySessions'], stats.dailySessions);
    _parseMap(json['weeklySessions'], stats.weeklySessions);
    _parseMap(json['monthlySessions'], stats.monthlySessions);
    _parseMap(json['dailyInterruptions'], stats.dailyInterruptions);
    _parseMap(json['weeklyInterruptions'], stats.weeklyInterruptions);
    _parseMap(json['monthlyInterruptions'], stats.monthlyInterruptions);

    if (json['hourlyDurations'] != null) {
      json['hourlyDurations'].forEach((key, value) {
        stats.hourlyDurations[int.parse(key)] = value;
      });
    }

    return stats;
  }

  // 辅助方法：解析Map
  static void _parseMap(dynamic jsonMap, Map<String, int> targetMap) {
    if (jsonMap != null) {
      jsonMap.forEach((key, value) {
        targetMap[key] = value;
      });
    }
  }
}
