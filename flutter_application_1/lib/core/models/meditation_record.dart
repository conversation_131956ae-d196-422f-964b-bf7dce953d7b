// 该文件定义了一个 MeditationRecord 类，用于表示用户的冥想记录。
// 它包含冥想的场景信息、持续时间、完成时间、评分和心情变化等数据。

class MeditationRecord {
  final String sceneId;
  final int duration;
  final DateTime completedAt;
  final int rating;
  final MoodRecord mood;
  final String? notes;

  MeditationRecord({
    required this.sceneId,
    required this.duration,
    required this.completedAt,
    required this.rating,
    required this.mood,
    this.notes,
  });

  factory MeditationRecord.fromJson(Map<String, dynamic> json) {
    return MeditationRecord(
      sceneId: json['sceneId'],
      duration: json['duration'],
      completedAt: DateTime.parse(json['completedAt']),
      rating: json['rating'],
      mood: MoodRecord.fromJson(json['mood']),
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'sceneId': sceneId,
      'duration': duration,
      'completedAt': completedAt.toIso8601String(),
      'rating': rating,
      'mood': mood.toJson(),
      'notes': notes,
    };
  }
}

class MoodRecord {
  final int before;
  final int after;

  MoodRecord({
    required this.before,
    required this.after,
  });

  factory MoodRecord.fromJson(Map<String, dynamic> json) {
    return MoodRecord(
      before: json['before'],
      after: json['after'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'before': before,
      'after': after,
    };
  }
}