import 'dart:convert';
import 'package:hive/hive.dart';
import '../focus_record.dart';

/// FocusRecordStatus适配器
class FocusRecordStatusAdapter extends TypeAdapter<FocusRecordStatus> {
  @override
  final int typeId = 21;

  @override
  FocusRecordStatus read(BinaryReader reader) {
    return FocusRecordStatus.values[reader.readInt()];
  }

  @override
  void write(BinaryWriter writer, FocusRecordStatus obj) {
    writer.writeInt(obj.index);
  }
}

/// FocusRecord适配器
class FocusRecordAdapter extends TypeAdapter<FocusRecord> {
  @override
  final int typeId = 22;

  @override
  FocusRecord read(BinaryReader reader) {
    final id = reader.readString();
    final projectId = reader.readString();
    final subjectId = reader.readString();
    final startTime = DateTime.fromMillisecondsSinceEpoch(reader.readInt());
    final endTime = DateTime.fromMillisecondsSinceEpoch(reader.readInt());
    final durationSeconds = reader.readInt();
    final isCountdown = reader.readBool();

    final hasPlannedDuration = reader.readBool();
    final plannedDurationMinutes = hasPlannedDuration ? reader.readInt() : null;

    final status = FocusRecordStatus.values[reader.readInt()];
    final interruptionCount = reader.readInt();

    final hasNotes = reader.readBool();
    final notes = hasNotes ? reader.readString() : null;

    final hasAdditionalData = reader.readBool();
    Map<String, dynamic>? additionalData;
    if (hasAdditionalData) {
      final jsonString = reader.readString();
      additionalData = Map<String, dynamic>.from(jsonDecode(jsonString));
    }

    return FocusRecord(
      id: id,
      projectId: projectId,
      subjectId: subjectId,
      startTime: startTime,
      endTime: endTime,
      durationSeconds: durationSeconds,
      isCountdown: isCountdown,
      plannedDurationMinutes: plannedDurationMinutes,
      status: status,
      interruptionCount: interruptionCount,
      notes: notes,
      additionalData: additionalData,
    );
  }

  @override
  void write(BinaryWriter writer, FocusRecord obj) {
    writer.writeString(obj.id);
    writer.writeString(obj.projectId);
    writer.writeString(obj.subjectId);
    writer.writeInt(obj.startTime.millisecondsSinceEpoch);
    writer.writeInt(obj.endTime.millisecondsSinceEpoch);
    writer.writeInt(obj.durationSeconds);
    writer.writeBool(obj.isCountdown);

    writer.writeBool(obj.plannedDurationMinutes != null);
    if (obj.plannedDurationMinutes != null) {
      writer.writeInt(obj.plannedDurationMinutes!);
    }

    writer.writeInt(obj.status.index);
    writer.writeInt(obj.interruptionCount);

    writer.writeBool(obj.notes != null);
    if (obj.notes != null) {
      writer.writeString(obj.notes!);
    }

    writer.writeBool(obj.additionalData != null);
    if (obj.additionalData != null) {
      writer.writeString(jsonEncode(obj.additionalData));
    }
  }
}
