import 'package:hive/hive.dart';
import '../archived_goal.dart';
import '../goal_milestone.dart';
import '../subject_project.dart';

/// ArchivedGoal适配器
class ArchivedGoalAdapter extends TypeAdapter<ArchivedGoal> {
  @override
  final int typeId = 28;

  @override
  ArchivedGoal read(BinaryReader reader) {
    final id = reader.readString();
    final name = reader.readString();
    final startDate = DateTime.parse(reader.readString());
    final endDate = DateTime.parse(reader.readString());
    final milestonesLength = reader.readInt();
    final milestones = List<Milestone>.generate(milestonesLength, (_) => reader.read() as Milestone);
    final subjectsLength = reader.readInt();
    final subjects = List.generate(subjectsLength, (_) => reader.read() as ArchivedSubject);
    final projectsLength = reader.readInt();
    final projects = List.generate(projectsLength, (_) => reader.read() as ArchivedProject);
    final archivedAt = DateTime.parse(reader.readString());

    return ArchivedGoal(
      id: id,
      name: name,
      startDate: startDate,
      endDate: endDate,
      milestones: milestones,
      subjects: subjects,
      projects: projects,
      archivedAt: archivedAt,
    );
  }

  @override
  void write(BinaryWriter writer, ArchivedGoal obj) {
    writer.writeString(obj.id);
    writer.writeString(obj.name);
    writer.writeString(obj.startDate.toIso8601String());
    writer.writeString(obj.endDate.toIso8601String());
    writer.writeInt(obj.milestones.length);
    obj.milestones.forEach(writer.write);
    writer.writeInt(obj.subjects.length);
    obj.subjects.forEach(writer.write);
    writer.writeInt(obj.projects.length);
    obj.projects.forEach(writer.write);
    writer.writeString(obj.archivedAt.toIso8601String());
  }
}

/// ArchivedSubject适配器
class ArchivedSubjectAdapter extends TypeAdapter<ArchivedSubject> {
  @override
  final int typeId = 29;

  @override
  ArchivedSubject read(BinaryReader reader) {
    final id = reader.readString();
    final name = reader.readString();
    final color = reader.readInt();

    return ArchivedSubject(
      id: id,
      name: name,
      color: color,
    );
  }

  @override
  void write(BinaryWriter writer, ArchivedSubject obj) {
    writer.writeString(obj.id);
    writer.writeString(obj.name);
    writer.writeInt(obj.color);
  }
}

/// ArchivedProject适配器
class ArchivedProjectAdapter extends TypeAdapter<ArchivedProject> {
  @override
  final int typeId = 30;

  @override
  ArchivedProject read(BinaryReader reader) {
    final id = reader.readString();
    final name = reader.readString();
    final subjectId = reader.readString();
    final startDate = DateTime.parse(reader.readString());
    final endDate = DateTime.parse(reader.readString());

    final hasDescription = reader.readBool();
    final description = hasDescription ? reader.readString() : null;

    final isTrackingEnabled = reader.readBool();

    final hasTrackingMode = reader.readBool();
    final trackingMode = hasTrackingMode ? reader.read() as ProgressTrackingMode : null;

    final hasTotalFocusHours = reader.readBool();
    final totalFocusHours = hasTotalFocusHours ? reader.readDouble() : null;

    final hasCurrentFocusHours = reader.readBool();
    final currentFocusHours = hasCurrentFocusHours ? reader.readDouble() : null;

    final hasTargetValue = reader.readBool();
    final targetValue = hasTargetValue ? reader.readDouble() : null;

    final hasCurrentCustomValue = reader.readBool();
    final currentCustomValue = hasCurrentCustomValue ? reader.readDouble() : null;

    final hasCustomUnit = reader.readBool();
    final customUnit = hasCustomUnit ? reader.readString() : null;

    return ArchivedProject(
      id: id,
      name: name,
      subjectId: subjectId,
      startDate: startDate,
      endDate: endDate,
      description: description,
      isTrackingEnabled: isTrackingEnabled,
      trackingMode: trackingMode,
      totalFocusHours: totalFocusHours,
      currentFocusHours: currentFocusHours,
      targetValue: targetValue,
      currentCustomValue: currentCustomValue,
      customUnit: customUnit,
    );
  }

  @override
  void write(BinaryWriter writer, ArchivedProject obj) {
    writer.writeString(obj.id);
    writer.writeString(obj.name);
    writer.writeString(obj.subjectId);
    writer.writeString(obj.startDate.toIso8601String());
    writer.writeString(obj.endDate.toIso8601String());

    writer.writeBool(obj.description != null);
    if (obj.description != null) {
      writer.writeString(obj.description!);
    }

    writer.writeBool(obj.isTrackingEnabled);

    writer.writeBool(obj.trackingMode != null);
    if (obj.trackingMode != null) {
      writer.write(obj.trackingMode);
    }

    writer.writeBool(obj.totalFocusHours != null);
    if (obj.totalFocusHours != null) {
      writer.writeDouble(obj.totalFocusHours!);
    }

    writer.writeBool(obj.currentFocusHours != null);
    if (obj.currentFocusHours != null) {
      writer.writeDouble(obj.currentFocusHours!);
    }

    writer.writeBool(obj.targetValue != null);
    if (obj.targetValue != null) {
      writer.writeDouble(obj.targetValue!);
    }

    writer.writeBool(obj.currentCustomValue != null);
    if (obj.currentCustomValue != null) {
      writer.writeDouble(obj.currentCustomValue!);
    }

    writer.writeBool(obj.customUnit != null);
    if (obj.customUnit != null) {
      writer.writeString(obj.customUnit!);
    }
  }
}
