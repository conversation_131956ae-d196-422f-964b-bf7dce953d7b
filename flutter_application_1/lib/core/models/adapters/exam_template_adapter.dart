import 'package:hive/hive.dart';
import '../../../features/exam/models/exam_template_models.dart';

/// ExamNumberingMode适配器
class ExamNumberingModeAdapter extends TypeAdapter<ExamNumberingMode> {
  @override
  final int typeId = 23; // 确保与其他适配器的typeId不冲突

  @override
  ExamNumberingMode read(BinaryReader reader) {
    return ExamNumberingMode.values[reader.readInt()];
  }

  @override
  void write(BinaryWriter writer, ExamNumberingMode obj) {
    writer.writeInt(obj.index);
  }
}

/// ExamSection适配器
class ExamSectionAdapter extends TypeAdapter<ExamSection> {
  @override
  final int typeId = 24; // 确保与其他适配器的typeId不冲突

  @override
  ExamSection read(BinaryReader reader) {
    final id = reader.readString();
    final name = reader.readString();
    final score = reader.readInt();
    final estimatedTime = reader.readInt();
    final templateId = reader.readString();

    return ExamSection(
      id: id,
      name: name,
      score: score,
      estimatedTime: estimatedTime,
      templateId: templateId,
    );
  }

  @override
  void write(BinaryWriter writer, ExamSection obj) {
    writer.writeString(obj.id);
    writer.writeString(obj.name);
    writer.writeInt(obj.score);
    writer.writeInt(obj.estimatedTime);
    writer.writeString(obj.templateId);
  }
}

/// TimeOfDay适配器
class TimeOfDayAdapter extends TypeAdapter<TimeOfDayHive> {
  @override
  final int typeId = 25; // 确保与其他适配器的typeId不冲突

  @override
  TimeOfDayHive read(BinaryReader reader) {
    final hour = reader.readInt();
    final minute = reader.readInt();
    return TimeOfDayHive(hour: hour, minute: minute);
  }

  @override
  void write(BinaryWriter writer, TimeOfDayHive obj) {
    writer.writeInt(obj.hour);
    writer.writeInt(obj.minute);
  }
}

/// ExamInstance适配器
class ExamInstanceAdapter extends TypeAdapter<ExamInstance> {
  @override
  final int typeId = 26; // 确保与其他适配器的typeId不冲突

  @override
  ExamInstance read(BinaryReader reader) {
    final id = reader.readString();
    final templateId = reader.readString();
    final number = reader.readString();
    final numberingMode = reader.read() as ExamNumberingMode;

    // 读取可选字段
    final hasExamDate = reader.readBool();
    final examDate = hasExamDate ? DateTime.parse(reader.readString()) : null;

    final hasStartTime = reader.readBool();
    final startTimeHive = hasStartTime ? reader.read() as TimeOfDayHive : null;
    final startTime = startTimeHive?.toTimeOfDay();

    final hasEndTime = reader.readBool();
    final endTimeHive = hasEndTime ? reader.read() as TimeOfDayHive : null;
    final endTime = endTimeHive?.toTimeOfDay();

    final hasCustomSettings = reader.readBool();
    final customSettings = hasCustomSettings
        ? Map<String, dynamic>.from(reader.readMap())
        : null;

    return ExamInstance(
      id: id,
      templateId: templateId,
      number: number,
      numberingMode: numberingMode,
      examDate: examDate,
      startTime: startTime,
      endTime: endTime,
      customSettings: customSettings,
    );
  }

  @override
  void write(BinaryWriter writer, ExamInstance obj) {
    writer.writeString(obj.id);
    writer.writeString(obj.templateId);
    writer.writeString(obj.number);
    writer.write(obj.numberingMode);

    // 写入可选字段
    writer.writeBool(obj.examDate != null);
    if (obj.examDate != null) {
      writer.writeString(obj.examDate!.toIso8601String());
    }

    writer.writeBool(obj.startTime != null);
    if (obj.startTime != null) {
      writer.write(TimeOfDayHive.fromTimeOfDay(obj.startTime!));
    }

    writer.writeBool(obj.endTime != null);
    if (obj.endTime != null) {
      writer.write(TimeOfDayHive.fromTimeOfDay(obj.endTime!));
    }

    writer.writeBool(obj.customSettings != null);
    if (obj.customSettings != null) {
      writer.writeMap(obj.customSettings!);
    }
  }
}

/// ExamTemplate适配器
class ExamTemplateAdapter extends TypeAdapter<ExamTemplate> {
  @override
  final int typeId = 27; // 确保与其他适配器的typeId不冲突

  @override
  ExamTemplate read(BinaryReader reader) {
    final id = reader.readString();
    final name = reader.readString();

    final hasSubjectId = reader.readBool();
    final subjectId = hasSubjectId ? reader.readString() : null;

    final totalScore = reader.readInt();
    final duration = reader.readInt();

    // 读取sections列表
    final sectionsLength = reader.readInt();
    final sections = <ExamSection>[];
    for (var i = 0; i < sectionsLength; i++) {
      sections.add(reader.read() as ExamSection);
    }

    // 读取instances列表
    final instancesLength = reader.readInt();
    final instances = <ExamInstance>[];
    for (var i = 0; i < instancesLength; i++) {
      instances.add(reader.read() as ExamInstance);
    }

    final createdAt = DateTime.parse(reader.readString());
    final updatedAt = DateTime.parse(reader.readString());

    return ExamTemplate(
      id: id,
      name: name,
      subjectId: subjectId,
      totalScore: totalScore,
      duration: duration,
      sections: sections,
      instances: instances,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  @override
  void write(BinaryWriter writer, ExamTemplate obj) {
    writer.writeString(obj.id);
    writer.writeString(obj.name);

    writer.writeBool(obj.subjectId != null);
    if (obj.subjectId != null) {
      writer.writeString(obj.subjectId!);
    }

    writer.writeInt(obj.totalScore);
    writer.writeInt(obj.duration);

    // 写入sections列表
    writer.writeInt(obj.sections.length);
    for (var section in obj.sections) {
      writer.write(section);
    }

    // 写入instances列表
    writer.writeInt(obj.instances.length);
    for (var instance in obj.instances) {
      writer.write(instance);
    }

    writer.writeString(obj.createdAt.toIso8601String());
    writer.writeString(obj.updatedAt.toIso8601String());
  }
}
