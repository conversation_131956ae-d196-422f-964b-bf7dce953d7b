import '../schedule.dart';
import 'package:hive/hive.dart';

/// ScheduleType适配器
class ScheduleTypeAdapter extends TypeAdapter<ScheduleType> {
  @override
  final int typeId = 17;

  @override
  ScheduleType read(BinaryReader reader) {
    return ScheduleType.values[reader.readByte()];
  }

  @override
  void write(BinaryWriter writer, ScheduleType obj) {
    writer.writeByte(obj.index);
  }
}

/// RepeatType适配器
class RepeatTypeAdapter extends TypeAdapter<RepeatType> {
  @override
  final int typeId = 18;

  @override
  RepeatType read(BinaryReader reader) {
    return RepeatType.values[reader.readByte()];
  }

  @override
  void write(BinaryWriter writer, RepeatType obj) {
    writer.writeByte(obj.index);
  }
}

/// ScheduleStatus适配器
class ScheduleStatusAdapter extends TypeAdapter<ScheduleStatus> {
  @override
  final int typeId = 19;

  @override
  ScheduleStatus read(BinaryReader reader) {
    return ScheduleStatus.values[reader.readByte()];
  }

  @override
  void write(BinaryWriter writer, ScheduleStatus obj) {
    writer.writeByte(obj.index);
  }
}

/// Schedule适配器
class ScheduleAdapter extends TypeAdapter<Schedule> {
  @override
  final int typeId = 20;

  @override
  Schedule read(BinaryReader reader) {
    final id = reader.readString();
    final title = reader.readString();
    final hasDescription = reader.readBool();
    final description = hasDescription ? reader.readString() : null;
    final type = reader.read() as ScheduleType;
    final createdAt = DateTime.fromMillisecondsSinceEpoch(reader.readInt());

    final hasDate = reader.readBool();
    final date = hasDate ? DateTime.fromMillisecondsSinceEpoch(reader.readInt()) : null;

    final hasTime = reader.readBool();
    final time = hasTime ? DateTime.fromMillisecondsSinceEpoch(reader.readInt()) : null;

    final hasEndDate = reader.readBool();
    final endDate = hasEndDate ? DateTime.fromMillisecondsSinceEpoch(reader.readInt()) : null;

    final isRepeat = reader.readBool();

    final hasRepeatType = reader.readBool();
    final repeatType = hasRepeatType ? reader.read() as RepeatType : null;

    final hasRepeatDays = reader.readBool();
    List<int>? repeatDays;
    if (hasRepeatDays) {
      final length = reader.readInt();
      repeatDays = [];
      for (var i = 0; i < length; i++) {
        repeatDays.add(reader.readInt());
      }
    }

    final status = reader.read() as ScheduleStatus;

    final hasCompletedDates = reader.readBool();
    List<DateTime>? completedDates;
    if (hasCompletedDates) {
      final length = reader.readInt();
      completedDates = [];
      for (var i = 0; i < length; i++) {
        completedDates.add(DateTime.fromMillisecondsSinceEpoch(reader.readInt()));
      }
    }

    return Schedule(
      id: id,
      title: title,
      description: description,
      type: type,
      createdAt: createdAt,
      date: date,
      time: time,
      endDate: endDate,
      isRepeat: isRepeat,
      repeatType: repeatType,
      repeatDays: repeatDays,
      status: status,
      completedDates: completedDates,
    );
  }

  @override
  void write(BinaryWriter writer, Schedule obj) {
    writer.writeString(obj.id);
    writer.writeString(obj.title);

    writer.writeBool(obj.description != null);
    if (obj.description != null) {
      writer.writeString(obj.description!);
    }

    writer.write(obj.type);
    writer.writeInt(obj.createdAt.millisecondsSinceEpoch);

    writer.writeBool(obj.date != null);
    if (obj.date != null) {
      writer.writeInt(obj.date!.millisecondsSinceEpoch);
    }

    writer.writeBool(obj.time != null);
    if (obj.time != null) {
      writer.writeInt(obj.time!.millisecondsSinceEpoch);
    }

    writer.writeBool(obj.endDate != null);
    if (obj.endDate != null) {
      writer.writeInt(obj.endDate!.millisecondsSinceEpoch);
    }

    writer.writeBool(obj.isRepeat);

    writer.writeBool(obj.repeatType != null);
    if (obj.repeatType != null) {
      writer.write(obj.repeatType);
    }

    writer.writeBool(obj.repeatDays != null);
    if (obj.repeatDays != null) {
      writer.writeInt(obj.repeatDays!.length);
      for (var day in obj.repeatDays!) {
        writer.writeInt(day);
      }
    }

    writer.write(obj.status);

    writer.writeBool(obj.completedDates != null);
    if (obj.completedDates != null) {
      writer.writeInt(obj.completedDates!.length);
      for (var date in obj.completedDates!) {
        writer.writeInt(date.millisecondsSinceEpoch);
      }
    }
  }
}
