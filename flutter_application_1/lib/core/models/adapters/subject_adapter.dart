import '../subject_project.dart';
import 'package:hive/hive.dart';
import 'package:flutter/foundation.dart';

/// Subject适配器 - 用于Hive序列化和反序列化
class SubjectAdapter extends TypeAdapter<Subject> {
  @override
  final int typeId = 3;

  @override
  Subject read(BinaryReader reader) {
    final id = reader.readString();
    final name = reader.readString();

    // 尝试读取颜色值，如果数据格式不包含颜色（旧数据），则使用默认值
    int color;
    try {
      color = reader.readInt(); // 尝试读取颜色值
    } catch (e) {
      // 如果读取失败（旧数据格式），使用默认绿色
      color = 0xFF4CAF50; // 默认绿色
      debugPrint('读取科目颜色失败，使用默认绿色: $e');
    }

    return Subject(
      id: id,
      name: name,
      color: color,
    );
  }

  @override
  void write(BinaryWriter writer, Subject obj) {
    writer.writeString(obj.id);
    writer.writeString(obj.name);
    writer.writeInt(obj.color); // 写入颜色值
  }
}

/// ProgressTrackingMode适配器 - 用于Hive序列化和反序列化
class ProgressTrackingModeAdapter extends TypeAdapter<ProgressTrackingMode> {
  @override
  final int typeId = 4;

  @override
  ProgressTrackingMode read(BinaryReader reader) {
    final index = reader.readInt();
    return ProgressTrackingMode.values[index];
  }

  @override
  void write(BinaryWriter writer, ProgressTrackingMode obj) {
    writer.writeInt(obj.index);
  }
}

/// Project适配器 - 用于Hive序列化和反序列化
class ProjectAdapter extends TypeAdapter<Project> {
  @override
  final int typeId = 5;

  @override
  Project read(BinaryReader reader) {
    final id = reader.readString();
    final name = reader.readString();
    final subjectId = reader.readString();
    final startDate = DateTime.fromMillisecondsSinceEpoch(reader.readInt());
    final endDate = DateTime.fromMillisecondsSinceEpoch(reader.readInt());
    final isTrackingEnabled = reader.readBool();

    ProgressTrackingMode? trackingMode;
    if (reader.readBool()) {
      trackingMode = reader.read() as ProgressTrackingMode;
    }

    double? totalFocusHours;
    if (reader.readBool()) {
      totalFocusHours = reader.readDouble();
    }

    String? customUnit;
    if (reader.readBool()) {
      customUnit = reader.readString();
    }

    int? targetValue;
    if (reader.readBool()) {
      targetValue = reader.readInt();
    }

    double? currentFocusHours;
    try {
      if (reader.readBool()) {
        currentFocusHours = reader.readDouble();
      }
    } catch (e) {
      // 如果读取失败，说明是旧数据，默认为null
    }

    int? currentCustomValue;
    try {
      if (reader.readBool()) {
        currentCustomValue = reader.readInt();
      }
    } catch (e) {
      // 如果读取失败，说明是旧数据，默认为null
    }

    String? description;
    try {
      if (reader.readBool()) {
        description = reader.readString();
      }
    } catch (e) {
      // 如果读取失败，说明是旧数据，默认为null
    }

    ProgressMode progressMode = ProgressMode.focusTime;
    try {
      if (reader.readBool()) {
        progressMode = reader.read() as ProgressMode;
      }
    } catch (e) {
      // 如果读取失败，说明是旧数据，默认为focusTime
    }

    double progress = 0.0;
    try {
      progress = reader.readDouble();
    } catch (e) {
      // 如果读取失败，说明是旧数据，默认为0.0
    }

    double targetHours = 10.0;
    try {
      targetHours = reader.readDouble();
    } catch (e) {
      // 如果读取失败，说明是旧数据，默认为10.0
    }

    double focusedHours = 0.0;
    try {
      focusedHours = reader.readDouble();
    } catch (e) {
      // 如果读取失败，说明是旧数据，默认为0.0
    }

    bool isArchived = false;
    try {
      isArchived = reader.readBool();
    } catch (e) {
      // 如果读取失败，说明是旧数据，默认为false
    }

    return Project(
      id: id,
      name: name,
      subjectId: subjectId,
      startDate: startDate,
      endDate: endDate,
      isTrackingEnabled: isTrackingEnabled,
      trackingMode: trackingMode,
      totalFocusHours: totalFocusHours,
      customUnit: customUnit,
      targetValue: targetValue,
      currentFocusHours: currentFocusHours,
      currentCustomValue: currentCustomValue,
      description: description,
      progressMode: progressMode,
      progress: progress,
      targetHours: targetHours,
      focusedHours: focusedHours,
      isArchived: isArchived,
    );
  }

  @override
  void write(BinaryWriter writer, Project obj) {
    writer.writeString(obj.id);
    writer.writeString(obj.name);
    writer.writeString(obj.subjectId);
    writer.writeInt(obj.startDate.millisecondsSinceEpoch);
    writer.writeInt(obj.endDate.millisecondsSinceEpoch);
    writer.writeBool(obj.isTrackingEnabled);

    writer.writeBool(obj.trackingMode != null);
    if (obj.trackingMode != null) {
      writer.write(obj.trackingMode);
    }

    writer.writeBool(obj.totalFocusHours != null);
    if (obj.totalFocusHours != null) {
      writer.writeDouble(obj.totalFocusHours!);
    }

    writer.writeBool(obj.customUnit != null);
    if (obj.customUnit != null) {
      writer.writeString(obj.customUnit!);
    }

    writer.writeBool(obj.targetValue != null);
    if (obj.targetValue != null) {
      writer.writeInt(obj.targetValue!);
    }

    // 写入currentFocusHours
    writer.writeBool(obj.currentFocusHours != null);
    if (obj.currentFocusHours != null) {
      writer.writeDouble(obj.currentFocusHours!);
    }

    // 写入currentCustomValue
    writer.writeBool(obj.currentCustomValue != null);
    if (obj.currentCustomValue != null) {
      writer.writeInt(obj.currentCustomValue!);
    }

    // 写入description
    writer.writeBool(obj.description != null);
    if (obj.description != null) {
      writer.writeString(obj.description!);
    }

    // 写入progressMode
    writer.writeBool(true); // 始终写入progressMode
    writer.write(obj.progressMode);

    // 写入progress
    writer.writeDouble(obj.progress);

    // 写入targetHours
    writer.writeDouble(obj.targetHours);

    // 写入focusedHours
    writer.writeDouble(obj.focusedHours);

    // 写入isArchived
    writer.writeBool(obj.isArchived);
  }
}