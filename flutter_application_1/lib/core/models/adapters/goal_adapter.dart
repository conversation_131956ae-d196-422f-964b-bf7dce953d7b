import '../goal_milestone.dart';
import 'package:hive/hive.dart';

/// Goal适配器 - 用于Hive序列化和反序列化
class GoalAdapter extends TypeAdapter<Goal> {
  @override
  final int typeId = 1;

  @override
  Goal read(BinaryReader reader) {
    final id = reader.readString();
    final name = reader.readString();
    final startDate = DateTime.fromMillisecondsSinceEpoch(reader.readInt());
    final endDate = DateTime.fromMillisecondsSinceEpoch(reader.readInt());

    final milestonesLength = reader.readInt();
    final milestones = <Milestone>[];

    for (var i = 0; i < milestonesLength; i++) {
      milestones.add(reader.read() as Milestone);
    }

    // 尝试读取isArchived字段，如果不存在则默认为false
    bool isArchived = false;
    try {
      isArchived = reader.readBool();
    } catch (e) {
      // 如果读取失败，说明是旧数据，默认为false
    }

    return Goal(
      id: id,
      name: name,
      startDate: startDate,
      endDate: endDate,
      milestones: milestones,
      isArchived: isArchived,
    );
  }

  @override
  void write(BinaryWriter writer, Goal obj) {
    writer.writeString(obj.id);
    writer.writeString(obj.name);
    writer.writeInt(obj.startDate.millisecondsSinceEpoch);
    writer.writeInt(obj.endDate.millisecondsSinceEpoch);

    writer.writeInt(obj.milestones.length);
    for (var milestone in obj.milestones) {
      writer.write(milestone);
    }

    // 写入isArchived字段
    writer.writeBool(obj.isArchived);
  }
}

/// Milestone适配器 - 用于Hive序列化和反序列化
class MilestoneAdapter extends TypeAdapter<Milestone> {
  @override
  final int typeId = 2;

  @override
  Milestone read(BinaryReader reader) {
    final id = reader.readString();
    final name = reader.readString();
    final goalId = reader.readString();
    final date = DateTime.fromMillisecondsSinceEpoch(reader.readInt());

    return Milestone(
      id: id,
      name: name,
      goalId: goalId,
      date: date,
    );
  }

  @override
  void write(BinaryWriter writer, Milestone obj) {
    writer.writeString(obj.id);
    writer.writeString(obj.name);
    writer.writeString(obj.goalId);
    writer.writeInt(obj.date.millisecondsSinceEpoch);
  }
}