import 'package:hive/hive.dart';
import 'goal_milestone.dart';
import 'subject_project.dart';

part 'archived_goal.g.dart';

/// 归档目标模型
@HiveType(typeId: 28)
class ArchivedGoal {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final DateTime startDate;

  @HiveField(3)
  final DateTime endDate;

  @HiveField(4)
  final List<Milestone> milestones;

  @HiveField(5)
  final List<ArchivedSubject> subjects;

  @HiveField(6)
  final List<ArchivedProject> projects;

  @HiveField(7)
  final DateTime archivedAt;

  ArchivedGoal({
    required this.id,
    required this.name,
    required this.startDate,
    required this.endDate,
    required this.milestones,
    required this.subjects,
    required this.projects,
    required this.archivedAt,
  });

  /// 从目标创建归档目标
  factory ArchivedGoal.fromGoal(
    Goal goal, {
    required List<Subject> subjects,
    required List<Project> projects,
  }) {
    return ArchivedGoal(
      id: goal.id,
      name: goal.name,
      startDate: goal.startDate,
      endDate: goal.endDate,
      milestones: goal.milestones,
      subjects: subjects.map((s) => ArchivedSubject.fromSubject(s)).toList(),
      projects: projects.map((p) => ArchivedProject.fromProject(p)).toList(),
      archivedAt: DateTime.now(),
    );
  }

  /// 创建副本
  ArchivedGoal copyWith({
    String? id,
    String? name,
    DateTime? startDate,
    DateTime? endDate,
    List<Milestone>? milestones,
    List<ArchivedSubject>? subjects,
    List<ArchivedProject>? projects,
    DateTime? archivedAt,
  }) {
    return ArchivedGoal(
      id: id ?? this.id,
      name: name ?? this.name,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      milestones: milestones ?? this.milestones,
      subjects: subjects ?? this.subjects,
      projects: projects ?? this.projects,
      archivedAt: archivedAt ?? this.archivedAt,
    );
  }
}

/// 归档科目模型
@HiveType(typeId: 29)
class ArchivedSubject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final int color;

  ArchivedSubject({
    required this.id,
    required this.name,
    required this.color,
  });

  /// 从科目创建归档科目
  factory ArchivedSubject.fromSubject(Subject subject) {
    return ArchivedSubject(
      id: subject.id,
      name: subject.name,
      color: subject.color,
    );
  }
}

/// 归档项目模型
@HiveType(typeId: 30)
class ArchivedProject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String subjectId;

  @HiveField(3)
  final DateTime startDate;

  @HiveField(4)
  final DateTime endDate;

  @HiveField(5)
  final String? description;

  @HiveField(6)
  final bool isTrackingEnabled;

  @HiveField(7)
  final ProgressTrackingMode? trackingMode;

  @HiveField(8)
  final double? totalFocusHours;

  @HiveField(9)
  final double? currentFocusHours;

  @HiveField(10)
  final double? targetValue;

  @HiveField(11)
  final double? currentCustomValue;

  @HiveField(12)
  final String? customUnit;

  ArchivedProject({
    required this.id,
    required this.name,
    required this.subjectId,
    required this.startDate,
    required this.endDate,
    this.description,
    required this.isTrackingEnabled,
    this.trackingMode,
    this.totalFocusHours,
    this.currentFocusHours,
    this.targetValue,
    this.currentCustomValue,
    this.customUnit,
  });

  /// 从项目创建归档项目
  factory ArchivedProject.fromProject(Project project) {
    return ArchivedProject(
      id: project.id,
      name: project.name,
      subjectId: project.subjectId,
      startDate: project.startDate,
      endDate: project.endDate,
      description: project.description,
      isTrackingEnabled: project.isTrackingEnabled,
      trackingMode: project.trackingMode,
      totalFocusHours: project.totalFocusHours,
      currentFocusHours: project.currentFocusHours,
      targetValue: project.targetValue?.toDouble(),
      currentCustomValue: project.currentCustomValue?.toDouble(),
      customUnit: project.customUnit,
    );
  }

  /// 计算进度
  double get progress {
    if (!isTrackingEnabled || trackingMode == null) return 0.0;

    if (trackingMode == ProgressTrackingMode.focusTime) {
      if (totalFocusHours == null || totalFocusHours! <= 0) return 0.0;
      return ((currentFocusHours ?? 0) / totalFocusHours!).clamp(0.0, 1.0);
    } else {
      if (targetValue == null || targetValue! <= 0) return 0.0;
      return ((currentCustomValue ?? 0) / targetValue!).clamp(0.0, 1.0);
    }
  }
}
