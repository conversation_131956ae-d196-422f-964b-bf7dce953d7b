// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'archived_goal.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ArchivedGoalAdapter extends TypeAdapter<ArchivedGoal> {
  @override
  final int typeId = 28;

  @override
  ArchivedGoal read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ArchivedGoal(
      id: fields[0] as String,
      name: fields[1] as String,
      startDate: fields[2] as DateTime,
      endDate: fields[3] as DateTime,
      milestones: (fields[4] as List).cast<Milestone>(),
      subjects: (fields[5] as List).cast<ArchivedSubject>(),
      projects: (fields[6] as List).cast<ArchivedProject>(),
      archivedAt: fields[7] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, ArchivedGoal obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.startDate)
      ..writeByte(3)
      ..write(obj.endDate)
      ..writeByte(4)
      ..write(obj.milestones)
      ..writeByte(5)
      ..write(obj.subjects)
      ..writeByte(6)
      ..write(obj.projects)
      ..writeByte(7)
      ..write(obj.archivedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ArchivedGoalAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ArchivedSubjectAdapter extends TypeAdapter<ArchivedSubject> {
  @override
  final int typeId = 29;

  @override
  ArchivedSubject read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ArchivedSubject(
      id: fields[0] as String,
      name: fields[1] as String,
      color: fields[2] as int,
    );
  }

  @override
  void write(BinaryWriter writer, ArchivedSubject obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.color);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ArchivedSubjectAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ArchivedProjectAdapter extends TypeAdapter<ArchivedProject> {
  @override
  final int typeId = 30;

  @override
  ArchivedProject read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ArchivedProject(
      id: fields[0] as String,
      name: fields[1] as String,
      subjectId: fields[2] as String,
      startDate: fields[3] as DateTime,
      endDate: fields[4] as DateTime,
      description: fields[5] as String?,
      isTrackingEnabled: fields[6] as bool,
      trackingMode: fields[7] as ProgressTrackingMode?,
      totalFocusHours: fields[8] as double?,
      currentFocusHours: fields[9] as double?,
      targetValue: fields[10] as double?,
      currentCustomValue: fields[11] as double?,
      customUnit: fields[12] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ArchivedProject obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.subjectId)
      ..writeByte(3)
      ..write(obj.startDate)
      ..writeByte(4)
      ..write(obj.endDate)
      ..writeByte(5)
      ..write(obj.description)
      ..writeByte(6)
      ..write(obj.isTrackingEnabled)
      ..writeByte(7)
      ..write(obj.trackingMode)
      ..writeByte(8)
      ..write(obj.totalFocusHours)
      ..writeByte(9)
      ..write(obj.currentFocusHours)
      ..writeByte(10)
      ..write(obj.targetValue)
      ..writeByte(11)
      ..write(obj.currentCustomValue)
      ..writeByte(12)
      ..write(obj.customUnit);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ArchivedProjectAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
