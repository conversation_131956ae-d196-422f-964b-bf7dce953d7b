// 该文件定义了一个 Scene 类，用于表示场景对象。
// 它包含场景的唯一标识符、名称、描述、图片 URL 和音频 URL。

// Scene 类，用于表示场景对象。
class Scene {
  // 基本信息
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final String category;
  final List<String> targetAudience;
  final int recommendationScore;
  final String status;
  final int version;
  final bool isPremium;

  // 音频信息
  final List<MeditationAudio> meditations;
  final List<String> defaultCombinations;

  // 标签
  final List<String> tags;

  // 统计信息
  final SceneStats stats;

  // 构造函数
  Scene({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.category,
    required this.targetAudience,
    required this.recommendationScore,
    required this.status,
    required this.version,
    required this.isPremium,
    required this.meditations,
    required this.defaultCombinations,
    required this.tags,
    required this.stats,
  });
 
  factory Scene.fromJson(Map<String, dynamic> json) {
    // 解析音频信息
    List<MeditationAudio> parseMeditations(Map<String, dynamic> audios) {
      var meditationsJson = audios['meditations'] as List<dynamic>;
      return meditationsJson.map((m) => MeditationAudio.fromJson(m)).toList();
    }

    // 解析目标受众
    List<String> parseTargetAudience(List<dynamic> audience) {
      return audience.map((a) => a.toString()).toList();
    }

    // 解析标签
    List<String> parseTags(List<dynamic> tags) {
      return tags.map((t) => t.toString()).toList();
    }

    // 解析默认组合
    List<String> parseDefaultCombinations(Map<String, dynamic> audios) {
      var combinations = audios['defaultCombinations'] as List<dynamic>;
      return combinations.map((c) => c.toString()).toList();
    }

    return Scene(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      category: json['category'] ?? '',
      targetAudience: parseTargetAudience(json['targetAudience'] as List<dynamic>),
      recommendationScore: json['recommendationScore'] as int,
      status: json['status'] ?? '',
      version: json['version'] as int,
      isPremium: json['isPremium'] as bool,
      meditations: parseMeditations(json['audios'] as Map<String, dynamic>),
      defaultCombinations: parseDefaultCombinations(json['audios'] as Map<String, dynamic>),
      tags: parseTags(json['tags'] as List<dynamic>),
      stats: SceneStats.fromJson(json['stats'] as Map<String, dynamic>),
    );
  }


}

// 音频信息类
class MeditationAudio {
  final int duration;
  final String voice;
  final String url;
  final String language;

  MeditationAudio({
    required this.duration,
    required this.voice,
    required this.url,
    required this.language,
  });

  factory MeditationAudio.fromJson(Map<String, dynamic> json) {
    return MeditationAudio(
      duration: json['duration'] as int,
      voice: json['voice'] as String,
      url: json['url'] as String,
      language: json['language'] as String,
    );
  }
}

// 统计信息类
class SceneStats {
  final int views;
  final int completions;
  final double averageRating;
  final int totalRatings;
  final int favoriteCount;

  SceneStats({
    required this.views,
    required this.completions,
    required this.averageRating,
    required this.totalRatings,
    required this.favoriteCount,
  });

  factory SceneStats.fromJson(Map<String, dynamic> json) {
    return SceneStats(
      views: json['views'] as int,
      completions: json['completions'] as int,
      averageRating: (json['averageRating'] as num).toDouble(),
      totalRatings: json['totalRatings'] as int,
      favoriteCount: json['favoriteCount'] as int,
    );
  }
 }



