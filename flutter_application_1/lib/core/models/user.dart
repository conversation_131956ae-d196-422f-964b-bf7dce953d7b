// 该文件定义了一个 User 类，用于表示用户对象。
// 它包含用户的基本信息、认证信息、会员信息和偏好设置等。
// 设计考虑了未来可能的扩展，如多种认证方式、会员功能等。

import 'package:flutter/foundation.dart';

/// User 类，用于表示用户对象。
class User {
  // 基本信息
  /// 用户的唯一标识符。
  final String id;
  /// 用户的用户名。
  final String username;
  /// 用户的昵称，用于显示。
  final String nickname;
  /// 用户头像的 URL。
  final String? avatar;

  // 联系方式
  /// 用户的电子邮箱。
  final String email;
  /// 用户的手机号。
  final String? phone;

  // 账户状态
  /// 账户创建时间。
  final DateTime createdAt;
  /// 最后登录时间。
  final DateTime? lastLoginAt;
  /// 账户状态：active, inactive, locked
  final String accountStatus;
  /// 认证方式列表：phone, email, wechat等
  final List<String> authMethods;

  // 会员信息（保留但初始版本不显示）
  /// 会员等级：free, basic, premium, pro等
  final String membershipType;
  /// 会员过期时间。
  final DateTime? membershipExpires;

  // 用户设置与偏好
  /// 用户偏好设置。
  final Map<String, dynamic> preferences;
  /// 应用设置。
  final Map<String, dynamic> settings;

  // 使用统计
  /// 使用统计数据。
  final Map<String, dynamic> usageStats;

  /// 构造函数，接收用户的所有属性作为参数。
  User({
    required this.id,
    required this.username,
    required this.nickname,
    this.avatar,
    required this.email,
    this.phone,
    DateTime? createdAt,
    this.lastLoginAt,
    this.accountStatus = 'active',
    this.authMethods = const ['email'],
    this.membershipType = 'free',
    this.membershipExpires,
    Map<String, dynamic>? preferences,
    Map<String, dynamic>? settings,
    Map<String, dynamic>? usageStats,
  }) :
    createdAt = createdAt ?? DateTime.now(),
    preferences = preferences ?? <String, dynamic>{},
    settings = settings ?? <String, dynamic>{},
    usageStats = usageStats ?? <String, dynamic>{};

  /// 解析认证方式数据
  static List<String> _parseAuthMethods(dynamic authMethodsData) {
    if (authMethodsData == null) {
      return ['email'];
    }

    // 如果是数组格式（旧格式）
    if (authMethodsData is List) {
      return List<String>.from(authMethodsData);
    }

    // 如果是对象格式（新格式）- 支持Map<dynamic, dynamic>和Map<String, dynamic>
    if (authMethodsData is Map) {
      List<String> methods = [];
      authMethodsData.forEach((key, value) {
        if (value == true) {
          methods.add(key.toString()); // 确保key是字符串类型
        }
      });
      return methods.isNotEmpty ? methods : ['email'];
    }

    // 默认返回邮箱认证
    return ['email'];
  }

  /// 解析Map数据，确保类型安全
  static Map<String, dynamic> _parseMapData(dynamic mapData) {
    if (mapData == null) {
      return <String, dynamic>{};
    }

    if (mapData is Map<String, dynamic>) {
      return mapData;
    }

    if (mapData is Map) {
      // 转换Map<dynamic, dynamic>为Map<String, dynamic>
      final result = <String, dynamic>{};
      mapData.forEach((key, value) {
        result[key.toString()] = value;
      });
      return result;
    }

    // 默认返回空Map
    return <String, dynamic>{};
  }

  /// 安全获取字符串值
  static String _safeString(dynamic value, {String defaultValue = ''}) {
    if (value == null) return defaultValue;
    return value.toString();
  }

  /// 安全获取可空字符串值
  static String? _safeStringNullable(dynamic value) {
    if (value == null) return null;
    return value.toString();
  }

  /// 安全获取DateTime值
  static DateTime? _safeDateTime(dynamic value) {
    if (value == null) return null;
    try {
      if (value is String) {
        return DateTime.parse(value);
      }
      return null;
    } catch (e) {
      debugPrint('解析DateTime失败: $value, 错误: $e');
      return null;
    }
  }

  /// 从 JSON 数据创建 User 对象的工厂方法。
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: _safeString(json['id']),
      username: _safeString(json['username']),
      nickname: _safeString(json['nickname'], defaultValue: '用户'),
      avatar: _safeStringNullable(json['avatar']),
      email: _safeString(json['email']),
      phone: _safeStringNullable(json['phone']),
      createdAt: _safeDateTime(json['createdAt']) ?? DateTime.now(),
      lastLoginAt: _safeDateTime(json['lastLoginAt']),
      accountStatus: _safeString(json['accountStatus'], defaultValue: 'active'),
      authMethods: _parseAuthMethods(json['authMethods']),
      membershipType: _safeString(json['membershipType'], defaultValue: 'free'),
      membershipExpires: _safeDateTime(json['membershipExpires']),
      preferences: _parseMapData(json['preferences']),
      settings: _parseMapData(json['settings']),
      usageStats: _parseMapData(json['usageStats']),
    );
  }

  /// 将 User 对象转换为 JSON 数据的方法。
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'nickname': nickname,
      'avatar': avatar,
      'email': email,
      'phone': phone,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
      'accountStatus': accountStatus,
      'authMethods': authMethods,
      'membershipType': membershipType,
      'membershipExpires': membershipExpires?.toIso8601String(),
      'preferences': preferences,
      'settings': settings,
      'usageStats': usageStats,
    };
  }

  /// 创建用户副本并更新部分属性
  User copyWith({
    String? username,
    String? nickname,
    String? avatar,
    String? email,
    String? phone,
    DateTime? lastLoginAt,
    String? accountStatus,
    List<String>? authMethods,
    String? membershipType,
    DateTime? membershipExpires,
    Map<String, dynamic>? preferences,
    Map<String, dynamic>? settings,
    Map<String, dynamic>? usageStats,
  }) {
    return User(
      id: id,
      username: username ?? this.username,
      nickname: nickname ?? this.nickname,
      avatar: avatar ?? this.avatar,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      createdAt: createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      accountStatus: accountStatus ?? this.accountStatus,
      authMethods: authMethods ?? this.authMethods,
      membershipType: membershipType ?? this.membershipType,
      membershipExpires: membershipExpires ?? this.membershipExpires,
      preferences: preferences ?? this.preferences,
      settings: settings ?? this.settings,
      usageStats: usageStats ?? this.usageStats,
    );
  }

  /// 检查用户是否是会员
  bool isMember() {
    return membershipType != 'free' &&
           membershipExpires != null &&
           membershipExpires!.isAfter(DateTime.now());
  }

  /// 获取会员等级名称
  String getMembershipTierName() {
    switch (membershipType) {
      case 'basic': return '基础会员';
      case 'premium': return '高级会员';
      case 'pro': return '专业版';
      default: return '免费用户';
    }
  }
}
