import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'subject_project.g.dart';

@HiveType(typeId: 3)
@JsonSerializable()
/// 科目模型
class Subject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final int color; // 科目颜色

  Subject({
    required this.id,
    required this.name,
    this.color = 0xFF4CAF50, // 默认为绿色
  });

  // JSON序列化支持
  factory Subject.fromJson(Map<String, dynamic> json) => _$SubjectFromJson(json);
  Map<String, dynamic> toJson() => _$SubjectToJson(this);

  Subject copyWith({
    String? id,
    String? name,
    int? color,
  }) {
    return Subject(
      id: id ?? this.id,
      name: name ?? this.name,
      color: color ?? this.color,
    );
  }
}

@HiveType(typeId: 4)
/// 进度追踪模式
enum ProgressTrackingMode {
  @HiveField(0)
  focusTime,  // 专注时间模式

  @HiveField(1)
  custom,    // 自定义模式
}

@HiveType(typeId: 6)
/// 进度模式
enum ProgressMode {
  @HiveField(0)
  focusTime,  // 专注时间模式

  @HiveField(1)
  custom,    // 自定义模式
}

@HiveType(typeId: 5)
@JsonSerializable()
/// 项目模型
class Project {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String subjectId; // 关联的科目ID

  @HiveField(3)
  final DateTime startDate; // 开始日期

  @HiveField(4)
  final DateTime endDate; // 结束日期

  @HiveField(5)
  final bool isTrackingEnabled; // 是否开启进度追踪

  @HiveField(6)
  final ProgressTrackingMode? trackingMode; // 进度追踪模式

  @HiveField(7)
  final double? totalFocusHours; // 专注时间模式：总专注时长（小时）

  @HiveField(8)
  final String? customUnit; // 自定义模式：自定义单位

  @HiveField(9)
  final int? targetValue; // 自定义模式：目标数值

  @HiveField(10)
  final double? currentFocusHours; // 专注时间模式：当前专注时长（小时）

  @HiveField(11)
  final int? currentCustomValue; // 自定义模式：当前进度值

  @HiveField(12)
  final String? description; // 项目描述

  @HiveField(13)
  final ProgressMode progressMode; // 进度模式

  @HiveField(14)
  final double progress; // 当前进度（0-1）

  @HiveField(15)
  final double targetHours; // 目标时间（小时）

  @HiveField(16)
  final double focusedHours; // 已专注时间（小时）

  @HiveField(17)
  final bool isArchived; // 是否归档

  Project({
    required this.id,
    required this.name,
    required this.subjectId,
    required this.startDate,
    required this.endDate,
    this.isTrackingEnabled = false,
    this.trackingMode,
    this.totalFocusHours,
    this.customUnit,
    this.targetValue,
    this.currentFocusHours,
    this.currentCustomValue,
    this.description,
    this.progressMode = ProgressMode.focusTime,
    this.progress = 0.0,
    this.targetHours = 10.0,
    this.focusedHours = 0.0,
    this.isArchived = false,
  });

  // JSON序列化支持
  factory Project.fromJson(Map<String, dynamic> json) => _$ProjectFromJson(json);
  Map<String, dynamic> toJson() => _$ProjectToJson(this);

  Project copyWith({
    String? id,
    String? name,
    String? subjectId,
    DateTime? startDate,
    DateTime? endDate,
    bool? isTrackingEnabled,
    ProgressTrackingMode? trackingMode,
    double? totalFocusHours,
    String? customUnit,
    int? targetValue,
    double? currentFocusHours,
    int? currentCustomValue,
    String? description,
    ProgressMode? progressMode,
    double? progress,
    double? targetHours,
    double? focusedHours,
    bool? isArchived,
  }) {
    return Project(
      id: id ?? this.id,
      name: name ?? this.name,
      subjectId: subjectId ?? this.subjectId,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isTrackingEnabled: isTrackingEnabled ?? this.isTrackingEnabled,
      trackingMode: trackingMode ?? this.trackingMode,
      totalFocusHours: totalFocusHours ?? this.totalFocusHours,
      customUnit: customUnit ?? this.customUnit,
      targetValue: targetValue ?? this.targetValue,
      currentFocusHours: currentFocusHours ?? this.currentFocusHours,
      currentCustomValue: currentCustomValue ?? this.currentCustomValue,
      description: description ?? this.description,
      progressMode: progressMode ?? this.progressMode,
      progress: progress ?? this.progress,
      targetHours: targetHours ?? this.targetHours,
      focusedHours: focusedHours ?? this.focusedHours,
      isArchived: isArchived ?? this.isArchived,
    );
  }
}