// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'focus_record.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class FocusRecordAdapter extends TypeAdapter<FocusRecord> {
  @override
  final int typeId = 22;

  @override
  FocusRecord read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return FocusRecord(
      id: fields[0] as String,
      projectId: fields[1] as String,
      subjectId: fields[2] as String,
      startTime: fields[3] as DateTime,
      endTime: fields[4] as DateTime,
      durationSeconds: fields[5] as int,
      isCountdown: fields[6] as bool,
      plannedDurationMinutes: fields[7] as int?,
      status: fields[8] as FocusRecordStatus,
      interruptionCount: fields[9] as int,
      notes: fields[10] as String?,
      additionalData: (fields[11] as Map?)?.cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, FocusRecord obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.projectId)
      ..writeByte(2)
      ..write(obj.subjectId)
      ..writeByte(3)
      ..write(obj.startTime)
      ..writeByte(4)
      ..write(obj.endTime)
      ..writeByte(5)
      ..write(obj.durationSeconds)
      ..writeByte(6)
      ..write(obj.isCountdown)
      ..writeByte(7)
      ..write(obj.plannedDurationMinutes)
      ..writeByte(8)
      ..write(obj.status)
      ..writeByte(9)
      ..write(obj.interruptionCount)
      ..writeByte(10)
      ..write(obj.notes)
      ..writeByte(11)
      ..write(obj.additionalData);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FocusRecordAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class FocusRecordStatusAdapter extends TypeAdapter<FocusRecordStatus> {
  @override
  final int typeId = 21;

  @override
  FocusRecordStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return FocusRecordStatus.completed;
      case 1:
        return FocusRecordStatus.interrupted;
      case 2:
        return FocusRecordStatus.paused;
      default:
        return FocusRecordStatus.completed;
    }
  }

  @override
  void write(BinaryWriter writer, FocusRecordStatus obj) {
    switch (obj) {
      case FocusRecordStatus.completed:
        writer.writeByte(0);
        break;
      case FocusRecordStatus.interrupted:
        writer.writeByte(1);
        break;
      case FocusRecordStatus.paused:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FocusRecordStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
