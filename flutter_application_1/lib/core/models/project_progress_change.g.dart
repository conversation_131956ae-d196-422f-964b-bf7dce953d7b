// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'project_progress_change.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ProjectProgressChangeAdapter extends TypeAdapter<ProjectProgressChange> {
  @override
  final int typeId = 34;

  @override
  ProjectProgressChange read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProjectProgressChange(
      id: fields[0] as String,
      projectId: fields[1] as String,
      timestamp: fields[2] as DateTime,
      previousValue: fields[3] as double,
      newValue: fields[4] as double,
      previousProgress: fields[5] as double,
      newProgress: fields[6] as double,
      source: fields[7] as ProgressChangeSource,
      focusRecordId: fields[8] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ProjectProgressChange obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.projectId)
      ..writeByte(2)
      ..write(obj.timestamp)
      ..writeByte(3)
      ..write(obj.previousValue)
      ..writeByte(4)
      ..write(obj.newValue)
      ..writeByte(5)
      ..write(obj.previousProgress)
      ..writeByte(6)
      ..write(obj.newProgress)
      ..writeByte(7)
      ..write(obj.source)
      ..writeByte(8)
      ..write(obj.focusRecordId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProjectProgressChangeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProgressChangeSourceAdapter extends TypeAdapter<ProgressChangeSource> {
  @override
  final int typeId = 33;

  @override
  ProgressChangeSource read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ProgressChangeSource.focusCompletion;
      case 1:
        return ProgressChangeSource.manualAdjustment;
      default:
        return ProgressChangeSource.focusCompletion;
    }
  }

  @override
  void write(BinaryWriter writer, ProgressChangeSource obj) {
    switch (obj) {
      case ProgressChangeSource.focusCompletion:
        writer.writeByte(0);
        break;
      case ProgressChangeSource.manualAdjustment:
        writer.writeByte(1);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProgressChangeSourceAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
