import 'package:flutter/material.dart';
import 'package:hive/hive.dart';

part 'check_in_task.g.dart';

/// 打卡任务状态
@HiveType(typeId: 36)
enum CheckInTaskStatus {
  /// 进行中
  @HiveField(0)
  inProgress,

  /// 已完成
  @HiveField(1)
  completed,

  /// 已失败
  @HiveField(2)
  failed,

  /// 已暂停
  @HiveField(3)
  paused,
}

/// 打卡任务模型
@HiveType(typeId: 35)
class CheckInTask extends HiveObject {
  /// 任务ID
  @HiveField(0)
  final String id;

  /// 任务名称
  @HiveField(1)
  String name;

  /// 任务描述
  @HiveField(2)
  String description;

  /// 任务颜色
  @HiveField(3)
  int color;

  /// 开始日期
  @HiveField(4)
  DateTime startDate;

  /// 结束日期
  @HiveField(5)
  DateTime? endDate;

  /// 打卡记录 - 记录每天的打卡状态，key为日期字符串(yyyy-MM-dd)，value为是否打卡
  @HiveField(6)
  Map<String, bool> checkInRecords;

  /// 任务状态
  @HiveField(7)
  CheckInTaskStatus status;

  /// 创建时间
  @HiveField(8)
  final DateTime createdAt;

  /// 更新时间
  @HiveField(9)
  DateTime updatedAt;

  /// 构造函数
  CheckInTask({
    required this.id,
    required this.name,
    this.description = '',
    required this.color,
    required this.startDate,
    this.endDate,
    Map<String, bool>? checkInRecords,
    this.status = CheckInTaskStatus.inProgress,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) :
    checkInRecords = checkInRecords ?? {},
    createdAt = createdAt ?? DateTime.now(),
    updatedAt = updatedAt ?? DateTime.now();

  /// 获取打卡总天数
  int get totalDays {
    if (endDate == null) {
      // 如果没有结束日期，计算从开始日期到今天的天数
      final now = DateTime.now();
      return now.difference(startDate).inDays + 1;
    } else {
      // 如果有结束日期，计算从开始日期到结束日期的天数
      return endDate!.difference(startDate).inDays + 1;
    }
  }

  /// 获取已打卡天数
  int get checkedInDays {
    return checkInRecords.values.where((checked) => checked).length;
  }

  /// 获取打卡进度（百分比）
  double get progress {
    if (totalDays == 0) return 0.0;
    return checkedInDays / totalDays;
  }

  /// 获取今天是否已打卡
  bool get isTodayCheckedIn {
    final today = DateTime.now();
    final dateStr = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';
    return checkInRecords[dateStr] ?? false;
  }

  /// 获取指定日期是否已打卡
  bool isDateCheckedIn(DateTime date) {
    final dateStr = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    return checkInRecords[dateStr] ?? false;
  }

  /// 打卡
  void checkIn(DateTime date) {
    final dateStr = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    checkInRecords[dateStr] = true;
    updatedAt = DateTime.now();
    save();
  }

  /// 取消打卡
  void cancelCheckIn(DateTime date) {
    final dateStr = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    checkInRecords.remove(dateStr);
    updatedAt = DateTime.now();
    save();
  }

  /// 获取任务颜色
  Color getColor() {
    return Color(color);
  }
}
