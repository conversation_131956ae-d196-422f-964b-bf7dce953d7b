import 'package:hive/hive.dart';

part 'project_progress_change.g.dart';

/// 进度变化来源
@HiveType(typeId: 33)
enum ProgressChangeSource {
  @HiveField(0)
  focusCompletion, // 专注完成后调整

  @HiveField(1)
  manualAdjustment, // 项目详情页手动调整
}

/// 项目进度变化记录
@HiveType(typeId: 34)
class ProjectProgressChange {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String projectId;

  @HiveField(2)
  final DateTime timestamp;

  @HiveField(3)
  final double previousValue;

  @HiveField(4)
  final double newValue;

  @HiveField(5)
  final double previousProgress;

  @HiveField(6)
  final double newProgress;

  @HiveField(7)
  final ProgressChangeSource source;

  @HiveField(8)
  final String? focusRecordId; // 关联的专注记录ID（如果有）

  ProjectProgressChange({
    required this.id,
    required this.projectId,
    required this.timestamp,
    required this.previousValue,
    required this.newValue,
    required this.previousProgress,
    required this.newProgress,
    required this.source,
    this.focusRecordId,
  });

  /// 获取值变化（原始值）
  /// 注意：这是实际的数值变化，可能为正、为负或为零
  double get valueChange => newValue - previousValue;

  /// 获取进度变化（原始值）
  /// 注意：这是实际的进度百分比变化，可能为正、为负或为零
  double get progressChange => newProgress - previousProgress;

  /// 获取用于显示的值变化
  /// 注意：当值减少时返回0，确保在数据分析中不显示负值变化
  double get displayValueChange {
    double change = valueChange;
    // 如果值变化为负，返回0（在数据分析中视为无进度推进）
    if (change < 0) {
      return 0;
    }
    // 如果值变化为0但来源是手动调整，返回一个最小值以确保显示
    if (change == 0 && source == ProgressChangeSource.manualAdjustment) {
      return 0.1;
    }
    return change;
  }

  /// 获取用于显示的进度变化
  /// 注意：当进度减少时返回0，确保在数据分析中不显示负值变化
  double get displayProgressChange {
    double change = progressChange;
    // 如果进度变化为负，返回0（在数据分析中视为无进度推进）
    if (change < 0) {
      return 0;
    }
    // 如果进度变化为0但值变化大于0，返回一个最小值以确保显示
    if (change == 0 && displayValueChange > 0) {
      return 0.001;
    }
    return change;
  }

  /// 创建副本并更新字段
  ProjectProgressChange copyWith({
    String? id,
    String? projectId,
    DateTime? timestamp,
    double? previousValue,
    double? newValue,
    double? previousProgress,
    double? newProgress,
    ProgressChangeSource? source,
    String? focusRecordId,
  }) {
    return ProjectProgressChange(
      id: id ?? this.id,
      projectId: projectId ?? this.projectId,
      timestamp: timestamp ?? this.timestamp,
      previousValue: previousValue ?? this.previousValue,
      newValue: newValue ?? this.newValue,
      previousProgress: previousProgress ?? this.previousProgress,
      newProgress: newProgress ?? this.newProgress,
      source: source ?? this.source,
      focusRecordId: focusRecordId ?? this.focusRecordId,
    );
  }
}
