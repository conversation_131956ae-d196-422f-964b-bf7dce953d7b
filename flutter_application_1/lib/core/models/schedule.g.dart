// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ScheduleAdapter extends TypeAdapter<Schedule> {
  @override
  final int typeId = 20;

  @override
  Schedule read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Schedule(
      id: fields[0] as String,
      title: fields[1] as String,
      description: fields[2] as String?,
      type: fields[3] as ScheduleType,
      createdAt: fields[4] as DateTime,
      date: fields[5] as DateTime?,
      time: fields[6] as DateTime?,
      endDate: fields[12] as DateTime?,
      isRepeat: fields[7] as bool,
      repeatType: fields[8] as RepeatType?,
      repeatDays: (fields[9] as List?)?.cast<int>(),
      status: fields[10] as ScheduleStatus,
      completedDates: (fields[11] as List?)?.cast<DateTime>(),
    );
  }

  @override
  void write(BinaryWriter writer, Schedule obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.type)
      ..writeByte(4)
      ..write(obj.createdAt)
      ..writeByte(5)
      ..write(obj.date)
      ..writeByte(6)
      ..write(obj.time)
      ..writeByte(12)
      ..write(obj.endDate)
      ..writeByte(7)
      ..write(obj.isRepeat)
      ..writeByte(8)
      ..write(obj.repeatType)
      ..writeByte(9)
      ..write(obj.repeatDays)
      ..writeByte(10)
      ..write(obj.status)
      ..writeByte(11)
      ..write(obj.completedDates);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ScheduleAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ScheduleTypeAdapter extends TypeAdapter<ScheduleType> {
  @override
  final int typeId = 17;

  @override
  ScheduleType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ScheduleType.plan;
      case 1:
        return ScheduleType.routine;
      case 2:
        return ScheduleType.todo;
      default:
        return ScheduleType.plan;
    }
  }

  @override
  void write(BinaryWriter writer, ScheduleType obj) {
    switch (obj) {
      case ScheduleType.plan:
        writer.writeByte(0);
        break;
      case ScheduleType.routine:
        writer.writeByte(1);
        break;
      case ScheduleType.todo:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ScheduleTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RepeatTypeAdapter extends TypeAdapter<RepeatType> {
  @override
  final int typeId = 18;

  @override
  RepeatType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return RepeatType.daily;
      case 1:
        return RepeatType.weekly;
      case 2:
        return RepeatType.monthly;
      case 3:
        return RepeatType.custom;
      default:
        return RepeatType.daily;
    }
  }

  @override
  void write(BinaryWriter writer, RepeatType obj) {
    switch (obj) {
      case RepeatType.daily:
        writer.writeByte(0);
        break;
      case RepeatType.weekly:
        writer.writeByte(1);
        break;
      case RepeatType.monthly:
        writer.writeByte(2);
        break;
      case RepeatType.custom:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RepeatTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ScheduleStatusAdapter extends TypeAdapter<ScheduleStatus> {
  @override
  final int typeId = 19;

  @override
  ScheduleStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ScheduleStatus.pending;
      case 1:
        return ScheduleStatus.completed;
      case 2:
        return ScheduleStatus.missed;
      default:
        return ScheduleStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, ScheduleStatus obj) {
    switch (obj) {
      case ScheduleStatus.pending:
        writer.writeByte(0);
        break;
      case ScheduleStatus.completed:
        writer.writeByte(1);
        break;
      case ScheduleStatus.missed:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ScheduleStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
