import 'package:hive/hive.dart';

part 'focus_record.g.dart';

@HiveType(typeId: 21)
enum FocusRecordStatus {
  @HiveField(0)
  completed,  // 完成

  @HiveField(1)
  interrupted,  // 中断

  @HiveField(2)
  paused,  // 暂停（未完成）
}

@HiveType(typeId: 22)
class FocusRecord {
  @HiveField(0)
  final String id;  // 唯一标识符

  @HiveField(1)
  final String projectId;  // 关联的项目ID

  @HiveField(2)
  final String subjectId;  // 关联的科目ID

  @HiveField(3)
  final DateTime startTime;  // 开始时间

  @HiveField(4)
  final DateTime endTime;  // 结束时间

  @HiveField(5)
  final int durationSeconds;  // 持续时间（秒）

  @HiveField(6)
  final bool isCountdown;  // 是否为倒计时模式

  @HiveField(7)
  final int? plannedDurationMinutes;  // 计划持续时间（分钟，仅倒计时模式）

  @HiveField(8)
  final FocusRecordStatus status;  // 状态

  @HiveField(9)
  final int interruptionCount;  // 中断次数

  @HiveField(10)
  final String? notes;  // 备注

  @HiveField(11)
  final Map<String, dynamic>? additionalData;  // 额外数据（可扩展）

  // 构造函数
  FocusRecord({
    required this.id,
    required this.projectId,
    required this.subjectId,
    required this.startTime,
    required this.endTime,
    required this.durationSeconds,
    required this.isCountdown,
    this.plannedDurationMinutes,
    required this.status,
    this.interruptionCount = 0,
    this.notes,
    this.additionalData,
  });

  // 计算专注时长（小时）
  double get durationHours => durationSeconds / 3600;

  // 计算完成率（仅倒计时模式）
  double? get completionRate {
    if (!isCountdown || plannedDurationMinutes == null || plannedDurationMinutes! <= 0) {
      return null;
    }
    return durationSeconds / (plannedDurationMinutes! * 60);
  }

  // 是否在工作时间（8:00-18:00）
  bool get isDuringWorkHours {
    final hour = startTime.hour;
    return hour >= 8 && hour < 18;
  }

  // 获取专注的日期（不含时间）
  DateTime get focusDate {
    return DateTime(startTime.year, startTime.month, startTime.day);
  }

  // 获取专注的周数（以周一为一周的开始）
  int get weekOfYear {
    final date = startTime;
    final firstDayOfYear = DateTime(date.year, 1, 1);
    final daysOffset = firstDayOfYear.weekday - 1;
    final firstMonday = daysOffset <= 0
        ? firstDayOfYear.subtract(Duration(days: -daysOffset))
        : firstDayOfYear.add(Duration(days: 7 - daysOffset));

    final diffDays = date.difference(firstMonday).inDays;
    return (diffDays / 7).floor() + 1;
  }

  // 获取专注的月份
  int get month => startTime.month;

  // 从JSON创建
  factory FocusRecord.fromJson(Map<String, dynamic> json) {
    return FocusRecord(
      id: json['id'],
      projectId: json['projectId'],
      subjectId: json['subjectId'],
      startTime: DateTime.parse(json['startTime']),
      endTime: DateTime.parse(json['endTime']),
      durationSeconds: json['durationSeconds'],
      isCountdown: json['isCountdown'],
      plannedDurationMinutes: json['plannedDurationMinutes'],
      status: FocusRecordStatus.values[json['status']],
      interruptionCount: json['interruptionCount'] ?? 0,
      notes: json['notes'],
      additionalData: json['additionalData'],
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'projectId': projectId,
      'subjectId': subjectId,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'durationSeconds': durationSeconds,
      'isCountdown': isCountdown,
      'plannedDurationMinutes': plannedDurationMinutes,
      'status': status.index,
      'interruptionCount': interruptionCount,
      'notes': notes,
      'additionalData': additionalData,
    };
  }
}
