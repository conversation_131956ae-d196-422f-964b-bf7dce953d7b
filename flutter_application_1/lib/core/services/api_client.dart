import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../utils/storage_utils.dart';
import '../utils/event_bus.dart';
import '../utils/device_utils.dart';

class ApiClient {
  // 根据环境变量或构建配置选择不同的API基础URL
  static String get baseUrl {
    // 在测试环境中使用本地测试服务器
    if (const bool.fromEnvironment('TESTING', defaultValue: false)) {
      return 'http://localhost:3000/api';
    }

    // 在开发环境中，由于模拟器可能无法访问localhost，直接使用生产服务器
    if (kDebugMode) {
      // 可以通过环境变量控制是否使用本地服务器
      const useLocalhost = bool.fromEnvironment('USE_LOCALHOST', defaultValue: false);
      if (useLocalhost) {
        return 'http://localhost:3000/api';
      }
      return 'https://arborflame.com/api';
    }

    // 在生产环境中使用生产服务器
    return 'https://arborflame.com/api';
  }
  static const int connectTimeout = 15000; // 15秒 - 增加连接超时时间
  static const int receiveTimeout = 30000; // 30秒 - 增加接收超时时间

  late Dio _dio;

  // 单例模式
  static final ApiClient _instance = ApiClient._internal();
  factory ApiClient() => _instance;

  ApiClient._internal() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: Duration(milliseconds: connectTimeout),
      receiveTimeout: Duration(milliseconds: receiveTimeout),
      contentType: 'application/json',
    ));

    // 添加拦截器
    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: _handleRequest,
      onResponse: _handleResponse,
      onError: _handleError,
    ));
  }

  // 请求拦截器
  void _handleRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    debugPrint('发送请求: ${options.method} ${options.path}');
    debugPrint('请求头: ${options.headers}');
    debugPrint('请求URL: ${options.baseUrl}${options.path}');

    // 添加认证令牌
    final token = await StorageUtils.getToken();
    debugPrint('获取到的令牌: ${token != null ? '有效' : '无效'}');
    if (token != null) {
      debugPrint('令牌值: $token');
    }

    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
      debugPrint('已添加认证令牌，更新后的请求头: ${options.headers}');
    } else {
      debugPrint('无认证令牌，请检查用户是否已登录');

      // 尝试获取用户信息，检查用户是否已登录
      final userInfo = await StorageUtils.getUserInfo();
      debugPrint('用户信息: ${userInfo != null ? '存在' : '不存在'}');
    }

    // 打印请求数据，便于调试
    if (options.data != null) {
      debugPrint('请求数据: ${options.data}');
    }

    return handler.next(options);
  }

  // 响应拦截器
  void _handleResponse(Response response, ResponseInterceptorHandler handler) {
    debugPrint('收到响应: ${response.statusCode} ${response.requestOptions.path}');

    // 打印响应数据，便于调试
    if (response.data != null) {
      debugPrint('响应数据: ${response.data}');
    }

    return handler.next(response);
  }

  // 错误拦截器
  void _handleError(DioException e, ErrorInterceptorHandler handler) async {
    debugPrint('API错误: ${e.message}, 状态码: ${e.response?.statusCode}, 路径: ${e.requestOptions.path}');

    // 处理401错误（未授权）
    if (e.response?.statusCode == 401) {
      // 检查错误类型
      final errorData = e.response?.data;
      final errorCode = errorData != null && errorData is Map ? errorData['code'] : null;

      debugPrint('收到401未授权错误，错误代码: $errorCode');

      // 如果是令牌过期错误，尝试刷新令牌
      if (errorCode == 'token_expired' || errorCode == 'invalid_token') {
        debugPrint('令牌过期或无效，尝试刷新令牌');

        // 尝试刷新令牌
        if (await _refreshToken()) {
          debugPrint('令牌刷新成功，重试原始请求');

          // 重试请求
          final options = e.requestOptions;
          final token = await StorageUtils.getToken();
          options.headers['Authorization'] = 'Bearer $token';

          debugPrint('使用新令牌重试请求: ${options.path}');

          try {
            final response = await _dio.fetch(options);
            debugPrint('重试请求成功: ${response.statusCode}');
            return handler.resolve(response);
          } catch (retryError) {
            debugPrint('重试请求失败: $retryError');
            return handler.next(retryError as DioException);
          }
        } else {
          debugPrint('令牌刷新失败，清除认证信息并通知应用');

          // 刷新令牌失败，清除认证信息
          await StorageUtils.clearAuthData();

          // 通知应用重定向到登录页面
          eventBus.fire(EventType.authTokenExpired);
          debugPrint('已发送令牌过期事件');
        }
      } else {
        debugPrint('未知的401错误，清除认证信息并通知应用');

        // 清除认证信息
        await StorageUtils.clearAuthData();

        // 通知应用重定向到登录页面
        eventBus.fire(EventType.authTokenExpired);
        debugPrint('已发送令牌过期事件');
      }
    } else if (e.response?.statusCode == 400) {
      // 处理验证错误
      final errorData = e.response?.data;
      debugPrint('400错误详细信息: $errorData');

      if (errorData != null && errorData is Map) {
        // 记录所有可能的错误字段
        debugPrint('错误消息: ${errorData['message']}');
        debugPrint('错误代码: ${errorData['code']}');
        debugPrint('错误详情: ${errorData['details']}');
        debugPrint('错误状态: ${errorData['status']}');

        final errors = errorData['errors'];
        if (errors != null && errors is List) {
          debugPrint('验证错误列表:');
          for (var error in errors) {
            if (error is Map) {
              debugPrint('  字段: ${error['field']}, 消息: ${error['message']}');
            } else {
              debugPrint('  错误: $error');
            }
          }
        } else if (errors != null) {
          debugPrint('验证错误对象: $errors');
        }

        // 检查是否有特定的字段验证错误
        if (errorData.containsKey('fieldErrors')) {
          debugPrint('字段错误: ${errorData['fieldErrors']}');
        }
      } else {
        debugPrint('收到400错误，无法解析错误数据: $errorData');
      }
    } else if (e.response?.statusCode == 429) {
      final errorData = e.response?.data;
      debugPrint('收到429错误，请求过于频繁: $errorData');

      // 提取重试等待时间和错误信息
      int retryAfter = 60; // 默认60秒
      String message = '请求过于频繁，请稍后再试';

      if (errorData is Map) {
        if (errorData['message'] != null) {
          message = errorData['message'].toString();
        }

        if (errorData['errors'] is Map) {
          final errors = errorData['errors'] as Map;
          if (errors['retryAfter'] is int) {
            retryAfter = errors['retryAfter'];
          }
        }
      }

      debugPrint('429错误详情: $message, 重试等待时间: $retryAfter秒');

      // 通知应用请求过于频繁
      eventBus.fire(EventType.apiRateLimited);
    } else {
      final errorData = e.response?.data;
      if (errorData != null && errorData is Map) {
        debugPrint('错误信息: ${errorData['message']}');
        debugPrint('错误代码: ${errorData['code']}');
      } else {
        debugPrint('其他错误: $errorData');
      }
    }

    return handler.next(e);
  }

  // 获取设备信息
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    return await DeviceUtils.getDeviceInfo();
  }

  // 刷新令牌
  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await StorageUtils.getRefreshToken();
      if (refreshToken == null) {
        debugPrint('刷新令牌不存在');
        return false;
      }

      // 获取设备信息
      final deviceInfo = await DeviceUtils.getDeviceInfo();

      // 打印刷新令牌请求，便于调试
      debugPrint('尝试刷新令牌: $refreshToken');
      debugPrint('设备信息: $deviceInfo');

      // 使用新的邮箱认证刷新令牌端点
      final response = await _dio.post(
        '/auth/refresh-token',
        data: {
          'refreshToken': refreshToken,
          'deviceInfo': deviceInfo,
        },
        options: Options(headers: {'Authorization': null}),
      );

      debugPrint('刷新令牌响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final token = response.data['data']['accessToken'];
        final newRefreshToken = response.data['data']['refreshToken'];

        debugPrint('获取到新的访问令牌和刷新令牌');
        await StorageUtils.saveToken(token);
        await StorageUtils.saveRefreshToken(newRefreshToken);

        return true;
      } else {
        debugPrint('刷新令牌响应不是200: ${response.statusCode}');
        debugPrint('响应数据: ${response.data}');
      }

      return false;
    } catch (e) {
      debugPrint('刷新令牌失败: $e');
      if (e is DioException) {
        debugPrint('DioException: ${e.response?.data}');

        // 如果新端点失败，尝试旧端点（兼容性处理）
        try {
          debugPrint('尝试使用旧的刷新令牌端点');
          final refreshToken = await StorageUtils.getRefreshToken();
          if (refreshToken == null) return false;

          final deviceInfo = await DeviceUtils.getDeviceInfo();

          final response = await _dio.post(
            '/auth/refresh',
            data: {
              'refreshToken': refreshToken,
              'deviceInfo': deviceInfo,
            },
            options: Options(headers: {'Authorization': null}),
          );

          if (response.statusCode == 200) {
            final token = response.data['data']['accessToken'];
            final newRefreshToken = response.data['data']['refreshToken'];

            await StorageUtils.saveToken(token);
            await StorageUtils.saveRefreshToken(newRefreshToken);

            return true;
          }
        } catch (retryError) {
          debugPrint('使用旧端点刷新令牌也失败: $retryError');
        }
      }
      return false;
    }
  }

  // HTTP请求方法
  Future<Response> get(String path, {Map<String, dynamic>? queryParameters, Options? options}) {
    return _dio.get(path, queryParameters: queryParameters, options: options);
  }

  Future<Response> post(String path, {dynamic data, Options? options}) {
    return _dio.post(path, data: data, options: options);
  }

  Future<Response> put(String path, {dynamic data, Options? options}) {
    return _dio.put(path, data: data, options: options);
  }

  Future<Response> delete(String path, {dynamic data, Map<String, dynamic>? queryParameters, Options? options}) {
    return _dio.delete(path, data: data, queryParameters: queryParameters, options: options);
  }
}
