import 'package:flutter/foundation.dart';
import '../models/focus_session.dart';
import 'preferences_service.dart';

/// 专注会话服务
/// 负责专注会话的持久化管理，支持后台计时和状态恢复
class FocusSessionService {
  final PreferencesService _preferencesService = PreferencesService();

  /// 保存专注会话
  Future<void> saveFocusSession(FocusSession session) async {
    try {
      final jsonString = session.toJsonString();
      await _preferencesService.saveFocusSession(jsonString);
      debugPrint('专注会话已保存: ${session.id}');
    } catch (e) {
      debugPrint('保存专注会话失败: $e');
      rethrow;
    }
  }

  /// 获取当前专注会话
  Future<FocusSession?> getCurrentFocusSession() async {
    try {
      final jsonString = await _preferencesService.getFocusSession();
      if (jsonString == null) return null;

      final session = FocusSession.fromJsonString(jsonString);

      // 检查会话是否有效
      if (!session.isValid) {
        debugPrint('专注会话已过期，自动清理: ${session.id}');
        await clearFocusSession();
        return null;
      }

      return session;
    } catch (e) {
      debugPrint('获取专注会话失败: $e');

      // 尝试从备份恢复
      try {
        final backupJsonString = await _preferencesService.getFocusSessionBackup();
        if (backupJsonString != null) {
          debugPrint('尝试从备份恢复专注会话');
          final session = FocusSession.fromJsonString(backupJsonString);

          if (session.isValid) {
            // 恢复成功，重新保存
            await saveFocusSession(session);
            return session;
          }
        }
      } catch (backupError) {
        debugPrint('从备份恢复专注会话失败: $backupError');
      }

      // 恢复失败，清理损坏的数据
      await clearFocusSession();
      return null;
    }
  }

  /// 更新专注会话
  Future<void> updateFocusSession(FocusSession session) async {
    final updatedSession = session.copyWith(updatedAt: DateTime.now());
    await saveFocusSession(updatedSession);
  }

  /// 暂停专注会话
  Future<FocusSession?> pauseFocusSession() async {
    final session = await getCurrentFocusSession();
    if (session == null) return null;

    final pausedSession = session.pause();
    await saveFocusSession(pausedSession);
    debugPrint('专注会话已暂停: ${session.id}');
    return pausedSession;
  }

  /// 恢复专注会话
  Future<FocusSession?> resumeFocusSession() async {
    final session = await getCurrentFocusSession();
    if (session == null) return null;

    final resumedSession = session.resume();
    await saveFocusSession(resumedSession);
    debugPrint('专注会话已恢复: ${session.id}');
    return resumedSession;
  }

  /// 清除专注会话
  Future<void> clearFocusSession() async {
    try {
      await _preferencesService.clearFocusSession();
      debugPrint('专注会话已清除');
    } catch (e) {
      debugPrint('清除专注会话失败: $e');
      rethrow;
    }
  }

  /// 检查是否存在专注会话
  Future<bool> hasFocusSession() async {
    try {
      return await _preferencesService.hasFocusSession();
    } catch (e) {
      debugPrint('检查专注会话存在性失败: $e');
      return false;
    }
  }

  /// 创建新的专注会话
  FocusSession createFocusSession({
    required String subjectId,
    required String projectId,
    required bool isCountdown,
    int? countdownDurationSeconds,
  }) {
    final now = DateTime.now();
    final id = '${now.millisecondsSinceEpoch}_${subjectId}_$projectId';

    return FocusSession(
      id: id,
      subjectId: subjectId,
      projectId: projectId,
      startTime: now,
      isCountdown: isCountdown,
      countdownDurationSeconds: countdownDurationSeconds,
      isPaused: false,
      pausedTime: null,
      pausedDurationSeconds: 0,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// 验证会话数据完整性
  Future<bool> validateSessionIntegrity() async {
    try {
      final session = await getCurrentFocusSession();
      if (session == null) return true; // 没有会话也算正常

      // 检查基本字段
      if (session.id.isEmpty ||
          session.subjectId.isEmpty ||
          session.projectId.isEmpty) {
        debugPrint('专注会话数据不完整');
        return false;
      }

      // 检查时间逻辑
      if (session.startTime.isAfter(DateTime.now())) {
        debugPrint('专注会话开始时间异常');
        return false;
      }

      // 检查暂停逻辑
      if (session.isPaused && session.pausedTime == null) {
        debugPrint('专注会话暂停状态异常');
        return false;
      }

      // 检查倒计时逻辑
      if (session.isCountdown && session.countdownDurationSeconds == null) {
        debugPrint('专注会话倒计时配置异常');
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('验证专注会话完整性失败: $e');
      return false;
    }
  }

  /// 获取会话统计信息（用于调试）
  Future<Map<String, dynamic>> getSessionStats() async {
    try {
      final session = await getCurrentFocusSession();
      if (session == null) {
        return {'hasSession': false};
      }

      return {
        'hasSession': true,
        'sessionId': session.id,
        'isCountdown': session.isCountdown,
        'isPaused': session.isPaused,
        'elapsedSeconds': session.currentElapsedSeconds,
        'remainingSeconds': session.remainingSeconds,
        'isValid': session.isValid,
        'createdAt': session.createdAt.toIso8601String(),
        'updatedAt': session.updatedAt.toIso8601String(),
      };
    } catch (e) {
      return {'hasSession': false, 'error': e.toString()};
    }
  }
}
