import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'api_client.dart';
import '../config/test_config.dart';
import '../utils/storage_utils.dart';

/// 订阅服务
/// 处理用户订阅相关的功能
class SubscriptionService {
  final ApiClient _apiClient = ApiClient();

  /// 获取价格计划列表
  Future<List<Map<String, dynamic>>> getSubscriptionPlans() async {
    try {
      // 检查是否处于测试模式
      if (TestConfig.isTestMode) {
        // 返回测试数据
        return [
          {
            'id': 'plan_monthly',
            'name': '月度订阅',
            'description': '每月自动续费',
            'price': 12.99,
            'currency': 'CNY',
            'interval': 'month',
            'intervalCount': 1,
            'trialPeriodDays': 7,
            'features': ['云备份', '多端同步', '高级统计'],
            'isActive': true,
          },
          {
            'id': 'plan_yearly',
            'name': '年度订阅',
            'description': '每年自动续费，享受8折优惠',
            'price': 99.99,
            'currency': 'CNY',
            'interval': 'year',
            'intervalCount': 1,
            'trialPeriodDays': 7,
            'features': ['云备份', '多端同步', '高级统计', '优先支持'],
            'isActive': true,
          },
        ];
      }

      // 正常流程
      final response = await _apiClient.get('/subscriptions/plans');

      if (response.statusCode == 200) {
        final items = response.data['data'] as List;
        return items.map((item) => item as Map<String, dynamic>).toList();
      }

      return [];
    } catch (e) {
      debugPrint('获取价格计划失败: $e');

      // 在测试模式下返回测试数据
      if (TestConfig.isTestMode) {
        return [
          {
            'id': 'plan_monthly',
            'name': '月度订阅',
            'description': '每月自动续费',
            'price': 12.99,
            'currency': 'CNY',
            'interval': 'month',
            'intervalCount': 1,
            'trialPeriodDays': 7,
            'features': ['云备份', '多端同步', '高级统计'],
            'isActive': true,
          },
          {
            'id': 'plan_yearly',
            'name': '年度订阅',
            'description': '每年自动续费，享受8折优惠',
            'price': 99.99,
            'currency': 'CNY',
            'interval': 'year',
            'intervalCount': 1,
            'trialPeriodDays': 7,
            'features': ['云备份', '多端同步', '高级统计', '优先支持'],
            'isActive': true,
          },
        ];
      }

      return [];
    }
  }

  /// 获取当前订阅信息
  Future<Map<String, dynamic>?> getCurrentSubscription() async {
    try {
      final response = await _apiClient.get('/subscriptions/active');

      if (response.statusCode == 200) {
        return response.data['data'];
      }

      return null;
    } catch (e) {
      debugPrint('获取当前订阅失败: $e');
      return null;
    }
  }

  /// 获取订阅历史
  Future<List<Map<String, dynamic>>> getSubscriptionHistory() async {
    try {
      final response = await _apiClient.get('/subscriptions/history');

      if (response.statusCode == 200) {
        final items = response.data['data'] as List;
        return items.map((item) => item as Map<String, dynamic>).toList();
      }

      return [];
    } catch (e) {
      debugPrint('获取订阅历史失败: $e');
      return [];
    }
  }

  /// 创建订阅
  Future<Map<String, dynamic>?> createSubscription(String planId) async {
    try {
      final response = await _apiClient.post('/subscriptions', data: {
        'planId': planId,
      });

      if (response.statusCode == 201) {
        return response.data['data'];
      }

      return null;
    } catch (e) {
      debugPrint('创建订阅失败: $e');
      return null;
    }
  }

  /// 处理苹果支付
  Future<bool> processApplePayment(String receipt) async {
    try {
      final response = await _apiClient.post('/subscriptions/apple', data: {
        'receipt': receipt,
      });

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('处理苹果支付失败: $e');
      return false;
    }
  }

  /// 取消订阅
  Future<bool> cancelSubscription(String subscriptionId, {String? reason}) async {
    try {
      final response = await _apiClient.put('/subscriptions/$subscriptionId/cancel', data: {
        if (reason != null) 'reason': reason,
      });

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('取消订阅失败: $e');
      return false;
    }
  }

  /// 获取支付记录
  Future<List<Map<String, dynamic>>> getPaymentHistory() async {
    try {
      final response = await _apiClient.get('/subscriptions/payments');

      if (response.statusCode == 200) {
        final items = response.data['data'] as List;
        return items.map((item) => item as Map<String, dynamic>).toList();
      }

      return [];
    } catch (e) {
      debugPrint('获取支付记录失败: $e');
      return [];
    }
  }

  /// 检查用户是否为付费用户
  Future<bool> isPremiumUser() async {
    try {
      // 检查是否处于测试模式
      if (TestConfig.isTestMode) {
        // 在测试模式下，测试账号始终为付费用户
        final userJson = await StorageUtils.getUserInfo();
        if (userJson != null) {
          final user = jsonDecode(userJson);
          if (user['email'] == TestConfig.testAccount['email']) {
            return true;
          }
        }
      }

      // 正常流程
      final subscription = await getCurrentSubscription();
      return subscription != null && subscription['status'] == 'active';
    } catch (e) {
      debugPrint('检查付费状态失败: $e');

      // 在测试模式下，测试账号始终为付费用户
      if (TestConfig.isTestMode) {
        final userJson = await StorageUtils.getUserInfo();
        if (userJson != null) {
          final user = jsonDecode(userJson);
          if (user['email'] == TestConfig.testAccount['email']) {
            return true;
          }
        }
      }

      return false;
    }
  }
}
