import 'dart:convert';
import 'package:file_selector/file_selector.dart' as file_selector;
import 'package:file_selector/file_selector.dart' show XFile, XTypeGroup;
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

/// iOS 原生文件选择器服务
/// 提供文件选择、保存等功能
class FileSelectorService {
  /// 选择 JSON 备份文件
  static Future<String?> pickJsonBackupFile() async {
    try {
      // 定义 JSON 文件类型
      final XTypeGroup jsonTypeGroup = XTypeGroup(
        label: '备份文件',
        extensions: ['json'],
        mimeTypes: ['application/json'],
        uniformTypeIdentifiers: ['public.json'], // iOS UTI
      );

      // 打开文件选择器
      final XFile? file = await file_selector.openFile(
        acceptedTypeGroups: [jsonTypeGroup],
      );

      return file?.path;
    } catch (e) {
      debugPrint('选择备份文件失败: $e');
      return null;
    }
  }

  /// 保存 JSON 备份文件
  static Future<String?> saveJsonBackupFile(String content, {String? suggestedName}) async {
    try {
      // 生成默认文件名
      final defaultName = suggestedName ?? 'backup_${DateTime.now().millisecondsSinceEpoch}.json';

      // 打开保存对话框
      // 注意：在iOS模拟器上，文件选择器可能不完全支持
      // 在真机上测试时应该正常工作
      String? path;
      try {
        // 在iOS上，我们直接使用应用文档目录
        // 这是因为file_selector在iOS模拟器上可能有限制
        final directory = await getApplicationDocumentsDirectory();
        path = '${directory.path}/$defaultName';
      } catch (e) {
        debugPrint('获取保存路径失败: $e');
        return null;
      }

      // 创建 XFile 并保存
      final XFile file = XFile.fromData(
        utf8.encode(content),
        mimeType: 'application/json',
        name: defaultName,
      );

      // 保存文件
      // path在这里已经确定不为null
      await file.saveTo(path);
      return path;
    } catch (e) {
      debugPrint('保存备份文件失败: $e');
      return null;
    }
  }
}
