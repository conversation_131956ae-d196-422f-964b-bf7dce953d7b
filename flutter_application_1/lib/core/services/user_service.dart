import 'dart:io';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../utils/storage_utils.dart';
import './api_client.dart';

/// 用户服务类，处理与用户相关的API请求
class UserService {
  final ApiClient _apiClient;

  // 单例模式
  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;

  UserService._internal() : _apiClient = ApiClient();

  /// 获取用户信息
  Future<User> getUserProfile() async {
    try {
      final response = await _apiClient.get('/users/me');

      if (response.statusCode == 200) {
        final data = response.data['data'];
        debugPrint('获取用户信息成功，数据: $data');

        // 直接构建User对象，绕过类型转换问题
        return User(
          id: data['_id']?.toString() ?? data['id']?.toString() ?? '',
          username: data['username']?.toString() ?? '',
          nickname: data['nickname']?.toString() ?? '用户',
          avatar: data['avatar']?.toString(),
          email: data['email']?.toString() ?? '',
          phone: data['phone']?.toString(),
          createdAt: data['createdAt'] != null
              ? DateTime.tryParse(data['createdAt'].toString()) ?? DateTime.now()
              : DateTime.now(),
          lastLoginAt: data['lastLoginAt'] != null
              ? DateTime.tryParse(data['lastLoginAt'].toString())
              : null,
          accountStatus: data['accountStatus']?.toString() ?? 'active',
          authMethods: _parseAuthMethodsSafe(data['authMethods']),
          membershipType: data['membershipType']?.toString() ?? 'free',
          membershipExpires: data['membershipExpires'] != null
              ? DateTime.tryParse(data['membershipExpires'].toString())
              : null,
          preferences: _parseMapSafe(data['preferences']),
          settings: _parseMapSafe(data['settings']),
          usageStats: _parseMapSafe(data['usageStats']),
        );
      }

      throw Exception('获取用户信息失败: ${response.statusCode}');
    } catch (e) {
      debugPrint('获取用户信息错误: $e');
      rethrow;
    }
  }

  /// 更新用户信息
  Future<User> updateUserProfile({
    String? username,
    String? nickname,
    String? email,
    Map<String, dynamic>? preferences,
    Map<String, dynamic>? settings,
  }) async {
    try {
      // 检查用户是否已登录
      final token = await StorageUtils.getToken();
      if (token == null) {
        debugPrint('更新用户信息失败: 用户未登录');
        throw Exception('用户未登录，请先登录');
      }

      debugPrint('准备更新用户信息，认证令牌: ${token.substring(0, 10)}...');

      final data = <String, dynamic>{};

      if (username != null) data['username'] = username;
      if (nickname != null) data['nickname'] = nickname;
      if (email != null) data['email'] = email;
      if (preferences != null) data['preferences'] = preferences;
      if (settings != null) data['settings'] = settings;

      // 显式添加认证头
      final options = Options(
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      debugPrint('发送更新用户信息请求，数据: $data');
      final response = await _apiClient.put('/users/me', data: data, options: options);

      if (response.statusCode == 200) {
        debugPrint('更新用户信息成功');
        debugPrint('后端响应数据: ${response.data}');

        final responseData = response.data['data'];
        debugPrint('用户数据: $responseData');

        // 直接构建User对象，绕过类型转换问题
        final updatedUser = User(
          id: responseData['_id']?.toString() ?? responseData['id']?.toString() ?? '',
          username: responseData['username']?.toString() ?? '',
          nickname: responseData['nickname']?.toString() ?? '用户',
          avatar: responseData['avatar']?.toString(),
          email: responseData['email']?.toString() ?? '',
          phone: responseData['phone']?.toString(),
          createdAt: responseData['createdAt'] != null
              ? DateTime.tryParse(responseData['createdAt'].toString()) ?? DateTime.now()
              : DateTime.now(),
          lastLoginAt: responseData['lastLoginAt'] != null
              ? DateTime.tryParse(responseData['lastLoginAt'].toString())
              : null,
          accountStatus: responseData['accountStatus']?.toString() ?? 'active',
          authMethods: _parseAuthMethodsSafe(responseData['authMethods']),
          membershipType: responseData['membershipType']?.toString() ?? 'free',
          membershipExpires: responseData['membershipExpires'] != null
              ? DateTime.tryParse(responseData['membershipExpires'].toString())
              : null,
          preferences: _parseMapSafe(responseData['preferences']),
          settings: _parseMapSafe(responseData['settings']),
          usageStats: _parseMapSafe(responseData['usageStats']),
        );

        // 🔧 关键修复：更新本地缓存
        try {
          final userJson = jsonEncode(updatedUser.toJson());
          await StorageUtils.saveUserInfo(userJson);
          debugPrint('✅ 本地用户缓存已更新');
        } catch (e) {
          debugPrint('⚠️ 更新本地缓存失败: $e');
          // 不影响主流程，继续返回更新后的用户数据
        }

        return updatedUser;
      }

      throw Exception('更新用户信息失败: ${response.statusCode}');
    } catch (e) {
      debugPrint('更新用户信息错误: $e');
      rethrow;
    }
  }

  /// 上传用户头像
  Future<String> uploadAvatar(File file) async {
    try {
      // 检查用户是否已登录
      final token = await StorageUtils.getToken();
      if (token == null) {
        debugPrint('上传头像失败: 用户未登录');
        throw Exception('用户未登录，请先登录');
      }

      debugPrint('准备上传头像，认证令牌: ${token.substring(0, 10)}...');

      // 创建FormData
      final formData = FormData.fromMap({
        'avatar': await MultipartFile.fromFile(
          file.path,
          filename: 'avatar_${DateTime.now().millisecondsSinceEpoch}.jpg',
        ),
      });

      // 显式添加认证头
      final options = Options(
        headers: {
          'Authorization': 'Bearer $token',
        },
        contentType: 'multipart/form-data',
      );

      debugPrint('发送上传头像请求');
      final response = await _apiClient.put(
        '/users/me/avatar',
        data: formData,
        options: options,
      );

      if (response.statusCode == 200) {
        debugPrint('上传头像成功');
        final avatarUrl = response.data['data']['avatar'];
        return avatarUrl;
      }

      throw Exception('上传头像失败: ${response.statusCode}');
    } catch (e) {
      debugPrint('上传头像错误: $e');
      rethrow;
    }
  }

  /// 更新用户密码
  Future<void> updatePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      // 检查用户是否已登录
      final token = await StorageUtils.getToken();
      if (token == null) {
        debugPrint('更新密码失败: 用户未登录');
        throw Exception('用户未登录，请先登录');
      }

      debugPrint('准备更新密码，认证令牌: ${token.substring(0, 10)}...');

      // 显式添加认证头
      final options = Options(
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      final response = await _apiClient.put(
        '/users/me/password',
        data: {
          'currentPassword': currentPassword,
          'newPassword': newPassword,
        },
        options: options,
      );

      if (response.statusCode != 200) {
        throw Exception('更新密码失败: ${response.statusCode}');
      }

      debugPrint('更新密码成功');
    } catch (e) {
      debugPrint('更新密码错误: $e');
      rethrow;
    }
  }

  /// 用户登录
  Future<Map<String, dynamic>> login({
    required String phone,
    required String password,
  }) async {
    try {
      final response = await _apiClient.post(
        '/auth/login',
        data: {
          'phone': phone,
          'password': password,
          'deviceInfo': _getDeviceInfo(),
        },
      );

      if (response.statusCode == 200) {
        // 保存令牌
        final token = response.data['data']['accessToken'];
        final refreshToken = response.data['data']['refreshToken'];
        await StorageUtils.saveToken(token);
        await StorageUtils.saveRefreshToken(refreshToken);

        debugPrint('登录成功，已保存令牌: $token');

        return response.data['data'];
      }

      throw Exception('登录失败: ${response.statusCode}');
    } catch (e) {
      debugPrint('登录错误: $e');
      rethrow;
    }
  }

  /// 用户注册
  Future<Map<String, dynamic>> register({
    required String phone,
    required String password,
    String? username,
    String? nickname,
  }) async {
    try {
      final data = {
        'phone': phone,
        'password': password,
        'deviceInfo': _getDeviceInfo(),
      };

      if (username != null) data['username'] = username;
      if (nickname != null) data['nickname'] = nickname;

      final response = await _apiClient.post(
        '/auth/register',
        data: data,
      );

      if (response.statusCode == 201) {
        return response.data['data'];
      }

      throw Exception('注册失败: ${response.statusCode}');
    } catch (e) {
      debugPrint('注册错误: $e');
      rethrow;
    }
  }

  /// 获取设备信息
  Map<String, dynamic> _getDeviceInfo() {
    return {
      'type': 'mobile',
      'platform': kIsWeb ? 'web' : Platform.operatingSystem,
      'deviceModel': kIsWeb ? 'browser' : Platform.localHostname,
      'appVersion': '1.0.0', // 应用版本号
    };
  }

  /// 安全解析认证方式
  List<String> _parseAuthMethodsSafe(dynamic authMethodsData) {
    if (authMethodsData == null) {
      return ['email'];
    }

    // 如果是数组格式（旧格式）
    if (authMethodsData is List) {
      return List<String>.from(authMethodsData);
    }

    // 如果是对象格式（新格式）
    if (authMethodsData is Map) {
      List<String> methods = [];
      authMethodsData.forEach((key, value) {
        if (value == true) {
          methods.add(key.toString());
        }
      });
      return methods.isNotEmpty ? methods : ['email'];
    }

    return ['email'];
  }

  /// 安全解析Map数据
  Map<String, dynamic> _parseMapSafe(dynamic mapData) {
    if (mapData == null) {
      return <String, dynamic>{};
    }

    if (mapData is Map<String, dynamic>) {
      return mapData;
    }

    if (mapData is Map) {
      final result = <String, dynamic>{};
      mapData.forEach((key, value) {
        result[key.toString()] = value;
      });
      return result;
    }

    return <String, dynamic>{};
  }


}
