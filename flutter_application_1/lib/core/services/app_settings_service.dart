import 'package:shared_preferences/shared_preferences.dart';
import 'dart:developer' as developer;

/// 应用设置服务
/// 用于管理应用的全局设置和状态
class AppSettingsService {
  // 首次启动标志的键
  static const String firstLaunchKey = 'is_first_launch';
  // 引导页完成标志的键
  static const String onboardingCompletedKey = 'is_onboarding_completed';
  // 最后一次启动时间的键
  static const String lastLaunchTimeKey = 'last_launch_time';

  /// 检查应用是否是首次启动
  Future<bool> isFirstLaunch() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // 如果键不存在，则返回true表示首次启动
      final isFirst = prefs.getBool(firstLaunchKey) ?? true;
      developer.log('检查是否首次启动: $isFirst', name: 'AppSettingsService');
      return isFirst;
    } catch (e) {
      developer.log('检查首次启动状态出错', error: e, name: 'AppSettingsService');
      // 出错时默认为非首次启动，避免重复显示引导页
      return false;
    }
  }

  /// 标记应用已启动
  Future<void> markAppLaunched() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(firstLaunchKey, false);
      // 记录启动时间
      await prefs.setString(lastLaunchTimeKey, DateTime.now().toIso8601String());
      developer.log('已标记应用启动', name: 'AppSettingsService');
    } catch (e) {
      developer.log('标记应用启动状态出错', error: e, name: 'AppSettingsService');
    }
  }

  /// 检查引导页是否已完成
  Future<bool> isOnboardingCompleted() async {
    try {
      // 根据需求，取消引导页，直接返回true表示引导页已完成
      developer.log('引导页已取消，直接返回已完成状态', name: 'AppSettingsService');
      return true;
    } catch (e) {
      developer.log('检查引导页完成状态出错', error: e, name: 'AppSettingsService');
      // 出错时默认为已完成，避免卡在引导页
      return true;
    }
  }

  /// 标记引导页已完成
  Future<void> markOnboardingCompleted() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(onboardingCompletedKey, true);
      developer.log('已标记引导页完成', name: 'AppSettingsService');
    } catch (e) {
      developer.log('标记引导页完成状态出错', error: e, name: 'AppSettingsService');
    }
  }

  /// 重置引导页状态（用于测试）
  Future<void> resetOnboardingStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(onboardingCompletedKey, false);
      developer.log('已重置引导页状态', name: 'AppSettingsService');
    } catch (e) {
      developer.log('重置引导页状态出错', error: e, name: 'AppSettingsService');
    }
  }

  /// 获取上次启动时间
  Future<DateTime?> getLastLaunchTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timeString = prefs.getString(lastLaunchTimeKey);
      if (timeString != null) {
        return DateTime.parse(timeString);
      }
      return null;
    } catch (e) {
      developer.log('获取上次启动时间出错', error: e, name: 'AppSettingsService');
      return null;
    }
  }
}
