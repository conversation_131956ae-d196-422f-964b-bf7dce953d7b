import 'dart:async';
import 'package:flutter/foundation.dart';

/// 计时器服务类，提供倒计时功能
class TimerService {
  Timer? _timer;
  int _remainingSeconds = 0;
  bool _isRunning = false;
  Function(int)? onTick;
  VoidCallback? onComplete;

  /// 获取当前剩余秒数
  int get remainingSeconds => _remainingSeconds;

  /// 获取当前计时器是否在运行
  bool get isRunning => _isRunning;

  /// 获取格式化后的时间字符串 (mm:ss)
  String get remainingTime {
    final minutes = _remainingSeconds ~/ 60;
    final seconds = _remainingSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// 开始计时
  /// [minutes] 设置的分钟数
  void startTimer(int minutes) {
    _remainingSeconds = minutes * 60;
    _timer?.cancel();
    _isRunning = true;
    _timer = Timer.periodic(
      const Duration(seconds: 1),
      (timer) {
        if (_remainingSeconds > 0) {
          _remainingSeconds--;
          onTick?.call(_remainingSeconds);
        } else {
          timer.cancel();
          _isRunning = false;
          onComplete?.call();
        }
      },
    );
  }

  /// 暂停计时
  void pauseTimer() {
    _timer?.cancel();
    _isRunning = false;
  }

  /// 恢复计时
  void resumeTimer() {
    if (_remainingSeconds > 0) {
      _isRunning = true;
      startTimer(_remainingSeconds ~/ 60);
    }
  }

  /// 取消计时
  void cancelTimer() {
    _timer?.cancel();
    _remainingSeconds = 0;
    _isRunning = false;
  }

  /// 销毁计时器服务
  void dispose() {
    _timer?.cancel();
    _timer = null;
    _remainingSeconds = 0;
    _isRunning = false;
    onTick = null;
    onComplete = null;
  }
}
