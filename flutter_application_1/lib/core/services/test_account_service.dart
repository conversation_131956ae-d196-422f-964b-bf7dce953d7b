import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import '../utils/storage_utils.dart';
import 'api_client.dart';

/// 测试账号服务
/// 提供测试账号登录功能，用于开发和测试
class TestAccountService {
  final ApiClient _apiClient = ApiClient();

  // 获取设备信息
  Map<String, dynamic> _getDeviceInfo() {
    return {
      'type': 'mobile',
      'platform': kIsWeb ? 'web' : Platform.operatingSystem,
      'deviceModel': kIsWeb ? 'browser' : Platform.localHostname,
      'appVersion': '1.0.0', // 应用版本号
    };
  }

  // 测试账号信息
  static const Map<String, String> _testCredentials = {
    'username': 'testuser',
    'password': 'Password123!',
    'email': '<EMAIL>',
  };

  /// 测试账号登录
  /// 使用预设的测试账号信息登录
  Future<bool> testLogin() async {
    try {
      debugPrint('开始测试账号登录流程');
      debugPrint('测试账号: ${_testCredentials['email']}');
      debugPrint('测试密码: ${_testCredentials['password']?.substring(0, 3)}***');

      // 获取设备信息
      final deviceInfo = _getDeviceInfo();
      debugPrint('设备信息: $deviceInfo');

      // 尝试使用API登录
      debugPrint('发送测试账号登录请求到: ${ApiClient.baseUrl}/auth/login');
      final response = await _apiClient.post('/auth/login', data: {
        'email': _testCredentials['email'],
        'password': _testCredentials['password'],
        'deviceInfo': deviceInfo,
      });

      debugPrint('测试账号登录响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint('测试账号登录成功，保存令牌和用户信息');

        // 保存令牌
        final token = response.data['data']['accessToken'];
        final refreshToken = response.data['data']['refreshToken'];
        await StorageUtils.saveToken(token);
        await StorageUtils.saveRefreshToken(refreshToken);

        debugPrint('令牌已保存: ${token.substring(0, 10)}...');

        // 保存用户信息
        final userJson = jsonEncode(response.data['data']['user']);
        await StorageUtils.saveUserInfo(userJson);
        debugPrint('用户信息已保存');

        return true;
      } else {
        debugPrint('测试账号登录失败，状态码: ${response.statusCode}');
        debugPrint('响应数据: ${response.data}');
      }

      debugPrint('服务器登录失败，尝试使用本地模拟登录');
      // 如果服务器登录失败，使用本地模拟登录
      return _mockTestLogin();
    } catch (e) {
      debugPrint('测试账号登录失败: $e');
      if (e is DioException) {
        debugPrint('DioException: ${e.response?.statusCode} ${e.response?.data}');
        debugPrint('请求URL: ${e.requestOptions.uri}');
        debugPrint('请求数据: ${e.requestOptions.data}');
      }

      debugPrint('API调用失败，尝试使用本地模拟登录');
      // 如果API调用失败，使用本地模拟登录
      return _mockTestLogin();
    }
  }

  /// 本地模拟测试账号登录
  /// 当服务器不可用时使用
  Future<bool> _mockTestLogin() async {
    try {
      // 创建模拟测试用户数据
      final testUser = {
        'id': 'test-user-id',
        'username': _testCredentials['username'],
        'nickname': '${_testCredentials['username']}的昵称',
        'email': _testCredentials['email'],
        'phone': '***********',
        'accountStatus': 'active',
        'preferences': {},
      };

      // 保存模拟令牌
      await StorageUtils.saveToken('mock-test-token');
      await StorageUtils.saveRefreshToken('mock-test-refresh-token');

      // 保存用户信息
      await StorageUtils.saveUserInfo(jsonEncode(testUser));

      return true;
    } catch (e) {
      debugPrint('模拟测试账号登录失败: $e');
      return false;
    }
  }
}
