import 'package:flutter/foundation.dart';
import 'package:limefocus/core/models/adapters/goal_adapter.dart';
import 'package:limefocus/core/models/adapters/subject_adapter.dart';
import 'package:limefocus/core/models/adapters/progress_mode_adapter.dart';
import 'package:limefocus/core/repositories/goal_repository.dart';
import 'package:limefocus/core/repositories/subject_repository.dart';
import 'package:limefocus/core/repositories/schedule_repository.dart';
import 'package:limefocus/core/repositories/focus_record_repository.dart';
import 'package:limefocus/core/repositories/exam_template_repository.dart';
import 'package:limefocus/core/repositories/archived_goal_repository.dart';
import 'package:limefocus/core/repositories/project_progress_repository.dart';
import 'package:limefocus/core/repositories/check_in_task_repository.dart';
import 'package:hive_flutter/hive_flutter.dart';

/// Hive服务类，负责初始化Hive并注册所有适配器
class HiveService {
  final GoalRepository _goalRepository = GoalRepository();
  final SubjectRepository _subjectRepository = SubjectRepository();
  final ScheduleRepository _scheduleRepository = ScheduleRepository();
  final FocusRecordRepository _focusRecordRepository = FocusRecordRepository();
  final ExamTemplateRepository _examTemplateRepository = ExamTemplateRepository();
  final ArchivedGoalRepository _archivedGoalRepository = ArchivedGoalRepository();
  final ProjectProgressRepository _projectProgressRepository = ProjectProgressRepository();
  final CheckInTaskRepository _checkInTaskRepository = CheckInTaskRepository();

  GoalRepository get goalRepository => _goalRepository;
  SubjectRepository get subjectRepository => _subjectRepository;
  ScheduleRepository get scheduleRepository => _scheduleRepository;
  FocusRecordRepository get focusRecordRepository => _focusRecordRepository;
  ExamTemplateRepository get examTemplateRepository => _examTemplateRepository;
  ArchivedGoalRepository get archivedGoalRepository => _archivedGoalRepository;
  ProjectProgressRepository get projectProgressRepository => _projectProgressRepository;
  CheckInTaskRepository get checkInTaskRepository => _checkInTaskRepository;

  /// 初始化Hive
  Future<void> initHive() async {
    debugPrint('HiveService.initHive 开始');
    try {
      // 初始化Hive（适用于Flutter应用）
      await Hive.initFlutter();
      debugPrint('Hive.initFlutter 完成');

      // 注册适配器
      _registerAdapters();
      debugPrint('_registerAdapters 完成');

      // 初始化仓库
      debugPrint('开始初始化仓库');
      try {
        await _goalRepository.init();
        debugPrint('_goalRepository.init 完成');
      } catch (e) {
        debugPrint('_goalRepository.init 失败: $e');
        // 继续初始化其他仓库
      }

      try {
        await _subjectRepository.init();
        debugPrint('_subjectRepository.init 完成');
      } catch (e) {
        debugPrint('_subjectRepository.init 失败: $e');
        throw Exception('科目仓库初始化失败: $e'); // 科目仓库是关键，必须成功初始化
      }

      try {
        await _scheduleRepository.init();
        debugPrint('_scheduleRepository.init 完成');
      } catch (e) {
        debugPrint('_scheduleRepository.init 失败: $e');
        // 继续初始化其他仓库
      }

      try {
        await _focusRecordRepository.init();
        debugPrint('_focusRecordRepository.init 完成');
      } catch (e) {
        debugPrint('_focusRecordRepository.init 失败: $e');
        // 继续初始化其他仓库
      }

      try {
        await _examTemplateRepository.init();
        debugPrint('_examTemplateRepository.init 完成');
      } catch (e) {
        debugPrint('_examTemplateRepository.init 失败: $e');
        // 继续初始化其他仓库
      }

      try {
        await _archivedGoalRepository.init();
        debugPrint('_archivedGoalRepository.init 完成');
      } catch (e) {
        debugPrint('_archivedGoalRepository.init 失败: $e');
        // 继续初始化其他仓库
      }

      try {
        await _projectProgressRepository.init();
        debugPrint('_projectProgressRepository.init 完成');
      } catch (e) {
        debugPrint('_projectProgressRepository.init 失败: $e');
        // 继续初始化其他仓库
      }

      try {
        await _checkInTaskRepository.init();
        debugPrint('_checkInTaskRepository.init 完成');
      } catch (e) {
        debugPrint('_checkInTaskRepository.init 失败: $e');
        // 继续初始化其他仓库
      }

      // 检查并创建默认数据
      // await _createDefaultDataIfNeeded();

      debugPrint('HiveService.initHive 完成');
    } catch (e) {
      debugPrint('HiveService.initHive 失败: $e');
      rethrow; // 重新抛出异常，让调用者处理
    }
  }

  /// 注册所有Hive适配器
  void _registerAdapters() {
    debugPrint('HiveService._registerAdapters 开始');
    try {
      // 注册Goal和Milestone适配器
      if (!Hive.isAdapterRegistered(1)) {
        Hive.registerAdapter(GoalAdapter());
        debugPrint('注册 GoalAdapter 成功');
      }
      if (!Hive.isAdapterRegistered(2)) {
        Hive.registerAdapter(MilestoneAdapter());
        debugPrint('注册 MilestoneAdapter 成功');
      }

      // 注册Subject和Project适配器 - 这些是关键适配器
      if (!Hive.isAdapterRegistered(3)) {
        Hive.registerAdapter(SubjectAdapter());
        debugPrint('注册 SubjectAdapter 成功');
      } else {
        debugPrint('SubjectAdapter 已注册');
      }

      if (!Hive.isAdapterRegistered(4)) {
        Hive.registerAdapter(ProgressTrackingModeAdapter());
        debugPrint('注册 ProgressTrackingModeAdapter 成功');
      }

      if (!Hive.isAdapterRegistered(5)) {
        Hive.registerAdapter(ProjectAdapter());
        debugPrint('注册 ProjectAdapter 成功');
      } else {
        debugPrint('ProjectAdapter 已注册');
      }

      // 注册Schedule相关适配器
      if (!Hive.isAdapterRegistered(6)) {
        Hive.registerAdapter(ProgressModeAdapter());
        debugPrint('注册 ProgressModeAdapter 成功');
      }

      // ProjectProgressChange相关适配器通过@HiveType注解自动注册

      // 所有其他适配器都通过@HiveType注解自动注册
      // Schedule, FocusRecord, ExamTemplate, ArchivedGoal, ProjectProgressChange, CheckInTask
      // 等相关适配器已通过@HiveType注解自动注册，无需手动注册

      debugPrint('HiveService._registerAdapters 完成');
    } catch (e) {
      debugPrint('HiveService._registerAdapters 失败: $e');
      throw Exception('注册Hive适配器失败: $e'); // 重新抛出异常，让调用者处理
    }
  }

  /// 检查并创建默认数据
  // Future<void> _createDefaultDataIfNeeded() async {
  //   try {
  //     // 获取所有科目
  //     final subjects = _subjectRepository.getAllSubjects();

  //     // 如果没有科目，创建默认的"其他"科目
  //     if (subjects.isEmpty) {
  //       final defaultSubject = Subject(
  //         id: 'default_subject',
  //         name: '其他',
  //       );
  //       await _subjectRepository.saveSubject(defaultSubject);
  //       debugPrint('创建默认科目: ${defaultSubject.name}');

  //       // 创建默认项目
  //       final defaultProject = Project(
  //         id: 'default_project',
  //         name: '其他',
  //         subjectId: defaultSubject.id,
  //         startDate: DateTime.now(),
  //         endDate: DateTime.now().add(const Duration(days: 365)),
  //         isTrackingEnabled: false,
  //       );
  //       await _subjectRepository.saveProject(defaultProject);
  //       debugPrint('创建默认项目: ${defaultProject.name}');
  //     }
  //   } catch (e) {
  //     debugPrint('创建默认数据时发生错误: $e');
  //   }
  // }

  /// 关闭Hive
  Future<void> closeHive() async {
    await _goalRepository.close();
    await _subjectRepository.close();
    await _scheduleRepository.close();
    await _focusRecordRepository.close();
    await _examTemplateRepository.close();
    await _archivedGoalRepository.close();
    await _projectProgressRepository.close();
    await _checkInTaskRepository.close();
    await Hive.close();
    debugPrint('Hive已关闭');
  }
}