import 'package:flutter/foundation.dart';
import '../services/hive_service.dart';

/// 项目数量限制服务
/// 管理进度追踪项目的数量限制
class ProjectLimitService {
  static const int _freeProjectLimit = 8; // 免费用户最多创建8个进度追踪项目
  
  final HiveService _hiveService = HiveService();

  /// 获取免费用户的项目数量限制
  static int get freeProjectLimit => _freeProjectLimit;

  /// 检查是否可以创建新的进度追踪项目
  /// 返回 true 表示可以创建，false 表示已达到限制
  Future<bool> canCreateTrackingProject({required bool isPremiumUser}) async {
    // 付费用户无限制
    if (isPremiumUser) {
      return true;
    }

    // 免费用户检查数量限制
    final currentCount = await getCurrentTrackingProjectCount();
    return currentCount < _freeProjectLimit;
  }

  /// 获取当前进度追踪项目的数量
  /// 包括归档的项目，但不包括已删除的项目
  Future<int> getCurrentTrackingProjectCount() async {
    try {
      // 确保Hive服务已初始化
      await _hiveService.initHive();
      
      // 获取所有项目
      final allProjects = _hiveService.subjectRepository.getAllProjects();
      
      // 筛选出开启了进度追踪的项目（包括归档的）
      final trackingProjects = allProjects.where((project) => 
        project.isTrackingEnabled
      ).toList();
      
      debugPrint('当前进度追踪项目数量: ${trackingProjects.length}');
      
      return trackingProjects.length;
    } catch (e) {
      debugPrint('获取进度追踪项目数量失败: $e');
      return 0;
    }
  }

  /// 获取剩余可创建的项目数量
  /// 仅对免费用户有意义
  Future<int> getRemainingProjectCount({required bool isPremiumUser}) async {
    if (isPremiumUser) {
      return -1; // -1 表示无限制
    }

    final currentCount = await getCurrentTrackingProjectCount();
    final remaining = _freeProjectLimit - currentCount;
    return remaining > 0 ? remaining : 0;
  }

  /// 获取项目限制状态信息
  Future<ProjectLimitStatus> getProjectLimitStatus({required bool isPremiumUser}) async {
    final currentCount = await getCurrentTrackingProjectCount();
    
    return ProjectLimitStatus(
      isPremiumUser: isPremiumUser,
      currentCount: currentCount,
      limit: isPremiumUser ? -1 : _freeProjectLimit,
      canCreate: isPremiumUser || currentCount < _freeProjectLimit,
      remaining: isPremiumUser ? -1 : (_freeProjectLimit - currentCount),
    );
  }
}

/// 项目限制状态
class ProjectLimitStatus {
  final bool isPremiumUser;
  final int currentCount;
  final int limit; // -1 表示无限制
  final bool canCreate;
  final int remaining; // -1 表示无限制

  ProjectLimitStatus({
    required this.isPremiumUser,
    required this.currentCount,
    required this.limit,
    required this.canCreate,
    required this.remaining,
  });

  /// 是否已达到限制
  bool get isAtLimit => !isPremiumUser && currentCount >= limit;

  /// 获取状态描述文本
  String get statusText {
    if (isPremiumUser) {
      return '已解锁无限项目创建';
    }
    
    if (isAtLimit) {
      return '已达到免费版限制 ($currentCount/$limit)';
    }
    
    return '还可创建 $remaining 个项目 ($currentCount/$limit)';
  }
}
