import 'package:flutter/material.dart';
import '../models/check_in_task.dart';
import '../models/schedule.dart';
import '../repositories/check_in_task_repository.dart';
import '../repositories/schedule_repository.dart';

/// 打卡任务同步服务
/// 负责将日程模块中的打卡任务（Schedule类型的routine）同步到数据分析模块的CheckInTask
class CheckInSyncService {
  final ScheduleRepository _scheduleRepository;
  final CheckInTaskRepository _checkInTaskRepository;

  CheckInSyncService({
    required ScheduleRepository scheduleRepository,
    required CheckInTaskRepository checkInTaskRepository,
  })  : _scheduleRepository = scheduleRepository,
        _checkInTaskRepository = checkInTaskRepository;

  /// 初始化服务
  Future<void> init() async {
    await _scheduleRepository.init();
    await _checkInTaskRepository.init();
  }

  /// 同步所有打卡任务
  Future<void> syncAllTasks() async {
    try {
      debugPrint('开始同步所有打卡任务...');
      
      // 获取所有日程打卡任务
      final routines = _scheduleRepository.getRoutines();
      debugPrint('找到 ${routines.length} 个日程打卡任务');
      
      // 获取所有CheckInTask
      final checkInTasks = _checkInTaskRepository.getAllTasks();
      debugPrint('找到 ${checkInTasks.length} 个CheckInTask');
      
      // 遍历所有日程打卡任务，同步到CheckInTask
      for (final routine in routines) {
        await syncScheduleToCheckInTask(routine);
      }
      
      debugPrint('所有打卡任务同步完成');
    } catch (e) {
      debugPrint('同步打卡任务出错: $e');
    }
  }

  /// 同步单个日程打卡任务到CheckInTask
  Future<void> syncScheduleToCheckInTask(Schedule schedule) async {
    try {
      // 只同步打卡类型的日程
      if (schedule.type != ScheduleType.routine) {
        return;
      }
      
      debugPrint('同步日程打卡任务: ${schedule.title}');
      
      // 检查是否已存在对应的CheckInTask
      final existingTasks = _checkInTaskRepository.getAllTasks()
          .where((task) => task.id == 'schedule_${schedule.id}')
          .toList();
      
      if (existingTasks.isNotEmpty) {
        // 更新现有任务
        final existingTask = existingTasks.first;
        await _updateCheckInTask(existingTask, schedule);
        debugPrint('更新现有CheckInTask: ${existingTask.name}');
      } else {
        // 创建新任务
        final newTask = _createCheckInTaskFromSchedule(schedule);
        await _checkInTaskRepository.addTask(newTask);
        debugPrint('创建新CheckInTask: ${newTask.name}');
      }
    } catch (e) {
      debugPrint('同步日程打卡任务出错: $e');
    }
  }

  /// 从Schedule创建CheckInTask
  CheckInTask _createCheckInTaskFromSchedule(Schedule schedule) {
    // 生成唯一ID，使用schedule_前缀加上日程ID
    final id = 'schedule_${schedule.id}';
    
    // 创建CheckInTask
    final checkInTask = CheckInTask(
      id: id,
      name: schedule.title,
      description: schedule.description ?? '',
      // 使用默认颜色，可以根据需要调整
      color: 0xFF4CAF50, // 绿色
      startDate: schedule.date ?? DateTime.now(),
      endDate: schedule.endDate,
      status: CheckInTaskStatus.inProgress,
    );
    
    // 同步完成日期
    if (schedule.completedDates != null && schedule.completedDates!.isNotEmpty) {
      for (final date in schedule.completedDates!) {
        checkInTask.checkIn(date);
      }
    }
    
    return checkInTask;
  }

  /// 更新CheckInTask
  Future<void> _updateCheckInTask(CheckInTask task, Schedule schedule) async {
    // 更新基本信息
    task.name = schedule.title;
    task.description = schedule.description ?? '';
    task.startDate = schedule.date ?? task.startDate;
    task.endDate = schedule.endDate;
    
    // 同步完成日期
    if (schedule.completedDates != null) {
      // 清空现有打卡记录
      task.checkInRecords.clear();
      
      // 添加新的打卡记录
      for (final date in schedule.completedDates!) {
        final dateStr = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
        task.checkInRecords[dateStr] = true;
      }
    }
    
    // 保存更新
    await _checkInTaskRepository.updateTask(task);
  }

  /// 同步打卡状态
  Future<void> syncCheckInStatus(String scheduleId, DateTime date, bool isCheckedIn) async {
    try {
      // 查找对应的CheckInTask
      final checkInTaskId = 'schedule_$scheduleId';
      final task = _checkInTaskRepository.getTaskById(checkInTaskId);
      
      if (task != null) {
        if (isCheckedIn) {
          // 标记为已打卡
          task.checkIn(date);
        } else {
          // 取消打卡
          task.cancelCheckIn(date);
        }
        
        debugPrint('同步打卡状态: ${task.name}, 日期: ${date.toString()}, 状态: ${isCheckedIn ? '已打卡' : '未打卡'}');
      }
    } catch (e) {
      debugPrint('同步打卡状态出错: $e');
    }
  }
}
