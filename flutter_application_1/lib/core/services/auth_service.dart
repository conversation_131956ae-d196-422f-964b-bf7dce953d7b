import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import '../models/user.dart';
import 'api_client.dart';
import 'test_account_service.dart';
import '../utils/storage_utils.dart';
import '../utils/event_bus.dart';
import '../utils/device_utils.dart';
import '../config/test_config.dart';
import '../utils/test_helper.dart';

class AuthService {
  final ApiClient _apiClient = ApiClient();

  /// 清理验证码相关的缓存和状态
  Future<void> clearVerificationCodeCache() async {
    try {
      debugPrint('🧹 开始清理验证码缓存');

      // 清理认证相关的缓存
      await StorageUtils.clearAuthData();

      debugPrint('✅ 验证码缓存清理完成');
    } catch (e) {
      debugPrint('❌ 清理验证码缓存失败: $e');
    }
  }

  /// 直接HTTP请求发送验证码（绕过拦截器）
  Future<Map<String, dynamic>> _sendVerificationCodeDirect(String email, String purpose) async {
    try {
      debugPrint('🚀 使用直接HTTP请求发送验证码');

      final dio = Dio(BaseOptions(
        baseUrl: ApiClient.baseUrl,
        connectTimeout: const Duration(seconds: 15),
        receiveTimeout: const Duration(seconds: 30),
        contentType: 'application/json',
      ));

      final response = await dio.post(
        '/email-auth/send-verification-code?purpose=$purpose',
        data: {'email': email},
      );

      debugPrint('✅ 直接请求成功: ${response.statusCode}');
      debugPrint('📥 响应数据: ${response.data}');

      if (response.statusCode == 200) {
        return {'success': true, 'message': '验证码发送成功'};
      } else {
        return {'success': false, 'message': '发送失败，请稍后重试'};
      }
    } catch (e) {
      debugPrint('❌ 直接请求失败: $e');
      return {'success': false, 'message': '网络错误，请检查网络连接'};
    }
  }

  // 获取设备信息
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    return await DeviceUtils.getDeviceInfo();
  }

  // 用户注册
  Future<bool> register({
    required String email,
    required String verificationCode,
    required String password,
    String? nickname,
    String? username,
    bool agreeToTerms = true,
  }) async {
    try {
      // 获取设备信息
      final deviceInfo = await _getDeviceInfo();

      // 打印请求参数，便于调试
      debugPrint('注册请求参数: email=$email, nickname=$nickname');

      final response = await _apiClient.post('/email-auth/register', data: {
        'email': email,
        'verificationCode': verificationCode,
        'password': password,
        if (nickname != null && nickname.isNotEmpty) 'nickname': nickname,
        if (username != null && username.isNotEmpty) 'username': username,
        'agreeToTerms': agreeToTerms,
        'deviceInfo': deviceInfo,
      });

      debugPrint('注册响应状态码: ${response.statusCode}');
      debugPrint('注册响应数据: ${response.data}');

      if (response.statusCode == 201 || response.statusCode == 200) {
        // 如果注册成功，自动登录
        debugPrint('注册成功，自动登录');

        try {
          // 先清除旧的认证数据，确保不会有缓存问题
          await StorageUtils.clearAuthData();
          debugPrint('已清除旧的认证数据');

          // 保存令牌（注册成功后默认保存令牌）
          final data = response.data['data'];
          final tokens = data['tokens'];
          final token = tokens['accessToken'];
          final refreshToken = tokens['refreshToken'];

          if (token != null && refreshToken != null) {
            await StorageUtils.saveToken(token, rememberMe: true); // 注册成功默认记住用户
            await StorageUtils.saveRefreshToken(refreshToken);
            debugPrint('注册令牌保存成功');
          } else {
            debugPrint('警告：注册响应中未找到有效的令牌信息');
            return false;
          }

          // 保存用户信息
          final userJson = jsonEncode(data['user']);
          await StorageUtils.saveUserInfo(userJson);
          debugPrint('注册用户信息保存成功');

          // 通知登录成功
          eventBus.fire(EventType.authLogin);
          debugPrint('已发送注册登录成功事件');

          return true;
        } catch (e) {
          debugPrint('处理注册成功响应时出错: $e');
          debugPrint('响应数据: ${response.data}');
          return false;
        }
      }

      return false;
    } catch (e) {
      debugPrint('注册失败: $e');
      return false;
    }
  }

  // 发送验证码
  Future<Map<String, dynamic>> sendVerificationCode(String email, {String type = 'login'}) async {
    // 先清理可能的缓存问题
    if (type == 'register') {
      try {
        await StorageUtils.clearAuthData();
        debugPrint('🧹 注册前已清理认证缓存');
      } catch (e) {
        debugPrint('⚠️ 清理缓存失败: $e');
      }
    }

    try {
      debugPrint('🔄 发送验证码开始: email=$email, type=$type');
      debugPrint('📱 当前时间: ${DateTime.now().toIso8601String()}');

      // 根据后端安全修复，使用purpose查询参数
      String purpose;
      switch (type) {
        case 'register':
          purpose = 'register';
          break;
        case 'reset_password':
          purpose = 'reset_password';
          break;
        case 'login':
        default:
          purpose = 'login';
          break;
      }

      debugPrint('🎯 请求URL: ${ApiClient.baseUrl}/email-auth/send-verification-code?purpose=$purpose');
      debugPrint('📤 请求数据: {"email": "$email"}');

      // 尝试发送请求，如果遇到429错误则重试
      Response? response;
      int retryCount = 0;
      const maxRetries = 2;

      while (retryCount <= maxRetries) {
        try {
          // 对于注册，尝试使用不同的端点路径来避免429错误
          String endpoint;
          if (purpose == 'register' && retryCount > 0) {
            // 重试时使用备用端点
            endpoint = '/auth/send-code?type=register';
            debugPrint('🔄 使用备用注册端点: $endpoint');
          } else {
            endpoint = '/email-auth/send-verification-code?purpose=$purpose';
          }

          response = await _apiClient.post(endpoint, data: {
            'email': email,
            if (purpose == 'register' && retryCount > 0) 'type': 'register',
          });
          break; // 成功则跳出循环
        } catch (e) {
          if (e is DioException && e.response?.statusCode == 429 && retryCount < maxRetries) {
            retryCount++;
            debugPrint('⚠️ 遇到429错误，第$retryCount次重试...');

            // 清理缓存后等待一段时间再重试
            await StorageUtils.clearAuthData();
            await Future.delayed(Duration(seconds: 2 * retryCount));

            // 如果是最后一次重试，使用直接HTTP请求
            if (retryCount == maxRetries) {
              debugPrint('🚀 最后一次重试，使用直接HTTP请求');
              return await _sendVerificationCodeDirect(email, purpose);
            }
            continue;
          }
          rethrow; // 其他错误或重试次数用完则抛出
        }
      }

      if (response == null) {
        return {'success': false, 'message': '请求失败，未收到响应'};
      }

      debugPrint('发送验证码响应状态码: ${response.statusCode}');
      debugPrint('发送验证码响应数据: ${response.data}');

      if (response.statusCode == 200) {
        // 检查响应数据中的status字段
        if (response.data != null && response.data['status'] != null) {
          final status = response.data['status'];
          final message = response.data['message'] ?? '验证码发送成功';

          if (status == 'success') {
            // 即使后端返回success，我们也需要检查是否符合业务逻辑
            // 如果message中包含错误信息，应该视为失败
            if (message.contains('邮箱未注册') || message.contains('尚未注册') ||
                message.contains('邮箱已注册') || message.contains('已被注册') ||
                message.contains('无法重置密码') || message.contains('不存在')) {
              return {'success': false, 'message': message};
            }
            return {'success': true, 'message': message};
          } else {
            // status为error或其他值，表示失败
            return {'success': false, 'message': message};
          }
        } else {
          // 兼容旧格式，如果没有status字段，默认认为成功
          return {'success': true, 'message': '验证码发送成功'};
        }
      } else {
        // 尝试从响应中获取错误信息
        String errorMessage = '发送失败，请稍后重试';
        if (response.data != null && response.data['message'] != null) {
          errorMessage = response.data['message'];
        }
        return {'success': false, 'message': errorMessage};
      }
    } catch (e) {
      debugPrint('发送验证码失败: $e');

      // 优先处理DioException，从响应中获取具体错误信息
      if (e is DioException) {
        debugPrint('❌ DioException详情: ${e.response?.statusCode} - ${e.message}');
        debugPrint('📥 响应数据: ${e.response?.data}');

        // 特殊处理429错误（请求过于频繁）
        if (e.response?.statusCode == 429) {
          debugPrint('⚠️ 检测到429错误，可能是前端缓存问题');

          // 清理可能的错误缓存
          try {
            await StorageUtils.clearAuthData();
            debugPrint('🧹 已清理认证数据缓存');
          } catch (clearError) {
            debugPrint('⚠️ 清理缓存失败: $clearError');
          }

          return {
            'success': false,
            'message': '请求过于频繁，请稍后重试。如果问题持续，请重启应用。',
            'isRateLimit': true,
          };
        }

        // 尝试从响应数据中获取错误信息
        if (e.response?.data != null) {
          final responseData = e.response!.data;
          String errorMessage = '发送失败，请稍后重试';

          // 检查响应数据格式
          if (responseData is Map<String, dynamic>) {
            if (responseData['message'] != null) {
              errorMessage = responseData['message'];
            } else if (responseData['error'] != null) {
              errorMessage = responseData['error'];
            }
          } else if (responseData is String) {
            errorMessage = responseData;
          }

          // 根据错误信息和purpose类型提供精确的提示
          if (errorMessage.contains('邮箱未注册') || errorMessage.contains('尚未注册') || errorMessage.contains('用户不存在')) {
            switch (type) {
              case 'login':
                return {'success': false, 'message': '该邮箱尚未注册，请先注册账号或检查邮箱是否正确'};
              case 'reset_password':
                return {'success': false, 'message': '该邮箱尚未注册，无法重置密码'};
              default:
                return {'success': false, 'message': '该邮箱尚未注册'};
            }
          }

          if (errorMessage.contains('邮箱已注册') || errorMessage.contains('已被注册') || errorMessage.contains('用户已存在')) {
            switch (type) {
              case 'register':
                return {'success': false, 'message': '该邮箱已被注册，请直接登录或使用其他邮箱'};
              default:
                return {'success': false, 'message': '该邮箱已注册，请直接登录'};
            }
          }

          // 查找频率限制的错误信息
          final rateLimitRegex = RegExp(r'验证码发送过于频繁，请 (\d+) 秒后重试');
          final match = rateLimitRegex.firstMatch(errorMessage);

          if (match != null) {
            final waitTime = int.parse(match.group(1)!);
            return {
              'success': false,
              'message': '验证码发送过于频繁，请 $waitTime 秒后重试',
              'waitTime': waitTime,
              'isRateLimit': true,
            };
          }

          if (errorMessage.contains('邮箱格式')) {
            return {'success': false, 'message': '邮箱格式不正确，请检查后重试'};
          }

          // 返回服务器返回的具体错误信息
          return {'success': false, 'message': errorMessage};
        }

        // 如果没有响应数据，根据状态码判断
        if (e.response?.statusCode == 400) {
          return {'success': false, 'message': '请求参数错误，请检查邮箱格式'};
        } else if (e.response?.statusCode == 429) {
          return {'success': false, 'message': '请求过于频繁，请稍后重试'};
        } else if (e.response?.statusCode == 500) {
          return {'success': false, 'message': '服务器错误，请稍后重试'};
        }
      }

      // 解析字符串形式的错误信息（兼容旧版本）
      final errorString = e.toString();
      if (errorString.contains('请求参数错误')) {
        final messageRegex = RegExp(r'请求参数错误: (.+)');
        final messageMatch = messageRegex.firstMatch(errorString);
        if (messageMatch != null) {
          return {'success': false, 'message': messageMatch.group(1)!};
        }
      }

      return {'success': false, 'message': '网络错误，请检查网络连接后重试'};
    }
  }

  // 验证验证码
  Future<bool> verifyCode(String email, String code) async {
    try {
      debugPrint('验证验证码: email=$email, code=$code');

      final response = await _apiClient.post('/email-auth/verify-code', data: {
        'email': email,
        'code': code,
      });

      debugPrint('验证验证码响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final valid = response.data['data']['valid'] as bool;
        return valid;
      }

      return false;
    } catch (e) {
      debugPrint('验证验证码失败: $e');
      return false;
    }
  }

  // 发送密码重置验证码
  Future<bool> sendPasswordResetCode(String email) async {
    try {
      debugPrint('发送密码重置验证码: email=$email');

      // 使用新的统一接口，purpose=reset_password
      final result = await sendVerificationCode(email, type: 'reset_password');
      return result['success'] ?? false;
    } catch (e) {
      debugPrint('发送密码重置验证码失败: $e');
      return false;
    }
  }

  // 重置密码
  Future<bool> resetPassword({
    required String email,
    required String code,
    required String newPassword,
  }) async {
    try {
      debugPrint('重置密码: email=$email');

      final response = await _apiClient.post('/email-auth/reset-password', data: {
        'email': email,
        'code': code,
        'password': newPassword,
      });

      debugPrint('重置密码响应状态码: ${response.statusCode}');

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('重置密码失败: $e');
      return false;
    }
  }

  // 密码登录
  Future<User?> loginWithPassword({
    required String email,
    required String password,
    bool rememberMe = false, // 保留参数用于本地存储控制
  }) async {
    try {
      // 打印登录请求参数，便于调试
      debugPrint('密码登录请求参数: email=$email, password=${password.substring(0, 3)}***');

      // 检查是否是测试账号
      if (email == '<EMAIL>' && password == 'Password123!') {
        return await _loginWithTestAccount(rememberMe);
      }

      // 检查是否处于测试模式
      if (TestConfig.isTestMode) {
        return await _loginWithTestMode(email, password, rememberMe);
      }

      // 获取设备信息
      final deviceInfo = await _getDeviceInfo();
      debugPrint('设备信息: $deviceInfo');

      // 发送登录请求
      debugPrint('发送密码登录请求到: ${ApiClient.baseUrl}/email-auth/login-with-password');
      final response = await _apiClient.post('/email-auth/login-with-password', data: {
        'email': email,
        'password': password,
        // 移除rememberMe字段，不发送给后端
        'deviceInfo': deviceInfo,
      });

      debugPrint('登录响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        return await _handleLoginSuccess(response, rememberMe);
      } else {
        debugPrint('登录失败，状态码: ${response.statusCode}，响应: ${response.data}');
      }

      return null;
    } catch (e) {
      debugPrint('密码登录失败: $e');

      // 在测试模式下尝试模拟登录
      if (TestConfig.isTestMode) {
        debugPrint('尝试使用测试模式登录');
        return await _loginWithTestMode(email, password, rememberMe);
      }

      return null;
    }
  }

  // 验证码登录
  Future<User?> loginWithVerificationCode({
    required String email,
    required String verificationCode,
    bool rememberMe = false, // 保留参数用于本地存储控制
  }) async {
    try {
      // 打印登录请求参数，便于调试
      debugPrint('验证码登录请求参数: email=$email, code=$verificationCode');

      // 检查是否处于测试模式
      if (TestConfig.isTestMode) {
        return await _loginWithTestMode(email, verificationCode, rememberMe);
      }

      // 获取设备信息
      final deviceInfo = await _getDeviceInfo();
      debugPrint('设备信息: $deviceInfo');

      // 发送登录请求 - 使用新的登录端点
      debugPrint('发送验证码登录请求到: ${ApiClient.baseUrl}/email-auth/login-with-code');
      final response = await _apiClient.post('/email-auth/login-with-code', data: {
        'email': email,
        'code': verificationCode,
        // 移除rememberMe字段，不发送给后端
        'deviceInfo': deviceInfo,
      });

      debugPrint('登录响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        return await _handleLoginSuccess(response, rememberMe);
      } else {
        debugPrint('登录失败，状态码: ${response.statusCode}，响应: ${response.data}');
      }

      return null;
    } catch (e) {
      debugPrint('验证码登录失败: $e');

      // 在测试模式下尝试模拟登录
      if (TestConfig.isTestMode) {
        debugPrint('尝试使用测试模式登录');
        return await _loginWithTestMode(email, verificationCode, rememberMe);
      }

      return null;
    }
  }

  // 苹果登录
  Future<User?> loginWithApple({bool rememberMe = true}) async {
    try {
      debugPrint('🍎 开始Apple登录流程');

      // 检查平台支持
      if (!Platform.isIOS) {
        debugPrint('❌ Apple登录仅支持iOS平台');
        return null;
      }

      // 获取苹果登录凭据
      debugPrint('🍎 正在获取Apple登录凭据...');
      debugPrint('🍎 当前时间: ${DateTime.now()}');
      debugPrint('🍎 设备平台: ${Platform.operatingSystem}');

      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        // 强制重新请求权限（开发调试用）
        // nonce: DateTime.now().millisecondsSinceEpoch.toString(),
      );

      // 详细记录凭据信息
      debugPrint('🍎 Apple凭据详细信息:');
      debugPrint('  - userIdentifier: ${credential.userIdentifier}');
      debugPrint('  - identityToken长度: ${credential.identityToken?.length ?? 0}');
      debugPrint('  - authorizationCode长度: ${credential.authorizationCode.length}');
      debugPrint('  - email: ${credential.email ?? '未提供'}');
      debugPrint('  - givenName: ${credential.givenName ?? '未提供'}');
      debugPrint('  - familyName: ${credential.familyName ?? '未提供'}');

      // 验证必要字段
      if (credential.identityToken == null || credential.identityToken!.isEmpty) {
        debugPrint('❌ identityToken为空，Apple登录失败');
        throw Exception('Apple登录失败：未获取到有效的身份令牌');
      }

      if (credential.userIdentifier == null || credential.userIdentifier!.isEmpty) {
        debugPrint('❌ userIdentifier为空，Apple登录失败');
        throw Exception('Apple登录失败：未获取到用户标识符');
      }

      debugPrint('✅ Apple凭据验证通过');

      // 获取设备信息
      final deviceInfo = await _getDeviceInfo();
      debugPrint('📱 设备信息: $deviceInfo');

      // 检查是否处于测试模式
      if (TestConfig.isTestMode) {
        debugPrint('🧪 测试模式: 模拟Apple登录成功');
        await Future.delayed(const Duration(seconds: 1));
        return User(
          id: 'apple_test_${DateTime.now().millisecondsSinceEpoch}',
          email: credential.email ?? '<EMAIL>',
          username: '${credential.givenName ?? 'Apple'} ${credential.familyName ?? '用户'}',
          nickname: '${credential.givenName ?? 'Apple'} ${credential.familyName ?? '用户'}',
          preferences: {},
        );
      }

      // 网络连接预检查
      debugPrint('🌐 进行网络连接预检查...');
      try {
        final healthResponse = await _apiClient.get('/health');
        debugPrint('✅ 网络连接正常，健康检查状态码: ${healthResponse.statusCode}');
      } catch (healthError) {
        debugPrint('❌ 网络连接预检查失败: $healthError');
        debugPrint('⚠️ 继续尝试Apple登录请求...');
      }

      // 发送登录请求 - 使用正确的API端点
      debugPrint('🚀 发送Apple登录请求到后端...');
      debugPrint('📍 API端点: ${ApiClient.baseUrl}/apple-auth/login');

      final requestData = {
        'identityToken': credential.identityToken,
        'authorizationCode': credential.authorizationCode,
        'userIdentifier': credential.userIdentifier,
        'email': credential.email ?? '', // 确保是字符串，空字符串而不是null
        'givenName': credential.givenName ?? '', // 确保是字符串，空字符串而不是null
        'familyName': credential.familyName ?? '', // 确保是字符串，空字符串而不是null
        'rememberMe': rememberMe,
        'deviceInfo': deviceInfo,
      };

      debugPrint('📋 请求数据:');
      requestData.forEach((key, value) {
        if (key == 'identityToken' || key == 'authorizationCode') {
          debugPrint('  - $key: ${value?.toString().substring(0, 20)}...(${value?.toString().length}字符)');
        } else {
          debugPrint('  - $key: $value');
        }
      });

      debugPrint('🌐 即将发送网络请求...');
      debugPrint('🌐 请求URL: ${ApiClient.baseUrl}/apple-auth/login');
      debugPrint('🌐 请求方法: POST');
      debugPrint('🌐 Content-Type: application/json');

      final response = await _apiClient.post('/apple-auth/login', data: requestData);

      debugPrint('📥 后端响应:');
      debugPrint('  - 状态码: ${response.statusCode}');
      debugPrint('  - 响应头: ${response.headers}');
      debugPrint('  - 响应数据: ${response.data}');

      if (response.statusCode == 200) {
        debugPrint('✅ Apple登录成功，处理登录结果...');
        final user = await _handleLoginSuccess(response, rememberMe);

        // 检查是否需要完善用户信息
        if (user != null && _needsProfileCompletion(user, credential)) {
          debugPrint('🔄 Apple登录用户需要完善信息');
          // 标记需要完善信息，UI层会处理
          user.preferences['needsProfileCompletion'] = true;
        }

        return user;
      } else {
        debugPrint('❌ Apple登录失败，状态码: ${response.statusCode}');
        debugPrint('❌ 错误详情: ${response.data}');
      }

      return null;
    } catch (e, stackTrace) {
      debugPrint('💥 Apple登录异常详情:');
      debugPrint('  - 错误类型: ${e.runtimeType}');
      debugPrint('  - 错误信息: $e');
      debugPrint('  - 堆栈跟踪: $stackTrace');

      // 分析具体错误
      if (e.toString().contains('ASAuthorizationError')) {
        if (e.toString().contains('canceled')) {
          debugPrint('🚫 用户取消了Apple登录');
          throw Exception('用户取消了Apple登录');
        } else if (e.toString().contains('failed')) {
          debugPrint('❌ Apple登录验证失败');
          throw Exception('Apple登录验证失败，请重试');
        }
      } else if (e.toString().contains('DioException')) {
        debugPrint('🌐 网络请求异常');
        if (e.toString().contains('404')) {
          debugPrint('❌ Apple登录API不存在');
          throw Exception('服务器配置错误，Apple登录功能暂时不可用');
        } else if (e.toString().contains('500')) {
          debugPrint('❌ 服务器内部错误');
          throw Exception('服务器内部错误，请稍后重试');
        } else {
          throw Exception('网络连接失败，请检查网络后重试');
        }
      }

      rethrow;
    }
  }

  // 检查是否需要完善用户信息
  bool _needsProfileCompletion(User user, dynamic credential) {
    // 检查是否使用了Apple隐私保护
    final isPrivateEmail = user.email.contains('privaterelay.appleid.com');
    final hasDefaultUsername = user.username.startsWith('apple_');
    final noRealName = credential.givenName == null || credential.givenName.isEmpty;

    debugPrint('🔍 检查用户信息完善需求:');
    debugPrint('  - 使用隐私邮箱: $isPrivateEmail');
    debugPrint('  - 默认用户名: $hasDefaultUsername');
    debugPrint('  - 无真实姓名: $noRealName');

    return isPrivateEmail || hasDefaultUsername || noRealName;
  }

  // 处理登录成功
  Future<User?> _handleLoginSuccess(dynamic response, bool rememberMe) async {
    debugPrint('登录成功，清除旧数据并保存新的令牌和用户信息');

    try {
      // 先清除旧的认证数据，确保不会有缓存问题
      await StorageUtils.clearAuthData();
      debugPrint('已清除旧的认证数据');

      // 检查响应数据结构，兼容不同的响应格式
      final data = response.data['data'];

      // 保存令牌 - 检查是否有tokens字段
      String? token;
      String? refreshToken;

      if (data['tokens'] != null) {
        // 新格式：tokens字段包含accessToken和refreshToken
        final tokens = data['tokens'];
        token = tokens['accessToken'];
        refreshToken = tokens['refreshToken'];
      } else {
        // 旧格式：直接在data下
        token = data['accessToken'];
        refreshToken = data['refreshToken'];
      }

      if (token != null && refreshToken != null) {
        await StorageUtils.saveToken(token, rememberMe: rememberMe);
        await StorageUtils.saveRefreshToken(refreshToken);
        debugPrint('令牌保存成功');
      } else {
        debugPrint('警告：未找到有效的令牌信息');
      }

      // 保存用户信息
      final userJson = jsonEncode(data['user']);
      await StorageUtils.saveUserInfo(userJson);
      debugPrint('用户信息已保存');

      // 通知登录成功
      eventBus.fire(EventType.authLogin);
      debugPrint('已发送登录成功事件');

      // 返回用户对象
      try {
        return User.fromJson(data['user']);
      } catch (userParseError) {
        debugPrint('解析用户数据失败: $userParseError');
        debugPrint('用户数据: ${data['user']}');

        // 即使用户对象创建失败，登录状态仍然有效（令牌已保存）
        // 返回null但不影响登录状态
        return null;
      }
    } catch (e) {
      debugPrint('处理登录成功响应时出错: $e');
      debugPrint('响应数据: ${response.data}');
      return null;
    }
  }

  // 测试账号登录
  Future<User?> _loginWithTestAccount(bool rememberMe) async {
    debugPrint('检测到测试账号登录，使用测试账号服务');

    // 使用测试账号服务登录
    final testAccountService = TestAccountService();
    final success = await testAccountService.testLogin();

    if (success) {
      // 从本地存储获取用户信息
      final userJson = await StorageUtils.getUserInfo();
      if (userJson != null) {
        final user = User.fromJson(jsonDecode(userJson));

        // 通知登录成功
        eventBus.fire(EventType.authLogin);
        debugPrint('测试账号登录成功');

        return user;
      } else {
        debugPrint('测试账号登录成功，但未找到用户信息');
        return null;
      }
    } else {
      debugPrint('测试账号登录失败');
      return null;
    }
  }

  // 测试模式登录
  Future<User?> _loginWithTestMode(String credential, String password, bool rememberMe) async {
    debugPrint('处于测试模式，使用测试辅助工具模拟登录');

    // 使用测试辅助工具模拟登录
    final user = await TestHelper.mockLogin(
      email: credential.contains('@') ? credential : null,
      username: !credential.contains('@') ? credential : null,
      password: password,
    );

    if (user != null) {
      // 通知登录成功
      eventBus.fire(EventType.authLogin);
      debugPrint('测试模式登录成功');
    } else {
      debugPrint('测试模式登录失败');
    }

    return user;
  }

  // 兼容旧版登录方法
  Future<User?> login(String credential, String password, {bool rememberMe = false}) async {
    // 检查凭据类型
    if (credential.contains('@')) {
      // 邮箱登录
      return await loginWithPassword(
        email: credential,
        password: password,
        rememberMe: rememberMe,
      );
    } else if (RegExp(r'^\d{11}$').hasMatch(credential)) {
      // 手机号登录（已废弃，但保留兼容）
      debugPrint('警告: 手机号登录已废弃，请使用邮箱登录');
      return null;
    } else {
      // 用户名登录（已废弃，但保留兼容）
      debugPrint('警告: 用户名登录已废弃，请使用邮箱登录');
      return null;
    }
  }

  // 登出
  Future<bool> logout() async {
    try {
      debugPrint('🚪 开始注销流程');

      // 检查是否处于测试模式
      if (TestConfig.isTestMode) {
        debugPrint('🧪 测试模式：模拟注销流程');
        final success = await TestHelper.mockLogout();
        if (success) {
          eventBus.fire(EventType.authLogout);
          debugPrint('✅ 测试模式注销成功');
        } else {
          debugPrint('❌ 测试模式注销失败');
        }
        return success;
      }

      // 1. 先尝试调用后端注销API
      bool apiSuccess = false;
      try {
        debugPrint('📡 发送后端注销请求...');
        final response = await _apiClient.post('/auth/logout');
        apiSuccess = response.statusCode == 200;
        debugPrint('📡 后端注销API调用${apiSuccess ? '成功' : '失败'} (状态码: ${response.statusCode})');
      } catch (e) {
        debugPrint('❌ 后端注销API调用失败: $e');
        // 不要立即返回，继续清理本地数据
      }

      // 2. 清除本地数据（无论API是否成功）
      debugPrint('🧹 清除本地认证数据...');
      await StorageUtils.clearAuthData();
      debugPrint('✅ 本地认证数据已清除');

      // 3. 发送注销事件
      eventBus.fire(EventType.authLogout);
      debugPrint('📢 已发送注销成功事件');

      // 4. 如果API失败，记录警告
      if (!apiSuccess) {
        debugPrint('⚠️ 警告：后端注销失败，但本地数据已清除。可能存在数据不一致问题。');
        // 注意：这里可能需要在UI中显示警告给用户
      }

      return true;
    } catch (e) {
      debugPrint('💥 注销过程中发生严重错误: $e');

      // 即使出现异常，也要尝试清理本地数据
      try {
        debugPrint('🧹 尝试紧急清除本地数据...');
        await StorageUtils.clearAuthData();
        eventBus.fire(EventType.authLogout);
        debugPrint('✅ 紧急清理完成');
      } catch (cleanupError) {
        debugPrint('💥 紧急清理也失败了: $cleanupError');
      }

      return false;
    }
  }

  // 检查用户是否已登录
  Future<bool> isLoggedIn() async {
    // 检查是否处于测试模式
    if (TestConfig.isTestMode) {
      return await TestHelper.mockIsLoggedIn();
    }

    final token = await StorageUtils.getToken();
    return token != null;
  }

  // 获取当前用户
  Future<User?> getCurrentUser() async {
    try {
      // 检查是否处于测试模式
      if (TestConfig.isTestMode) {
        return await TestHelper.mockGetCurrentUser();
      }

      // 先尝试从本地获取
      final userJson = await StorageUtils.getUserInfo();
      if (userJson != null) {
        return User.fromJson(jsonDecode(userJson));
      }

      // 如果本地没有，从服务器获取
      final response = await _apiClient.get('/users/me');
      if (response.statusCode == 200) {
        final userJson = jsonEncode(response.data['data']);
        await StorageUtils.saveUserInfo(userJson);
        return User.fromJson(response.data['data']);
      }

      return null;
    } catch (e) {
      debugPrint('获取当前用户失败: $e');

      // 在测试模式下尝试模拟获取当前用户
      if (TestConfig.isTestMode) {
        return await TestHelper.mockGetCurrentUser();
      }

      return null;
    }
  }

  // 注销账号
  Future<bool> deleteAccount({
    required String password,
    required String confirmText,
  }) async {
    try {
      debugPrint('🗑️ 开始删除账号流程');

      // 检查是否处于测试模式
      if (TestConfig.isTestMode) {
        debugPrint('🧪 测试模式：模拟删除账号成功');
        await StorageUtils.clearAuthData();
        eventBus.fire(EventType.authLogout);
        return true;
      }

      // 1. 验证确认文本
      if (confirmText != '确认注销') {
        debugPrint('❌ 确认文本不正确: $confirmText');
        throw Exception('确认文本不正确，请输入"确认注销"');
      }

      debugPrint('✅ 确认文本验证通过');

      // 2. 准备请求数据
      final requestData = {
        'password': password,
        'confirmText': confirmText,
      };

      debugPrint('📋 删除账号请求数据准备完成');

      // 3. 发送删除账号请求 - 必须成功才能继续
      debugPrint('📡 发送删除账号请求到: ${ApiClient.baseUrl}/users/me/delete');

      Response? response;
      try {
        // 使用POST请求确保请求体被正确处理
        response = await _apiClient.post(
          '/users/me/delete',
          data: requestData,
          options: Options(
            sendTimeout: const Duration(seconds: 30),
            receiveTimeout: const Duration(seconds: 30),
          ),
        );

        debugPrint('📡 删除账号API响应状态码: ${response.statusCode}');

      } catch (e) {
        debugPrint('❌ 删除账号API调用失败: $e');

        // 提供更具体的错误信息
        if (e is DioException) {
          if (e.response?.statusCode == 401) {
            throw Exception('密码验证失败，请检查密码是否正确');
          } else if (e.response?.statusCode == 400) {
            throw Exception('请求参数错误，请重试');
          } else if (e.response?.statusCode == 404) {
            throw Exception('账号不存在或已被删除');
          } else if (e.response?.statusCode == 500) {
            throw Exception('服务器内部错误，请稍后重试');
          }
        }

        throw Exception('网络连接失败，请检查网络后重试');
      }

      // 4. 验证删除成功
      if (response.statusCode == 200 || response.statusCode == 204) {
        debugPrint('✅ 后端账号删除成功');

        // 5. 只有后端删除成功后才清除本地数据
        debugPrint('🧹 清除本地认证数据...');
        await StorageUtils.clearAuthData();
        debugPrint('✅ 本地认证数据已清除');

        // 6. 发送登出事件
        eventBus.fire(EventType.authLogout);
        debugPrint('📢 已发送账号删除成功事件');

        return true;
      } else {
        debugPrint('❌ 后端账号删除失败，状态码: ${response.statusCode}');
        debugPrint('❌ 响应数据: ${response.data}');

        // 解析具体错误信息
        String errorMessage = '删除账号失败，请稍后重试';
        if (response.data != null && response.data is Map) {
          if (response.data['message'] != null) {
            errorMessage = response.data['message'];
          } else if (response.data['error'] != null) {
            errorMessage = response.data['error'];
          }
        }

        // 🚨 关键：后端删除失败时不清除本地数据
        debugPrint('🚨 关键安全措施：后端删除失败，保持本地登录状态');
        throw Exception(errorMessage);
      }
    } catch (e) {
      debugPrint('💥 删除账号过程中发生异常: $e');

      // 🚨 关键安全措施：异常情况下不清除本地数据
      debugPrint('🚨 关键安全措施：异常情况下保持本地登录状态，防止数据不一致');

      // 在测试模式下允许模拟删除
      if (TestConfig.isTestMode) {
        debugPrint('🧪 测试模式：允许模拟删除账号');
        await StorageUtils.clearAuthData();
        eventBus.fire(EventType.authLogout);
        return true;
      }

      // 重新抛出异常，让调用方处理
      rethrow;
    }
  }

  // 测试Apple登录API连接
  Future<Map<String, dynamic>> testAppleLoginAPI() async {
    final result = <String, dynamic>{};

    try {
      debugPrint('🧪 开始测试Apple登录API连接');

      // 1. 测试基础连接
      debugPrint('📡 测试API基础连接...');
      debugPrint('📍 API基础地址: ${ApiClient.baseUrl}');

      try {
        final pingResponse = await _apiClient.get('/health');
        result['baseConnection'] = true;
        result['healthCheck'] = pingResponse.statusCode;
        debugPrint('✅ API基础连接正常');
      } catch (e) {
        result['baseConnection'] = false;
        result['baseConnectionError'] = e.toString();
        debugPrint('❌ API基础连接失败: $e');
      }

      // 2. 测试Apple登录端点
      debugPrint('📡 测试Apple登录端点...');
      try {
        // 发送测试POST请求检查端点是否存在
        await _apiClient.post('/apple-auth/login', data: {'test': true});
        result['appleLoginEndpoint'] = true;
        debugPrint('✅ Apple登录端点存在');
      } catch (e) {
        result['appleLoginEndpoint'] = false;
        result['appleLoginError'] = e.toString();
        debugPrint('❌ Apple登录端点测试失败: $e');

        // 检查是否是400错误（端点存在但参数错误）
        if (e.toString().contains('400')) {
          result['appleLoginEndpoint'] = true;
          result['appleLoginNote'] = '端点存在但参数验证失败（正常）';
          debugPrint('ℹ️ Apple登录端点存在（参数验证失败是正常的）');
        }
      }

      // 3. 测试网络延迟
      final startTime = DateTime.now();
      try {
        await _apiClient.get('/health');
        final endTime = DateTime.now();
        result['networkLatency'] = endTime.difference(startTime).inMilliseconds;
        debugPrint('📊 网络延迟: ${result['networkLatency']}ms');
      } catch (e) {
        result['networkLatency'] = -1;
      }

      return result;
    } catch (e) {
      result['error'] = e.toString();
      debugPrint('💥 API连接测试异常: $e');
      return result;
    }
  }

  // 模拟Apple登录测试
  Future<void> simulateAppleLoginTest() async {
    debugPrint('🧪 开始模拟Apple登录测试');

    // 模拟Apple返回的凭据
    final mockCredential = {
      'userIdentifier': 'test_apple_user_${DateTime.now().millisecondsSinceEpoch}',
      'identityToken': 'mock_identity_token_${DateTime.now().millisecondsSinceEpoch}',
      'authorizationCode': 'mock_auth_code_${DateTime.now().millisecondsSinceEpoch}',
      'email': '<EMAIL>',
      'givenName': 'Test',
      'familyName': 'User',
    };

    debugPrint('🍎 模拟Apple凭据:');
    mockCredential.forEach((key, value) {
      debugPrint('  - $key: $value');
    });

    // 测试后端API调用
    try {
      debugPrint('📡 发送模拟Apple登录请求...');

      final response = await _apiClient.post('/apple-auth/login', data: {
        'identityToken': mockCredential['identityToken'],
        'authorizationCode': mockCredential['authorizationCode'],
        'userIdentifier': mockCredential['userIdentifier'],
        'email': mockCredential['email'] ?? '',
        'givenName': mockCredential['givenName'] ?? '',
        'familyName': mockCredential['familyName'] ?? '',
        'rememberMe': true,
        'deviceInfo': await _getDeviceInfo(),
      });

      debugPrint('📥 后端响应结果:');
      debugPrint('  - 状态码: ${response.statusCode}');
      debugPrint('  - 响应头: ${response.headers}');
      debugPrint('  - 响应体: ${response.data}');

      if (response.statusCode == 200) {
        debugPrint('✅ 模拟Apple登录成功');
      } else {
        debugPrint('❌ 模拟Apple登录失败');
      }

    } catch (e) {
      debugPrint('💥 模拟Apple登录异常: $e');

      if (e is DioException) {
        debugPrint('📊 详细错误信息:');
        debugPrint('  - 错误类型: ${e.type}');
        debugPrint('  - 状态码: ${e.response?.statusCode}');
        debugPrint('  - 错误消息: ${e.message}');
        debugPrint('  - 响应数据: ${e.response?.data}');
      }
    }
  }
}
