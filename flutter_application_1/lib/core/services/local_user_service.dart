import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

/// 本地用户服务
/// 管理未登录状态下的本地用户昵称
class LocalUserService {
  static const String _localNicknameKey = 'local_user_nickname';
  static const String _userCounterKey = 'user_counter';

  /// 获取本地用户昵称
  /// 如果不存在则生成一个新的
  Future<String> getLocalNickname() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? nickname = prefs.getString(_localNicknameKey);

      if (nickname == null || nickname.isEmpty) {
        // 生成新的用户昵称
        nickname = await _generateNewNickname();
        await prefs.setString(_localNicknameKey, nickname);
        debugPrint('生成新的本地用户昵称: $nickname');
      }

      return nickname;
    } catch (e) {
      debugPrint('获取本地用户昵称失败: $e');
      return 'User1'; // 默认昵称
    }
  }

  /// 保存本地用户昵称
  Future<bool> saveLocalNickname(String nickname) async {
    try {
      if (nickname.trim().isEmpty) {
        debugPrint('昵称不能为空');
        return false;
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_localNicknameKey, nickname.trim());
      debugPrint('保存本地用户昵称成功: ${nickname.trim()}');
      return true;
    } catch (e) {
      debugPrint('保存本地用户昵称失败: $e');
      return false;
    }
  }

  /// 生成新的用户昵称
  Future<String> _generateNewNickname() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      int counter = prefs.getInt(_userCounterKey) ?? 0;
      counter++;
      await prefs.setInt(_userCounterKey, counter);
      return 'User$counter';
    } catch (e) {
      debugPrint('生成用户昵称失败: $e');
      return 'User1';
    }
  }

  /// 清除本地用户昵称
  Future<void> clearLocalNickname() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_localNicknameKey);
      debugPrint('清除本地用户昵称成功');
    } catch (e) {
      debugPrint('清除本地用户昵称失败: $e');
    }
  }

  /// 检查是否有本地用户昵称
  Future<bool> hasLocalNickname() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final nickname = prefs.getString(_localNicknameKey);
      return nickname != null && nickname.isNotEmpty;
    } catch (e) {
      debugPrint('检查本地用户昵称失败: $e');
      return false;
    }
  }
}
