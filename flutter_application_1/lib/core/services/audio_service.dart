import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:dio/dio.dart';

/// 音频播放状态枚举
enum AudioPlaybackState {
  loading,    // 加载中
  playing,    // 播放中
  paused,     // 已暂停
  stopped,    // 已停止
  completed,  // 播放完成
  buffering,  // 缓冲中
  error       // 错误状态
}

/// 自定义音频服务异常类
class AudioServiceException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  AudioServiceException(this.message, {this.code, this.originalError});

  @override
  String toString() => 'AudioServiceException: $message (code: $code)';
}

/// 音频播放统计数据类
class AudioPlaybackStats {
  int playCount = 0;          // 播放次数
  Duration totalPlayTime = Duration.zero;  // 总播放时长
  double completionRate = 0.0;  // 完成率 (0.0-1.0)
  int seekCount = 0;          // 跳转次数
  int errorCount = 0;         // 错误次数

  void reset() {
    playCount = 0;
    totalPlayTime = Duration.zero;
    completionRate = 0.0;
    seekCount = 0;
    errorCount = 0;
  }

  Map<String, dynamic> toJson() => {
    'playCount': playCount,
    'totalPlayTimeSeconds': totalPlayTime.inSeconds,
    'completionRate': completionRate,
    'seekCount': seekCount,
    'errorCount': errorCount,
  };
}

/// 音频缓存配置
class AudioCacheConfig {
  final int maxSizeBytes;      // 最大缓存大小（字节）
  final Duration maxAge;       // 缓存最大保留时间
  final bool enablePreloading; // 是否启用预加载

  const AudioCacheConfig({
    this.maxSizeBytes = 100 * 1024 * 1024, // 默认100MB
    this.maxAge = const Duration(days: 7),
    this.enablePreloading = true,
  });
}

/// 音频服务类 - 负责音频播放、状态管理和缓存控制
class AudioService {
  // 核心播放器
  late AudioPlayer _player;
  late Connectivity _connectivity;
  final Dio _dio = Dio();

  // 状态管理
  final StreamController<AudioPlaybackState> _stateController =
      StreamController<AudioPlaybackState>.broadcast();
  final StreamController<Duration> _positionController =
      StreamController<Duration>.broadcast();
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();

  // 当前播放状态
  AudioPlaybackState _currentState = AudioPlaybackState.stopped;

  // 当前播放URL和位置
  String? _currentUrl;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;

  // 错误重试相关
  int _retryCount = 0;
  final int _maxRetries = 3;
  Timer? _retryTimer;

  // 网络连接监听
  StreamSubscription? _connectivitySubscription;

  // 缓存配置
  final AudioCacheConfig _cacheConfig;
  final Map<String, String> _urlToFilePathMap = {};

  // 统计数据
  final AudioPlaybackStats _stats = AudioPlaybackStats();
  DateTime? _playStartTime;

  // 获取状态流
  Stream<AudioPlaybackState> get stateStream => _stateController.stream;
  Stream<Duration> get positionStream => _positionController.stream;
  Stream<String> get errorStream => _errorController.stream;

  // 获取当前状态
  AudioPlaybackState get currentState => _currentState;
  Duration get currentPosition => _currentPosition;
  Duration get totalDuration => _totalDuration;
  AudioPlaybackStats get stats => _stats;

  // 构造函数
  AudioService({AudioCacheConfig? cacheConfig}) :
      _cacheConfig = cacheConfig ?? const AudioCacheConfig() {
    _initPlayer();
    _initConnectivity();
    _setupCacheDirectory();
  }

  // 初始化播放器
  Future<void> _initPlayer() async {
    _player = AudioPlayer();

    // 监听播放状态变化
    _player.playerStateStream.listen((playerState) {
      _handlePlayerStateChange(playerState);
    });

    // 监听播放位置变化
    _player.positionStream.listen((position) {
      _currentPosition = position;
      _positionController.add(position);
      _updatePlaybackStats(position);
    });

    // 监听播放完成
    _player.processingStateStream.listen((state) {
      if (state == ProcessingState.completed) {
        _updateState(AudioPlaybackState.completed);
        _completePlaybackStats();
      }
    });

    // 监听错误
    _player.playbackEventStream.listen((event) {},
      onError: (Object error, StackTrace stackTrace) {
        _handlePlaybackError(error.toString());
      });
  }

  // 初始化网络连接监听
  void _initConnectivity() {
    _connectivity = Connectivity();
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen((results) {
      // 如果网络恢复且当前状态是错误，尝试重新播放
      if (results.isNotEmpty && !results.contains(ConnectivityResult.none) &&
          _currentState == AudioPlaybackState.error &&
          _currentUrl != null) {
        _retryPlayback();
      }
    });
  }

  // 设置缓存目录
  Future<void> _setupCacheDirectory() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      final audioCacheDir = Directory('${cacheDir.path}/audio_cache');
      if (!await audioCacheDir.exists()) {
        await audioCacheDir.create(recursive: true);
      }

      // 清理过期缓存
      _cleanExpiredCache(audioCacheDir);
    } catch (e) {
      debugPrint('设置缓存目录失败: $e');
    }
  }

  // 清理过期缓存
  Future<void> _cleanExpiredCache(Directory cacheDir) async {
    try {
      final now = DateTime.now();
      final files = await cacheDir.list().toList();
      int totalSize = 0;

      // 计算总大小并删除过期文件
      for (var entity in files) {
        if (entity is File) {
          final stat = await entity.stat();
          final fileAge = now.difference(stat.modified);

          if (fileAge > _cacheConfig.maxAge) {
            await entity.delete();
          } else {
            totalSize += stat.size;
          }
        }
      }

      // 如果总大小超过限制，删除最旧的文件
      if (totalSize > _cacheConfig.maxSizeBytes) {
        // 先收集所有文件的状态信息
        final fileStats = await Future.wait(
          files.map((entity) async {
            if (entity is File) {
              final stat = await entity.stat();
              return MapEntry(entity, stat);
            }
            return null;
          })
        );

        // 过滤掉非文件项并按修改时间排序
        final sortedFiles = fileStats
          .where((entry) => entry != null)
          .map((entry) => entry!)
          .toList()
          ..sort((a, b) => a.value.modified.compareTo(b.value.modified));

        // 删除旧文件直到总大小符合要求
        for (var entry in sortedFiles) {
          final file = entry.key;
          final size = entry.value.size;
          await file.delete();
          totalSize -= size;
          if (totalSize <= _cacheConfig.maxSizeBytes) break;
        }
      }
    } catch (e) {
      debugPrint('清理缓存失败: $e');
    }
  }

  // 处理播放器状态变化
  void _handlePlayerStateChange(PlayerState playerState) {
    switch (playerState.processingState) {
      case ProcessingState.loading:
      case ProcessingState.buffering:
        _updateState(AudioPlaybackState.buffering);
        break;
      case ProcessingState.ready:
        if (playerState.playing) {
          _updateState(AudioPlaybackState.playing);
        } else {
          _updateState(AudioPlaybackState.paused);
        }
        break;
      case ProcessingState.completed:
        _updateState(AudioPlaybackState.completed);
        break;
      case ProcessingState.idle:
        _updateState(AudioPlaybackState.stopped);
        break;
      default:
        break;
    }
  }

  // 更新播放状态
  void _updateState(AudioPlaybackState newState) {
    if (_currentState != newState) {
      _currentState = newState;
      _stateController.add(newState);

      // 记录播放开始时间
      if (newState == AudioPlaybackState.playing && _playStartTime == null) {
        _playStartTime = DateTime.now();
      }

      // 记录播放结束时间并更新统计数据
      if ((newState == AudioPlaybackState.paused ||
           newState == AudioPlaybackState.stopped ||
           newState == AudioPlaybackState.completed) &&
          _playStartTime != null) {
        final playDuration = DateTime.now().difference(_playStartTime!);
        _stats.totalPlayTime += playDuration;
        _playStartTime = null;
      }
    }
  }

  // 处理播放错误
  void _handlePlaybackError(String errorMessage) {
    _stats.errorCount++;
    _updateState(AudioPlaybackState.error);
    _errorController.add(errorMessage);

    // 记录详细错误日志
    debugPrint('音频播放错误: $errorMessage');
    debugPrint('当前URL: $_currentUrl');
    debugPrint('重试次数: $_retryCount');

    // 检查是否需要重试
    if (_retryCount < _maxRetries) {
      _checkNetworkAndRetry();
    }
  }

  // 检查网络连接并尝试重试
  Future<void> _checkNetworkAndRetry() async {
    final connectivityResults = await _connectivity.checkConnectivity();
    if (connectivityResults.isNotEmpty && !connectivityResults.contains(ConnectivityResult.none)) {
      // 增加重试延迟
      final delay = Duration(seconds: 2 * (1 << _retryCount));
      debugPrint('准备重试播放，延迟: ${delay.inSeconds}秒');

      _retryTimer?.cancel();
      _retryTimer = Timer(delay, () async {
        try {
          if (_currentUrl != null) {
            debugPrint('开始第${_retryCount + 1}次重试');
            await play(_currentUrl!);
          }
        } catch (e) {
          debugPrint('重试失败: $e');
          _handlePlaybackError('重试失败: $e');
        }
      });
    } else {
      debugPrint('网络未连接，无法重试');
      _errorController.add('网络未连接，请检查网络后重试');
    }
  }

  // 重试播放
  Future<void> _retryPlayback() async {
    if (_retryCount < _maxRetries && _currentUrl != null) {
      _retryCount++;

      // 使用指数退避策略
      final delay = Duration(seconds: 1 * (1 << (_retryCount - 1)));
      _retryTimer?.cancel();
      _retryTimer = Timer(delay, () async {
        try {
          await play(_currentUrl!);
        } catch (e) {
          // 重试失败，继续处理错误
          _handlePlaybackError('重试失败: $e');
        }
      });
    } else {
      // 超过最大重试次数
      _errorController.add('播放失败，已超过最大重试次数');
    }
  }

  // 更新播放统计数据
  void _updatePlaybackStats(Duration position) {
    if (_totalDuration.inMilliseconds > 0) {
      _stats.completionRate = position.inMilliseconds / _totalDuration.inMilliseconds;
    }
  }

  // 完成播放统计
  void _completePlaybackStats() {
    _stats.completionRate = 1.0;
  }

  // 验证URL是否有效
  Future<bool> _isValidUrl(String url) async {
    if (url.isEmpty) return false;

    try {
      // 检查URL格式
      Uri uri = Uri.parse(url);
      if (!uri.isAbsolute) return false;

      // 对于HTTP/HTTPS URL，尝试检查资源是否存在
      if (uri.scheme == 'http' || uri.scheme == 'https') {
        try {
          final response = await _dio.head(
            url,
            options: Options(
              validateStatus: (status) => status != null && status < 400,
              sendTimeout: const Duration(seconds: 5),
              receiveTimeout: const Duration(seconds: 5),
              followRedirects: true,
              maxRedirects: 5
            )
          );
          return response.statusCode != null && response.statusCode! < 400;
        } catch (e) {
          debugPrint('URL验证请求失败: $e');
          // 对于网络错误，我们仍然返回true，因为URL格式本身是有效的
          return true;
        }
      }

      // 对于非HTTP URL（如file://），只要格式正确就认为是有效的
      return true;
    } catch (e) {
      debugPrint('URL解析失败: $e');
      return false;
    }
  }

  // 检查并下载音频到缓存
  Future<String> _getCachedAudioPath(String url) async {
    final cacheDir = await getTemporaryDirectory();
    final audioCacheDir = Directory('${cacheDir.path}/audio_cache');
    if (!await audioCacheDir.exists()) {
      await audioCacheDir.create(recursive: true);
    }

    // 使用URL的哈希值和原始文件扩展名作为缓存文件名
    final uri = Uri.parse(url);
    final fileExtension = uri.path.split('.').last.toLowerCase();
    final fileName = '${url.hashCode}.$fileExtension';
    final audioPath = '${audioCacheDir.path}/$fileName';

    // 检查缓存文件是否存在
    final audioFile = File(audioPath);
    if (!await audioFile.exists()) {
      debugPrint('缓存文件不存在，开始下载...');
      try {
        final response = await _dio.get(
          url,
          options: Options(responseType: ResponseType.bytes),
        );
        await audioFile.writeAsBytes(response.data);
        debugPrint('音频文件下载完成');
      } catch (e) {
        throw AudioServiceException('音频文件下载失败: $e', code: 'download_error');
      }
    }

    return audioPath;
  }

  // 预加载下一个音频
  Future<void> preloadAudio(String url) async {
    if (!_cacheConfig.enablePreloading) return;

    try {
      // 验证URL
      if (!await _isValidUrl(url)) {
        return;
      }

      // 检查是否已缓存
      if (_urlToFilePathMap.containsKey(url)) {
        final cachedPath = _urlToFilePathMap[url]!;
        final cachedFile = File(cachedPath);
        if (await cachedFile.exists()) {
          return; // 已缓存，无需预加载
        }
      }

      // 异步下载，不等待结果
      _getCachedAudioPath(url).then((_) {
        debugPrint('预加载完成: $url');
      }).catchError((e) {
        debugPrint('预加载失败: $e');
      });
    } catch (e) {
      debugPrint('预加载异常: $e');
    }
  }

  // 播放音频
  Future<void> play(String url) async {
    try {
      // 重置重试计数
      _retryCount = 0;

      // 检查网络连接
      final connectivityResults = await _connectivity.checkConnectivity();
      if (connectivityResults.isEmpty || connectivityResults.contains(ConnectivityResult.none)) {
        throw AudioServiceException('无网络连接');
      }

      // 验证URL
      debugPrint('正在验证音频URL: $url');
      if (!await _isValidUrl(url)) {
        throw AudioServiceException('无效的音频URL', code: 'invalid_url');
      }

      _currentUrl = url;
      _updateState(AudioPlaybackState.loading);

      // 获取缓存路径
      debugPrint('正在获取音频缓存路径...');
      final audioPath = await _getCachedAudioPath(url);
      debugPrint('音频缓存路径: $audioPath');

      // 确保文件存在且可读
      final audioFile = File(audioPath);
      if (!await audioFile.exists()) {
        throw AudioServiceException('音频文件不存在', code: 'file_not_found');
      }

      // 设置音频源前先停止当前播放
      await _player.stop();

      // 验证音频文件格式
      String getFileExtension(String path) {
        final uriPath = Uri.parse(path).path.toLowerCase();
        return uriPath.split('.').last;
      }

      final urlExtension = getFileExtension(url);
      final fileExtension = getFileExtension(audioPath);
      final supportedFormats = ['mp3', 'wav', 'aac', 'm4a'];

      if (!supportedFormats.contains(urlExtension)) {
        throw AudioServiceException('不支持的音频格式: $urlExtension', code: 'format_error');
      }

      if (!supportedFormats.contains(fileExtension)) {
        throw AudioServiceException('缓存文件格式错误: $fileExtension', code: 'format_error');
      }

      // 设置音频源并等待加载完成
      try {
        debugPrint('正在设置音频源...');
        debugPrint('音频文件格式: $fileExtension');
        await _player.setAudioSource(AudioSource.file(audioPath));
      } catch (e) {
        debugPrint('设置音频源失败: $e');
        throw AudioServiceException('音频源设置失败 (格式: $fileExtension)', code: 'source_error', originalError: e);
      }

      // 等待获取音频时长
      _totalDuration = _player.duration ?? Duration.zero;
      if (_totalDuration == Duration.zero) {
        throw AudioServiceException('无法获取音频时长', code: 'duration_error');
      }

      // 开始播放
      debugPrint('开始播放音频...');
      await _player.play();

      // 更新统计数据
      _stats.playCount++;

    } catch (e) {
      final errorMessage = e is AudioServiceException ? e.toString() : '播放失败: ${e.toString()}';
      debugPrint('音频播放错误: $errorMessage');
      _handlePlaybackError(errorMessage);
      rethrow;
    }
  }

  // 暂停播放
  Future<void> pause() async {
    try {
      await _player.pause();
      _updateState(AudioPlaybackState.paused);
    } catch (e) {
      _handlePlaybackError('暂停失败: $e');
      rethrow;
    }
  }

  // 停止播放
  Future<void> stop() async {
    try {
      await _player.stop();
      _updateState(AudioPlaybackState.stopped);
      _currentPosition = Duration.zero;
    } catch (e) {
      _handlePlaybackError('停止失败: $e');
      rethrow;
    }
  }

  // 跳转到指定位置
  Future<void> seekTo(Duration position) async {
    try {
      await _player.seek(position);
      _currentPosition = position;
      _positionController.add(position);
      _stats.seekCount++;
    } catch (e) {
      _handlePlaybackError('跳转失败: $e');
      rethrow;
    }
  }

  // 调整音量
  Future<void> changeVolume(double volume) async {
    try {
      if (volume < 0.0) volume = 0.0;
      if (volume > 1.0) volume = 1.0;

      await _player.setVolume(volume);
    } catch (e) {
      _handlePlaybackError('调整音量失败: $e');
      rethrow;
    }
  }

  // 设置播放速度
  Future<void> setPlaybackSpeed(double speed) async {
    try {
      if (speed < 0.5) speed = 0.5;
      if (speed > 2.0) speed = 2.0;

      await _player.setSpeed(speed);
    } catch (e) {
      _handlePlaybackError('设置播放速度失败: $e');
      rethrow;
    }
  }

  // 清除缓存
  Future<void> clearCache() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      final audioCacheDir = Directory('${cacheDir.path}/audio_cache');

      if (await audioCacheDir.exists()) {
        await audioCacheDir.delete(recursive: true);
        await audioCacheDir.create(recursive: true);
      }

      _urlToFilePathMap.clear();
    } catch (e) {
      debugPrint('清除缓存失败: $e');
    }
  }

  // 获取缓存大小
  Future<int> getCacheSize() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      final audioCacheDir = Directory('${cacheDir.path}/audio_cache');

      if (!await audioCacheDir.exists()) {
        return 0;
      }

      int totalSize = 0;
      final files = await audioCacheDir.list().toList();

      for (var entity in files) {
        if (entity is File) {
          final stat = await entity.stat();
          totalSize += stat.size;
        }
      }

      return totalSize;
    } catch (e) {
      debugPrint('获取缓存大小失败: $e');
      return 0;
    }
  }

  // 释放资源
  void dispose() {
    _player.dispose();
    _stateController.close();
    _positionController.close();
    _errorController.close();
    _connectivitySubscription?.cancel();
    _retryTimer?.cancel();
  }
}