import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:image/image.dart' as img;

/// 本地存储服务，用于处理文件存储
class LocalStorageService {
  // 单例模式
  static final LocalStorageService _instance = LocalStorageService._internal();
  factory LocalStorageService() => _instance;
  
  LocalStorageService._internal();
  
  /// 获取应用文档目录
  Future<Directory> get _appDocDir async {
    return await getApplicationDocumentsDirectory();
  }
  
  /// 获取头像目录
  Future<Directory> get _avatarDir async {
    final appDir = await _appDocDir;
    final avatarDir = Directory('${appDir.path}/avatars');
    
    // 确保目录存在
    if (!await avatarDir.exists()) {
      await avatarDir.create(recursive: true);
    }
    
    return avatarDir;
  }
  
  /// 保存头像到本地
  /// 返回保存后的文件路径
  Future<String> saveAvatar(File imageFile) async {
    try {
      // 获取头像目录
      final avatarDir = await _avatarDir;
      
      // 生成唯一文件名
      final fileName = 'avatar_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final filePath = path.join(avatarDir.path, fileName);
      
      // 压缩图片
      final compressedFile = await _compressImage(imageFile, filePath);
      
      return compressedFile.path;
    } catch (e) {
      debugPrint('保存头像失败: $e');
      rethrow;
    }
  }
  
  /// 压缩图片
  Future<File> _compressImage(File imageFile, String targetPath) async {
    try {
      // 读取图片
      final bytes = await imageFile.readAsBytes();
      img.Image? image = img.decodeImage(bytes);
      
      if (image == null) {
        throw Exception('无法解码图片');
      }
      
      // 调整图片大小
      int targetWidth = 300;
      int targetHeight = (image.height * targetWidth / image.width).round();
      
      // 调整图片大小
      final resizedImage = img.copyResize(
        image,
        width: targetWidth,
        height: targetHeight,
      );
      
      // 编码为JPEG
      final compressedBytes = img.encodeJpg(resizedImage, quality: 85);
      
      // 保存到文件
      final compressedFile = File(targetPath);
      await compressedFile.writeAsBytes(compressedBytes);
      
      return compressedFile;
    } catch (e) {
      debugPrint('压缩图片失败: $e');
      
      // 如果压缩失败，直接复制原文件
      return await imageFile.copy(targetPath);
    }
  }
  
  /// 获取头像文件
  Future<File?> getAvatarFile(String fileName) async {
    try {
      final avatarDir = await _avatarDir;
      final filePath = path.join(avatarDir.path, fileName);
      final file = File(filePath);
      
      if (await file.exists()) {
        return file;
      }
      
      return null;
    } catch (e) {
      debugPrint('获取头像文件失败: $e');
      return null;
    }
  }
  
  /// 删除头像文件
  Future<bool> deleteAvatarFile(String fileName) async {
    try {
      final avatarDir = await _avatarDir;
      final filePath = path.join(avatarDir.path, fileName);
      final file = File(filePath);
      
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('删除头像文件失败: $e');
      return false;
    }
  }
  
  /// 清理头像目录
  Future<bool> cleanAvatarDirectory() async {
    try {
      final avatarDir = await _avatarDir;
      
      if (await avatarDir.exists()) {
        await avatarDir.delete(recursive: true);
        await avatarDir.create(recursive: true);
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('清理头像目录失败: $e');
      return false;
    }
  }
}
