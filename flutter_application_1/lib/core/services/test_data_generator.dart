import 'dart:math';
import 'package:flutter/material.dart';
import '../models/focus_record.dart';
import '../models/subject_project.dart';
import 'hive_service.dart';

/// 测试数据生成器
/// 用于生成模拟专注数据，以便测试数据分析页面
class TestDataGenerator {
  final HiveService _hiveService;
  final Random _random = Random();

  TestDataGenerator(this._hiveService);

  /// 初始化数据生成器
  /// 确保 Hive 数据库已正确初始化
  /// 返回是否有可用的科目和项目
  Future<bool> initialize() async {
    debugPrint('TestDataGenerator: 开始初始化');
    try {
      // 确保 Hive 服务已初始化
      await _hiveService.initHive();

      // 检查是否有可用的科目和项目
      final hasSubjectsAndProjects = await checkSubjectsAndProjects();

      if (!hasSubjectsAndProjects) {
        debugPrint('TestDataGenerator: 警告 - 没有可用的科目和项目，无法生成有效的专注记录');
        debugPrint('TestDataGenerator: 请先创建至少一个科目和项目，再使用测试数据生成器');
        return false;
      }

      debugPrint('TestDataGenerator: 初始化成功，可以生成专注记录');
      return true;
    } catch (e, stackTrace) {
      debugPrint('TestDataGenerator: 初始化失败: $e');
      debugPrint('TestDataGenerator: 错误堆栈: $stackTrace');
      return false;
    }
  }

  /// 生成今日专注记录
  /// [count] 记录数量
  /// [minDuration] 最小专注时长（分钟）
  /// [maxDuration] 最大专注时长（分钟）
  /// [completionRate] 完成率（0-1）
  Future<List<FocusRecord>> generateTodayRecords({
    int count = 5,
    int minDuration = 15,
    int maxDuration = 120,
    double completionRate = 0.8,
  }) async {
    debugPrint('generateTodayRecords: 开始生成今日记录');

    // 确保已初始化
    final initialized = await initialize();
    if (!initialized) {
      debugPrint('generateTodayRecords: 初始化失败，无法生成专注记录');
      return [];
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    debugPrint('generateTodayRecords: 今日日期为 ${today.toString().split(' ')[0]}');

    try {
      final records = await _generateRecordsForDate(
        date: today,
        count: count,
        minDuration: minDuration,
        maxDuration: maxDuration,
        completionRate: completionRate,
        forceToday: true, // 强制使用今天的日期
      );
      debugPrint('generateTodayRecords: 成功生成 ${records.length} 条记录');

      // 验证生成的记录是否都是今天的
      for (final record in records) {
        final recordDate = DateTime(
          record.startTime.year,
          record.startTime.month,
          record.startTime.day,
        );
        final isToday = recordDate.isAtSameMomentAs(today);
        debugPrint('记录日期: ${record.startTime.toString().split(' ')[0]}, 是今天: $isToday');
      }

      return records;
    } catch (e, stackTrace) {
      debugPrint('generateTodayRecords: 生成记录失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      rethrow;
    }
  }

  /// 生成本周专注记录
  /// [countPerDay] 每天的记录数量
  /// [minDuration] 最小专注时长（分钟）
  /// [maxDuration] 最大专注时长（分钟）
  /// [completionRate] 完成率（0-1）
  Future<List<FocusRecord>> generateWeekRecords({
    int countPerDay = 3,
    int minDuration = 15,
    int maxDuration = 120,
    double completionRate = 0.8,
  }) async {
    debugPrint('generateWeekRecords: 开始生成本周记录');

    // 确保已初始化
    final initialized = await initialize();
    if (!initialized) {
      debugPrint('generateWeekRecords: 初始化失败，无法生成专注记录');
      return [];
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final records = <FocusRecord>[];

    try {
      // 生成过去7天的记录
      for (int i = 0; i < 7; i++) {
        final date = today.subtract(Duration(days: i));
        debugPrint('generateWeekRecords: 生成 ${date.toString().split(' ')[0]} 的记录');

        final dailyRecords = await _generateRecordsForDate(
          date: date,
          count: countPerDay,
          minDuration: minDuration,
          maxDuration: maxDuration,
          completionRate: completionRate,
        );

        records.addAll(dailyRecords);
      }

      debugPrint('generateWeekRecords: 成功生成 ${records.length} 条记录');
      return records;
    } catch (e, stackTrace) {
      debugPrint('generateWeekRecords: 生成记录失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      return [];
    }
  }

  /// 生成本月专注记录
  /// [countPerDay] 每天的记录数量
  /// [minDuration] 最小专注时长（分钟）
  /// [maxDuration] 最大专注时长（分钟）
  /// [completionRate] 完成率（0-1）
  Future<List<FocusRecord>> generateMonthRecords({
    int countPerDay = 2,
    int minDuration = 15,
    int maxDuration = 120,
    double completionRate = 0.8,
  }) async {
    debugPrint('generateMonthRecords: 开始生成本月记录');

    // 确保已初始化
    final initialized = await initialize();
    if (!initialized) {
      debugPrint('generateMonthRecords: 初始化失败，无法生成专注记录');
      return [];
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final records = <FocusRecord>[];

    try {
      // 生成过去30天的记录
      for (int i = 0; i < 30; i++) {
        final date = today.subtract(Duration(days: i));
        debugPrint('generateMonthRecords: 生成 ${date.toString().split(' ')[0]} 的记录');

        final dailyRecords = await _generateRecordsForDate(
          date: date,
          count: countPerDay,
          minDuration: minDuration,
          maxDuration: maxDuration,
          completionRate: completionRate,
        );

        records.addAll(dailyRecords);
      }

      debugPrint('generateMonthRecords: 成功生成 ${records.length} 条记录');
      return records;
    } catch (e, stackTrace) {
      debugPrint('generateMonthRecords: 生成记录失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      return [];
    }
  }

  /// 生成历史专注记录
  /// [months] 生成多少个月的数据
  /// [countPerDay] 每天的记录数量
  /// [minDuration] 最小专注时长（分钟）
  /// [maxDuration] 最大专注时长（分钟）
  /// [completionRate] 完成率（0-1）
  Future<List<FocusRecord>> generateHistoryRecords({
    int months = 3,
    int countPerDay = 1,
    int minDuration = 15,
    int maxDuration = 120,
    double completionRate = 0.8,
  }) async {
    debugPrint('generateHistoryRecords: 开始生成历史记录');

    // 确保已初始化
    final initialized = await initialize();
    if (!initialized) {
      debugPrint('generateHistoryRecords: 初始化失败，无法生成专注记录');
      return [];
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final records = <FocusRecord>[];

    try {
      // 生成过去几个月的记录
      for (int i = 0; i < months * 30; i++) {
        final date = today.subtract(Duration(days: i));

        // 每10天输出一次日志，避免日志过多
        if (i % 10 == 0) {
          debugPrint('generateHistoryRecords: 生成 ${date.toString().split(' ')[0]} 的记录');
        }

        final dailyRecords = await _generateRecordsForDate(
          date: date,
          count: countPerDay,
          minDuration: minDuration,
          maxDuration: maxDuration,
          completionRate: completionRate,
        );

        records.addAll(dailyRecords);
      }

      debugPrint('generateHistoryRecords: 成功生成 ${records.length} 条记录');
      return records;
    } catch (e, stackTrace) {
      debugPrint('generateHistoryRecords: 生成记录失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      return [];
    }
  }

  /// 为指定日期生成专注记录
  Future<List<FocusRecord>> _generateRecordsForDate({
    required DateTime date,
    required int count,
    required int minDuration,
    required int maxDuration,
    required double completionRate,
    bool forceToday = false, // 是否强制使用今天的日期
  }) async {
    debugPrint('_generateRecordsForDate: 开始为 ${date.toString().split(' ')[0]} 生成 $count 条记录');
    final records = <FocusRecord>[];

    try {
      // 获取现有科目，不创建新科目
      debugPrint('_generateRecordsForDate: 获取现有科目');
      final subjects = _hiveService.subjectRepository.getAllSubjects();

      if (subjects.isEmpty) {
        debugPrint('_generateRecordsForDate: 错误 - 没有可用的科目，无法生成专注记录');
        return records;
      }

      debugPrint('_generateRecordsForDate: 获取到 ${subjects.length} 个科目');

      for (int i = 0; i < count; i++) {
        debugPrint('_generateRecordsForDate: 生成第 ${i+1} 条记录');

        // 随机选择一个科目
        final subject = subjects[_random.nextInt(subjects.length)];
        debugPrint('_generateRecordsForDate: 选择科目 ${subject.name}');

        // 获取该科目的现有项目，不创建新项目
        debugPrint('_generateRecordsForDate: 获取科目 ${subject.id} 的项目');
        final projects = _hiveService.subjectRepository.getProjectsBySubjectId(subject.id);

        Project? project;
        if (projects.isNotEmpty) {
          project = projects[_random.nextInt(projects.length)];
          debugPrint('_generateRecordsForDate: 选择项目 ${project.name}');
        } else {
          debugPrint('_generateRecordsForDate: 科目 ${subject.name} 没有项目，将不使用项目');
        }

        // 随机生成开始时间
        DateTime startTime;

        if (forceToday) {
          // 如果强制使用今天的日期，则使用今天的日期和随机时刻
          final now = DateTime.now();
          final today = DateTime(now.year, now.month, now.day);

          // 生成今天的随机时刻，但确保不超过当前时间
          final currentHour = now.hour;
          final startHour = _random.nextInt(currentHour > 6 ? currentHour - 5 : 1) + 6; // 6:00 - 当前时间
          final startMinute = _random.nextInt(60);

          startTime = DateTime(
            today.year,
            today.month,
            today.day,
            startHour,
            startMinute,
          );

          debugPrint('_generateRecordsForDate: 强制使用今天的日期，生成时间: ${startTime.toString()}');
        } else {
          // 否则使用指定的日期和随机时刻
          final startHour = _random.nextInt(16) + 6; // 6:00 - 22:00
          final startMinute = _random.nextInt(60);

          startTime = DateTime(
            date.year,
            date.month,
            date.day,
            startHour,
            startMinute,
          );
        }

        // 随机生成持续时间（分钟）
        final durationMinutes = minDuration + _random.nextInt(maxDuration - minDuration + 1);
        final durationSeconds = durationMinutes * 60;

        // 随机决定是否完成
        final isCompleted = _random.nextDouble() < completionRate;

        // 计算结束时间
        final endTime = startTime.add(Duration(seconds: durationSeconds));

        debugPrint('_generateRecordsForDate: 创建专注记录');
        // 创建专注记录
        final record = FocusRecord(
          id: DateTime.now().millisecondsSinceEpoch.toString() + i.toString(),
          subjectId: subject.id,
          projectId: project?.id ?? '',
          startTime: startTime,
          endTime: endTime,
          durationSeconds: durationSeconds,
          isCountdown: true,
          plannedDurationMinutes: durationMinutes,
          status: isCompleted ? FocusRecordStatus.completed : FocusRecordStatus.interrupted,
          interruptionCount: isCompleted ? 0 : _random.nextInt(3) + 1,
          notes: '测试数据 - ${subject.name} ${project?.name ?? ''}',
        );

        debugPrint('_generateRecordsForDate: 记录创建成功，添加到列表');
        records.add(record);

        // 等待一毫秒，确保ID不重复
        await Future.delayed(const Duration(milliseconds: 1));
      }

      debugPrint('_generateRecordsForDate: 成功生成 ${records.length} 条记录');
      return records;
    } catch (e, stackTrace) {
      debugPrint('_generateRecordsForDate: 生成记录失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      rethrow;
    }
  }

  /// 检查是否有可用的科目和项目
  Future<bool> checkSubjectsAndProjects() async {
    debugPrint('checkSubjectsAndProjects: 开始检查科目和项目');
    try {
      // 确保仓库已初始化
      await _hiveService.subjectRepository.init();

      // 获取所有科目
      final subjects = _hiveService.subjectRepository.getAllSubjects();
      debugPrint('checkSubjectsAndProjects: 找到 ${subjects.length} 个科目');

      if (subjects.isEmpty) {
        debugPrint('checkSubjectsAndProjects: 没有找到科目，请先创建科目');
        return false;
      }

      // 检查每个科目是否有项目
      int subjectsWithProjects = 0;
      for (final subject in subjects) {
        final projects = _hiveService.subjectRepository.getProjectsBySubjectId(subject.id);
        if (projects.isNotEmpty) {
          subjectsWithProjects++;
          debugPrint('checkSubjectsAndProjects: 科目 ${subject.name} 有 ${projects.length} 个项目');
        } else {
          debugPrint('checkSubjectsAndProjects: 科目 ${subject.name} 没有项目');
        }
      }

      if (subjectsWithProjects == 0) {
        debugPrint('checkSubjectsAndProjects: 所有科目都没有项目，建议先创建项目');
      }

      return true;
    } catch (e, stackTrace) {
      debugPrint('checkSubjectsAndProjects: 检查科目和项目失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      return false;
    }
  }

  /// 保存生成的记录
  Future<void> saveRecords(List<FocusRecord> records) async {
    debugPrint('saveRecords: 开始保存 ${records.length} 条记录');
    try {
      // 确保仓库已初始化
      debugPrint('saveRecords: 初始化专注记录仓库');
      await _hiveService.focusRecordRepository.init();

      for (int i = 0; i < records.length; i++) {
        final record = records[i];
        debugPrint('saveRecords: 保存第 ${i+1}/${records.length} 条记录，日期: ${record.startTime.toString().split(' ')[0]}');
        await _hiveService.focusRecordRepository.saveFocusRecord(record);
      }

      debugPrint('saveRecords: 所有记录保存成功');

      // 验证今日记录是否保存成功
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final tomorrow = today.add(const Duration(days: 1));

      final todayRecords = _hiveService.focusRecordRepository.getFocusRecordsByDateRange(today, tomorrow);
      debugPrint('saveRecords: 验证 - 今日记录数量: ${todayRecords.length}');

      // 检查今日科目分布
      if (todayRecords.isNotEmpty) {
        final Map<String, double> subjectHours = {};
        double totalHours = 0;

        for (final record in todayRecords) {
          final subjectId = record.subjectId;
          final hours = record.durationSeconds / 3600.0;

          if (!subjectHours.containsKey(subjectId)) {
            subjectHours[subjectId] = 0;
          }

          subjectHours[subjectId] = (subjectHours[subjectId] ?? 0) + hours;
          totalHours += hours;
        }

        debugPrint('saveRecords: 验证 - 今日总专注时长: ${totalHours.toStringAsFixed(2)} 小时');
        debugPrint('saveRecords: 验证 - 今日科目分布:');

        for (final entry in subjectHours.entries) {
          final subject = _hiveService.subjectRepository.getSubjectById(entry.key);
          if (subject != null) {
            final percentage = totalHours > 0 ? entry.value / totalHours * 100 : 0;
            debugPrint('  ${subject.name}: ${entry.value.toStringAsFixed(2)} 小时 (${percentage.toStringAsFixed(1)}%)');
          }
        }
      }
    } catch (e, stackTrace) {
      debugPrint('saveRecords: 保存记录失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      rethrow;
    }
  }

  /// 清除所有测试数据
  Future<void> clearAllTestData() async {
    // 清除所有专注记录
    final records = _hiveService.focusRecordRepository.getAllFocusRecords();
    for (final record in records) {
      if (record.notes != null && record.notes!.startsWith('测试数据')) {
        await _hiveService.focusRecordRepository.deleteFocusRecord(record.id);
      }
    }
  }

  /// 一键生成完整测试数据集
  Future<bool> generateCompleteTestDataSet() async {
    debugPrint('generateCompleteTestDataSet: 开始生成完整测试数据集');

    // 确保已初始化
    final initialized = await initialize();
    if (!initialized) {
      debugPrint('generateCompleteTestDataSet: 初始化失败，无法生成专注记录');
      return false;
    }

    try {
      // 清除现有测试数据
      await clearAllTestData();

      // 生成今日数据
      final todayRecords = await generateTodayRecords(
        count: 8,
        minDuration: 20,
        maxDuration: 90,
        completionRate: 0.85,
      );
      if (todayRecords.isNotEmpty) {
        await saveRecords(todayRecords);
      }

      // 生成本周数据
      final weekRecords = await generateWeekRecords(
        countPerDay: 5,
        minDuration: 15,
        maxDuration: 120,
        completionRate: 0.8,
      );
      if (weekRecords.isNotEmpty) {
        await saveRecords(weekRecords);
      }

      // 生成本月数据
      final monthRecords = await generateMonthRecords(
        countPerDay: 3,
        minDuration: 15,
        maxDuration: 150,
        completionRate: 0.75,
      );
      if (monthRecords.isNotEmpty) {
        await saveRecords(monthRecords);
      }

      // 生成历史数据
      final historyRecords = await generateHistoryRecords(
        months: 6,
        countPerDay: 2,
        minDuration: 10,
        maxDuration: 180,
        completionRate: 0.7,
      );
      if (historyRecords.isNotEmpty) {
        await saveRecords(historyRecords);
      }

      final totalRecords = todayRecords.length + weekRecords.length + monthRecords.length + historyRecords.length;

      if (totalRecords > 0) {
        debugPrint('generateCompleteTestDataSet: 已生成完整测试数据集');
        debugPrint('今日数据: ${todayRecords.length} 条');
        debugPrint('本周数据: ${weekRecords.length} 条');
        debugPrint('本月数据: ${monthRecords.length} 条');
        debugPrint('历史数据: ${historyRecords.length} 条');
        debugPrint('总计: $totalRecords 条');
        return true;
      } else {
        debugPrint('generateCompleteTestDataSet: 未能生成任何测试数据');
        return false;
      }
    } catch (e, stackTrace) {
      debugPrint('generateCompleteTestDataSet: 生成完整测试数据集失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      return false;
    }
  }
}
