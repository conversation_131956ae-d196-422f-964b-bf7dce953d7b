import 'package:flutter/foundation.dart';
import 'package:limefocus/core/models/adapters/goal_adapter.dart';
import 'package:limefocus/core/models/adapters/subject_adapter.dart';
import 'package:limefocus/core/models/adapters/schedule_adapter.dart';
import 'package:limefocus/core/models/adapters/focus_record_adapter.dart';
import 'package:limefocus/core/models/adapters/exam_template_adapter.dart';
// ProjectProgressChange和CheckInTask适配器已通过@HiveType注解自动注册
import 'package:limefocus/core/repositories/goal_repository.dart';
import 'package:limefocus/core/repositories/subject_repository.dart';
import 'package:limefocus/core/repositories/schedule_repository.dart';
import 'package:limefocus/core/repositories/focus_record_repository.dart';
import 'package:limefocus/core/repositories/exam_template_repository.dart';
import 'package:limefocus/core/repositories/project_progress_repository.dart';
import 'package:limefocus/core/repositories/check_in_task_repository.dart';
import 'package:limefocus/core/models/project_progress_change.dart';
import 'package:limefocus/core/models/check_in_task.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'check_in_sync_service.dart';

/// 增强版Hive服务类，添加了错误处理、恢复机制和备份功能
class EnhancedHiveService {
  // 单例模式
  static final EnhancedHiveService _instance = EnhancedHiveService._internal();
  factory EnhancedHiveService() => _instance;
  EnhancedHiveService._internal() {
    // 初始化同步服务
    _checkInSyncService = CheckInSyncService(
      scheduleRepository: _scheduleRepository,
      checkInTaskRepository: _checkInTaskRepository,
    );
  }

  // 仓库实例
  final GoalRepository _goalRepository = GoalRepository();
  final SubjectRepository _subjectRepository = SubjectRepository();
  final ScheduleRepository _scheduleRepository = ScheduleRepository();
  final FocusRecordRepository _focusRecordRepository = FocusRecordRepository();
  final ExamTemplateRepository _examTemplateRepository = ExamTemplateRepository();
  final ProjectProgressRepository _projectProgressRepository = ProjectProgressRepository();
  final CheckInTaskRepository _checkInTaskRepository = CheckInTaskRepository();

  // 服务实例
  late final CheckInSyncService _checkInSyncService;

  // 版本信息
  static const int currentVersion = 1;
  static const String versionBoxName = 'app_version';

  // 公开的仓库访问器
  GoalRepository get goalRepository => _goalRepository;
  SubjectRepository get subjectRepository => _subjectRepository;
  ScheduleRepository get scheduleRepository => _scheduleRepository;
  FocusRecordRepository get focusRecordRepository => _focusRecordRepository;
  ExamTemplateRepository get examTemplateRepository => _examTemplateRepository;
  ProjectProgressRepository get projectProgressRepository => _projectProgressRepository;
  CheckInTaskRepository get checkInTaskRepository => _checkInTaskRepository;

  // 公开的服务访问器
  CheckInSyncService get checkInSyncService => _checkInSyncService;

  // 初始化状态
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  /// 初始化Hive
  Future<void> initHive() async {
    if (_isInitialized) {
      debugPrint('Hive已经初始化，跳过');
      return;
    }

    try {
      // 初始化Hive（适用于Flutter应用）
      await Hive.initFlutter();

      // 注册适配器
      _registerAdapters();

      // 检查版本并执行迁移（如果需要）
      await _checkVersionAndMigrate();

      // 初始化仓库
      await _initRepositories();

      // 初始化同步服务
      await _checkInSyncService.init();

      // 同步打卡任务
      await _checkInSyncService.syncAllTasks();

      // 标记为已初始化
      _isInitialized = true;
      debugPrint('Hive初始化完成');
    } catch (e) {
      debugPrint('初始化Hive时发生错误: $e');

      // 尝试恢复
      await _attemptRecovery();
    }
  }

  /// 尝试恢复Hive数据库
  Future<void> _attemptRecovery() async {
    debugPrint('尝试恢复Hive数据库...');

    try {
      // 关闭所有盒子
      await Hive.close();

      // 尝试重新初始化，但清除现有数据
      await Hive.initFlutter();
      _registerAdapters();

      // 重新初始化仓库，但清除现有数据
      await _initRepositoriesWithClear();

      _isInitialized = true;
      debugPrint('Hive恢复成功（数据已重置）');
    } catch (e) {
      debugPrint('恢复Hive失败: $e');

      // 最后的手段：删除所有Hive数据
      await _resetHive();
    }
  }

  /// 完全重置Hive数据库
  Future<void> _resetHive() async {
    debugPrint('执行完全重置Hive数据库...');

    try {
      // 关闭所有盒子
      await Hive.close();

      // 删除Hive目录
      final appDir = await getApplicationDocumentsDirectory();
      final hiveDir = Directory('${appDir.path}/hive');
      if (await hiveDir.exists()) {
        await hiveDir.delete(recursive: true);
        debugPrint('已删除Hive数据目录');
      }

      // 重新初始化
      await Hive.initFlutter();
      _registerAdapters();
      await _initRepositories();

      _isInitialized = true;
      debugPrint('Hive完全重置成功');
    } catch (e) {
      debugPrint('重置Hive失败: $e');
      throw Exception('无法初始化Hive数据库，请尝试重新安装应用');
    }
  }

  /// 检查版本并执行迁移
  Future<void> _checkVersionAndMigrate() async {
    try {
      // 打开版本盒子
      final versionBox = await Hive.openBox(versionBoxName);

      // 获取当前存储的版本
      final storedVersion = versionBox.get('version', defaultValue: 0) as int;

      if (storedVersion < currentVersion) {
        debugPrint('检测到版本变更: $storedVersion -> $currentVersion，执行数据迁移');

        // 执行迁移
        await _migrateData(storedVersion, currentVersion);

        // 更新版本号
        await versionBox.put('version', currentVersion);
        debugPrint('数据迁移完成，版本已更新');
      }
    } catch (e) {
      debugPrint('版本检查或迁移失败: $e');
      // 继续初始化，不中断流程
    }
  }

  /// 数据迁移
  Future<void> _migrateData(int fromVersion, int toVersion) async {
    // 根据版本执行不同的迁移策略
    if (fromVersion == 0 && toVersion >= 1) {
      // 从版本0迁移到版本1的逻辑
      debugPrint('执行从版本0到版本1的迁移');
      // 这里添加具体的迁移代码
    }

    // 可以添加更多版本迁移逻辑
  }

  /// 注册所有Hive适配器
  void _registerAdapters() {
    try {
      // 注册Goal和Milestone适配器
      if (!Hive.isAdapterRegistered(1)) Hive.registerAdapter(GoalAdapter());
      if (!Hive.isAdapterRegistered(2)) Hive.registerAdapter(MilestoneAdapter());

      // 注册Subject和Project适配器
      if (!Hive.isAdapterRegistered(3)) Hive.registerAdapter(SubjectAdapter());
      if (!Hive.isAdapterRegistered(4)) Hive.registerAdapter(ProgressTrackingModeAdapter());
      if (!Hive.isAdapterRegistered(5)) Hive.registerAdapter(ProjectAdapter());

      // 注册Schedule相关适配器
      if (!Hive.isAdapterRegistered(6)) Hive.registerAdapter(ScheduleTypeAdapter());
      if (!Hive.isAdapterRegistered(7)) Hive.registerAdapter(RepeatTypeAdapter());
      if (!Hive.isAdapterRegistered(8)) Hive.registerAdapter(ScheduleStatusAdapter());
      if (!Hive.isAdapterRegistered(9)) Hive.registerAdapter(ScheduleAdapter());

      // 注册FocusRecord相关适配器
      if (!Hive.isAdapterRegistered(10)) Hive.registerAdapter(FocusRecordStatusAdapter());
      if (!Hive.isAdapterRegistered(11)) Hive.registerAdapter(FocusRecordAdapter());

      // 注册ExamTemplate相关适配器
      if (!Hive.isAdapterRegistered(12)) Hive.registerAdapter(ExamNumberingModeAdapter());
      if (!Hive.isAdapterRegistered(13)) Hive.registerAdapter(ExamSectionAdapter());
      if (!Hive.isAdapterRegistered(14)) Hive.registerAdapter(TimeOfDayAdapter());
      if (!Hive.isAdapterRegistered(15)) Hive.registerAdapter(ExamInstanceAdapter());
      if (!Hive.isAdapterRegistered(16)) Hive.registerAdapter(ExamTemplateAdapter());

      // 注册ProjectProgressChange相关适配器
      if (!Hive.isAdapterRegistered(33)) Hive.registerAdapter(ProgressChangeSourceAdapter());
      if (!Hive.isAdapterRegistered(34)) Hive.registerAdapter(ProjectProgressChangeAdapter());

      // 注册CheckInTask相关适配器
      if (!Hive.isAdapterRegistered(35)) Hive.registerAdapter(CheckInTaskAdapter());
      if (!Hive.isAdapterRegistered(36)) Hive.registerAdapter(CheckInTaskStatusAdapter());

      debugPrint('Hive适配器注册完成');
    } catch (e) {
      debugPrint('注册Hive适配器发生错误: $e');
      // 继续初始化，不中断流程
    }
  }

  /// 初始化所有仓库
  Future<void> _initRepositories() async {
    try {
      await _goalRepository.init();
      await _subjectRepository.init();
      await _scheduleRepository.init();
      await _focusRecordRepository.init();
      await _examTemplateRepository.init();
      await _projectProgressRepository.init();
      await _checkInTaskRepository.init();
      debugPrint('所有仓库初始化完成');
    } catch (e) {
      debugPrint('初始化仓库时发生错误: $e');
      rethrow; // 重新抛出异常，触发恢复流程
    }
  }

  /// 初始化所有仓库（清除现有数据）
  Future<void> _initRepositoriesWithClear() async {
    try {
      // 关闭并删除现有盒子
      await _closeAndDeleteBox('goals');
      await _closeAndDeleteBox('milestones');
      await _closeAndDeleteBox('subjects');
      await _closeAndDeleteBox('projects');
      await _closeAndDeleteBox('schedules');
      await _closeAndDeleteBox('focus_records');
      await _closeAndDeleteBox('exam_templates');
      await _closeAndDeleteBox('exam_sections');
      await _closeAndDeleteBox('exam_instances');
      await _closeAndDeleteBox('project_progress_changes');
      await _closeAndDeleteBox('check_in_tasks');

      // 重新初始化仓库
      await _goalRepository.init();
      await _subjectRepository.init();
      await _scheduleRepository.init();
      await _focusRecordRepository.init();
      await _examTemplateRepository.init();
      await _projectProgressRepository.init();
      await _checkInTaskRepository.init();

      debugPrint('所有仓库已重新初始化（数据已清除）');
    } catch (e) {
      debugPrint('清除并重新初始化仓库时发生错误: $e');
      rethrow;
    }
  }

  /// 关闭并删除盒子
  Future<void> _closeAndDeleteBox(String boxName) async {
    try {
      if (Hive.isBoxOpen(boxName)) {
        await Hive.box(boxName).close();
      }
      await Hive.deleteBoxFromDisk(boxName);
      debugPrint('已删除盒子: $boxName');
    } catch (e) {
      debugPrint('删除盒子 $boxName 时出错: $e');
      // 继续处理其他盒子
    }
  }

  /// 创建数据备份
  Future<String?> createBackup() async {
    try {
      // 获取应用文档目录
      final appDir = await getApplicationDocumentsDirectory();
      final hiveDir = Directory('${appDir.path}/hive');
      final backupDir = Directory('${appDir.path}/backups');

      // 创建备份目录（如果不存在）
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      // 创建带时间戳的备份文件夹
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final backupPath = '${backupDir.path}/backup_$timestamp';
      final backupFolder = Directory(backupPath);
      await backupFolder.create();

      // 确保所有数据已保存到磁盘
      await _flushAllBoxes();

      // 复制Hive文件到备份目录
      if (await hiveDir.exists()) {
        await _copyDirectory(hiveDir.path, backupPath);
        debugPrint('数据备份已创建: $backupPath');
        return backupPath;
      } else {
        debugPrint('Hive目录不存在，无法创建备份');
        return null;
      }
    } catch (e) {
      debugPrint('创建备份时出错: $e');
      return null;
    }
  }

  /// 确保所有盒子的数据已保存到磁盘
  Future<void> _flushAllBoxes() async {
    try {
      // 获取所有已打开的盒子
      final boxNames = [
        'goals', 'milestones', 'subjects', 'projects',
        'schedules', 'focus_records', 'exam_templates',
        'exam_sections', 'exam_instances', 'project_progress_changes',
        'check_in_tasks'
      ];

      // 刷新每个盒子
      for (final boxName in boxNames) {
        if (Hive.isBoxOpen(boxName)) {
          await Hive.box(boxName).flush();
        }
      }

      debugPrint('所有盒子数据已刷新到磁盘');
    } catch (e) {
      debugPrint('刷新盒子数据时出错: $e');
    }
  }

  /// 从备份恢复数据
  Future<bool> restoreFromBackup(String backupPath) async {
    try {
      // 关闭所有盒子
      await Hive.close();
      _isInitialized = false;

      // 获取应用文档目录
      final appDir = await getApplicationDocumentsDirectory();
      final hiveDir = Directory('${appDir.path}/hive');

      // 删除当前Hive目录
      if (await hiveDir.exists()) {
        await hiveDir.delete(recursive: true);
      }

      // 创建新的Hive目录
      await hiveDir.create();

      // 从备份复制文件
      final backupDir = Directory(backupPath);
      if (await backupDir.exists()) {
        await _copyDirectory(backupPath, hiveDir.path);
        debugPrint('已从备份恢复数据: $backupPath');

        // 重新初始化Hive
        await Hive.initFlutter();
        _registerAdapters();
        await _initRepositories();
        _isInitialized = true;

        return true;
      } else {
        debugPrint('备份目录不存在: $backupPath');

        // 重新初始化Hive
        await Hive.initFlutter();
        _registerAdapters();
        await _initRepositories();
        _isInitialized = true;

        return false;
      }
    } catch (e) {
      debugPrint('从备份恢复时出错: $e');

      // 确保Hive重新初始化
      try {
        await _resetHive();
      } catch (resetError) {
        debugPrint('重置Hive失败: $resetError');
      }

      return false;
    }
  }

  /// 复制目录
  Future<void> _copyDirectory(String source, String destination) async {
    final sourceDir = Directory(source);
    final destDir = Directory(destination);

    if (!await destDir.exists()) {
      await destDir.create(recursive: true);
    }

    await for (final entity in sourceDir.list(recursive: false)) {
      final newPath = '${destDir.path}/${entity.path.split('/').last}';

      if (entity is File) {
        await entity.copy(newPath);
      } else if (entity is Directory) {
        await _copyDirectory(entity.path, newPath);
      }
    }
  }

  /// 验证数据完整性
  Future<bool> validateDataIntegrity() async {
    try {
      // 验证各个盒子是否可以正常打开和读取
      final goalsValid = await _validateBox('goals');
      final milestonesValid = await _validateBox('milestones');
      final subjectsValid = await _validateBox('subjects');
      final projectsValid = await _validateBox('projects');
      final schedulesValid = await _validateBox('schedules');
      final focusRecordsValid = await _validateBox('focus_records');
      final progressChangesValid = await _validateBox('project_progress_changes');
      final checkInTasksValid = await _validateBox('check_in_tasks');

      // 所有盒子都有效才返回true
      return goalsValid && milestonesValid && subjectsValid &&
             projectsValid && schedulesValid && focusRecordsValid &&
             progressChangesValid && checkInTasksValid;
    } catch (e) {
      debugPrint('验证数据完整性时出错: $e');
      return false;
    }
  }

  /// 验证单个盒子
  Future<bool> _validateBox(String boxName) async {
    try {
      if (!Hive.isBoxOpen(boxName)) {
        await Hive.openBox(boxName);
      }

      final box = Hive.box(boxName);
      // 尝试读取一些数据
      box.keys;

      return true;
    } catch (e) {
      debugPrint('验证盒子 $boxName 时出错: $e');
      return false;
    }
  }

  /// 压缩数据库
  Future<void> compactDatabase() async {
    try {
      // 压缩各个盒子
      await _compactBox('goals');
      await _compactBox('milestones');
      await _compactBox('subjects');
      await _compactBox('projects');
      await _compactBox('schedules');
      await _compactBox('focus_records');
      await _compactBox('exam_templates');
      await _compactBox('exam_sections');
      await _compactBox('exam_instances');
      await _compactBox('project_progress_changes');
      await _compactBox('check_in_tasks');

      debugPrint('数据库压缩完成');
    } catch (e) {
      debugPrint('压缩数据库时出错: $e');
    }
  }

  /// 压缩单个盒子
  Future<void> _compactBox(String boxName) async {
    try {
      if (Hive.isBoxOpen(boxName)) {
        await Hive.box(boxName).compact();
        debugPrint('已压缩盒子: $boxName');
      } else {
        final box = await Hive.openBox(boxName);
        await box.compact();
        debugPrint('已打开并压缩盒子: $boxName');
      }
    } catch (e) {
      debugPrint('压缩盒子 $boxName 时出错: $e');
    }
  }

  /// 关闭Hive
  Future<void> closeHive() async {
    try {
      await _goalRepository.close();
      await _subjectRepository.close();
      await _scheduleRepository.close();
      await _focusRecordRepository.close();
      await _examTemplateRepository.close();
      await _projectProgressRepository.close();
      await _checkInTaskRepository.close();
      await Hive.close();

      _isInitialized = false;
      debugPrint('Hive已关闭');
    } catch (e) {
      debugPrint('关闭Hive时出错: $e');
    }
  }

  /// 公开的重置方法（谨慎使用）
  Future<void> resetDatabase() async {
    await _resetHive();
  }
}
