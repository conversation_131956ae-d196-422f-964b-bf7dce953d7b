import 'package:shared_preferences/shared_preferences.dart';

/// 偏好设置服务
/// 用于保存和加载用户偏好设置
class PreferencesService {
  static const String lastSubjectIdKey = 'last_subject_id';
  static const String lastProjectIdKey = 'last_project_id';

  // 专注会话相关键名
  static const String focusSessionKey = 'current_focus_session';
  static const String focusSessionBackupKey = 'focus_session_backup';

  // 屏幕常亮设置键名
  static const String keepScreenOnKey = 'keep_screen_on';

  /// 保存上次选择的科目ID
  Future<void> saveLastSubjectId(String subjectId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(lastSubjectIdKey, subjectId);
  }

  /// 加载上次选择的科目ID
  Future<String?> getLastSubjectId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(lastSubjectIdKey);
  }

  /// 保存上次选择的项目ID
  Future<void> saveLastProjectId(String projectId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(lastProjectIdKey, projectId);
  }

  /// 加载上次选择的项目ID
  Future<String?> getLastProjectId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(lastProjectIdKey);
  }

  /// 保存上次选择的科目和项目ID
  Future<void> saveLastSelection(String subjectId, String projectId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(lastSubjectIdKey, subjectId);
    await prefs.setString(lastProjectIdKey, projectId);
  }

  // ==================== 专注会话持久化方法 ====================

  /// 保存专注会话数据
  Future<void> saveFocusSession(String sessionJson) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(focusSessionKey, sessionJson);
    // 同时保存备份
    await prefs.setString(focusSessionBackupKey, sessionJson);
  }

  /// 获取专注会话数据
  Future<String?> getFocusSession() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(focusSessionKey);
  }

  /// 获取专注会话备份数据
  Future<String?> getFocusSessionBackup() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(focusSessionBackupKey);
  }

  /// 清除专注会话数据
  Future<void> clearFocusSession() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(focusSessionKey);
    await prefs.remove(focusSessionBackupKey);
  }

  /// 检查是否存在专注会话
  Future<bool> hasFocusSession() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.containsKey(focusSessionKey);
  }

  // ==================== 屏幕常亮设置方法 ====================

  /// 保存屏幕常亮设置
  Future<void> saveKeepScreenOnSetting(bool keepScreenOn) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(keepScreenOnKey, keepScreenOn);
  }

  /// 获取屏幕常亮设置
  /// 默认返回true（开启）
  Future<bool> getKeepScreenOnSetting() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(keepScreenOnKey) ?? true; // 默认开启
  }
}
