// 该文件定义了一个 SceneApi 类，用于与服务器端的场景 API 进行交互。
// 它提供了获取场景列表和场景详情的方法。

import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/scene.dart';

// SceneApi 类，用于与服务器端的场景 API 进行交互。
class SceneApi {
  // 服务器端 API 的基础 URL。
  static const String baseUrl = 'http://localhost:3000';

  // 获取场景列表的异步方法。
  Future<List<Scene>> getScenes() async {
    try {
      // 发送 GET 请求到服务器端的场景列表 API。
      final response = await http.get(Uri.parse('$baseUrl/scenes'));
      
      // 如果响应状态码为 200，表示请求成功。
      if (response.statusCode == 200) {
        // 将响应体解析为 JSON 格式。
        final List<dynamic> data = json.decode(response.body);
        // 将 JSON 数据转换为 Scene 对象列表并返回。
        return data.map((json) => Scene.fromJson(json)).toList();
      }
      
      // 如果响应状态码不是 200，抛出异常。
      throw Exception('获取场景列表失败：${response.statusCode}');
    } catch (e) {
      // 如果发生异常，抛出网络请求错误异常。
      throw Exception('网络请求错误: $e');
    }
  }

  // 根据ID获取场景详情的异步方法。
  Future<Scene> getSceneById(String id) async {
    try {
      // 发送 GET 请求到服务器端的场景详情 API。
      final response = await http.get(Uri.parse('$baseUrl/scenes/$id'));
      
      // 如果响应状态码为 200，表示请求成功。
      if (response.statusCode == 200) {
        // 将响应体解析为 JSON 格式。
        final Map<String, dynamic> data = json.decode(response.body);
        // 将 JSON 数据转换为 Scene 对象并返回。
        return Scene.fromJson(data);
      }
      
      // 如果响应状态码不是 200，抛出异常。
      throw Exception('获取场景详情失败：${response.statusCode}');
    } catch (e) {
      // 如果发生异常，抛出网络请求错误异常。
      throw Exception('网络请求错误: $e');
    }
  }
}

//6797156e97659239d1bc88db
