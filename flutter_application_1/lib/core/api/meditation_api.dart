// 该文件定义了一个 MeditationApi 类，用于与服务器端的冥想记录 API 进行交互。
// 它提供了获取冥想历史记录等功能。

import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/meditation_record.dart';
import 'user_api.dart';

class MeditationApi {
  static const String baseUrl = 'YOUR_API_BASE_URL';

  // 获取认证headers
  Map<String, String> _getAuthHeaders() {
    final token = UserApi().getToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // 获取冥想历史记录
  Future<Map<String, dynamic>> getMeditationHistory({
    int page = 1,
    int limit = 10,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
        if (startDate != null) 'startDate': startDate.toIso8601String(),
        if (endDate != null) 'endDate': endDate.toIso8601String(),
      };

      final uri = Uri.parse('$baseUrl/api/users/meditation-history')
          .replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getAuthHeaders());

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final records = (data['records'] as List)
            .map((record) => MeditationRecord.fromJson(record))
            .toList();

        return {
          'total': data['total'],
          'page': data['page'],
          'limit': data['limit'],
          'records': records,
        };
      }
      throw Exception('获取冥想历史记录失败');
    } catch (e) {
      throw Exception('网络请求错误: $e');
    }
  }

  // 添加冥想记录
  Future<void> addMeditationRecord(MeditationRecord record) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/users/meditation-history'),
        headers: _getAuthHeaders(),
        body: json.encode(record.toJson()),
      );

      if (response.statusCode != 201) {
        throw Exception('添加冥想记录失败');
      }
    } catch (e) {
      throw Exception('网络请求错误: $e');
    }
  }
}