// 该文件定义了一个 UserApi 类，用于与服务器端的用户 API 进行交互。
// 它提供了一个方法 getUserProfile，用于获取用户的个人资料。

import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/user.dart';

// UserApi 类，用于与服务器端的用户 API 进行交互。
class UserApi {
  // 服务器端 API 的基础 URL。
  static const String baseUrl = 'YOUR_API_BASE_URL';
  static String? _token;

  // 设置认证token
  static void setToken(String token) {
    _token = token;
  }

  // 获取token
  String? getToken() {
    return _token;
  }

  // 获取认证headers
  Map<String, String> _getAuthHeaders() {
    return {
      'Content-Type': 'application/json',
      if (_token != null) 'Authorization': 'Bearer $_token',
    };
  }

  // 用户注册
  Future<Map<String, dynamic>> register({
    required String phone,
    required String password,
    String? username,
    String? nickname,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/users/register'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'phone': phone,
          'password': password,
          if (username != null) 'username': username,
          if (nickname != null) 'nickname': nickname,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setToken(data['token']);
        return data;
      }
      throw Exception('注册失败');
    } catch (e) {
      throw Exception('网络请求错误: $e');
    }
  }

  // 用户登录
  Future<Map<String, dynamic>> login(String phone, String password) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/users/login'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'phone': phone,
          'password': password,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setToken(data['token']);
        return data;
      }
      throw Exception('登录失败');
    } catch (e) {
      throw Exception('网络请求错误: $e');
    }
  }

  // 获取用户信息
  Future<User> getUserProfile() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/users/profile'),
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return User.fromJson(data['user']);
      }
      throw Exception('获取用户信息失败');
    } catch (e) {
      throw Exception('网络请求错误: $e');
    }
  }

  // 更新用户信息
  Future<User> updateUserProfile({
    String? nickname,
    String? avatar,
    String? email,
    Map<String, dynamic>? preferences,
    Map<String, dynamic>? settings,
    Map<String, dynamic>? usageStats,
  }) async {
    try {
      // 检查是否为开发环境或测试环境
      if (baseUrl == 'YOUR_API_BASE_URL') {
        // 模拟API调用延迟
        await Future.delayed(const Duration(seconds: 1));

        // 创建模拟用户数据
        return User(
          id: 'mock-user-id',
          username: '测试用户',
          nickname: nickname ?? '专注达人',
          phone: '13800138000',
          avatar: avatar,
          email: email ?? '',
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
          lastLoginAt: DateTime.now(),
          preferences: preferences ?? {},
          settings: settings ?? {
            'theme': 'light',
            'notifications': true,
          },
          usageStats: usageStats ?? {
            'totalFocusTime': 3600, // 秒
            'sessionsCompleted': 10,
            'lastUsedDate': DateTime.now().toIso8601String(),
          },
        );
      }

      // 实际API调用
      final response = await http.put(
        Uri.parse('$baseUrl/api/users/profile'),
        headers: _getAuthHeaders(),
        body: json.encode({
          if (nickname != null) 'nickname': nickname,
          if (avatar != null) 'avatar': avatar,
          if (email != null) 'email': email,
          if (preferences != null) 'preferences': preferences,
          if (settings != null) 'settings': settings,
          if (usageStats != null) 'usageStats': usageStats,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return User.fromJson(data['user']);
      }
      throw Exception('更新用户信息失败');
    } catch (e) {
      throw Exception('网络请求错误: $e');
    }
  }
}
