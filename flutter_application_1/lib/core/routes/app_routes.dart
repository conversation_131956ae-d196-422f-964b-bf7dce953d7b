import 'package:flutter/material.dart';
import '../../features/profile/screens/profile_screen.dart';
import '../../features/profile/screens/profile_edit_screen.dart';
import '../../features/profile/screens/membership_screen.dart';
// 备份相关页面已在MVP版本中移除
import '../../features/profile/screens/about_screen.dart';
import '../../features/help/screens/help_feedback_screen.dart';

import '../../features/subscription/screens/premium_subscription_screen.dart';
import '../../features/data/screens/data_detail_screen.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/auth/screens/forgot_password_screen.dart';


import '../../features/onboarding/screens/onboarding_screen.dart';
import '../../features/dev/screens/developer_tools_screen.dart';
import '../../main.dart'; // 导入MainScreen

/// 应用路由配置
/// 定义应用中所有页面的路由
class AppRoutes {
  /// 路由名称常量
  static const String home = '/'; // 主页路由
  static const String splash = '/splash';
  static const String onboarding = '/onboarding';
  static const String login = '/auth/login';
  static const String register = '/auth/register';
  static const String forgotPassword = '/auth/forgot-password';
  static const String resetPassword = '/auth/reset-password';


  static const String profile = '/profile';
  static const String profileEdit = '/profile/edit';
  static const String membership = '/profile/membership';
  static const String subscription = '/profile/subscription';
  static const String premiumSubscription = '/subscription/premium';
  static const String databaseManagement = '/profile/database-management';
  static const String localBackup = '/profile/local-backup';
  static const String cloudBackup = '/profile/cloud-backup';
  static const String about = '/about';
  static const String terms = '/terms';
  static const String privacy = '/privacy';
  static const String help = '/help';

  static const String data = '/data';
  static const String dataDetail = '/data/detail';
  static const String devTools = '/dev/tools';
  static const String dataExport = '/data/export';
  static const String dataClearCache = '/data/clear-cache';

  /// 路由表
  static Map<String, WidgetBuilder> routes = {
    home: (context) => const MainScreen(), // 主页路由 - "/"
    onboarding: (context) => const OnboardingScreen(),
    login: (context) => const LoginScreen(),
    register: (context) => const RegisterScreen(),
    forgotPassword: (context) => const ForgotPasswordScreen(),
    profile: (context) => const ProfileScreen(),
    profileEdit: (context) => const ProfileEditScreen(),
    membership: (context) => const MembershipScreen(),
    subscription: (context) => const PremiumSubscriptionScreen(),
    premiumSubscription: (context) => const PremiumSubscriptionScreen(),
    // 备份相关路由已在MVP版本中移除
    about: (context) => const AboutScreen(),
    help: (context) => const HelpFeedbackScreen(),
    // terms: (context) => const TermsScreen(),
    // privacy: (context) => const PrivacyScreen(),
    data: (context) => const DataDetailScreen(),
    dataDetail: (context) => const DataDetailScreen(),
    devTools: (context) => const DeveloperToolsScreen(),
  };

  /// 路由生成器
  /// 用于处理未在路由表中定义的路由
  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    // 在这里处理动态路由，例如带参数的路由
    return null;
  }

  /// 未知路由处理
  /// 当请求的路由不存在时调用
 static Route<dynamic> onUnknownRoute(RouteSettings settings) {
    return MaterialPageRoute(
      builder: (context) => Scaffold(
        appBar: AppBar(title: const Text('页面不存在')),
        body: const Center(child: Text('找不到请求的页面')),
      ),
    );
  }
}
