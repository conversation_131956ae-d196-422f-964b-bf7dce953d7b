import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import '../models/project_progress_change.dart';

/// 项目进度变化仓库
class ProjectProgressRepository {
  static const String _boxName = 'project_progress_changes';
  late Box<ProjectProgressChange> _box;
  bool _isInitialized = false;

  /// 初始化仓库
  Future<void> init() async {
    if (_isInitialized) {
      debugPrint('ProjectProgressRepository 已经初始化，跳过');
      return;
    }

    try {
      // 适配器应该已经在HiveService中注册，这里直接打开盒子
      debugPrint('打开 project_progress_changes 盒子');
      _box = await Hive.openBox<ProjectProgressChange>(_boxName);

      _isInitialized = true;
      debugPrint('ProjectProgressRepository 初始化成功，盒子中有 ${_box.length} 条记录');
    } catch (e) {
      debugPrint('ProjectProgressRepository 初始化失败: $e');

      // 尝试恢复
      try {
        debugPrint('尝试恢复 project_progress_changes 盒子');
        await Hive.deleteBoxFromDisk(_boxName);
        _box = await Hive.openBox<ProjectProgressChange>(_boxName);
        _isInitialized = true;
        debugPrint('ProjectProgressRepository 恢复成功');
      } catch (recoverError) {
        debugPrint('ProjectProgressRepository 恢复失败: $recoverError');
        rethrow;
      }
    }
  }

  /// 保存项目进度变化记录
  Future<void> saveProgressChange(ProjectProgressChange change) async {
    try {
      if (!_isInitialized) {
        debugPrint('ProjectProgressRepository 初始化中...');
        await init();
      }

      // 确保盒子已打开
      if (!Hive.isBoxOpen(_boxName)) {
        debugPrint('重新打开 project_progress_changes 盒子...');
        _box = await Hive.openBox<ProjectProgressChange>(_boxName);
      }

      // 保存进度变化记录
      await _box.put(change.id, change);
      debugPrint('进度变化记录已保存: ${change.id}');

      // 刷新盒子，确保数据写入磁盘
      await _box.flush();
    } catch (e) {
      debugPrint('保存进度变化记录失败: $e');
      rethrow; // 重新抛出异常，让调用者处理
    }
  }

  /// 获取项目的所有进度变化记录
  List<ProjectProgressChange> getProjectProgressChanges(String projectId) {
    if (!_isInitialized) {
      debugPrint('ProjectProgressRepository 未初始化');
      return [];
    }

    return _box.values
        .where((change) => change.projectId == projectId)
        .toList();
  }

  /// 获取项目的专注完成后的进度变化记录
  List<ProjectProgressChange> getProjectFocusCompletionChanges(String projectId) {
    if (!_isInitialized) {
      debugPrint('ProjectProgressRepository 未初始化');
      return [];
    }

    return _box.values
        .where((change) =>
            change.projectId == projectId &&
            change.source == ProgressChangeSource.focusCompletion)
        .toList();
  }

  /// 获取项目在指定日期范围内的进度变化记录
  List<ProjectProgressChange> getProjectProgressChangesByDateRange(
    String projectId,
    DateTime startDate,
    DateTime endDate,
  ) {
    if (!_isInitialized) {
      debugPrint('ProjectProgressRepository 未初始化');
      return [];
    }

    final start = DateTime(startDate.year, startDate.month, startDate.day);
    final end = DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);

    return _box.values
        .where((change) =>
            change.projectId == projectId &&
            change.timestamp.isAfter(start) &&
            change.timestamp.isBefore(end))
        .toList();
  }

  /// 删除项目的所有进度变化记录
  Future<void> deleteProjectProgressChanges(String projectId) async {
    if (!_isInitialized) await init();

    final keysToDelete = _box.keys
        .where((key) => _box.get(key)?.projectId == projectId)
        .toList();

    for (final key in keysToDelete) {
      await _box.delete(key);
    }
  }

  /// 清除所有进度变化记录
  Future<void> clearAllProgressChanges() async {
    if (!_isInitialized) await init();
    await _box.clear();
  }

  /// 获取所有进度变化记录
  Future<List<ProjectProgressChange>> getAllProgressChangesAsync() async {
    if (!_isInitialized) {
      debugPrint('ProjectProgressRepository 未初始化，正在初始化...');
      await init();
    }

    // 确保盒子已打开
    if (!Hive.isBoxOpen(_boxName)) {
      debugPrint('重新打开 project_progress_changes 盒子...');
      _box = await Hive.openBox<ProjectProgressChange>(_boxName);
    }

    final changes = _box.values.toList();
    debugPrint('获取到 ${changes.length} 条进度变化记录');
    return changes;
  }

  /// 获取所有进度变化记录（同步版本）
  List<ProjectProgressChange> getAllProgressChanges() {
    if (!_isInitialized) {
      debugPrint('ProjectProgressRepository 未初始化，返回空列表');
      return [];
    }

    if (!Hive.isBoxOpen(_boxName)) {
      debugPrint('project_progress_changes 盒子未打开，返回空列表');
      return [];
    }

    final changes = _box.values.toList();
    debugPrint('获取到 ${changes.length} 条进度变化记录');
    return changes;
  }

  /// 关闭仓库
  Future<void> close() async {
    if (_isInitialized) {
      await _box.close();
      _isInitialized = false;
    }
  }
}
