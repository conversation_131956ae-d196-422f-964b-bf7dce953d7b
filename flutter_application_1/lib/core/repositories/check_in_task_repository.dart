import 'package:hive/hive.dart';
import '../models/check_in_task.dart';

/// 打卡任务存储库
class CheckInTaskRepository {
  static const String boxName = 'check_in_tasks';
  late Box<CheckInTask> _box;
  bool _isInitialized = false;

  /// 初始化
  Future<void> init() async {
    if (!_isInitialized) {
      if (!Hive.isBoxOpen(boxName)) {
        _box = await Hive.openBox<CheckInTask>(boxName);
      } else {
        _box = Hive.box<CheckInTask>(boxName);
      }
      _isInitialized = true;
    }
  }

  /// 获取所有打卡任务
  List<CheckInTask> getAllTasks() {
    if (!_isInitialized) {
      throw Exception('CheckInTaskRepository not initialized');
    }
    return _box.values.toList();
  }

  /// 获取进行中的打卡任务
  List<CheckInTask> getActiveTasks() {
    if (!_isInitialized) {
      throw Exception('CheckInTaskRepository not initialized');
    }
    return _box.values.where((task) => task.status == CheckInTaskStatus.inProgress).toList();
  }

  /// 根据ID获取打卡任务
  CheckInTask? getTaskById(String id) {
    if (!_isInitialized) {
      throw Exception('CheckInTaskRepository not initialized');
    }
    try {
      return _box.values.firstWhere((task) => task.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 添加打卡任务
  Future<void> addTask(CheckInTask task) async {
    if (!_isInitialized) {
      throw Exception('CheckInTaskRepository not initialized');
    }
    await _box.put(task.id, task);
  }

  /// 更新打卡任务
  Future<void> updateTask(CheckInTask task) async {
    if (!_isInitialized) {
      throw Exception('CheckInTaskRepository not initialized');
    }
    await _box.put(task.id, task);
  }

  /// 删除打卡任务
  Future<void> deleteTask(String id) async {
    if (!_isInitialized) {
      throw Exception('CheckInTaskRepository not initialized');
    }
    final task = getTaskById(id);
    if (task != null) {
      await task.delete();
    }
  }

  /// 打卡
  Future<void> checkIn(String taskId, DateTime date) async {
    if (!_isInitialized) {
      throw Exception('CheckInTaskRepository not initialized');
    }
    final task = getTaskById(taskId);
    if (task != null) {
      task.checkIn(date);
      await updateTask(task);
    }
  }

  /// 取消打卡
  Future<void> cancelCheckIn(String taskId, DateTime date) async {
    if (!_isInitialized) {
      throw Exception('CheckInTaskRepository not initialized');
    }
    final task = getTaskById(taskId);
    if (task != null) {
      task.cancelCheckIn(date);
      await updateTask(task);
    }
  }

  /// 关闭存储库
  Future<void> close() async {
    if (_isInitialized && Hive.isBoxOpen(boxName)) {
      await _box.close();
      _isInitialized = false;
    }
  }
}
