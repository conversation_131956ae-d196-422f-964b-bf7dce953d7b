import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import '../models/archived_goal.dart';
import '../models/goal_milestone.dart';
import '../models/subject_project.dart';

/// 归档目标仓库，负责管理归档目标的持久化存储
class ArchivedGoalRepository {
  static const String archivedGoalBoxName = 'archived_goals';

  late Box<ArchivedGoal> _archivedGoalBox;
  bool _isInitialized = false;

  bool get isInitialized => _isInitialized;

  /// 初始化仓库，打开Hive盒子
  Future<void> init() async {
    if (_isInitialized) {
      debugPrint('ArchivedGoalRepository已经初始化');
      return;
    }

    debugPrint('ArchivedGoalRepository.init 开始');
    
    if (!Hive.isBoxOpen(archivedGoalBoxName)) {
      _archivedGoalBox = await Hive.openBox<ArchivedGoal>(archivedGoalBoxName);
    } else {
      _archivedGoalBox = Hive.box<ArchivedGoal>(archivedGoalBoxName);
    }
    
    _isInitialized = true;
    debugPrint('ArchivedGoalRepository.init 完成');
  }

  /// 获取所有归档目标
  List<ArchivedGoal> getAllArchivedGoals() {
    if (!_isInitialized) {
      debugPrint('getAllArchivedGoals: 仓库未初始化，尝试初始化');
      try {
        // 同步方式打开盒子
        if (!Hive.isBoxOpen(archivedGoalBoxName)) {
          _archivedGoalBox = Hive.box<ArchivedGoal>(archivedGoalBoxName);
        }
        _isInitialized = true;
      } catch (e) {
        debugPrint('getAllArchivedGoals: 初始化失败: $e');
        return []; // 如果初始化失败，返回空列表
      }
    }

    return _archivedGoalBox.values.toList();
  }

  /// 根据ID获取归档目标
  ArchivedGoal? getArchivedGoalById(String id) {
    if (!_isInitialized) {
      debugPrint('getArchivedGoalById: 仓库未初始化，尝试初始化');
      try {
        // 同步方式打开盒子
        if (!Hive.isBoxOpen(archivedGoalBoxName)) {
          _archivedGoalBox = Hive.box<ArchivedGoal>(archivedGoalBoxName);
        }
        _isInitialized = true;
      } catch (e) {
        debugPrint('getArchivedGoalById: 初始化失败: $e');
        return null; // 如果初始化失败，返回null
      }
    }

    return _archivedGoalBox.get(id);
  }

  /// 保存归档目标
  Future<void> saveArchivedGoal(ArchivedGoal archivedGoal) async {
    debugPrint('ArchivedGoalRepository.saveArchivedGoal 开始: ${archivedGoal.id}, ${archivedGoal.name}');
    
    if (!_isInitialized) {
      debugPrint('saveArchivedGoal: 仓库未初始化，正在初始化...');
      await init();
    }

    try {
      await _archivedGoalBox.put(archivedGoal.id, archivedGoal);
      debugPrint('归档目标保存成功: ${archivedGoal.id}, ${archivedGoal.name}');
    } catch (e) {
      debugPrint('归档目标保存失败: $e');
      rethrow;
    }
  }

  /// 删除归档目标
  Future<void> deleteArchivedGoal(String id) async {
    debugPrint('ArchivedGoalRepository.deleteArchivedGoal 开始: $id');
    
    if (!_isInitialized) {
      debugPrint('deleteArchivedGoal: 仓库未初始化，正在初始化...');
      await init();
    }

    try {
      await _archivedGoalBox.delete(id);
      debugPrint('归档目标删除成功: $id');
    } catch (e) {
      debugPrint('归档目标删除失败: $e');
      rethrow;
    }
  }

  /// 归档目标
  Future<ArchivedGoal> archiveGoal(
    Goal goal, {
    required List<Subject> subjects,
    required List<Project> projects,
  }) async {
    debugPrint('ArchivedGoalRepository.archiveGoal 开始: ${goal.id}, ${goal.name}');
    
    if (!_isInitialized) {
      debugPrint('archiveGoal: 仓库未初始化，正在初始化...');
      await init();
    }

    try {
      // 创建归档目标
      final archivedGoal = ArchivedGoal.fromGoal(
        goal,
        subjects: subjects,
        projects: projects,
      );

      // 保存归档目标
      await saveArchivedGoal(archivedGoal);
      
      debugPrint('目标归档成功: ${goal.id}, ${goal.name}');
      return archivedGoal;
    } catch (e) {
      debugPrint('目标归档失败: $e');
      rethrow;
    }
  }

  /// 关闭Hive盒子
  Future<void> close() async {
    if (_isInitialized) {
      await _archivedGoalBox.close();
      _isInitialized = false;
    }
  }
}
