import 'package:limefocus/core/models/goal_milestone.dart';
import 'package:hive/hive.dart';
import 'dart:async';

/// 目标仓库，负责管理目标和里程碑的持久化存储
class GoalRepository {
  static const String goalBoxName = 'goals';
  static const String milestoneBoxName = 'milestones';

  late Box<Goal> _goalBox;
  late Box<Milestone> _milestoneBox;

  /// 初始化仓库，打开Hive盒子
  Future<void> init() async {
    if (!Hive.isBoxOpen(goalBoxName)) {
      _goalBox = await Hive.openBox<Goal>(goalBoxName);
    } else {
      _goalBox = Hive.box<Goal>(goalBoxName);
    }

    if (!Hive.isBoxOpen(milestoneBoxName)) {
      _milestoneBox = await Hive.openBox<Milestone>(milestoneBoxName);
    } else {
      _milestoneBox = Hive.box<Milestone>(milestoneBoxName);
    }
  }

  /// 获取所有目标
  List<Goal> getAllGoals({bool includeArchived = false}) {
    if (includeArchived) {
      return _goalBox.values.toList();
    } else {
      return _goalBox.values.where((goal) => !goal.isArchived).toList();
    }
  }

  /// 获取所有归档目标
  List<Goal> getArchivedGoals() {
    return _goalBox.values.where((goal) => goal.isArchived).toList();
  }

  /// 根据ID获取目标
  Goal? getGoalById(String id) {
    return _goalBox.get(id);
  }

  /// 保存目标
  Future<void> saveGoal(Goal goal) async {
    await _goalBox.put(goal.id, goal);
  }

  /// 删除目标
  Future<void> deleteGoal(String id) async {
    await _goalBox.delete(id);

    // 删除关联的里程碑
    final milestonesToDelete = _milestoneBox.values
        .where((milestone) => milestone.goalId == id)
        .toList();

    for (var milestone in milestonesToDelete) {
      await _milestoneBox.delete(milestone.id);
    }
  }

  /// 获取所有里程碑
  List<Milestone> getAllMilestones() {
    return _milestoneBox.values.toList();
  }

  /// 获取指定目标的里程碑
  List<Milestone> getMilestonesByGoalId(String goalId) {
    return _milestoneBox.values
        .where((milestone) => milestone.goalId == goalId)
        .toList();
  }

  /// 保存里程碑
  Future<void> saveMilestone(Milestone milestone) async {
    await _milestoneBox.put(milestone.id, milestone);
  }

  /// 删除里程碑
  Future<void> deleteMilestone(String id) async {
    await _milestoneBox.delete(id);
  }

  /// 获取目标及其所有里程碑
  Goal? getGoalWithMilestones(String goalId) {
    final goal = getGoalById(goalId);
    if (goal == null) return null;

    final milestones = getMilestonesByGoalId(goalId);
    return goal.copyWith(milestones: milestones);
  }

  /// 归档目标
  Future<void> archiveGoal(String id) async {
    // 获取目标
    final goal = getGoalById(id);
    if (goal == null) {
      throw Exception('目标不存在: $id');
    }

    // 更新目标为归档状态
    final updatedGoal = goal.copyWith(isArchived: true);

    // 保存更新后的目标
    await saveGoal(updatedGoal);
  }

  /// 取消归档目标
  Future<void> unarchiveGoal(String id) async {
    // 获取目标
    final goal = getGoalById(id);
    if (goal == null) {
      throw Exception('目标不存在: $id');
    }

    // 更新目标为非归档状态
    final updatedGoal = goal.copyWith(isArchived: false);

    // 保存更新后的目标
    await saveGoal(updatedGoal);
  }

  /// 关闭Hive盒子
  Future<void> close() async {
    await _goalBox.close();
    await _milestoneBox.close();
  }
}