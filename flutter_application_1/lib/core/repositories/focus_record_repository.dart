import 'package:hive/hive.dart';
import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/focus_record.dart';
import '../models/focus_stats.dart';

/// 专注记录仓库，负责管理专注记录的持久化存储
class FocusRecordRepository {
  static const String focusRecordBoxName = 'focus_records';

  late Box<FocusRecord> _focusRecordBox;

  /// 初始化仓库，打开Hive盒子
  Future<void> init() async {
    if (!Hive.isBoxOpen(focusRecordBoxName)) {
      _focusRecordBox = await Hive.openBox<FocusRecord>(focusRecordBoxName);
    } else {
      _focusRecordBox = Hive.box<FocusRecord>(focusRecordBoxName);
    }
  }

  /// 获取所有专注记录
  List<FocusRecord> getAllFocusRecords() {
    return _focusRecordBox.values.toList();
  }

  /// 根据ID获取专注记录
  FocusRecord? getFocusRecordById(String id) {
    return _focusRecordBox.get(id);
  }

  /// 保存专注记录
  Future<void> saveFocusRecord(FocusRecord record) async {
    await _focusRecordBox.put(record.id, record);
  }

  /// 删除专注记录
  Future<void> deleteFocusRecord(String id) async {
    await _focusRecordBox.delete(id);
  }

  /// 获取指定项目的专注记录
  List<FocusRecord> getFocusRecordsByProjectId(String projectId) {
    return _focusRecordBox.values
        .where((record) => record.projectId == projectId)
        .toList();
  }

  /// 获取指定科目的专注记录
  List<FocusRecord> getFocusRecordsBySubjectId(String subjectId) {
    return _focusRecordBox.values
        .where((record) => record.subjectId == subjectId)
        .toList();
  }

  /// 获取指定日期范围内的专注记录
  List<FocusRecord> getFocusRecordsByDateRange(DateTime start, DateTime end) {
    return _focusRecordBox.values
        .where((record) =>
            record.startTime.isAfter(start.subtract(const Duration(seconds: 1))) &&
            record.startTime.isBefore(end.add(const Duration(seconds: 1))))
        .toList();
  }

  /// 获取今天的专注记录
  List<FocusRecord> getTodayFocusRecords() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    return getFocusRecordsByDateRange(today, tomorrow);
  }

  /// 获取本周的专注记录
  List<FocusRecord> getWeeklyFocusRecords() {
    final now = DateTime.now();
    final startOfWeek = DateTime(
      now.year,
      now.month,
      now.day - now.weekday + 1
    );
    return getFocusRecordsByDateRange(startOfWeek, now);
  }

  /// 获取本月的专注记录
  List<FocusRecord> getMonthlyFocusRecords() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    return getFocusRecordsByDateRange(startOfMonth, now);
  }

  /// 获取专注统计数据
  FocusStats getFocusStats() {
    final stats = FocusStats();
    final records = getAllFocusRecords();

    for (var record in records) {
      stats.addRecord(record);
    }

    return stats;
  }

  /// 获取指定日期范围内的专注统计数据
  FocusStats getFocusStatsInRange(DateTime start, DateTime end) {
    final stats = FocusStats();
    final records = getFocusRecordsByDateRange(start, end);

    for (var record in records) {
      stats.addRecord(record);
    }

    return stats;
  }

  /// 获取指定科目的专注统计数据
  FocusStats getFocusStatsBySubject(String subjectId) {
    final stats = FocusStats();
    final records = getFocusRecordsBySubjectId(subjectId);

    for (var record in records) {
      stats.addRecord(record);
    }

    return stats;
  }

  /// 获取指定项目的专注统计数据
  FocusStats getFocusStatsByProject(String projectId) {
    final stats = FocusStats();
    final records = getFocusRecordsByProjectId(projectId);

    for (var record in records) {
      stats.addRecord(record);
    }

    return stats;
  }

  /// 关闭Hive盒子
  Future<void> close() async {
    await _focusRecordBox.close();
  }

  /// 清除所有专注记录
  Future<void> clearAllRecords() async {
    await _focusRecordBox.clear();
  }

  /// 从备份恢复专注记录数据
  Future<void> restoreFocusRecords(List<Map<String, dynamic>> recordsData) async {
    try {
      // 确保仓库已初始化
      await init();

      // 清空现有数据
      await clearAllRecords();

      // 恢复专注记录数据
      for (final recordData in recordsData) {
        final record = FocusRecord.fromJson(recordData);
        await saveFocusRecord(record);
      }

      debugPrint('专注记录数据恢复成功: ${recordsData.length} 条记录');
    } catch (e) {
      debugPrint('专注记录数据恢复失败: $e');
      rethrow;
    }
  }
}
