import 'package:limefocus/core/models/schedule.dart';
import 'package:hive/hive.dart';
import 'dart:async';
import 'package:flutter/foundation.dart';

/// 日程仓库，负责管理日程的持久化存储
class ScheduleRepository {
  static const String scheduleBoxName = 'schedules';

  late Box<Schedule> _scheduleBox;

  /// 初始化仓库，打开Hive盒子
  Future<void> init() async {
    if (!Hive.isBoxOpen(scheduleBoxName)) {
      _scheduleBox = await Hive.openBox<Schedule>(scheduleBoxName);
    } else {
      _scheduleBox = Hive.box<Schedule>(scheduleBoxName);
    }
  }

  /// 获取所有日程
  List<Schedule> getAllSchedules() {
    return _scheduleBox.values.toList();
  }

  /// 根据ID获取日程
  Schedule? getScheduleById(String id) {
    return _scheduleBox.get(id);
  }

  /// 获取指定日期的日程
  List<Schedule> getSchedulesByDate(DateTime date) {
    return _scheduleBox.values
        .where((schedule) => schedule.isOnDate(date))
        .toList();
  }

  /// 获取指定类型的日程
  List<Schedule> getSchedulesByType(ScheduleType type) {
    return _scheduleBox.values
        .where((schedule) => schedule.type == type)
        .toList();
  }

  /// 获取待办事项
  List<Schedule> getTodos() {
    return getSchedulesByType(ScheduleType.todo);
  }

  /// 获取计划
  List<Schedule> getPlans() {
    return getSchedulesByType(ScheduleType.plan);
  }

  /// 获取打卡任务
  List<Schedule> getRoutines() {
    return getSchedulesByType(ScheduleType.routine);
  }

  /// 保存日程
  Future<void> saveSchedule(Schedule schedule) async {
    await _scheduleBox.put(schedule.id, schedule);
  }

  /// 删除日程
  Future<void> deleteSchedule(String id) async {
    await _scheduleBox.delete(id);
  }

  /// 更新日程状态
  Future<void> updateScheduleStatus(String id, ScheduleStatus status) async {
    final schedule = getScheduleById(id);
    if (schedule != null) {
      final updatedSchedule = schedule.copyWith(status: status);
      await saveSchedule(updatedSchedule);
    }
  }

  /// 标记日程为已完成
  Future<void> markScheduleAsCompleted(String id, [DateTime? completedDate]) async {
    final schedule = getScheduleById(id);
    if (schedule != null) {
      final date = completedDate ?? DateTime.now();

      // 确保日期只包含年月日，不包含时分秒
      final normalizedDate = DateTime(date.year, date.month, date.day);

      if (schedule.isRepeat) {
        // 对于重复任务，添加到完成日期列表
        final completedDates = List<DateTime>.from(schedule.completedDates ?? []);

        // 检查是否已经在完成日期列表中，避免重复添加
        final isAlreadyCompleted = completedDates.any((completedDate) =>
          completedDate.year == normalizedDate.year &&
          completedDate.month == normalizedDate.month &&
          completedDate.day == normalizedDate.day
        );

        if (!isAlreadyCompleted) {
          completedDates.add(normalizedDate);
        }

        final updatedSchedule = schedule.copyWith(
          completedDates: completedDates,
        );
        await saveSchedule(updatedSchedule);
      } else {
        // 对于非重复任务，直接更新状态
        final updatedSchedule = schedule.copyWith(
          status: ScheduleStatus.completed,
        );
        await saveSchedule(updatedSchedule);
      }
    }
  }

  /// 切换日程完成状态
  Future<void> toggleScheduleCompletion(String id, [DateTime? completedDate]) async {
    final schedule = getScheduleById(id);
    if (schedule != null) {
      final date = completedDate ?? DateTime.now();

      // 确保日期只包含年月日，不包含时分秒
      final normalizedDate = DateTime(date.year, date.month, date.day);

      if (schedule.isRepeat) {
        // 对于重复任务，从完成日期列表中移除或添加
        final completedDates = List<DateTime>.from(schedule.completedDates ?? []);

        // 检查是否已经在完成日期列表中
        final isCompleted = completedDates.any((completedDate) =>
          completedDate.year == normalizedDate.year &&
          completedDate.month == normalizedDate.month &&
          completedDate.day == normalizedDate.day
        );

        if (isCompleted) {
          // 如果已完成，则移除该日期
          completedDates.removeWhere((completedDate) =>
            completedDate.year == normalizedDate.year &&
            completedDate.month == normalizedDate.month &&
            completedDate.day == normalizedDate.day
          );
        } else {
          // 如果未完成，则添加该日期
          completedDates.add(normalizedDate);
        }

        final updatedSchedule = schedule.copyWith(
          completedDates: completedDates,
        );
        await saveSchedule(updatedSchedule);
      } else {
        // 对于非重复任务，切换状态
        final newStatus = schedule.status == ScheduleStatus.completed
          ? ScheduleStatus.pending
          : ScheduleStatus.completed;

        final updatedSchedule = schedule.copyWith(
          status: newStatus,
        );
        await saveSchedule(updatedSchedule);
      }
    }
  }

  /// 关闭Hive盒子
  Future<void> close() async {
    await _scheduleBox.close();
  }

  /// 清除所有日程
  Future<void> clearAllSchedules() async {
    await _scheduleBox.clear();
  }

  /// 从备份恢复日程数据
  Future<void> restoreSchedules(List<Map<String, dynamic>> schedulesData) async {
    try {
      // 确保仓库已初始化
      await init();

      // 清空现有数据
      await clearAllSchedules();

      // 恢复日程数据
      for (final scheduleData in schedulesData) {
        try {
          final schedule = _scheduleFromJson(scheduleData);
          await saveSchedule(schedule);
        } catch (e) {
          debugPrint('恢复单个日程失败: $e');
          // 继续处理下一个日程
        }
      }

      debugPrint('日程数据恢复成功: ${schedulesData.length} 条日程');
    } catch (e) {
      debugPrint('日程数据恢复失败: $e');
      rethrow;
    }
  }

  /// 从JSON创建Schedule对象
  Schedule _scheduleFromJson(Map<String, dynamic> json) {
    return Schedule(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      type: ScheduleType.values[json['type']],
      createdAt: DateTime.parse(json['createdAt']),
      date: json['date'] != null ? DateTime.parse(json['date']) : null,
      time: json['time'] != null ? DateTime.parse(json['time']) : null,
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      isRepeat: json['isRepeat'] ?? false,
      repeatType: json['repeatType'] != null ? RepeatType.values[json['repeatType']] : null,
      repeatDays: json['repeatDays'] != null ? List<int>.from(json['repeatDays']) : null,
      status: ScheduleStatus.values[json['status']],
      completedDates: json['completedDates'] != null
          ? (json['completedDates'] as List).map((date) => DateTime.parse(date)).toList()
          : null,
    );
  }
}
