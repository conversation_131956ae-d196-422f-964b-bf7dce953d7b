import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import '../../features/exam/models/exam_template_models.dart';

/// 考试模板仓库，负责管理考试模板和实例的持久化存储
class ExamTemplateRepository {
  static const String templateBoxName = 'exam_templates';
  static const String sectionBoxName = 'exam_sections';
  static const String instanceBoxName = 'exam_instances';

  Box<ExamTemplate>? _templateBox;
  Box<ExamSection>? _sectionBox;
  Box<ExamInstance>? _instanceBox;

  bool get isInitialized => _templateBox != null && _sectionBox != null && _instanceBox != null;

  /// 初始化仓库，打开Hive盒子
  Future<void> init() async {
    try {
      if (!Hive.isBoxOpen(templateBoxName)) {
        _templateBox = await Hive.openBox<ExamTemplate>(templateBoxName);
      } else {
        _templateBox = Hive.box<ExamTemplate>(templateBoxName);
      }

      if (!Hive.isBoxOpen(sectionBoxName)) {
        _sectionBox = await Hive.openBox<ExamSection>(sectionBoxName);
      } else {
        _sectionBox = Hive.box<ExamSection>(sectionBoxName);
      }

      if (!Hive.isBoxOpen(instanceBoxName)) {
        _instanceBox = await Hive.openBox<ExamInstance>(instanceBoxName);
      } else {
        _instanceBox = Hive.box<ExamInstance>(instanceBoxName);
      }

      debugPrint('ExamTemplateRepository初始化完成');

      // 不创建示例数据，删除可能存在的示例数据
      if (_templateBox != null && _templateBox!.isNotEmpty) {
        // 删除示例数据（如果存在）
        if (_templateBox!.containsKey('template_1')) {
          await _templateBox!.delete('template_1');
        }
        if (_templateBox!.containsKey('template_2')) {
          await _templateBox!.delete('template_2');
        }
      }
    } catch (e) {
      debugPrint('初始化ExamTemplateRepository时发生错误: $e');
      rethrow;
    }
  }

  /// 确保仓库已初始化
  Future<void> ensureInitialized() async {
    if (!isInitialized) {
      await init();
    }
  }

  /// 获取所有考试模板
  Future<List<ExamTemplate>> getAllTemplates() async {
    await ensureInitialized();

    // 获取所有模板，并过滤掉示例数据
    List<ExamTemplate> templates = _templateBox?.values.toList() ?? [];

    // 过滤掉示例数据（如果有）
    templates = templates.where((template) =>
      template.id != 'template_1' && template.id != 'template_2'
    ).toList();

    return templates;
  }

  /// 根据ID获取考试模板
  Future<ExamTemplate?> getTemplateById(String id) async {
    await ensureInitialized();
    return _templateBox?.get(id);
  }

  /// 保存考试模板
  Future<void> saveTemplate(ExamTemplate template) async {
    await ensureInitialized();
    await _templateBox?.put(template.id, template);

    // 保存关联的题型模块
    for (var section in template.sections) {
      await _sectionBox?.put(section.id, section);
    }

    // 保存关联的考试实例
    for (var instance in template.instances) {
      await _instanceBox?.put(instance.id, instance);
    }
  }

  /// 删除考试模板
  Future<void> deleteTemplate(String id) async {
    await ensureInitialized();
    final template = await getTemplateById(id);
    if (template == null) return;

    // 删除关联的题型模块
    for (var section in template.sections) {
      await _sectionBox?.delete(section.id);
    }

    // 删除关联的考试实例
    for (var instance in template.instances) {
      await _instanceBox?.delete(instance.id);
    }

    // 删除模板
    await _templateBox?.delete(id);
  }

  /// 获取所有题型模块
  Future<List<ExamSection>> getAllSections() async {
    await ensureInitialized();
    return _sectionBox?.values.toList() ?? [];
  }

  /// 根据ID获取题型模块
  Future<ExamSection?> getSectionById(String id) async {
    await ensureInitialized();
    return _sectionBox?.get(id);
  }

  /// 获取指定模板的题型模块
  Future<List<ExamSection>> getSectionsByTemplateId(String templateId) async {
    await ensureInitialized();
    return _sectionBox?.values
        .where((section) => section.templateId == templateId)
        .toList() ?? [];
  }

  /// 保存题型模块
  Future<void> saveSection(ExamSection section) async {
    await ensureInitialized();
    await _sectionBox?.put(section.id, section);

    // 更新关联的模板
    final template = await getTemplateById(section.templateId);
    if (template != null) {
      final updatedSections = template.sections.map((s) {
        if (s.id == section.id) {
          return section;
        }
        return s;
      }).toList();

      if (!updatedSections.contains(section)) {
        updatedSections.add(section);
      }

      final updatedTemplate = template.copyWith(
        sections: updatedSections,
        updatedAt: DateTime.now(),
      );

      await _templateBox?.put(template.id, updatedTemplate);
    }
  }

  /// 删除题型模块
  Future<void> deleteSection(String id) async {
    await ensureInitialized();
    final section = await getSectionById(id);
    if (section == null) return;

    // 更新关联的模板
    final template = await getTemplateById(section.templateId);
    if (template != null) {
      final updatedSections = template.sections
          .where((s) => s.id != id)
          .toList();

      final updatedTemplate = template.copyWith(
        sections: updatedSections,
        updatedAt: DateTime.now(),
      );

      await _templateBox?.put(template.id, updatedTemplate);
    }

    // 删除题型模块
    await _sectionBox?.delete(id);
  }

  /// 获取所有考试实例
  Future<List<ExamInstance>> getAllInstances() async {
    await ensureInitialized();
    return _instanceBox?.values.toList() ?? [];
  }

  /// 根据ID获取考试实例
  Future<ExamInstance?> getInstanceById(String id) async {
    await ensureInitialized();
    return _instanceBox?.get(id);
  }

  /// 获取指定模板的考试实例
  Future<List<ExamInstance>> getInstancesByTemplateId(String templateId) async {
    await ensureInitialized();
    return _instanceBox?.values
        .where((instance) => instance.templateId == templateId)
        .toList() ?? [];
  }

  /// 保存考试实例
  Future<void> saveInstance(ExamInstance instance) async {
    await ensureInitialized();
    await _instanceBox?.put(instance.id, instance);

    // 更新关联的模板
    final template = await getTemplateById(instance.templateId);
    if (template != null) {
      final updatedInstances = template.instances.map((i) {
        if (i.id == instance.id) {
          return instance;
        }
        return i;
      }).toList();

      if (!updatedInstances.contains(instance)) {
        updatedInstances.add(instance);
      }

      final updatedTemplate = template.copyWith(
        instances: updatedInstances,
        updatedAt: DateTime.now(),
      );

      await _templateBox?.put(template.id, updatedTemplate);
    }
  }

  /// 删除考试实例
  Future<void> deleteInstance(String id) async {
    await ensureInitialized();
    final instance = await getInstanceById(id);
    if (instance == null) return;

    // 更新关联的模板
    final template = await getTemplateById(instance.templateId);
    if (template != null) {
      final updatedInstances = template.instances
          .where((i) => i.id != id)
          .toList();

      final updatedTemplate = template.copyWith(
        instances: updatedInstances,
        updatedAt: DateTime.now(),
      );

      await _templateBox?.put(template.id, updatedTemplate);
    }

    // 删除考试实例
    await _instanceBox?.delete(id);
  }

  /// 关闭Hive盒子
  Future<void> close() async {
    await _templateBox?.close();
    await _sectionBox?.close();
    await _instanceBox?.close();
  }
}
