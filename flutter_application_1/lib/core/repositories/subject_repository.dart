import 'package:limefocus/core/models/subject_project.dart';
import 'package:hive/hive.dart';
import 'dart:async';
import 'package:flutter/foundation.dart';

/// 科目仓库，负责管理科目和项目的持久化存储
class SubjectRepository {
  static const String subjectBoxName = 'subjects';
  static const String projectBoxName = 'projects';

  Box<Subject>? _subjectBox;
  Box<Project>? _projectBox;

  // 检查盒子是否已初始化
  bool get isInitialized => _subjectBox != null && _projectBox != null;

  /// 初始化仓库，打开Hive盒子
  Future<void> init() async {
    debugPrint('SubjectRepository.init 开始');

    try {
      if (!Hive.isBoxOpen(subjectBoxName)) {
        debugPrint('打开科目盒子');
        _subjectBox = await Hive.openBox<Subject>(subjectBoxName);
        debugPrint('科目盒子打开成功，数量: ${_subjectBox?.length ?? 0}');
      } else {
        debugPrint('科目盒子已打开，直接获取');
        _subjectBox = Hive.box<Subject>(subjectBoxName);
        debugPrint('获取科目盒子成功，数量: ${_subjectBox?.length ?? 0}');
      }

      if (!Hive.isBoxOpen(projectBoxName)) {
        debugPrint('打开项目盒子');
        _projectBox = await Hive.openBox<Project>(projectBoxName);
        debugPrint('项目盒子打开成功，数量: ${_projectBox?.length ?? 0}');
      } else {
        debugPrint('项目盒子已打开，直接获取');
        _projectBox = Hive.box<Project>(projectBoxName);
        debugPrint('获取项目盒子成功，数量: ${_projectBox?.length ?? 0}');
      }

      // 验证初始化是否成功
      if (!isInitialized) {
        throw Exception('初始化失败：盒子未正确打开');
      }

      // 执行数据迁移检查
      _checkAndMigrateSubjectColors();

      debugPrint('SubjectRepository.init 完成');
    } catch (e) {
      debugPrint('SubjectRepository.init 出错: $e');
      // 确保盒子为空，以便下次重试
      _subjectBox = null;
      _projectBox = null;
      rethrow; // 重新抛出异常，让调用者处理
    }
  }

  /// 获取所有科目
  List<Subject> getAllSubjects() {
    // 确保盒子已初始化
    if (!isInitialized) {
      debugPrint('getAllSubjects: 盒子未初始化，尝试初始化');
      try {
        // 同步方式打开盒子
        if (!Hive.isBoxOpen(subjectBoxName)) {
          _subjectBox = Hive.box<Subject>(subjectBoxName);
        }
        if (!Hive.isBoxOpen(projectBoxName)) {
          _projectBox = Hive.box<Project>(projectBoxName);
        }
      } catch (e) {
        debugPrint('getAllSubjects: 初始化失败: $e');
        return []; // 如果初始化失败，返回空列表
      }
    }

    // 再次检查初始化状态
    if (_subjectBox == null) {
      debugPrint('getAllSubjects: 盒子仍未初始化，返回空列表');
      return [];
    }

    return _subjectBox!.values.toList();
  }

  /// 根据ID获取科目
  Subject? getSubjectById(String id) {
    // 确保盒子已初始化
    if (!isInitialized) {
      debugPrint('getSubjectById: 盒子未初始化，尝试初始化');
      try {
        // 同步方式打开盒子
        if (!Hive.isBoxOpen(subjectBoxName)) {
          _subjectBox = Hive.box<Subject>(subjectBoxName);
        }
        if (!Hive.isBoxOpen(projectBoxName)) {
          _projectBox = Hive.box<Project>(projectBoxName);
        }
      } catch (e) {
        debugPrint('getSubjectById: 初始化失败: $e');
        return null; // 如果初始化失败，返回null
      }
    }

    // 再次检查初始化状态
    if (_subjectBox == null) {
      debugPrint('getSubjectById: 盒子仍未初始化，返回null');
      return null;
    }

    return _subjectBox!.get(id);
  }

  /// 保存科目
  Future<void> saveSubject(Subject subject) async {
    debugPrint('SubjectRepository.saveSubject 开始: ${subject.id}, ${subject.name}');

    // 确保盒子已初始化
    if (!isInitialized) {
      debugPrint('saveSubject: 盒子未初始化，正在初始化...');
      await init();
    }

    // 再次检查初始化状态
    if (_subjectBox == null) {
      throw Exception('saveSubject: 盒子初始化失败');
    }

    try {
      await _subjectBox!.put(subject.id, subject);
      debugPrint('科目保存成功: ${subject.id}, ${subject.name}');
    } catch (e) {
      debugPrint('科目保存失败: $e');
      rethrow;
    }
  }

  /// 删除科目
  Future<void> deleteSubject(String id) async {
    debugPrint('SubjectRepository.deleteSubject 开始: $id');

    // 确保盒子已初始化
    if (!isInitialized) {
      debugPrint('deleteSubject: 盒子未初始化，正在初始化...');
      await init();
    }

    // 再次检查初始化状态
    if (_subjectBox == null || _projectBox == null) {
      throw Exception('deleteSubject: 盒子初始化失败');
    }

    // 检查科目是否存在
    final subject = _subjectBox!.get(id);
    if (subject == null) {
      debugPrint('科目不存在: $id');
      return;
    }
    debugPrint('找到要删除的科目: $id, ${subject.name}');

    // 删除前的科目数量
    final beforeCount = _subjectBox!.length;
    debugPrint('删除前科目数量: $beforeCount');

    // 执行删除操作
    try {
      await _subjectBox!.delete(id);
      debugPrint('_subjectBox.delete 执行成功');
    } catch (e) {
      debugPrint('_subjectBox.delete 执行失败: $e');
      rethrow; // 重新抛出异常，让调用者处理
    }

    // 删除后的科目数量
    final afterCount = _subjectBox!.length;
    debugPrint('删除后科目数量: $afterCount');

    // 验证删除是否成功
    final checkSubject = _subjectBox!.get(id);
    if (checkSubject == null) {
      debugPrint('科目已成功删除: $id');
    } else {
      debugPrint('警告：科目删除失败，仍然存在: $id');
      throw Exception('deleteSubject: 科目删除失败，仍然存在');
    }

    // 删除关联的项目
    final projectsToDelete = _projectBox!.values
        .where((project) => project.subjectId == id)
        .toList();

    debugPrint('需要删除的关联项目数量: ${projectsToDelete.length}');

    for (var project in projectsToDelete) {
      debugPrint('删除关联项目: ${project.id}, ${project.name}');
      await _projectBox!.delete(project.id);
    }

    debugPrint('SubjectRepository.deleteSubject 完成: $id');
  }

  /// 获取所有项目
  List<Project> getAllProjects({bool includeArchived = false}) {
    // 确保盒子已初始化
    if (!isInitialized) {
      debugPrint('getAllProjects: 盒子未初始化，尝试初始化');
      try {
        // 同步方式打开盒子
        if (!Hive.isBoxOpen(subjectBoxName)) {
          _subjectBox = Hive.box<Subject>(subjectBoxName);
        }
        if (!Hive.isBoxOpen(projectBoxName)) {
          _projectBox = Hive.box<Project>(projectBoxName);
        }
      } catch (e) {
        debugPrint('getAllProjects: 初始化失败: $e');
        return []; // 如果初始化失败，返回空列表
      }
    }

    // 再次检查初始化状态
    if (_projectBox == null) {
      debugPrint('getAllProjects: 盒子仍未初始化，返回空列表');
      return [];
    }

    if (includeArchived) {
      return _projectBox!.values.toList();
    } else {
      return _projectBox!.values.where((project) => !project.isArchived).toList();
    }
  }

  /// 获取所有归档项目
  List<Project> getArchivedProjects() {
    // 确保盒子已初始化
    if (!isInitialized) {
      debugPrint('getArchivedProjects: 盒子未初始化，尝试初始化');
      try {
        // 同步方式打开盒子
        if (!Hive.isBoxOpen(projectBoxName)) {
          _projectBox = Hive.box<Project>(projectBoxName);
        }
      } catch (e) {
        debugPrint('getArchivedProjects: 初始化失败: $e');
        return []; // 如果初始化失败，返回空列表
      }
    }

    // 再次检查初始化状态
    if (_projectBox == null) {
      debugPrint('getArchivedProjects: 盒子仍未初始化，返回空列表');
      return [];
    }

    return _projectBox!.values.where((project) => project.isArchived).toList();
  }

  /// 根据ID获取项目
  Project? getProjectById(String id) {
    // 确保盒子已初始化
    if (!isInitialized) {
      debugPrint('getProjectById: 盒子未初始化，尝试初始化');
      try {
        // 同步方式打开盒子
        if (!Hive.isBoxOpen(subjectBoxName)) {
          _subjectBox = Hive.box<Subject>(subjectBoxName);
        }
        if (!Hive.isBoxOpen(projectBoxName)) {
          _projectBox = Hive.box<Project>(projectBoxName);
        }
      } catch (e) {
        debugPrint('getProjectById: 初始化失败: $e');
        return null; // 如果初始化失败，返回null
      }
    }

    // 再次检查初始化状态
    if (_projectBox == null) {
      debugPrint('getProjectById: 盒子仍未初始化，返回null');
      return null;
    }

    return _projectBox!.get(id);
  }

  /// 获取指定科目的项目
  List<Project> getProjectsBySubjectId(String subjectId, {bool includeArchived = false}) {
    // 确保盒子已初始化
    if (!isInitialized) {
      debugPrint('getProjectsBySubjectId: 盒子未初始化，尝试初始化');
      try {
        // 同步方式打开盒子
        if (!Hive.isBoxOpen(subjectBoxName)) {
          _subjectBox = Hive.box<Subject>(subjectBoxName);
        }
        if (!Hive.isBoxOpen(projectBoxName)) {
          _projectBox = Hive.box<Project>(projectBoxName);
        }
      } catch (e) {
        debugPrint('getProjectsBySubjectId: 初始化失败: $e');
        return []; // 如果初始化失败，返回空列表
      }
    }

    // 再次检查初始化状态
    if (_projectBox == null) {
      debugPrint('getProjectsBySubjectId: 盒子仍未初始化，返回空列表');
      return [];
    }

    if (includeArchived) {
      return _projectBox!.values
          .where((project) => project.subjectId == subjectId)
          .toList();
    } else {
      return _projectBox!.values
          .where((project) => project.subjectId == subjectId && !project.isArchived)
          .toList();
    }
  }

  /// 归档项目
  Future<void> archiveProject(String id) async {
    debugPrint('SubjectRepository.archiveProject 开始: $id');

    // 获取项目
    final project = getProjectById(id);
    if (project == null) {
      throw Exception('项目不存在: $id');
    }

    // 更新项目为归档状态
    final updatedProject = project.copyWith(isArchived: true);

    // 保存更新后的项目
    await saveProject(updatedProject);

    debugPrint('项目归档成功: $id');
  }

  /// 取消归档项目
  Future<void> unarchiveProject(String id) async {
    debugPrint('SubjectRepository.unarchiveProject 开始: $id');

    // 获取项目
    final project = getProjectById(id);
    if (project == null) {
      throw Exception('项目不存在: $id');
    }

    // 更新项目为非归档状态
    final updatedProject = project.copyWith(isArchived: false);

    // 保存更新后的项目
    await saveProject(updatedProject);

    debugPrint('项目取消归档成功: $id');
  }

  /// 保存项目
  Future<void> saveProject(Project project) async {
    debugPrint('SubjectRepository.saveProject 开始: ${project.id}, ${project.name}');

    // 确保盒子已初始化
    if (!isInitialized) {
      debugPrint('saveProject: 盒子未初始化，正在初始化...');
      await init();
    }

    // 再次检查初始化状态
    if (_projectBox == null) {
      throw Exception('saveProject: 盒子初始化失败');
    }

    try {
      await _projectBox!.put(project.id, project);
      debugPrint('项目保存成功: ${project.id}, ${project.name}');
    } catch (e) {
      debugPrint('项目保存失败: $e');
      rethrow;
    }
  }

  /// 删除项目
  Future<void> deleteProject(String id) async {
    debugPrint('SubjectRepository.deleteProject 开始: $id');

    // 确保盒子已初始化
    if (!isInitialized) {
      debugPrint('deleteProject: 盒子未初始化，正在初始化...');
      await init();
    }

    // 再次检查初始化状态
    if (_projectBox == null) {
      throw Exception('deleteProject: 盒子初始化失败');
    }

    try {
      await _projectBox!.delete(id);
      debugPrint('项目删除成功: $id');
    } catch (e) {
      debugPrint('项目删除失败: $e');
      rethrow;
    }
  }

  /// 获取科目及其所有项目
  Map<String, dynamic> getSubjectWithProjects(String subjectId) {
    final subject = getSubjectById(subjectId);
    if (subject == null) return {};

    final projects = getProjectsBySubjectId(subjectId);
    return {
      'subject': subject,
      'projects': projects,
    };
  }

  /// 检查并迁移科目颜色数据
  void _checkAndMigrateSubjectColors() {
    if (_subjectBox == null || _subjectBox!.isEmpty) return;

    debugPrint('开始检查科目颜色数据...');
    final subjects = _subjectBox!.values.toList();
    int migratedCount = 0;

    for (final subject in subjects) {
      // 检查颜色是否为默认绿色，如果是，可能是因为适配器未正确读取颜色
      // 我们可以为其分配一个新的颜色
      if (subject.color == 0xFF4CAF50) {
        debugPrint('科目 ${subject.name} 使用默认绿色，可能需要迁移');
        // 这里我们不做任何操作，因为我们已经修改了适配器来处理旧数据
        // 如果需要，可以在这里为科目分配新的颜色
      } else {
        debugPrint('科目 ${subject.name} 颜色正常: 0x${subject.color.toRadixString(16).toUpperCase()}');
      }
    }

    debugPrint('科目颜色数据检查完成，共 ${subjects.length} 个科目，迁移 $migratedCount 个');
  }

  /// 关闭Hive盒子
  Future<void> close() async {
    if (_subjectBox != null) {
      await _subjectBox!.close();
    }
    if (_projectBox != null) {
      await _projectBox!.close();
    }
  }

  /// 从备份恢复科目数据
  Future<void> restoreSubjects(List<Map<String, dynamic>> subjectsData) async {
    debugPrint('SubjectRepository.restoreSubjects 开始: ${subjectsData.length} 个科目');

    // 确保盒子已初始化
    if (!isInitialized) {
      debugPrint('restoreSubjects: 盒子未初始化，正在初始化...');
      await init();
    }

    // 再次检查初始化状态
    if (_subjectBox == null) {
      throw Exception('restoreSubjects: 盒子初始化失败');
    }

    try {
      // 清空现有数据
      await _subjectBox!.clear();

      // 恢复科目数据
      for (final subjectData in subjectsData) {
        final subject = Subject.fromJson(subjectData);
        await _subjectBox!.put(subject.id, subject);
      }

      debugPrint('科目数据恢复成功: ${subjectsData.length} 个科目');
    } catch (e) {
      debugPrint('科目数据恢复失败: $e');
      rethrow;
    }
  }

  /// 从备份恢复项目数据
  Future<void> restoreProjects(List<Map<String, dynamic>> projectsData) async {
    debugPrint('SubjectRepository.restoreProjects 开始: ${projectsData.length} 个项目');

    // 确保盒子已初始化
    if (!isInitialized) {
      debugPrint('restoreProjects: 盒子未初始化，正在初始化...');
      await init();
    }

    // 再次检查初始化状态
    if (_projectBox == null) {
      throw Exception('restoreProjects: 盒子初始化失败');
    }

    try {
      // 清空现有数据
      await _projectBox!.clear();

      // 恢复项目数据
      for (final projectData in projectsData) {
        final project = Project.fromJson(projectData);
        await _projectBox!.put(project.id, project);
      }

      debugPrint('项目数据恢复成功: ${projectsData.length} 个项目');
    } catch (e) {
      debugPrint('项目数据恢复失败: $e');
      rethrow;
    }
  }
}