# Hive本地存储库

本目录包含用于本地持久化存储的仓库类，基于Hive数据库实现。

## 可用的仓库

1. `GoalRepository` - 管理目标和里程碑数据
2. `SubjectRepository` - 管理科目和项目数据

## 使用方法

### 初始化

在应用启动时，通过`HiveService`初始化Hive并注册适配器：

```dart
final hiveService = HiveService();
await hiveService.initHive();
```

### 使用Goal和Milestone仓库

```dart
// 获取目标仓库实例
final goalRepo = hiveService.goalRepository;

// 创建并保存目标
final goal = Goal(
  id: 'goal1',
  name: '考研',
  startDate: DateTime.now(),
  endDate: DateTime.now().add(const Duration(days: 365)),
);
await goalRepo.saveGoal(goal);

// 创建并保存里程碑
final milestone = Milestone(
  id: 'ms1',
  name: '完成数学复习',
  goalId: 'goal1',
  date: DateTime.now().add(const Duration(days: 90)),
);
await goalRepo.saveMilestone(milestone);

// 获取所有目标
final allGoals = goalRepo.getAllGoals();

// 获取特定目标及其里程碑
final goalWithMilestones = goalRepo.getGoalWithMilestones('goal1');
```

### 使用Subject和Project仓库

```dart
// 获取科目仓库实例
final subjectRepo = hiveService.subjectRepository;

// 创建并保存科目
final subject = Subject(
  id: 'subj1',
  name: '数学',
);
await subjectRepo.saveSubject(subject);

// 创建并保存项目
final project = Project(
  id: 'proj1',
  name: '数学练习',
  subjectId: 'subj1',
  startDate: DateTime.now(),
  endDate: DateTime.now().add(const Duration(days: 30)),
  isTrackingEnabled: true,
  trackingMode: ProgressTrackingMode.focusTime,
  totalFocusHours: 50,
);
await subjectRepo.saveProject(project);

// 获取所有科目
final allSubjects = subjectRepo.getAllSubjects();

// 获取特定科目及其项目
final subjectWithProjects = subjectRepo.getSubjectWithProjects('subj1');
```

### 关闭Hive

在应用退出时关闭Hive：

```dart
await hiveService.closeHive();
``` 