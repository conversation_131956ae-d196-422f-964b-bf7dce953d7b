import 'package:flutter/material.dart';

/// 日期工具类
class DateUtil {
  /// 获取包含指定日期的周的日期范围（周一到周日）
  static DateTimeRange getWeekRange(DateTime date) {
    // 计算本周的周一
    final weekStart = date.subtract(Duration(days: date.weekday - 1));
    // 计算本周的周日
    final weekEnd = weekStart.add(const Duration(days: 6));

    // 返回日期范围，从周一的0点到周日的23:59:59
    return DateTimeRange(
      start: DateTime(weekStart.year, weekStart.month, weekStart.day),
      end: DateTime(weekEnd.year, weekEnd.month, weekEnd.day, 23, 59, 59),
    );
  }

  /// 获取包含指定日期的月的日期范围
  static DateTimeRange getMonthRange(DateTime date) {
    // 计算本月的第一天
    final monthStart = DateTime(date.year, date.month, 1);
    // 计算本月的最后一天
    final monthEnd = DateTime(date.year, date.month + 1, 0);

    // 返回日期范围，从月初的0点到月末的23:59:59
    return DateTimeRange(
      start: monthStart,
      end: DateTime(monthEnd.year, monthEnd.month, monthEnd.day, 23, 59, 59),
    );
  }

  /// 获取包含指定日期的年的日期范围
  static DateTimeRange getYearRange(DateTime date) {
    // 计算本年的第一天
    final yearStart = DateTime(date.year, 1, 1);
    // 计算本年的最后一天
    final yearEnd = DateTime(date.year, 12, 31);

    // 返回日期范围，从年初的0点到年末的23:59:59
    return DateTimeRange(
      start: yearStart,
      end: DateTime(yearEnd.year, yearEnd.month, yearEnd.day, 23, 59, 59),
    );
  }
}
