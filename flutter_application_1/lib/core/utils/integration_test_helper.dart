import '../config/test_config.dart';
import '../models/user.dart';
import '../services/auth_service.dart';
// 备份和云同步服务已在MVP版本中移除
import '../services/subscription_service.dart';
import 'storage_utils.dart';

/// 集成测试辅助类
/// 用于管理测试环境和提供测试数据
class IntegrationTestHelper {
  final AuthService authService;
  final SubscriptionService subscriptionService;

  IntegrationTestHelper({
    required this.authService,
    required this.subscriptionService,
  });

  /// 初始化测试环境
  Future<void> initTestEnvironment() async {
    try {
      // 清除所有本地数据
      await StorageUtils.clearAllData();

      // 设置测试模式
      TestConfig.setTestMode(true);

      TestConfig.log('测试环境初始化成功');
    } catch (e) {
      TestConfig.log('测试环境初始化失败: $e');
    }
  }

  /// 登录测试账号
  Future<User?> loginTestAccount() async {
    try {
      // 先检查是否已经登录
      final isLoggedIn = await authService.isLoggedIn();

      if (isLoggedIn) {
        // 如果已经登录，获取当前用户
        final currentUser = await authService.getCurrentUser();

        if (currentUser != null) {
          // 检查当前用户是否是测试账号
          if (currentUser.email == TestConfig.testAccount['email']) {
            TestConfig.log('已经登录测试账号，无需重新登录');
            return currentUser;
          } else {
            // 如果当前用户不是测试账号，先注销
            TestConfig.log('当前登录的不是测试账号，先注销');
            await authService.logout();
          }
        }
      }

      // 登录测试账号
      final user = await authService.login(
        TestConfig.testAccount['email']!,
        TestConfig.testAccount['password']!,
      );

      TestConfig.log('测试账号登录${user != null ? '成功' : '失败'}');

      return user;
    } catch (e) {
      TestConfig.log('测试账号登录失败: $e');
      return null;
    }
  }

  /// 创建测试数据
  Future<void> createTestData() async {
    try {
      // 备份功能已在MVP版本中移除
      TestConfig.log('测试数据创建完成（备份功能已移除）');
    } catch (e) {
      TestConfig.log('创建测试数据失败: $e');
    }
  }

  /// 创建测试云备份
  Future<Map<String, dynamic>?> createTestCloudBackup() async {
    try {
      // 备份功能已在MVP版本中移除
      TestConfig.log('云备份功能已在MVP版本中移除');
      return null;
    } catch (e) {
      TestConfig.log('创建测试云备份失败: $e');
      return null;
    }
  }

  /// 测试订阅功能
  Future<bool> testSubscription() async {
    try {
      // 检查用户是否已登录
      if (!await authService.isLoggedIn()) {
        await loginTestAccount();
      }

      // 获取订阅计划
      final plans = await subscriptionService.getSubscriptionPlans();

      if (plans.isEmpty) {
        TestConfig.log('获取订阅计划失败');
        return false;
      }

      // 检查用户是否为付费用户
      final isPremium = await subscriptionService.isPremiumUser();

      TestConfig.log('用户是${isPremium ? '' : '非'}付费用户');

      return true;
    } catch (e) {
      TestConfig.log('测试订阅功能失败: $e');
      return false;
    }
  }

  /// 清理测试环境
  Future<void> cleanupTestEnvironment() async {
    try {
      // 注销测试账号
      await authService.logout();

      // 清除所有本地数据
      await StorageUtils.clearAllData();

      TestConfig.log('测试环境清理成功');
    } catch (e) {
      TestConfig.log('测试环境清理失败: $e');
    }
  }
}
