import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// 设备信息工具类
class DeviceUtils {
  static final DeviceInfoPlugin _deviceInfoPlugin = DeviceInfoPlugin();
  
  /// 获取设备信息
  static Future<Map<String, dynamic>> getDeviceInfo() async {
    final deviceInfo = <String, dynamic>{};
    
    try {
      // 获取应用信息
      final packageInfo = await PackageInfo.fromPlatform();
      deviceInfo['appName'] = packageInfo.appName;
      deviceInfo['packageName'] = packageInfo.packageName;
      deviceInfo['version'] = packageInfo.version;
      deviceInfo['buildNumber'] = packageInfo.buildNumber;
      
      // 获取设备信息
      if (kIsWeb) {
        // Web平台
        final webInfo = await _deviceInfoPlugin.webBrowserInfo;
        deviceInfo['platform'] = 'web';
        deviceInfo['browserName'] = webInfo.browserName.name;
        deviceInfo['userAgent'] = webInfo.userAgent;
      } else if (Platform.isAndroid) {
        // Android平台
        final androidInfo = await _deviceInfoPlugin.androidInfo;
        deviceInfo['platform'] = 'android';
        deviceInfo['model'] = androidInfo.model;
        deviceInfo['manufacturer'] = androidInfo.manufacturer;
        deviceInfo['androidVersion'] = androidInfo.version.release;
        deviceInfo['sdkInt'] = androidInfo.version.sdkInt;
        deviceInfo['deviceId'] = androidInfo.id;
      } else if (Platform.isIOS) {
        // iOS平台
        final iosInfo = await _deviceInfoPlugin.iosInfo;
        deviceInfo['platform'] = 'ios';
        deviceInfo['model'] = iosInfo.model;
        deviceInfo['systemName'] = iosInfo.systemName;
        deviceInfo['systemVersion'] = iosInfo.systemVersion;
        deviceInfo['deviceId'] = iosInfo.identifierForVendor;
      } else if (Platform.isMacOS) {
        // macOS平台
        final macOsInfo = await _deviceInfoPlugin.macOsInfo;
        deviceInfo['platform'] = 'macos';
        deviceInfo['model'] = macOsInfo.model;
        deviceInfo['osVersion'] = macOsInfo.osRelease;
      } else if (Platform.isWindows) {
        // Windows平台
        final windowsInfo = await _deviceInfoPlugin.windowsInfo;
        deviceInfo['platform'] = 'windows';
        deviceInfo['computerName'] = windowsInfo.computerName;
        deviceInfo['osVersion'] = windowsInfo.productName;
      } else if (Platform.isLinux) {
        // Linux平台
        final linuxInfo = await _deviceInfoPlugin.linuxInfo;
        deviceInfo['platform'] = 'linux';
        deviceInfo['name'] = linuxInfo.name;
        deviceInfo['version'] = linuxInfo.version;
      }
      
      // 添加时间戳
      deviceInfo['timestamp'] = DateTime.now().toIso8601String();
    } catch (e) {
      debugPrint('获取设备信息失败: $e');
      // 添加基本信息
      deviceInfo['platform'] = kIsWeb ? 'web' : Platform.operatingSystem;
      deviceInfo['timestamp'] = DateTime.now().toIso8601String();
    }
    
    return deviceInfo;
  }
}
