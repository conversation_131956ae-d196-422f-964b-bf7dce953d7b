import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../config/test_config.dart';
import '../models/user.dart';
import 'storage_utils.dart';

/// 测试辅助工具
/// 用于在测试环境中模拟API响应
class TestHelper {
  /// 模拟登录
  static Future<User?> mockLogin({
    String? username,
    String? email,
    String? password,
  }) async {
    try {
      // 检查是否处于测试模式
      if (!TestConfig.isTestMode && !kDebugMode) {
        return null;
      }

      // 检查凭据是否匹配测试账号
      final testAccount = TestConfig.testAccount;
      if ((username != null && username != testAccount['username']) ||
          (email != null && email != testAccount['email']) ||
          (password != null && password != testAccount['password'])) {
        return null;
      }

      // 创建测试用户
      final user = User(
        id: 'test-user-id',
        username: testAccount['username']!,
        nickname: '${testAccount['username']!}的昵称',
        phone: '***********', // 默认测试手机号
        email: testAccount['email'] ?? '<EMAIL>',
        accountStatus: 'active',
        preferences: {}, // 空的偏好设置
      );

      // 保存测试令牌
      await StorageUtils.saveToken(TestConfig.testToken);
      await StorageUtils.saveRefreshToken(TestConfig.testRefreshToken);

      // 保存用户信息
      await StorageUtils.saveUserInfo(jsonEncode(user.toJson()));

      TestConfig.log('模拟登录成功: ${user.username}');

      return user;
    } catch (e) {
      TestConfig.log('模拟登录失败: $e');
      return null;
    }
  }

  /// 模拟注销
  static Future<bool> mockLogout() async {
    try {
      // 检查是否处于测试模式
      if (!TestConfig.isTestMode && !kDebugMode) {
        return false;
      }

      // 清除认证数据
      await StorageUtils.clearAuthData();

      TestConfig.log('模拟注销成功');

      return true;
    } catch (e) {
      TestConfig.log('模拟注销失败: $e');
      return false;
    }
  }

  /// 模拟获取当前用户
  static Future<User?> mockGetCurrentUser() async {
    try {
      // 检查是否处于测试模式
      if (!TestConfig.isTestMode && !kDebugMode) {
        return null;
      }

      // 从本地获取用户信息
      final userJson = await StorageUtils.getUserInfo();
      if (userJson != null) {
        return User.fromJson(jsonDecode(userJson));
      }

      // 如果本地没有，创建测试用户
      final testAccount = TestConfig.testAccount;
      final user = User(
        id: 'test-user-id',
        username: testAccount['username']!,
        nickname: '${testAccount['username']!}的昵称',
        phone: '***********', // 默认测试手机号
        email: testAccount['email'] ?? '<EMAIL>',
        accountStatus: 'active',
        preferences: {}, // 空的偏好设置
      );

      // 保存用户信息
      await StorageUtils.saveUserInfo(jsonEncode(user.toJson()));

      TestConfig.log('模拟获取当前用户成功: ${user.username}');

      return user;
    } catch (e) {
      TestConfig.log('模拟获取当前用户失败: $e');
      return null;
    }
  }

  /// 模拟检查用户是否已登录
  static Future<bool> mockIsLoggedIn() async {
    try {
      // 检查是否处于测试模式
      if (!TestConfig.isTestMode && !kDebugMode) {
        return false;
      }

      // 检查是否有令牌
      final token = await StorageUtils.getToken();
      return token != null;
    } catch (e) {
      TestConfig.log('模拟检查用户是否已登录失败: $e');
      return false;
    }
  }
}
