import 'dart:async';

/// 事件类型枚举
enum EventType {
  /// 认证相关事件
  authLogout,
  authLogin,
  authTokenExpired,
  authTokenRefreshed,

  /// 网络相关事件
  networkConnected,
  networkDisconnected,
  apiRateLimited,

  /// 数据同步相关事件
  syncStarted,
  syncCompleted,
  syncFailed,

  /// 应用生命周期事件
  appPaused,
  appResumed,
}

/// 事件总线
/// 用于应用内不同组件之间的通信
class EventBus {
  // 单例模式
  static final EventBus _instance = EventBus._internal();
  factory EventBus() => _instance;

  EventBus._internal();

  // 事件流控制器
  final _controller = StreamController<EventType>.broadcast();

  /// 获取事件流
  Stream<EventType> get stream => _controller.stream;

  /// 发布事件
  void fire(EventType event) {
    _controller.add(event);
  }

  /// 关闭事件总线
  void dispose() {
    _controller.close();
  }
}

// 全局事件总线实例
final eventBus = EventBus();
