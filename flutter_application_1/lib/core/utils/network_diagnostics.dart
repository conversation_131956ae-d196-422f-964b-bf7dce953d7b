import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// 网络诊断工具
class NetworkDiagnostics {
  static final Dio _dio = Dio();

  /// 测试网络连接
  static Future<Map<String, dynamic>> testConnection() async {
    final results = <String, dynamic>{};

    // 测试基本网络连接
    results['internet'] = await _testInternetConnection();

    // 测试本地后端连接
    results['localhost'] = await _testLocalBackend();

    // 测试生产后端连接
    results['production'] = await _testProductionBackend();

    return results;
  }

  /// 测试互联网连接
  static Future<bool> _testInternetConnection() async {
    try {
      final response = await _dio.get(
        'https://www.google.com',
        options: Options(
          sendTimeout: const Duration(seconds: 5),
          receiveTimeout: const Duration(seconds: 5),
        ),
      );
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('互联网连接测试失败: $e');
      return false;
    }
  }

  /// 测试本地后端连接
  static Future<Map<String, dynamic>> _testLocalBackend() async {
    try {
      final response = await _dio.get(
        'http://localhost:3000/api/users/me',
        options: Options(
          sendTimeout: const Duration(seconds: 3),
          receiveTimeout: const Duration(seconds: 3),
        ),
      );
      return {
        'connected': true,
        'statusCode': response.statusCode,
        'message': '本地后端连接成功',
      };
    } catch (e) {
      debugPrint('本地后端连接测试失败: $e');
      return {
        'connected': false,
        'error': e.toString(),
        'message': '本地后端连接失败',
      };
    }
  }

  /// 测试生产后端连接
  static Future<Map<String, dynamic>> _testProductionBackend() async {
    try {
      final response = await _dio.get(
        'https://arborflame.com/api/users/me',
        options: Options(
          sendTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
        ),
      );
      return {
        'connected': true,
        'statusCode': response.statusCode,
        'message': '生产后端连接成功',
      };
    } catch (e) {
      debugPrint('生产后端连接测试失败: $e');
      return {
        'connected': false,
        'error': e.toString(),
        'message': '生产后端连接失败',
      };
    }
  }

  /// 测试特定API端点
  static Future<Map<String, dynamic>> testApiEndpoint(String baseUrl, String endpoint) async {
    try {
      final response = await _dio.get(
        '$baseUrl$endpoint',
        options: Options(
          sendTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
        ),
      );
      return {
        'success': true,
        'statusCode': response.statusCode,
        'data': response.data,
        'message': 'API端点测试成功',
      };
    } catch (e) {
      debugPrint('API端点测试失败: $e');
      return {
        'success': false,
        'error': e.toString(),
        'message': 'API端点测试失败',
      };
    }
  }

  /// 测试登录API
  static Future<Map<String, dynamic>> testLoginApi(String baseUrl) async {
    try {
      // 测试发送验证码API
      final verifyCodeResult = await _dio.post(
        '$baseUrl/email-auth/send-verification-code?purpose=login',
        data: {
          'email': '<EMAIL>',
        },
        options: Options(
          sendTimeout: const Duration(seconds: 5),
          receiveTimeout: const Duration(seconds: 5),
          validateStatus: (status) => true, // 接受所有状态码
        ),
      );

      // 测试登录API（不发送实际请求，只测试端点是否存在）
      final loginResult = await _dio.post(
        '$baseUrl/email-auth/login-with-password',
        data: {
          'email': '<EMAIL>',
          'password': 'test123',
        },
        options: Options(
          sendTimeout: const Duration(seconds: 5),
          receiveTimeout: const Duration(seconds: 5),
          validateStatus: (status) => true, // 接受所有状态码
        ),
      );

      return {
        'verifyCode': {
          'statusCode': verifyCodeResult.statusCode,
          'connected': true,
          'data': verifyCodeResult.data,
          'message': verifyCodeResult.statusCode == 400 ? '邮件服务异常' : 'API端点可访问',
        },
        'login': {
          'statusCode': loginResult.statusCode,
          'connected': true,
          'message': '登录API端点可访问',
        },
      };
    } catch (e) {
      debugPrint('登录API测试失败: $e');
      return {
        'verifyCode': {'success': false, 'error': e.toString()},
        'login': {'connected': false, 'error': e.toString()},
      };
    }
  }

  /// 获取网络诊断报告
  static Future<String> getDiagnosticReport() async {
    final results = await testConnection();
    final buffer = StringBuffer();

    buffer.writeln('=== LimeFocus 网络诊断报告 ===');
    buffer.writeln('时间: ${DateTime.now()}');
    buffer.writeln();

    // 互联网连接
    buffer.writeln('1. 互联网连接: ${results['internet'] ? '✅ 正常' : '❌ 异常'}');

    // 本地后端
    final localhost = results['localhost'] as Map<String, dynamic>;
    buffer.writeln('2. 本地后端 (localhost:3000):');
    buffer.writeln('   状态: ${localhost['connected'] ? '✅ 连接成功' : '❌ 连接失败'}');
    if (localhost['connected']) {
      buffer.writeln('   状态码: ${localhost['statusCode']}');
    } else {
      buffer.writeln('   错误: ${localhost['error']}');
    }

    // 生产后端
    final production = results['production'] as Map<String, dynamic>;
    buffer.writeln('3. 生产后端 (arborflame.com):');
    buffer.writeln('   状态: ${production['connected'] ? '✅ 连接成功' : '❌ 连接失败'}');
    if (production['connected']) {
      buffer.writeln('   状态码: ${production['statusCode']}');
    } else {
      buffer.writeln('   错误: ${production['error']}');
    }

    buffer.writeln();
    buffer.writeln('=== 建议 ===');

    if (!results['internet']) {
      buffer.writeln('• 检查网络连接');
    }

    if (!localhost['connected']) {
      buffer.writeln('• 启动本地后端服务器');
      buffer.writeln('• 检查端口3000是否被占用');
    }

    if (!production['connected']) {
      buffer.writeln('• 检查生产服务器状态');
      buffer.writeln('• 验证域名解析');
    }

    return buffer.toString();
  }
}
