// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import '../../../shared/theme/constants.dart';
// import '../../profile/screens/subscription_screen.dart';

// /// 高级功能锁定页面
// /// 当用户尝试访问需要订阅的功能时显示
// class PremiumFeatureLockScreen extends ConsumerWidget {
//   final String featureName;
//   final String description;
//   final List<String> benefits;

//   const PremiumFeatureLockScreen({
//     super.key,
//     required this.featureName,
//     required this.description,
//     required this.benefits,
//   });

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return Container(
//       decoration: const BoxDecoration(
//         gradient: AppColors.pageBackground,
//       ),
//       child: SafeArea(
//         child: Padding(
//           padding: const EdgeInsets.all(AppSizes.paddingLarge),
//           child: Column(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               // 锁定图标
//               Container(
//                 width: 80,
//                 height: 80,
//                 decoration: BoxDecoration(
//                   color: AppColors.primary.withAlpha(AppColors.alpha10),
//                   borderRadius: BorderRadius.circular(40),
//                 ),
//                 child: const Icon(
//                   Icons.lock_outline,
//                   size: 40,
//                   color: AppColors.primary,
//                 ),
//               ),

//               const SizedBox(height: AppSizes.paddingLarge),

//               // 功能名称
//               Text(
//                 featureName,
//                 style: AppTextStyles.headline2,
//                 textAlign: TextAlign.center,
//               ),

//               const SizedBox(height: AppSizes.paddingMedium),

//               // 功能描述
//               Text(
//                 description,
//                 style: AppTextStyles.bodyMedium.copyWith(
//                   color: AppColors.textSecondary,
//                 ),
//                 textAlign: TextAlign.center,
//               ),

//               const SizedBox(height: AppSizes.paddingXLarge),

//               // 会员权益列表
//               Container(
//                 padding: const EdgeInsets.all(AppSizes.paddingLarge),
//                 decoration: BoxDecoration(
//                   color: Colors.white,
//                   borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
//                   boxShadow: [AppShadows.low],
//                 ),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     const Text(
//                       '升级会员解锁更多功能',
//                       style: AppTextStyles.headline3,
//                     ),
//                     const SizedBox(height: AppSizes.paddingMedium),
//                     ...benefits.map((benefit) => Padding(
//                       padding: const EdgeInsets.symmetric(vertical: 4),
//                       child: Row(
//                         children: [
//                           const Icon(
//                             Icons.check_circle,
//                             color: AppColors.success,
//                             size: 20,
//                           ),
//                           const SizedBox(width: 12),
//                           Expanded(
//                             child: Text(
//                               benefit,
//                               style: AppTextStyles.bodyMedium,
//                             ),
//                           ),
//                         ],
//                       ),
//                     )),
//                   ],
//                 ),
//               ),

//               const SizedBox(height: AppSizes.paddingXLarge),

//               // 升级按钮
//               SizedBox(
//                 width: double.infinity,
//                 child: ElevatedButton(
//                   onPressed: () {
//                     Navigator.push(
//                       context,
//                       MaterialPageRoute(
//                         builder: (context) => const SubscriptionScreen(),
//                       ),
//                     );
//                   },
//                   style: ElevatedButton.styleFrom(
//                     backgroundColor: AppColors.primary,
//                     foregroundColor: Colors.white,
//                     padding: const EdgeInsets.symmetric(vertical: 16),
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
//                     ),
//                     elevation: 2,
//                   ),
//                   child: const Text(
//                     '立即升级会员',
//                     style: AppTextStyles.buttonLarge,
//                   ),
//                 ),
//               ),

//               const SizedBox(height: AppSizes.paddingMedium),

//               // 稍后再说按钮
//               TextButton(
//                 onPressed: () {
//                   Navigator.pop(context);
//                 },
//                 child: Text(
//                   '稍后再说',
//                   style: AppTextStyles.bodyMedium.copyWith(
//                     color: AppColors.textSecondary,
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }

// /// 专注数据分析功能锁定页面
// class FocusAnalysisLockScreen extends ConsumerWidget {
//   const FocusAnalysisLockScreen({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return const PremiumFeatureLockScreen(
//       featureName: '专注数据分析',
//       description: '深入了解您的专注习惯和效率趋势，制定更科学的学习计划',
//       benefits: [
//         '详细的专注时间趋势分析',
//         '专注效率评估和建议',
//         '最佳专注时段识别',
//         '专注习惯养成跟踪',
//         '个性化专注建议',
//       ],
//     );
//   }
// }

// /// 项目数据分析功能锁定页面
// class ProjectAnalysisLockScreen extends ConsumerWidget {
//   const ProjectAnalysisLockScreen({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return const PremiumFeatureLockScreen(
//       featureName: '项目数据分析',
//       description: '全面掌握项目进度和推进速度，优化项目管理策略',
//       benefits: [
//         '项目进度可视化分析',
//         '项目推进速度对比',
//         '科目专注分布统计',
//         '项目完成预测',
//         '项目效率优化建议',
//       ],
//     );
//   }
// }
