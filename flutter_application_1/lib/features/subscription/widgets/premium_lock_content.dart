import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../screens/premium_subscription_screen.dart';

/// 高级功能锁定内容组件
/// 用于在数据分析页面的专注和项目标签页显示锁定状态
class PremiumLockContent extends ConsumerWidget {
  final String featureType; // 'focus' 或 'project'

  const PremiumLockContent({
    super.key,
    required this.featureType,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: const Color.fromARGB(255, 241, 242, 232), // 浅灰色背景，符合参考图
      body: SizedBox(
        width: double.infinity,
        child: SingleChildScrollView(
          child: Column(
            children: [
              // 顶部标题区域
              _buildTitleSection(),

              // 锁定状态长图
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: _buildLockImages(),
                ),
              ),

              // 底部留出悬浮按钮的空间
              const SizedBox(height: 100),
            ],
          ),
        ),
      ),
      // 底部悬浮解锁Pro按钮
      floatingActionButton: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(horizontal: 16),
        child: FloatingActionButton.extended(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const PremiumSubscriptionScreen(),
              ),
            );
          },
          backgroundColor: AppColors.primary,
          elevation: 8,
          icon: const Icon(
            Icons.lock_open,
            color: Colors.white,
            size: 24,
          ),
          label: const Text(
            '解锁 Pro 版本',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  /// 构建顶部标题区域
  Widget _buildTitleSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(20, 40, 20, 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 主标题
          const Text(
            '订阅PRO',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppColors.text,
              height: 1.2,
            ),
          ),

          const SizedBox(height: 4),

          // 副标题
          const Text(
            '解锁更多详细数据分析',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: AppColors.text,
              height: 1.3,
            ),
          ),
        ],
      ),
    );
  }

  /// 根据功能类型构建对应的锁定图片
  List<Widget> _buildLockImages() {
    final List<String> imagePaths;

    if (featureType == 'focus') {
      imagePaths = [
        'assets/lock-images/专注1.png',
        'assets/lock-images/专注2.png',
        'assets/lock-images/专注3.png',
        'assets/lock-images/专注4.png',
      ];
    } else {
      imagePaths = [
        'assets/lock-images/项目1.png',
        'assets/lock-images/项目2.png',
        'assets/lock-images/项目3.png',
      ];
    }

    return imagePaths.asMap().entries.map((entry) {
      final index = entry.key;
      final imagePath = entry.value;

      return Container(
        margin: EdgeInsets.only(
          bottom: index == imagePaths.length - 1 ? 0 : 16, // 增加图片间距
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12), // 稍微减小圆角
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.08), // 减轻阴影
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Image.asset(
              imagePath,
              width: double.infinity,
              fit: BoxFit.fitWidth,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: 300,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.grey[200]!,
                      width: 1,
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.image_not_supported_outlined,
                        color: Colors.grey[400],
                        size: 48,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        '图片加载失败',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '请检查 ${imagePath.split('/').last} 文件',
                        style: TextStyle(
                          color: Colors.grey[500],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      );
    }).toList();
  }
}
