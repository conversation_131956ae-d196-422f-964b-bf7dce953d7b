// import 'package:flutter/material.dart';
// import 'dart:ui';
// import '../../../shared/theme/constants.dart';
// import '../../profile/screens/subscription_screen.dart';

// /// 高级功能预览遮罩组件
// /// 允许用户查看内容但限制交互，激励付费升级
// class PremiumPreviewOverlay extends StatelessWidget {
//   final Widget child;
//   final String featureName;
//   final String description;
//   final List<String> benefits;

//   const PremiumPreviewOverlay({
//     super.key,
//     required this.child,
//     required this.featureName,
//     required this.description,
//     required this.benefits,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Stack(
//       children: [
//         // 原始内容（禁用交互）
//         AbsorbPointer(
//           child: child,
//         ),

//         // 半透明遮罩层
//         Container(
//           decoration: BoxDecoration(
//             color: Colors.white.withOpacity(0.7),
//           ),
//           child: BackdropFilter(
//             filter: ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
//             child: Container(
//               color: Colors.white.withOpacity(0.1),
//             ),
//           ),
//         ),

//         // 顶部提示区域
//         Positioned(
//           top: 0,
//           left: 0,
//           right: 0,
//           child: Container(
//             padding: const EdgeInsets.all(AppSizes.paddingMedium),
//             decoration: BoxDecoration(
//               gradient: LinearGradient(
//                 begin: Alignment.topCenter,
//                 end: Alignment.bottomCenter,
//                 colors: [
//                   AppColors.primary.withOpacity(0.9),
//                   AppColors.primary.withOpacity(0.7),
//                   Colors.transparent,
//                 ],
//               ),
//             ),
//             child: SafeArea(
//               child: Column(
//                 children: [
//                   // 锁定图标
//                   Container(
//                     width: 48,
//                     height: 48,
//                     decoration: BoxDecoration(
//                       color: Colors.white.withOpacity(0.2),
//                       borderRadius: BorderRadius.circular(24),
//                     ),
//                     child: const Icon(
//                       Icons.lock_outline,
//                       color: Colors.white,
//                       size: 24,
//                     ),
//                   ),

//                   const SizedBox(height: AppSizes.paddingSmall),

//                   // 功能名称
//                   Text(
//                     featureName,
//                     style: AppTextStyles.headline3.copyWith(
//                       color: Colors.white,
//                       fontWeight: FontWeight.bold,
//                     ),
//                     textAlign: TextAlign.center,
//                   ),

//                   const SizedBox(height: AppSizes.paddingXSmall),

//                   // 功能描述
//                   Text(
//                     description,
//                     style: AppTextStyles.bodyMedium.copyWith(
//                       color: Colors.white.withOpacity(0.9),
//                     ),
//                     textAlign: TextAlign.center,
//                     maxLines: 2,
//                     overflow: TextOverflow.ellipsis,
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),

//         // 底部升级按钮
//         Positioned(
//           bottom: 0,
//           left: 0,
//           right: 0,
//           child: Container(
//             padding: const EdgeInsets.all(AppSizes.paddingMedium),
//             decoration: BoxDecoration(
//               gradient: LinearGradient(
//                 begin: Alignment.topCenter,
//                 end: Alignment.bottomCenter,
//                 colors: [
//                   Colors.transparent,
//                   Colors.white.withOpacity(0.9),
//                   Colors.white,
//                 ],
//               ),
//             ),
//             child: SafeArea(
//               child: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   // 功能特性列表
//                   Container(
//                     padding: const EdgeInsets.all(AppSizes.paddingMedium),
//                     decoration: BoxDecoration(
//                       color: Colors.white,
//                       borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
//                       boxShadow: [AppShadows.medium],
//                     ),
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Text(
//                           '升级会员解锁：',
//                           style: AppTextStyles.labelMedium.copyWith(
//                             color: AppColors.textSecondary,
//                             fontWeight: FontWeight.bold,
//                           ),
//                         ),
//                         const SizedBox(height: AppSizes.paddingSmall),
//                         ...benefits.take(3).map((benefit) => Padding(
//                           padding: const EdgeInsets.only(bottom: 4),
//                           child: Row(
//                             children: [
//                               Icon(
//                                 Icons.check_circle,
//                                 size: 16,
//                                 color: AppColors.primary,
//                               ),
//                               const SizedBox(width: 8),
//                               Expanded(
//                                 child: Text(
//                                   benefit,
//                                   style: AppTextStyles.bodySmall.copyWith(
//                                     color: AppColors.text,
//                                   ),
//                                 ),
//                               ),
//                             ],
//                           ),
//                         )),
//                         if (benefits.length > 3)
//                           Text(
//                             '还有 ${benefits.length - 3} 项功能...',
//                             style: AppTextStyles.bodySmall.copyWith(
//                               color: AppColors.textSecondary,
//                               fontStyle: FontStyle.italic,
//                             ),
//                           ),
//                       ],
//                     ),
//                   ),

//                   const SizedBox(height: AppSizes.paddingMedium),

//                   // 升级按钮
//                   SizedBox(
//                     width: double.infinity,
//                     child: ElevatedButton(
//                       onPressed: () {
//                         Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                             builder: (context) => const SubscriptionScreen(),
//                           ),
//                         );
//                       },
//                       style: ElevatedButton.styleFrom(
//                         backgroundColor: AppColors.primary,
//                         foregroundColor: Colors.white,
//                         padding: const EdgeInsets.symmetric(
//                           vertical: AppSizes.paddingMedium,
//                         ),
//                         shape: RoundedRectangleBorder(
//                           borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
//                         ),
//                         elevation: 4,
//                       ),
//                       child: Row(
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         children: [
//                           const Icon(Icons.star, size: 20),
//                           const SizedBox(width: 8),
//                           Text(
//                             '立即升级会员',
//                             style: AppTextStyles.buttonLarge.copyWith(
//                               color: Colors.white,
//                               fontWeight: FontWeight.bold,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),

//                   const SizedBox(height: AppSizes.paddingSmall),

//                   // 稍后再说按钮
//                   TextButton(
//                     onPressed: () {
//                       Navigator.pop(context);
//                     },
//                     child: Text(
//                       '稍后再说',
//                       style: AppTextStyles.bodyMedium.copyWith(
//                         color: AppColors.textSecondary,
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//       ],
//     );
//   }
// }

// /// 专注数据分析预览遮罩
// class FocusAnalysisPreviewOverlay extends StatelessWidget {
//   final Widget child;

//   const FocusAnalysisPreviewOverlay({
//     super.key,
//     required this.child,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return PremiumPreviewOverlay(
//       featureName: '专注数据分析',
//       description: '深入了解您的专注习惯和效率趋势',
//       benefits: const [
//         '详细的专注时间趋势分析',
//         '专注效率评估和建议',
//         '最佳专注时段识别',
//         '专注习惯养成跟踪',
//         '个性化专注建议',
//       ],
//       child: child,
//     );
//   }
// }

// /// 项目数据分析预览遮罩
// class ProjectAnalysisPreviewOverlay extends StatelessWidget {
//   final Widget child;

//   const ProjectAnalysisPreviewOverlay({
//     super.key,
//     required this.child,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return PremiumPreviewOverlay(
//       featureName: '项目数据分析',
//       description: '全面掌握项目进度和推进速度',
//       benefits: const [
//         '项目进度可视化分析',
//         '项目推进速度对比',
//         '科目专注分布统计',
//         '项目完成预测',
//         '项目效率优化建议',
//       ],
//       child: child,
//     );
//   }
// }
