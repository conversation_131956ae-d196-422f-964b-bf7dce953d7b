import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/config/release_config.dart';
import '../../../core/services/auth_service.dart';
import 'subscription_debug_screen.dart';
import 'register_debug_screen.dart';

/// 开发者工具主页面
/// 提供各种开发和调试功能的入口
class DeveloperToolsScreen extends ConsumerWidget {
  const DeveloperToolsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('开发者工具'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 环境信息卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '环境信息',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 12),
                    ...ReleaseConfig.developerInfo.entries.map((entry) =>
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: Text(
                                entry.key,
                                style: const TextStyle(fontWeight: FontWeight.w500),
                              ),
                            ),
                            Expanded(
                              flex: 3,
                              child: Text(entry.value.toString()),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 调试工具卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '调试工具',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),

                    // 订阅状态调试
                    _buildToolButton(
                      context,
                      icon: Icons.subscriptions,
                      title: '订阅状态调试',
                      subtitle: '管理和测试订阅状态',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const SubscriptionDebugScreen(),
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: 12),

                    // 注册功能测试
                    _buildToolButton(
                      context,
                      icon: Icons.person_add,
                      title: '注册功能测试',
                      subtitle: '测试用户注册流程',
                      onTap: () {
                        Navigator.pushNamed(context, '/auth/register');
                      },
                    ),

                    const SizedBox(height: 12),

                    // 注册问题排查
                    _buildToolButton(
                      context,
                      icon: Icons.bug_report,
                      title: '注册问题排查',
                      subtitle: '分析注册失败原因',
                      onTap: () {
                        _showRegisterDebugDialog(context);
                      },
                    ),

                    const SizedBox(height: 12),

                    // 注册诊断工具
                    _buildToolButton(
                      context,
                      icon: Icons.medical_services,
                      title: '注册诊断工具',
                      subtitle: '逐步测试注册流程',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const RegisterDebugScreen(),
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: 12),

                    // 注销账号测试
                    _buildToolButton(
                      context,
                      icon: Icons.delete_forever,
                      title: '注销账号测试',
                      subtitle: '测试注销账号API',
                      onTap: () {
                        _showDeleteAccountTestDialog(context);
                      },
                    ),

                    const SizedBox(height: 12),

                    // 登录功能测试
                    _buildToolButton(
                      context,
                      icon: Icons.login,
                      title: '登录功能测试',
                      subtitle: '测试用户登录流程',
                      onTap: () {
                        Navigator.pushNamed(context, '/auth/login');
                      },
                    ),

                    const SizedBox(height: 12),

                    // 订阅页面测试
                    _buildToolButton(
                      context,
                      icon: Icons.star,
                      title: '订阅页面测试',
                      subtitle: '测试订阅购买流程',
                      onTap: () {
                        Navigator.pushNamed(context, '/subscription/premium');
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 快速操作卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '快速操作',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),

                    // 重启应用
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          _showRestartDialog(context);
                        },
                        icon: const Icon(Icons.restart_alt),
                        label: const Text('重启应用'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),

                    const SizedBox(height: 8),

                    // 清除所有数据
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          _showClearDataDialog(context);
                        },
                        icon: const Icon(Icons.delete_forever),
                        label: const Text('清除所有数据'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 说明文字
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '使用说明',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '• 此页面仅在开发模式下可用\n'
                      '• 所有调试功能仅用于开发和测试\n'
                      '• 生产环境中此页面将被隐藏\n'
                      '• 请谨慎使用数据清除功能',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToolButton(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(icon, color: AppColors.primary),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.arrow_forward_ios, size: 16),
          ],
        ),
      ),
    );
  }

  void _showRestartDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重启应用'),
        content: const Text('此功能需要手动重启应用。\n请关闭应用后重新打开。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showClearDataDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清除所有数据'),
        content: const Text('此操作将删除所有本地数据，包括：\n• 用户数据\n• 订阅状态\n• 应用设置\n\n此操作不可恢复，确定要继续吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _clearAllData(context);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('确定清除'),
          ),
        ],
      ),
    );
  }

  void _showRegisterDebugDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('注册问题排查'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                '常见注册失败原因：',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text('1. 密码格式不符合要求'),
              const Text('   • 需要6-30位字符'),
              const Text('   • 必须包含至少一个小写字母'),
              const Text('   • 必须包含一个大写字母或数字'),
              const SizedBox(height: 8),
              const Text('2. 验证码问题'),
              const Text('   • 验证码已过期（5分钟有效期）'),
              const Text('   • 验证码输入错误'),
              const SizedBox(height: 8),
              const Text('3. 邮箱问题'),
              const Text('   • 邮箱已被注册'),
              const Text('   • 邮箱格式不正确'),
              const SizedBox(height: 8),
              const Text('4. 网络问题'),
              const Text('   • 网络连接不稳定'),
              const Text('   • 服务器响应超时'),
              const SizedBox(height: 12),
              const Text(
                '建议解决方案：',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text('• 使用6-30位密码，包含小写字母和大写字母或数字'),
              const Text('• 确保验证码在有效期内'),
              const Text('• 检查邮箱是否已注册'),
              const Text('• 确保网络连接正常'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/auth/register');
            },
            child: const Text('去注册'),
          ),
        ],
      ),
    );
  }

  void _clearAllData(BuildContext context) {
    // TODO: 实现清除所有数据的功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('数据清除功能待实现'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  // 显示注销账号测试弹窗
  void _showDeleteAccountTestDialog(BuildContext context) {
    final passwordController = TextEditingController();
    final confirmTextController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('注销账号API测试'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '此功能用于测试注销账号API（严格按照API文档格式）',
                style: TextStyle(fontSize: 14, color: Colors.orange),
              ),
              const SizedBox(height: 8),
              const Text(
                'API格式：{"password": "xxx", "confirmText": "确认注销"}',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
              const SizedBox(height: 16),

              TextField(
                controller: passwordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: '当前密码',
                  border: OutlineInputBorder(),
                  isDense: true,
                ),
              ),
              const SizedBox(height: 12),

              TextField(
                controller: confirmTextController,
                decoration: const InputDecoration(
                  labelText: '确认文本',
                  hintText: '确认注销',
                  border: OutlineInputBorder(),
                  isDense: true,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              passwordController.dispose();
              confirmTextController.dispose();
              Navigator.pop(context);
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              final password = passwordController.text;
              final confirmText = confirmTextController.text;

              passwordController.dispose();
              confirmTextController.dispose();
              Navigator.pop(context);

              await _testDeleteAccountAPI(context, password, confirmText);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('测试API'),
          ),
        ],
      ),
    );
  }

  // 测试注销账号API
  Future<void> _testDeleteAccountAPI(BuildContext context, String password, String confirmText) async {
    try {
      // 显示加载指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('测试中...'),
            ],
          ),
        ),
      );

      // 调用注销账号API
      final authService = AuthService();
      final success = await authService.deleteAccount(
        password: password,
        confirmText: confirmText,
      );

      // 关闭加载指示器
      if (context.mounted) {
        Navigator.pop(context);
      }

      // 显示结果
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(success ? '测试成功' : '测试失败'),
            content: Text(success ? '注销账号API调用成功' : '注销账号API调用失败'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('确定'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // 关闭加载指示器
      if (context.mounted) {
        Navigator.pop(context);
      }

      // 显示错误详情
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('API测试失败'),
            content: SingleChildScrollView(
              child: Text('错误详情：\n$e'),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('确定'),
              ),
            ],
          ),
        );
      }
    }
  }
}
