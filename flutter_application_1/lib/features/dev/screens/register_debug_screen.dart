import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/services/auth_service.dart';
import '../../../core/utils/device_utils.dart';
import '../../../shared/theme/constants.dart';

/// 注册问题诊断页面
/// 用于测试和诊断注册流程中的问题
class RegisterDebugScreen extends ConsumerStatefulWidget {
  const RegisterDebugScreen({super.key});

  @override
  ConsumerState<RegisterDebugScreen> createState() => _RegisterDebugScreenState();
}

class _RegisterDebugScreenState extends ConsumerState<RegisterDebugScreen> {
  final AuthService _authService = AuthService();
  final _emailController = TextEditingController(text: '<EMAIL>');
  final _codeController = TextEditingController();
  final _passwordController = TextEditingController(text: 'TestPass1'); // 符合新规则：小写+大写+数字
  final _nicknameController = TextEditingController(text: 'TestUser');

  bool _isLoading = false;
  String? _lastError;
  Map<String, dynamic>? _deviceInfo;
  Map<String, dynamic>? _lastResponse;

  @override
  void initState() {
    super.initState();
    _loadDeviceInfo();
  }

  Future<void> _loadDeviceInfo() async {
    try {
      final deviceInfo = await DeviceUtils.getDeviceInfo();
      setState(() {
        _deviceInfo = deviceInfo;
      });
    } catch (e) {
      debugPrint('获取设备信息失败: $e');
    }
  }

  Future<void> _testSendCode() async {
    setState(() {
      _isLoading = true;
      _lastError = null;
    });

    try {
      final result = await _authService.sendVerificationCode(
        _emailController.text.trim(),
        type: 'register',
      );

      setState(() {
        _lastResponse = result;
        _isLoading = false;
      });

      _showMessage('验证码发送${result['success'] ? '成功' : '失败'}: ${result['message']}');
    } catch (e) {
      setState(() {
        _lastError = e.toString();
        _isLoading = false;
      });
      _showMessage('发送验证码失败: $e');
    }
  }

  Future<void> _testVerifyCode() async {
    if (_codeController.text.trim().isEmpty) {
      _showMessage('请输入验证码');
      return;
    }

    setState(() {
      _isLoading = true;
      _lastError = null;
    });

    try {
      final result = await _authService.verifyCode(
        _emailController.text.trim(),
        _codeController.text.trim(),
      );

      setState(() {
        _isLoading = false;
      });

      _showMessage('验证码验证${result ? '成功' : '失败'}');
    } catch (e) {
      setState(() {
        _lastError = e.toString();
        _isLoading = false;
      });
      _showMessage('验证码验证失败: $e');
    }
  }

  Future<void> _testRegister() async {
    if (_codeController.text.trim().isEmpty) {
      _showMessage('请先输入验证码');
      return;
    }

    setState(() {
      _isLoading = true;
      _lastError = null;
    });

    try {
      // 生成不同的用户名策略
      String username;
      final nickname = _nicknameController.text.trim();

      if (nickname.isNotEmpty) {
        // 策略1: 使用昵称
        username = nickname;
      } else {
        // 策略2: 使用邮箱前缀 + 随机数
        final emailPrefix = _emailController.text.trim().split('@')[0];
        final cleanPrefix = emailPrefix.replaceAll(RegExp(r'[^a-zA-Z0-9]'), '');
        final randomSuffix = DateTime.now().millisecondsSinceEpoch % 10000;
        username = '${cleanPrefix}_$randomSuffix';
      }

      debugPrint('尝试注册 - 用户名: $username, 密码: ${_passwordController.text}');

      final result = await _authService.register(
        email: _emailController.text.trim(),
        verificationCode: _codeController.text.trim(),
        password: _passwordController.text.trim(),
        nickname: nickname.isNotEmpty ? nickname : null,
        username: username,
        agreeToTerms: true, // 诊断工具默认同意条款
      );

      setState(() {
        _isLoading = false;
      });

      _showMessage('注册${result ? '成功' : '失败'}');
    } catch (e) {
      setState(() {
        _lastError = e.toString();
        _isLoading = false;
      });
      _showMessage('注册失败: $e');
      debugPrint('注册失败详细信息: $e');
    }
  }

  void _showMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message)),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('注册问题诊断'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 测试参数卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '测试参数',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),

                    TextField(
                      controller: _emailController,
                      decoration: const InputDecoration(
                        labelText: '邮箱',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 12),

                    TextField(
                      controller: _codeController,
                      decoration: const InputDecoration(
                        labelText: '验证码',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 12),

                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _passwordController,
                            decoration: const InputDecoration(
                              labelText: '密码',
                              border: OutlineInputBorder(),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        PopupMenuButton<String>(
                          icon: const Icon(Icons.more_vert),
                          onSelected: (value) {
                            _passwordController.text = value;
                          },
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'TestPass1',
                              child: Text('TestPass1 ✅ (小写+大写+数字)'),
                            ),
                            const PopupMenuItem(
                              value: 'testPass1',
                              child: Text('testPass1 ✅ (小写+大写+数字)'),
                            ),
                            const PopupMenuItem(
                              value: 'Test123',
                              child: Text('Test123 ✅ (小写+大写+数字)'),
                            ),
                            const PopupMenuItem(
                              value: 'testpass1',
                              child: Text('testpass1 ✅ (小写+数字)'),
                            ),
                            const PopupMenuItem(
                              value: 'TESTPASS1',
                              child: Text('TESTPASS1 ❌ (只有大写+数字)'),
                            ),
                            const PopupMenuItem(
                              value: 'testpass',
                              child: Text('testpass ❌ (只有小写)'),
                            ),
                            const PopupMenuItem(
                              value: '123456',
                              child: Text('123456 ❌ (只有数字)'),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),

                    TextField(
                      controller: _nicknameController,
                      decoration: const InputDecoration(
                        labelText: '昵称',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 测试操作卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '测试操作',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),

                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _testSendCode,
                        child: const Text('1. 发送验证码'),
                      ),
                    ),
                    const SizedBox(height: 8),

                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _testVerifyCode,
                        child: const Text('2. 验证验证码（可选）'),
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      '注意：验证码只能使用一次，验证后请立即注册',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                    const SizedBox(height: 8),

                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _testRegister,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('3. 执行注册'),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 设备信息卡片
            if (_deviceInfo != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '设备信息',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 12),
                      ..._deviceInfo!.entries.map((entry) =>
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: Text(
                                  entry.key,
                                  style: const TextStyle(fontWeight: FontWeight.w500),
                                ),
                              ),
                              Expanded(
                                flex: 3,
                                child: Text(entry.value.toString()),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // 错误信息卡片
            if (_lastError != null)
              Card(
                color: Colors.red.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.error, color: Colors.red.shade700),
                          const SizedBox(width: 8),
                          Text(
                            '最后错误',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: Colors.red.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _lastError!,
                        style: TextStyle(color: Colors.red.shade700),
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // 响应信息卡片
            if (_lastResponse != null)
              Card(
                color: Colors.green.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.check_circle, color: Colors.green.shade700),
                          const SizedBox(width: 8),
                          Text(
                            '最后响应',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: Colors.green.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _lastResponse.toString(),
                        style: TextStyle(color: Colors.green.shade700),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _codeController.dispose();
    _passwordController.dispose();
    _nicknameController.dispose();
    super.dispose();
  }
}
