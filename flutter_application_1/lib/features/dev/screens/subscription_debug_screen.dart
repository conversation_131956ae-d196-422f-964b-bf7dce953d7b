import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/services/apple_subscription_service.dart';
import '../../../core/utils/storage_utils.dart';
import '../../../shared/theme/constants.dart';

/// 订阅调试页面
/// 用于开发和测试阶段管理订阅状态
class SubscriptionDebugScreen extends ConsumerStatefulWidget {
  const SubscriptionDebugScreen({super.key});

  @override
  ConsumerState<SubscriptionDebugScreen> createState() => _SubscriptionDebugScreenState();
}

class _SubscriptionDebugScreenState extends ConsumerState<SubscriptionDebugScreen> {
  final AppleSubscriptionService _subscriptionService = AppleSubscriptionService();
  bool? _currentStatus;
  Map<String, dynamic>? _subscriptionDetails;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCurrentStatus();
  }

  Future<void> _loadCurrentStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final status = await _subscriptionService.isPremiumUser();
      final details = await _subscriptionService.getSubscriptionDetails();
      
      setState(() {
        _currentStatus = status;
        _subscriptionDetails = details;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showMessage('加载状态失败: $e');
    }
  }

  Future<void> _setTestStatus(bool status) async {
    try {
      _subscriptionService.setTestSubscriptionStatus(status);
      await StorageUtils.setTestPremiumStatus(status);
      await _loadCurrentStatus();
      _showMessage('测试状态已设置为: ${status ? "付费用户" : "免费用户"}');
    } catch (e) {
      _showMessage('设置测试状态失败: $e');
    }
  }

  Future<void> _clearTestData() async {
    try {
      await _subscriptionService.clearTestSubscriptionData();
      await _loadCurrentStatus();
      _showMessage('测试数据已清除');
    } catch (e) {
      _showMessage('清除测试数据失败: $e');
    }
  }

  Future<void> _refreshStatus() async {
    try {
      setState(() {
        _isLoading = true;
      });
      
      final status = await _subscriptionService.refreshSubscriptionStatus();
      await _loadCurrentStatus();
      _showMessage('状态已刷新: ${status ? "付费用户" : "免费用户"}');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showMessage('刷新状态失败: $e');
    }
  }

  void _showMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message)),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('订阅状态调试'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // 当前状态卡片
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '当前订阅状态',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(
                                _currentStatus == true ? Icons.check_circle : Icons.cancel,
                                color: _currentStatus == true ? Colors.green : Colors.red,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _currentStatus == true ? '付费用户' : '免费用户',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: _currentStatus == true ? Colors.green : Colors.red,
                                ),
                              ),
                            ],
                          ),
                          if (_subscriptionDetails != null) ...[
                            const SizedBox(height: 12),
                            const Divider(),
                            const SizedBox(height: 8),
                            Text(
                              '订阅详情',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 8),
                            ..._subscriptionDetails!.entries.map((entry) => 
                              Padding(
                                padding: const EdgeInsets.symmetric(vertical: 2),
                                child: Row(
                                  children: [
                                    Expanded(
                                      flex: 2,
                                      child: Text(
                                        entry.key,
                                        style: const TextStyle(fontWeight: FontWeight.w500),
                                      ),
                                    ),
                                    Expanded(
                                      flex: 3,
                                      child: Text(entry.value.toString()),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 测试操作按钮
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '测试操作',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: 16),
                          
                          // 设置为付费用户
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: () => _setTestStatus(true),
                              icon: const Icon(Icons.star),
                              label: const Text('设置为付费用户'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                          
                          const SizedBox(height: 8),
                          
                          // 设置为免费用户
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: () => _setTestStatus(false),
                              icon: const Icon(Icons.person),
                              label: const Text('设置为免费用户'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                          
                          const SizedBox(height: 8),
                          
                          // 清除测试数据
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _clearTestData,
                              icon: const Icon(Icons.clear_all),
                              label: const Text('清除测试数据'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                          
                          const SizedBox(height: 8),
                          
                          // 刷新状态
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _refreshStatus,
                              icon: const Icon(Icons.refresh),
                              label: const Text('刷新状态'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.primary,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 说明文字
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '使用说明',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            '• 此页面仅用于开发和测试\n'
                            '• 设置测试状态会覆盖真实的订阅状态\n'
                            '• 清除测试数据会删除所有本地订阅信息\n'
                            '• 刷新状态会重新检查订阅状态\n'
                            '• 生产环境中此功能将被禁用',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
