import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/improved_calendar_widget.dart';

/// 日历组件测试页面
/// 用于单独测试日历组件的功能
class CalendarTestScreen extends ConsumerStatefulWidget {
  const CalendarTestScreen({super.key});

  @override
  ConsumerState<CalendarTestScreen> createState() => _CalendarTestScreenState();
}

class _CalendarTestScreenState extends ConsumerState<CalendarTestScreen> {
  bool _isCalendarExpanded = true;
  DateTime _selectedDate = DateTime.now();
  String _log = '';

  void _addLog(String message) {
    setState(() {
      _log = '$message\n$_log';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('日历组件测试'),
      ),
      body: Column(
        children: [
          // 日历组件
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: ImprovedCalendarWidget(
              isExpanded: _isCalendarExpanded,
              onExpandToggle: (isExpanded) {
                setState(() {
                  _isCalendarExpanded = isExpanded;
                });
                _addLog('日历${isExpanded ? "展开" : "收起"}');
              },
              onDateSelected: (date) {
                setState(() {
                  _selectedDate = date;
                });
                _addLog('选择日期: ${date.year}-${date.month}-${date.day}');
              },
            ),
          ),

          // 当前选中的日期
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              '当前选中日期: ${_selectedDate.year}-${_selectedDate.month}-${_selectedDate.day}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),

          // 操作按钮
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _isCalendarExpanded = !_isCalendarExpanded;
                    });
                    _addLog('手动${_isCalendarExpanded ? "展开" : "收起"}日历');
                  },
                  child: Text(_isCalendarExpanded ? '收起日历' : '展开日历'),
                ),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _selectedDate = DateTime.now();
                    });
                    _addLog('重置为今天: ${_selectedDate.year}-${_selectedDate.month}-${_selectedDate.day}');
                  },
                  child: const Text('今天'),
                ),
              ],
            ),
          ),

          // 日志区域
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16.0),
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: SingleChildScrollView(
                child: Text(_log),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
