import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/models/schedule.dart';
import '../../../core/services/check_in_sync_service.dart';
import '../widgets/improved_calendar_widget.dart';
import '../widgets/schedule_item_card.dart';
import '../widgets/schedule_create_sheet.dart';
import '../providers/schedule_state.dart';
import '../../../core/services/hive_service.dart';

/// 日程页面
/// 显示用户的日程安排，包括计划、打卡和待办事项
class TaskScheduleScreen extends ConsumerStatefulWidget {
  const TaskScheduleScreen({super.key});

  @override
  ConsumerState<TaskScheduleScreen> createState() => _TaskScheduleScreenState();
}

class _TaskScheduleScreenState extends ConsumerState<TaskScheduleScreen> {
  // Hive服务
  final HiveService _hiveService = HiveService();

  // 日历展开状态
  bool _isCalendarExpanded = false;

  // 是否已初始化
  bool _isInitialized = false;

  // 各类型日程的折叠状态
  bool _isPlanCollapsed = false;
  bool _isRoutineCollapsed = false;
  bool _isTodoCollapsed = false;

  @override
  void initState() {
    super.initState();
    // 从Hive加载数据
    _loadData();
  }

  // 从Hive加载数据
  Future<void> _loadData() async {
    try {
      // 确保Hive服务已初始化
      await _hiveService.initHive();

      // 获取所有日程
      final schedules = _hiveService.scheduleRepository.getAllSchedules();

      // 使用Future.microtask确保不在构建生命周期中直接修改状态
      Future.microtask(() {
        // 设置加载状态
        ref.read(scheduleStateProvider.notifier).setLoading(true);

        // 设置日程数据
        ref.read(scheduleStateProvider.notifier).setSchedules(schedules);

        // 设置当前选中日期为今天
        ref.read(scheduleStateProvider.notifier).setSelectedDate(DateTime.now());

        // 设置加载完成
        ref.read(scheduleStateProvider.notifier).setLoading(false);
        setState(() {
          _isInitialized = true;
        });
      });
    } catch (e) {
      print('加载数据出错: $e');
      ref.read(scheduleStateProvider.notifier).setError('加载数据失败: $e');
    }
  }

  // 显示创建日程弹窗
  void _showCreateScheduleSheet() {
    final selectedDate = ref.read(scheduleStateProvider).selectedDate ?? DateTime.now();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ScheduleCreateSheet(
        selectedDate: selectedDate,
        onScheduleCreated: (schedule) async {
          // 保存到Hive
          await _hiveService.scheduleRepository.saveSchedule(schedule);

          // 如果是打卡类型，同步到CheckInTask
          if (schedule.type == ScheduleType.routine) {
            try {
              final checkInSyncService = CheckInSyncService(
                scheduleRepository: _hiveService.scheduleRepository,
                checkInTaskRepository: _hiveService.checkInTaskRepository,
              );
              await checkInSyncService.syncScheduleToCheckInTask(schedule);
              debugPrint('打卡任务同步成功: ${schedule.title}');
            } catch (e) {
              debugPrint('打卡任务同步失败: $e');
            }
          }

          // 更新Riverpod状态
          ref.read(scheduleStateProvider.notifier).addSchedule(schedule);
        },
      ),
    );
  }

  // 显示编辑日程弹窗
  void _showEditScheduleSheet(Schedule schedule) {
    final selectedDate = ref.read(scheduleStateProvider).selectedDate ?? DateTime.now();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ScheduleCreateSheet(
        selectedDate: selectedDate,
        schedule: schedule,
        onScheduleCreated: (updatedSchedule) async {
          // 保存到Hive
          await _hiveService.scheduleRepository.saveSchedule(updatedSchedule);

          // 更新Riverpod状态
          ref.read(scheduleStateProvider.notifier).updateSchedule(updatedSchedule);
        },
      ),
    );
  }

  // 显示删除确认对话框
  Future<void> _showDeleteConfirmDialog(Schedule schedule) async {
    // 只对打卡类型进行验证，计划和待办不需要验证
    bool shouldDelete = true;

    if (schedule.type == ScheduleType.routine) {
      // 对打卡类型显示确认对话框
      shouldDelete = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('确认删除'),
          content: Text('确定要删除"${schedule.title}"吗？\n\n删除打卡记录将会丢失所有历史数据。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            TextButton(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.error,
              ),
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('删除'),
            ),
          ],
        ),
      ) ?? false;
    }

    if (shouldDelete) {
      // 从Hive删除
      await _hiveService.scheduleRepository.deleteSchedule(schedule.id);

      // 更新Riverpod状态
      ref.read(scheduleStateProvider.notifier).deleteSchedule(schedule.id);
    }
  }

  // 切换日程完成状态
  Future<void> _markScheduleAsCompleted(Schedule schedule) async {
    try {
      final selectedDate = ref.read(scheduleStateProvider).selectedDate ?? DateTime.now();
      final isCompletedOnDate = schedule.isCompletedOnDate(selectedDate);

      debugPrint('切换日程完成状态: ${schedule.title}, 日期: ${selectedDate.toString()}, 当前状态: ${isCompletedOnDate ? '已完成' : '未完成'}');

      if (isCompletedOnDate) {
        // 如果已完成，则取消完成状态
        debugPrint('取消完成状态');
        await _hiveService.scheduleRepository.toggleScheduleCompletion(schedule.id, selectedDate);
      } else {
        // 如果未完成，则标记为完成
        debugPrint('标记为完成');
        await _hiveService.scheduleRepository.markScheduleAsCompleted(schedule.id, selectedDate);
      }

      // 获取更新后的日程
      final updatedSchedule = _hiveService.scheduleRepository.getScheduleById(schedule.id);
      if (updatedSchedule != null) {
        debugPrint('更新后的完成日期列表: ${updatedSchedule.completedDates?.map((d) => d.toString()).join(', ') ?? '空'}');
      }

      // 重新加载数据以更新UI
      final updatedSchedules = _hiveService.scheduleRepository.getAllSchedules();
      ref.read(scheduleStateProvider.notifier).setSchedules(updatedSchedules);

      // 强制刷新UI
      setState(() {});
    } catch (e) {
      debugPrint('切换日程完成状态出错: $e');
    }
  }

  // 构建分区标题
  Widget _buildSectionHeader(String title, Color color, IconData icon, {
    required bool isCollapsed,
    required VoidCallback onToggle,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 左侧标题部分
        Row(
          children: [
            Icon(
              icon,
              size: 18,
              color: color,
            ),
            const SizedBox(width: 6),
            Text(
              title,
              style: AppTextStyles.headline3.copyWith(
                fontSize: 16,
                color: AppColors.text,
              ),
            ),
          ],
        ),

        // 右侧折叠按钮
        IconButton(
          icon: Icon(
            isCollapsed ? Icons.keyboard_arrow_down : Icons.keyboard_arrow_up,
            size: 20,
            color: AppColors.textSecondary,
          ),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(
            minWidth: 32,
            minHeight: 32,
          ),
          onPressed: onToggle,
        ),
      ],
    );
  }

  // 构建日程内容（包括日历和列表）
  Widget _buildScheduleContent() {
    final selectedDate = ref.watch(scheduleStateProvider).selectedDate ?? DateTime.now();
    final schedules = ref.watch(scheduleStateProvider).schedules;

    // 按类型分组
    final plans = schedules.where((s) =>
      s.type == ScheduleType.plan && s.isOnDate(selectedDate)
    ).toList();

    final routines = schedules.where((s) =>
      s.type == ScheduleType.routine && s.isOnDate(selectedDate)
    ).toList();

    final todos = schedules.where((s) =>
      s.type == ScheduleType.todo && s.isOnDate(selectedDate)
    ).toList();

    // 如果没有任何日程
    if (plans.isEmpty && routines.isEmpty && todos.isEmpty) {
      return ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        children: [
          // 日历部分
          ImprovedCalendarWidget(
            isExpanded: _isCalendarExpanded,
            onExpandToggle: (isExpanded) {
              setState(() {
                _isCalendarExpanded = isExpanded;
              });
            },
            onDateSelected: (date) {
              ref.read(scheduleStateProvider.notifier).setSelectedDate(date);
            },
          ),
          const SizedBox(height: 32),
          // 空状态提示
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.event_note,
                  size: 64,
                  color: AppColors.textHint,
                ),
                const SizedBox(height: 16),
                Text(
                  '暂无日程',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: AppColors.textHint,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '点击右上角按钮创建新日程',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textHint,
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return ListView(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      children: [
        // 日历部分
        ImprovedCalendarWidget(
          isExpanded: _isCalendarExpanded,
          onExpandToggle: (isExpanded) {
            setState(() {
              _isCalendarExpanded = isExpanded;
            });
          },
          onDateSelected: (date) {
            ref.read(scheduleStateProvider.notifier).setSelectedDate(date);
          },
        ),
        const SizedBox(height: 16),

        // 计划部分
        if (plans.isNotEmpty) ...[
          _buildSectionHeader(
            '计划',
            AppColors.primary,
            Icons.event,
            isCollapsed: _isPlanCollapsed,
            onToggle: () {
              setState(() {
                _isPlanCollapsed = !_isPlanCollapsed;
              });
            },
          ),
          const SizedBox(height: 8), // 使用AppSizes.paddingSmall的值
          if (!_isPlanCollapsed) Container(
            decoration: BoxDecoration(
              color: Colors.transparent, // 使用透明背景
              borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
              border: Border.all(
                color: AppColors.primary.withAlpha(AppColors.alpha10), // 非常淡的边框
                width: 0.5,
              ),
            ),
            clipBehavior: Clip.antiAlias, // 确保内容不超出圆角
            child: ListView.builder(
              shrinkWrap: true, // 使ListView高度适应内容
              physics: const NeverScrollableScrollPhysics(), // 禁用滚动，由外层ListView处理
              padding: EdgeInsets.zero, // 移除内边距
              itemCount: plans.length,
              itemBuilder: (context, index) => ScheduleItemCard(
                schedule: plans[index],
                selectedDate: selectedDate,
                onEdit: () => _showEditScheduleSheet(plans[index]),
                onDelete: () => _showDeleteConfirmDialog(plans[index]),
                onComplete: () => _markScheduleAsCompleted(plans[index]),
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],

        // 打卡部分
        if (routines.isNotEmpty) ...[
          _buildSectionHeader(
            '打卡',
            AppColors.secondary,
            Icons.repeat,
            isCollapsed: _isRoutineCollapsed,
            onToggle: () {
              setState(() {
                _isRoutineCollapsed = !_isRoutineCollapsed;
              });
            },
          ),
          const SizedBox(height: 8), // 使用AppSizes.paddingSmall的值
          if (!_isRoutineCollapsed) Container(
            decoration: BoxDecoration(
              color: Colors.transparent, // 使用透明背景
              borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
              border: Border.all(
                color: AppColors.secondary.withAlpha(AppColors.alpha10), // 非常淡的边框
                width: 0.5,
              ),
            ),
            clipBehavior: Clip.antiAlias, // 确保内容不超出圆角
            child: ListView.builder(
              shrinkWrap: true, // 使ListView高度适应内容
              physics: const NeverScrollableScrollPhysics(), // 禁用滚动，由外层ListView处理
              padding: EdgeInsets.zero, // 移除内边距
              itemCount: routines.length,
              itemBuilder: (context, index) => ScheduleItemCard(
                schedule: routines[index],
                selectedDate: selectedDate,
                onEdit: () => _showEditScheduleSheet(routines[index]),
                onDelete: () => _showDeleteConfirmDialog(routines[index]),
                onComplete: () => _markScheduleAsCompleted(routines[index]),
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],

        // 待办部分
        if (todos.isNotEmpty) ...[
          _buildSectionHeader(
            '待办',
            AppColors.accent,
            Icons.check_circle_outline,
            isCollapsed: _isTodoCollapsed,
            onToggle: () {
              setState(() {
                _isTodoCollapsed = !_isTodoCollapsed;
              });
            },
          ),
          const SizedBox(height: 8), // 使用AppSizes.paddingSmall的值
          if (!_isTodoCollapsed) Container(
            decoration: BoxDecoration(
              color: Colors.transparent, // 使用透明背景
              borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
              border: Border.all(
                color: AppColors.accent.withAlpha(AppColors.alpha10), // 非常淡的边框
                width: 0.5,
              ),
            ),
            clipBehavior: Clip.antiAlias, // 确保内容不超出圆角
            child: ListView.builder(
              shrinkWrap: true, // 使ListView高度适应内容
              physics: const NeverScrollableScrollPhysics(), // 禁用滚动，由外层ListView处理
              padding: EdgeInsets.zero, // 移除内边距
              itemCount: todos.length,
              itemBuilder: (context, index) => ScheduleItemCard(
                schedule: todos[index],
                selectedDate: selectedDate,
                onEdit: () => _showEditScheduleSheet(todos[index]),
                onDelete: () => _showDeleteConfirmDialog(todos[index]),
                onComplete: () => _markScheduleAsCompleted(todos[index]),
              ),
            ),
          ),
        ],
      ],
    );
  }

  // 构建日程列表（包含日历）
  Widget _buildScheduleList() {
    final selectedDate = ref.watch(scheduleStateProvider).selectedDate ?? DateTime.now();
    final schedules = ref.watch(scheduleStateProvider).schedules;

    // 按类型分组
    final plans = schedules.where((s) =>
      s.type == ScheduleType.plan && s.isOnDate(selectedDate)
    ).toList();

    final routines = schedules.where((s) =>
      s.type == ScheduleType.routine && s.isOnDate(selectedDate)
    ).toList();

    final todos = schedules.where((s) =>
      s.type == ScheduleType.todo && s.isOnDate(selectedDate)
    ).toList();

    // 如果没有任何日程
    if (plans.isEmpty && routines.isEmpty && todos.isEmpty) {
      return ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        children: [
          // 日历部分
          ImprovedCalendarWidget(
            isExpanded: _isCalendarExpanded,
            onExpandToggle: (isExpanded) {
              setState(() {
                _isCalendarExpanded = isExpanded;
              });
            },
            onDateSelected: (date) {
              ref.read(scheduleStateProvider.notifier).setSelectedDate(date);
            },
          ),
          const SizedBox(height: 32),
          // 空状态提示
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.event_note,
                  size: 64,
                  color: AppColors.textHint,
                ),
                const SizedBox(height: 16),
                Text(
                  '暂无日程',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: AppColors.textHint,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '点击右上角按钮创建新日程',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textHint,
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return ListView(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      children: [
        // 日历部分
        ImprovedCalendarWidget(
          isExpanded: _isCalendarExpanded,
          onExpandToggle: (isExpanded) {
            setState(() {
              _isCalendarExpanded = isExpanded;
            });
          },
          onDateSelected: (date) {
            ref.read(scheduleStateProvider.notifier).setSelectedDate(date);
          },
        ),
        const SizedBox(height: 16),

        // 计划部分
        if (plans.isNotEmpty) ...[
          _buildSectionHeader(
            '计划',
            AppColors.primary,
            Icons.event,
            isCollapsed: _isPlanCollapsed,
            onToggle: () {
              setState(() {
                _isPlanCollapsed = !_isPlanCollapsed;
              });
            },
          ),
          const SizedBox(height: 8), // 使用AppSizes.paddingSmall的值
          if (!_isPlanCollapsed) Container(
            decoration: BoxDecoration(
              color: Colors.transparent, // 使用透明背景
              borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
              border: Border.all(
                color: AppColors.primary.withAlpha(AppColors.alpha10), // 非常淡的边框
                width: 0.5,
              ),
            ),
            clipBehavior: Clip.antiAlias, // 确保内容不超出圆角
            child: ListView.builder(
              shrinkWrap: true, // 使ListView高度适应内容
              physics: const NeverScrollableScrollPhysics(), // 禁用滚动，由外层ListView处理
              padding: EdgeInsets.zero, // 移除内边距
              itemCount: plans.length,
              itemBuilder: (context, index) => ScheduleItemCard(
                schedule: plans[index],
                selectedDate: selectedDate,
                onEdit: () => _showEditScheduleSheet(plans[index]),
                onDelete: () => _showDeleteConfirmDialog(plans[index]),
                onComplete: () => _markScheduleAsCompleted(plans[index]),
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],

        // 打卡部分
        if (routines.isNotEmpty) ...[
          _buildSectionHeader(
            '打卡',
            AppColors.secondary,
            Icons.repeat,
            isCollapsed: _isRoutineCollapsed,
            onToggle: () {
              setState(() {
                _isRoutineCollapsed = !_isRoutineCollapsed;
              });
            },
          ),
          const SizedBox(height: 8), // 使用AppSizes.paddingSmall的值
          if (!_isRoutineCollapsed) Container(
            decoration: BoxDecoration(
              color: Colors.transparent, // 使用透明背景
              borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
              border: Border.all(
                color: AppColors.secondary.withAlpha(AppColors.alpha10), // 非常淡的边框
                width: 0.5,
              ),
            ),
            clipBehavior: Clip.antiAlias, // 确保内容不超出圆角
            child: ListView.builder(
              shrinkWrap: true, // 使ListView高度适应内容
              physics: const NeverScrollableScrollPhysics(), // 禁用滚动，由外层ListView处理
              padding: EdgeInsets.zero, // 移除内边距
              itemCount: routines.length,
              itemBuilder: (context, index) => ScheduleItemCard(
                schedule: routines[index],
                selectedDate: selectedDate,
                onEdit: () => _showEditScheduleSheet(routines[index]),
                onDelete: () => _showDeleteConfirmDialog(routines[index]),
                onComplete: () => _markScheduleAsCompleted(routines[index]),
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],

        // 待办部分
        if (todos.isNotEmpty) ...[
          _buildSectionHeader(
            '待办',
            AppColors.accent,
            Icons.check_circle_outline,
            isCollapsed: _isTodoCollapsed,
            onToggle: () {
              setState(() {
                _isTodoCollapsed = !_isTodoCollapsed;
              });
            },
          ),
          const SizedBox(height: 8), // 使用AppSizes.paddingSmall的值
          if (!_isTodoCollapsed) Container(
            decoration: BoxDecoration(
              color: Colors.transparent, // 使用透明背景
              borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
              border: Border.all(
                color: AppColors.accent.withAlpha(AppColors.alpha10), // 非常淡的边框
                width: 0.5,
              ),
            ),
            clipBehavior: Clip.antiAlias, // 确保内容不超出圆角
            child: ListView.builder(
              shrinkWrap: true, // 使ListView高度适应内容
              physics: const NeverScrollableScrollPhysics(), // 禁用滚动，由外层ListView处理
              padding: EdgeInsets.zero, // 移除内边距
              itemCount: todos.length,
              itemBuilder: (context, index) => ScheduleItemCard(
                schedule: todos[index],
                selectedDate: selectedDate,
                onEdit: () => _showEditScheduleSheet(todos[index]),
                onDelete: () => _showDeleteConfirmDialog(todos[index]),
                onComplete: () => _markScheduleAsCompleted(todos[index]),
              ),
            ),
          ),
        ],
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final isLoading = ref.watch(scheduleStateProvider).isLoading;

    if (isLoading && !_isInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    return Scaffold(
      // 使用渐变背景，与应用其他页面保持一致
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color.fromARGB(255, 204, 255, 229), // 顶部颜色
              Color.fromARGB(255, 255, 250, 240), // 底部颜色
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 标题
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      '日程',
                      style: AppTextStyles.headline2,
                    ),
                    // 新增按钮 - 参考目标页面卡片编辑按钮样式
                    Container(
                      height: 32, // 保持高度一致
                      width: 32, // 保持宽度一致
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8), // 保持圆角一致
                        color: const Color.fromARGB(255, 255, 255, 255), // 使用背景色，不使用边框
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.add_outlined, size: 16),
                        tooltip: "新增日程",
                        color: AppColors.textSecondary,
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                        onPressed: _showCreateScheduleSheet,
                      ),
                    ),
                  ],
                ),
              ),

              // 内容部分（包含日历和日程列表）
              Expanded(
                child: _buildScheduleList(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
