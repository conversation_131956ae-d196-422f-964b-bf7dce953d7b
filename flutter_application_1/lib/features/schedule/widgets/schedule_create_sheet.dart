import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../shared/widgets/common_bottom_sheet.dart';
import '../../../core/models/schedule.dart';
import '../../../features/task/providers/goal_state.dart';
import '../../../core/services/hive_service.dart';
import '../../../shared/widgets/improved_date_picker.dart';
import '../../../shared/widgets/milestone_date_picker.dart';
import '../../../shared/widgets/custom_time_picker.dart';
import '../../../shared/widgets/keyboard_input_overlay.dart';
import '../../../shared/widgets/top_message_overlay.dart';

/// 日程创建底部弹窗
/// 用于创建新的日程项目
class ScheduleCreateSheet extends ConsumerStatefulWidget {
  final DateTime selectedDate;
  final Schedule? schedule; // 如果是编辑模式，传入要编辑的日程
  final Function(Schedule) onScheduleCreated;

  const ScheduleCreateSheet({
    super.key,
    required this.selectedDate,
    this.schedule,
    required this.onScheduleCreated,
  });

  @override
  ConsumerState<ScheduleCreateSheet> createState() => _ScheduleCreateSheetState();
}

class _ScheduleCreateSheetState extends ConsumerState<ScheduleCreateSheet> {
  // Hive服务
  final HiveService _hiveService = HiveService();
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late ScheduleType _selectedType;
  late DateTime _selectedDate;
  DateTime? _selectedTime;
  bool _isRepeat = false;
  RepeatType _repeatType = RepeatType.daily;
  List<int> _repeatDays = [];

  final List<String> _weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

  // 错误提示状态
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    // 如果是编辑模式，使用传入的日程数据初始化
    if (widget.schedule != null) {
      _titleController = TextEditingController(text: widget.schedule!.title);
      _descriptionController = TextEditingController(text: widget.schedule!.description ?? '');
      _selectedType = widget.schedule!.type;
      _selectedDate = widget.schedule!.date ?? widget.selectedDate;
      _selectedTime = widget.schedule!.time;

      // 对于打卡类型，始终设置为重复且默认每天重复
      if (_selectedType == ScheduleType.routine) {
        _isRepeat = true;
        _repeatType = widget.schedule!.repeatType ?? RepeatType.daily;
        _repeatDays = widget.schedule!.repeatDays ?? [];
      } else {
        _isRepeat = widget.schedule!.isRepeat;
        _repeatType = widget.schedule!.repeatType ?? RepeatType.daily;
        _repeatDays = widget.schedule!.repeatDays ?? [];
      }
    } else {
      // 创建模式，使用默认值
      _titleController = TextEditingController();
      _descriptionController = TextEditingController();
      _selectedType = ScheduleType.plan;
      _isRepeat = false;
      _repeatType = RepeatType.daily;

      // 默认选择周一到周五（对于每周重复）
      _repeatDays = [1, 2, 3, 4, 5]; // 1-7 代表周一到周日

      // 设置默认日期
      _selectedDate = widget.selectedDate;
    }

    // 初始化Hive服务
    _initHiveService();

    // 如果是创建模式且是打卡类型，设置默认截止日期
    if (widget.schedule == null && _selectedType == ScheduleType.routine) {
      _setDefaultEndDate();
    }
  }

  // 设置默认截止日期
  Future<void> _setDefaultEndDate() async {
    if (_selectedType == ScheduleType.routine) {
      try {
        await _hiveService.initHive();

        // 获取当前目标（如果有）
        final currentGoal = ref.read(goalStateProvider).currentGoal;

        if (currentGoal != null && currentGoal.endDate.isAfter(DateTime.now())) {
          // 使用目标的截止日期作为默认日期
          setState(() {
            _selectedDate = currentGoal.endDate;
          });
          debugPrint('使用目标截止日期作为默认日期: ${_selectedDate.toString()}');
        } else {
          // 如果没有目标或目标截止日期已过，默认为一年后
          final now = DateTime.now();
          setState(() {
            _selectedDate = DateTime(now.year + 1, now.month, now.day);
          });
          debugPrint('使用一年后作为默认日期: ${_selectedDate.toString()}');
        }
      } catch (e) {
        debugPrint('设置默认截止日期出错: $e');
      }
    }
  }

  // 初始化Hive服务
  Future<void> _initHiveService() async {
    try {
      await _hiveService.initHive();
    } catch (e) {
      debugPrint('初始化Hive服务出错: $e');
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  // 生成符合重复规则的日期序列
  List<DateTime> _generateDateSequence(DateTime startDate, DateTime endDate, RepeatType repeatType, List<int> repeatDays) {
    final List<DateTime> dates = [];

    // 确保开始日期和结束日期只包含日期部分，不包含时间
    final start = DateTime(startDate.year, startDate.month, startDate.day);
    final end = DateTime(endDate.year, endDate.month, endDate.day);

    // 如果开始日期晚于结束日期，返回空列表
    if (start.isAfter(end)) {
      return dates;
    }

    // 当前日期，从开始日期开始
    DateTime current = start;

    // 循环直到当前日期超过结束日期
    while (!current.isAfter(end)) {
      bool shouldInclude = false;

      switch (repeatType) {
        case RepeatType.daily:
          // 每天都包含
          shouldInclude = true;
          break;

        case RepeatType.weekly:
          // 检查是否是指定的星期几（1-7，其中1是星期一）
          final weekday = current.weekday;
          shouldInclude = repeatDays.contains(weekday);
          break;

        case RepeatType.monthly:
          final day = current.day;
          // 获取当月的最后一天
          final lastDayOfMonth = DateTime(current.year, current.month + 1, 0).day;

          for (final repeatDay in repeatDays) {
            // 处理正常日期（1-28）
            if (repeatDay > 0 && repeatDay <= 28) {
              if (day == repeatDay) {
                shouldInclude = true;
                break;
              }
            }
            // 处理特殊日期：每月最后一天 (-1)
            else if (repeatDay == -1 && day == lastDayOfMonth) {
              shouldInclude = true;
              break;
            }
            // 处理特殊日期：倒数第二天 (-2)
            else if (repeatDay == -2 && day == lastDayOfMonth - 1) {
              shouldInclude = true;
              break;
            }
            // 处理特殊日期：倒数第三天 (-3)
            else if (repeatDay == -3 && day == lastDayOfMonth - 2) {
              shouldInclude = true;
              break;
            }
          }
          break;

        case RepeatType.custom:
          // 自定义重复规则暂不支持
          break;
      }

      if (shouldInclude) {
        dates.add(DateTime(current.year, current.month, current.day));
      }

      // 移动到下一天
      current = current.add(const Duration(days: 1));
    }

    return dates;
  }

  // 创建或更新日程
  void _createOrUpdateSchedule() {
    // 验证标题不能为空
    if (_titleController.text.trim().isEmpty) {
      _showInlineError('请输入标题');
      return;
    }

    // 对于计划类型，日期是必须的
    // 注意：_selectedDate在初始化时已设置为默认值，不会为null

    // 对于重复任务，验证重复设置
    if (_isRepeat) {
      // 对于每周和每月重复，必须选择具体日期
      if ((_repeatType == RepeatType.weekly || _repeatType == RepeatType.monthly) && _repeatDays.isEmpty) {
        _showInlineError('请选择重复日期');
        return;
      }
      // 每天重复不需要选择具体日期，_repeatDays为空是正常的
    }

    // 获取当前日期作为开始日期
    final now = DateTime.now();
    final startDate = DateTime(now.year, now.month, now.day);

    // 如果是编辑模式，使用原始开始日期
    final actualStartDate = widget.schedule?.date ?? startDate;

    // 设置结束日期
    final endDate = _selectedDate;

    // 如果是打卡类型，创建多个日程
    if (_selectedType == ScheduleType.routine) {
      // 如果是编辑模式，先删除原有日程
      if (widget.schedule != null) {
        // 回调通知创建成功（实际上是更新）
        final updatedSchedule = Schedule(
          id: widget.schedule!.id,
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim().isEmpty ? null : _descriptionController.text.trim(),
          type: _selectedType,
          createdAt: widget.schedule!.createdAt,
          date: actualStartDate,
          endDate: endDate,
          time: _selectedTime,
          isRepeat: true,
          repeatType: _repeatType,
          repeatDays: _repeatDays,
          status: widget.schedule!.status,
          completedDates: widget.schedule!.completedDates,
        );

        widget.onScheduleCreated(updatedSchedule);
      } else {
        // 生成符合重复规则的日期序列
        final dates = _generateDateSequence(actualStartDate, endDate, _repeatType, _repeatDays);

        // 如果没有符合条件的日期，显示提示
        if (dates.isEmpty) {
          _showInlineError('在选定的日期范围内没有符合重复规则的日期');
          return;
        }

        // 创建基础日程对象
        final baseSchedule = Schedule(
          id: const Uuid().v4(),
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim().isEmpty ? null : _descriptionController.text.trim(),
          type: _selectedType,
          createdAt: DateTime.now(),
          date: actualStartDate,
          endDate: endDate,
          time: _selectedTime,
          isRepeat: true,
          repeatType: _repeatType,
          repeatDays: _repeatDays,
          status: ScheduleStatus.pending,
          completedDates: [],
        );

        // 回调通知创建成功
        widget.onScheduleCreated(baseSchedule);
      }
    } else {
      // 非打卡类型，创建单个日程
      final schedule = Schedule(
        id: widget.schedule?.id ?? const Uuid().v4(),
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim().isEmpty ? null : _descriptionController.text.trim(),
        type: _selectedType,
        createdAt: widget.schedule?.createdAt ?? DateTime.now(),
        date: _selectedType == ScheduleType.todo ? null : _selectedDate,
        endDate: null, // 非打卡类型不需要结束日期
        time: _selectedTime,
        isRepeat: _isRepeat,
        repeatType: _isRepeat ? _repeatType : null,
        repeatDays: _isRepeat ? _repeatDays : null,
        status: widget.schedule?.status ?? ScheduleStatus.pending,
        completedDates: widget.schedule?.completedDates,
      );

      // 回调通知创建成功
      widget.onScheduleCreated(schedule);
    }

    // 关闭弹窗
    Navigator.pop(context);
  }

  // 构建类型选择器
  Widget _buildTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 使用iOS风格的分段控制器
        Center(
          child: Container(
            height: AppSizes.buttonHeightMedium, // 使用标准按钮高度
            decoration: BoxDecoration(
              color: Colors.white, // 白色背景
              borderRadius: BorderRadius.circular(AppSizes.radiusMedium), // 中等圆角
              border: Border.all(color: AppColors.border.withAlpha(128), width: 0.5), // 添加边框增强层级感
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(20), // 增强阴影
                  blurRadius: 4,
                  spreadRadius: 0,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            padding: const EdgeInsets.all(2.0), // 减小内边距，让选项更贴近边缘
            child: Row(
              children: [
                // 专注选项（原计划选项，改名为专注）
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedType = ScheduleType.plan;
                        // 专注类型默认使用当天日期
                        _selectedDate = widget.selectedDate;
                        // 切换tab时清除时间选择
                        _selectedTime = null;
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: _selectedType == ScheduleType.plan
                            ? AppColors.primary.withAlpha(AppColors.alpha30)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                      ),
                      // 移除margin，让高亮覆盖整个区域
                      height: double.infinity, // 确保高度填满父容器
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.timer,
                            size: 16,
                            color: _selectedType == ScheduleType.plan
                                ? AppColors.primary
                                : AppColors.text,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '专注',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: _selectedType == ScheduleType.plan
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: _selectedType == ScheduleType.plan
                                  ? AppColors.primary
                                  : AppColors.text,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // 待办选项（移到第二位）
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedType = ScheduleType.todo;
                        // 如果切换到待办类型，重置日期和重复设置
                        _isRepeat = false;
                        // 重置日期为当天
                        _selectedDate = widget.selectedDate;
                        // 切换tab时清除时间选择
                        _selectedTime = null;
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: _selectedType == ScheduleType.todo
                            ? AppColors.primary.withAlpha(AppColors.alpha30)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                      ),
                      // 移除margin，让高亮覆盖整个区域
                      height: double.infinity, // 确保高度填满父容器
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.check_circle_outline,
                            size: 16,
                            color: _selectedType == ScheduleType.todo
                                ? AppColors.primary
                                : AppColors.text,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '待办',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: _selectedType == ScheduleType.todo
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: _selectedType == ScheduleType.todo
                                  ? AppColors.primary
                                  : AppColors.text,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // 打卡选项（移到最右边）
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedType = ScheduleType.routine;
                        // 打卡类型自动设置为重复且每天重复
                        _isRepeat = true;
                        _repeatType = RepeatType.daily;
                        _repeatDays = []; // 每天重复不需要指定具体日期
                        // 切换tab时清除时间选择
                        _selectedTime = null;
                        // 清除错误提示
                        _errorMessage = null;
                      });
                      // 设置默认截止日期
                      _setDefaultEndDate();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: _selectedType == ScheduleType.routine
                            ? AppColors.primary.withAlpha(AppColors.alpha30)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                      ),
                      // 移除margin，让高亮覆盖整个区域
                      height: double.infinity, // 确保高度填满父容器
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.repeat,
                            size: 16,
                            color: _selectedType == ScheduleType.routine
                                ? AppColors.primary
                                : AppColors.text,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '打卡',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: _selectedType == ScheduleType.routine
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: _selectedType == ScheduleType.routine
                                  ? AppColors.primary
                                  : AppColors.text,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 构建日期选择器
  Widget _buildDateSelector() {
    if (_selectedType == ScheduleType.todo) {
      return const SizedBox.shrink(); // 待办类型不需要日期
    }

    // 打卡类型显示截止日期选择器，计划类型显示日期选择器
    final String labelText = _selectedType == ScheduleType.routine ? '截止日期' : '日期';
    final String titleText = _selectedType == ScheduleType.routine ? '选择截止日期' : '选择日期';

    // 对于打卡类型，如果是编辑模式且有结束日期，则使用结束日期作为初始日期
    final DateTime initialDate = _selectedType == ScheduleType.routine && widget.schedule?.endDate != null
        ? widget.schedule!.endDate!
        : _selectedDate;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Text(labelText, style: AppTextStyles.labelMedium),
        const SizedBox(height: 8),
        // 对于打卡类型使用里程碑日期选择器，否则使用普通日期选择器
        _selectedType == ScheduleType.routine
            ? MilestoneDatePicker(
                initialDate: initialDate,
                title: titleText,
                buttonText: titleText,
                onDateSelected: (date) {
                  if (date != null) {
                    setState(() {
                      _selectedDate = date;
                    });
                  }
                },
              )
            : ImprovedDatePicker(
                initialDate: initialDate,
                title: titleText,
                onDateSelected: (date) {
                  if (date != null) {
                    setState(() {
                      _selectedDate = date;
                    });
                  }
                },
              ),
      ],
    );
  }

  // 构建时间选择器
  Widget _buildTimeSelector() {
    if (_selectedType == ScheduleType.todo) {
      return const SizedBox.shrink(); // 待办类型不需要时间
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Row(
          children: [
            const Text('时间', style: AppTextStyles.labelMedium),
            const SizedBox(width: 8),
            Text('(可选)', style: AppTextStyles.caption.copyWith(color: AppColors.textHint)),
          ],
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () async {
            final time = await CustomTimePicker.show(
              context: context,
              initialTime: _selectedTime != null
                  ? TimeOfDay(hour: _selectedTime!.hour, minute: _selectedTime!.minute)
                  : null,
            );

            setState(() {
              if (time != null) {
                final now = DateTime.now();
                _selectedTime = DateTime(
                  now.year,
                  now.month,
                  now.day,
                  time.hour,
                  time.minute,
                );
              } else {
                _selectedTime = null;
              }
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.border),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _selectedTime != null
                      ? '${_selectedTime!.hour.toString().padLeft(2, '0')}:${_selectedTime!.minute.toString().padLeft(2, '0')}'
                      : '选择时间',
                  style: AppTextStyles.bodyMedium,
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 清除按钮 - 只在有选择时间时显示
                    if (_selectedTime != null)
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedTime = null;
                          });
                        },
                        child: const Padding(
                          padding: EdgeInsets.only(right: 8),
                          child: Icon(Icons.clear, color: AppColors.textHint, size: 18),
                        ),
                      ),
                    const Icon(Icons.access_time, color: AppColors.textHint, size: 20),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 构建重复设置
  Widget _buildRepeatSettings() {
    if (_selectedType != ScheduleType.routine) {
      return const SizedBox.shrink(); // 只有打卡类型需要重复设置
    }

    // 打卡类型默认为重复，不显示开关
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        const Text('重复频率', style: AppTextStyles.labelMedium),
        const SizedBox(height: 8),

        // 重复类型选择 - iOS风格分段控制器
        Container(
          height: AppSizes.buttonHeightMedium, // 使用标准按钮高度
          decoration: BoxDecoration(
            color: Colors.white, // 白色背景
            borderRadius: BorderRadius.circular(AppSizes.radiusMedium), // 中等圆角
            border: Border.all(color: AppColors.border.withAlpha(128), width: 0.5), // 添加边框增强层级感
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(20), // 增强阴影
                blurRadius: 4,
                spreadRadius: 0,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          padding: const EdgeInsets.all(2.0), // 减小内边距，让选项更贴近边缘
          child: Row(
            children: [
              // 每天选项
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _repeatType = RepeatType.daily;
                      _repeatDays = []; // 每天不需要指定具体日期
                      _errorMessage = null; // 清除错误提示
                    });
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: _repeatType == RepeatType.daily
                          ? AppColors.primary.withAlpha(AppColors.alpha30)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                    ),
                    // 移除margin，让高亮覆盖整个区域
                    height: double.infinity, // 确保高度填满父容器
                    child: Center(
                      child: Text(
                        '每天',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: _repeatType == RepeatType.daily
                              ? FontWeight.w600
                              : FontWeight.w500,
                          color: _repeatType == RepeatType.daily
                              ? AppColors.primary
                              : AppColors.text,
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              // 每周选项
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _repeatType = RepeatType.weekly;
                      _repeatDays = [1, 2, 3, 4, 5]; // 默认周一到周五
                      _errorMessage = null; // 清除错误提示
                    });
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: _repeatType == RepeatType.weekly
                          ? AppColors.primary.withAlpha(AppColors.alpha30)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                    ),
                    // 移除margin，让高亮覆盖整个区域
                    height: double.infinity, // 确保高度填满父容器
                    child: Center(
                      child: Text(
                        '每周',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: _repeatType == RepeatType.weekly
                              ? FontWeight.w600
                              : FontWeight.w500,
                          color: _repeatType == RepeatType.weekly
                              ? AppColors.primary
                              : AppColors.text,
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              // 每月选项
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _repeatType = RepeatType.monthly;
                      _repeatDays = [1]; // 默认每月1号
                      _errorMessage = null; // 清除错误提示
                    });
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: _repeatType == RepeatType.monthly
                          ? AppColors.primary.withAlpha(AppColors.alpha30)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                    ),
                    // 移除margin，让高亮覆盖整个区域
                    height: double.infinity, // 确保高度填满父容器
                    child: Center(
                      child: Text(
                        '每月',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: _repeatType == RepeatType.monthly
                              ? FontWeight.w600
                              : FontWeight.w500,
                          color: _repeatType == RepeatType.monthly
                              ? AppColors.primary
                              : AppColors.text,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // 根据重复类型显示不同的选择器
        if (_repeatType == RepeatType.weekly)
          _buildWeekdaySelector(),

        if (_repeatType == RepeatType.monthly)
          _buildMonthDaySelector(),
      ],
    );
  }

  // 构建星期选择器
  Widget _buildWeekdaySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('重复日期', style: AppTextStyles.labelMedium),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: List.generate(7, (index) {
            final weekday = index + 1; // 1-7 代表周一到周日
            final isSelected = _repeatDays.contains(weekday);

            return GestureDetector(
              onTap: () {
                setState(() {
                  if (isSelected) {
                    _repeatDays.remove(weekday);
                  } else {
                    _repeatDays.add(weekday);
                  }
                });
              },
              child: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.primary : Colors.transparent,
                  border: Border.all(
                    color: isSelected ? AppColors.primary : AppColors.border,
                  ),
                  borderRadius: BorderRadius.circular(18),
                ),
                child: Center(
                  child: Text(
                    _weekdays[index],
                    style: AppTextStyles.bodySmall.copyWith(
                      color: isSelected ? Colors.white : AppColors.text,
                    ),
                  ),
                ),
              ),
            );
          }),
        ),
      ],
    );
  }

  // 构建月份日期选择器
  Widget _buildMonthDaySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('每月重复日期', style: AppTextStyles.labelMedium),
        const SizedBox(height: 8),

        // 1-28号的日期选择器
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: List.generate(28, (index) {
            final day = index + 1; // 1-28
            final isSelected = _repeatDays.contains(day);

            return GestureDetector(
              onTap: () {
                setState(() {
                  if (isSelected) {
                    _repeatDays.remove(day);
                  } else {
                    _repeatDays.add(day);
                  }
                });
              },
              child: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.primary : Colors.transparent,
                  border: Border.all(
                    color: isSelected ? AppColors.primary : AppColors.border,
                  ),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Center(
                  child: Text(
                    day.toString(),
                    style: AppTextStyles.bodySmall.copyWith(
                      color: isSelected ? Colors.white : AppColors.text,
                    ),
                  ),
                ),
              ),
            );
          }),
        ),

        const SizedBox(height: 16),

        // 特殊选项标题
        const Text('特殊日期选项', style: AppTextStyles.labelMedium),
        const SizedBox(height: 8),

        // 特殊选项：每月最后一天、倒数第二天、倒数第三天
        Row(
          children: [
            Expanded(child: _buildSpecialDaySelector(-1, '最后一天')),
            const SizedBox(width: 8),
            Expanded(child: _buildSpecialDaySelector(-2, '倒数第二天')),
            const SizedBox(width: 8),
            Expanded(child: _buildSpecialDaySelector(-3, '倒数第三天')),
          ],
        ),
      ],
    );
  }

  // 构建特殊日期选择器（最后一天、倒数第二天、倒数第三天）
  Widget _buildSpecialDaySelector(int value, String label) {
    final isSelected = _repeatDays.contains(value);

    return GestureDetector(
      onTap: () {
        setState(() {
          if (isSelected) {
            _repeatDays.remove(value);
          } else {
            _repeatDays.add(value);
          }
        });
      },
      child: Container(
        height: 40, // 稍微高一点，更容易点击
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.transparent,
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.border,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: AppTextStyles.bodySmall.copyWith(
              color: isSelected ? Colors.white : AppColors.text,
              fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isEditMode = widget.schedule != null;

    return CommonBottomSheet(
      title: isEditMode ? "编辑日程" : "创建日程",
      actions: [
        TextButton(
          onPressed: _createOrUpdateSchedule,
          child: Text(
            isEditMode ? '保存' : '创建',
            style: const TextStyle(color: AppColors.primary),
          ),
        ),
      ],
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 错误提示显示
            if (_errorMessage != null)
              Container(
                width: double.infinity,
                margin: const EdgeInsets.only(bottom: 16),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: Colors.red.shade600,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: TextStyle(
                          color: Colors.red.shade700,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _errorMessage = null;
                        });
                      },
                      child: Icon(
                        Icons.close,
                        color: Colors.red.shade600,
                        size: 18,
                      ),
                    ),
                  ],
                ),
              ),

            // 类型选择器 - 移到最上方
            _buildTypeSelector(),

            const SizedBox(height: 16),

            // 标题输入
            const Text('标题', style: AppTextStyles.labelMedium),
            const SizedBox(height: 8),
            KeyboardAwareTextField(
              controller: _titleController,
              hintText: '请输入标题',
              maxLength: 50, // 添加长度限制
              onTap: () {
                // 用户点击时清除错误提示
                if (_errorMessage != null) {
                  setState(() {
                    _errorMessage = null;
                  });
                }
              },
            ),

            const SizedBox(height: 16),

            // 描述输入
            Row(
              children: [
                const Text('描述', style: AppTextStyles.labelMedium),
                const SizedBox(width: 8),
                Text('(可选)', style: AppTextStyles.caption.copyWith(color: AppColors.textHint)),
              ],
            ),
            const SizedBox(height: 8),
            KeyboardAwareTextField(
              controller: _descriptionController,
              hintText: '请输入描述',
              maxLength: 200, // 添加长度限制
            ),

            const SizedBox(height: 16),

            // 日期选择器
            _buildDateSelector(),

            // 时间选择器
            _buildTimeSelector(),

            // 重复设置
            _buildRepeatSettings(),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  // 显示内联错误提示（解决SnackBar被底部弹窗遮挡的问题）
  void _showInlineError(String message) {
    setState(() {
      _errorMessage = message;
    });

    // 3秒后自动清除错误提示
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _errorMessage = null;
        });
      }
    });
  }


}
