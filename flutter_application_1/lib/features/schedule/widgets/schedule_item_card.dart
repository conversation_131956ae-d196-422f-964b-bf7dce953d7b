import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/models/schedule.dart';

/// 日程项目卡片
/// 用于显示单个日程项目
class ScheduleItemCard extends StatefulWidget {
  final Schedule schedule;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onComplete;
  final DateTime selectedDate;

  const ScheduleItemCard({
    super.key,
    required this.schedule,
    this.onEdit,
    this.onDelete,
    this.onComplete,
    required this.selectedDate,
  });

  @override
  State<ScheduleItemCard> createState() => _ScheduleItemCardState();
}

class _ScheduleItemCardState extends State<ScheduleItemCard> with AutomaticKeepAliveClientMixin {
  // 滑动状态
  bool _isOpen = false;
  final GlobalKey _cardKey = GlobalKey();

  @override
  bool get wantKeepAlive => false; // 不保持状态，确保重建时重置

  @override
  void didUpdateWidget(ScheduleItemCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 如果ID变化，说明是不同的卡片，重置滑动状态
    if (oldWidget.schedule.id != widget.schedule.id) {
      setState(() {
        _isOpen = false;
      });
    }
  }

  // 移除未使用的滑动操作区域宽度常量

  // 获取日程类型颜色
  Color _getTypeColor() {
    switch (widget.schedule.type) {
      case ScheduleType.plan:
        return AppColors.primary;
      case ScheduleType.routine:
        return AppColors.secondary;
      case ScheduleType.todo:
        return AppColors.accent;
    }
  }

  // 移除未使用的状态相关方法

  // 格式化时间
  String _formatTime(DateTime time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  // 切换完成状态
  void _toggleComplete() {
    // 添加调试信息
    final isCompletedOnDate = widget.schedule.isCompletedOnDate(widget.selectedDate);
    debugPrint('点击切换完成状态: ${widget.schedule.title}');
    debugPrint('当前日期: ${widget.selectedDate.toString()}');
    debugPrint('当前完成状态: ${isCompletedOnDate ? '已完成' : '未完成'}');

    if (widget.schedule.isRepeat) {
      debugPrint('重复类型: ${widget.schedule.repeatType.toString()}');
      debugPrint('完成日期列表: ${widget.schedule.completedDates?.map((d) => d.toString()).join(', ') ?? '空'}');
    }

    // 无论当前状态如何，都调用onComplete来切换状态
    if (widget.onComplete != null) {
      widget.onComplete!();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 调用父类的build方法
    // 检查是否在选中日期已完成（对于重复任务）
    final isCompletedOnDate = widget.schedule.isCompletedOnDate(widget.selectedDate);

    // 构建卡片内容
    Widget cardContent = SizedBox(
      height: 64, // 固定高度，比之前更小
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 左侧完成按钮 - 圆形复选框，更明显的样式
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _toggleComplete, // 支持点击切换完成状态
              borderRadius: BorderRadius.circular(15), // 圆形点击区域
              child: Padding(
                padding: const EdgeInsets.all(5.0), // 增加点击区域
                child: Container(
                  width: 22, // 稍微增大尺寸
                  height: 22, // 稍微增大尺寸
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isCompletedOnDate ? _getTypeColor().withAlpha(50) : Colors.transparent,
                    border: Border.all(
                      color: isCompletedOnDate ? _getTypeColor() : _getTypeColor().withAlpha(180),
                      width: 2.0, // 增加边框宽度
                    ),
                  ),
                  child: isCompletedOnDate
                    ? Center(
                        child: Icon(
                          Icons.check,
                          size: 14, // 增大图标尺寸
                          color: _getTypeColor(),
                        ),
                      )
                    : const SizedBox(), // 未完成状态下为空
                ),
              ),
            ),
          ),

          // 移除类型标识小圆点，保留一点间距
          const SizedBox(width: 8),

          // 中间内容区域
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题行
                Row(
                  children: [
                    // 标题
                    Expanded(
                      child: Text(
                        widget.schedule.title,
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w500,
                          decoration: isCompletedOnDate ? TextDecoration.lineThrough : null,
                          decorationColor: AppColors.textTertiary, // 使用灰色作为删除线颜色
                          color: isCompletedOnDate ? AppColors.textTertiary : AppColors.text,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                    // 时间信息（如果有）
                    if (widget.schedule.time != null) ...[
                      const SizedBox(width: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                        decoration: BoxDecoration(
                          color: AppColors.textSecondary.withAlpha(10),
                          borderRadius: BorderRadius.circular(2),
                        ),
                        child: Text(
                          _formatTime(widget.schedule.time!),
                          style: AppTextStyles.caption.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ),
                    ],

                    // 移除重复信息图标
                  ],
                ),

                // 描述（如果有）
                if (widget.schedule.description != null && widget.schedule.description!.isNotEmpty) ...[
                  const SizedBox(height: 2),
                  Text(
                    widget.schedule.description!,
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textTertiary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],

                // 移除状态标签，因为已有复选框
              ],
            ),
          ),
        ],
      ),
    );

    // 使用Stack和GestureDetector实现滑动不回弹
    return Stack(
      key: _cardKey,
      children: [
        // 滑动后显示的背景
        Positioned.fill(
          child: AnimatedOpacity(
            opacity: _isOpen ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 200),
            child: Container(
              alignment: Alignment.centerRight,
              padding: const EdgeInsets.only(right: 16.0),
              decoration: BoxDecoration(
                color: Colors.grey.withAlpha(10), // 浅灰色背景，增加层级感
                border: Border(
                  bottom: BorderSide(
                    color: AppColors.divider,
                    width: 0.5,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // 编辑按钮
                  if (widget.onEdit != null)
                    Container(
                      width: 36, // 减小尺寸
                      height: 36, // 减小尺寸
                      margin: const EdgeInsets.only(right: 8),
                      decoration: BoxDecoration(
                        color: AppColors.secondary.withAlpha(26),
                        borderRadius: BorderRadius.circular(18),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(10),
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.edit_outlined),
                        color: AppColors.secondary,
                        iconSize: 16, // 减小图标尺寸
                        padding: EdgeInsets.zero,
                        onPressed: () {
                          // 先关闭滑动状态，再执行编辑操作
                          setState(() {
                            _isOpen = false;
                          });
                          // 延迟执行编辑操作，确保动画完成
                          Future.delayed(const Duration(milliseconds: 200), () {
                            if (widget.onEdit != null) {
                              widget.onEdit!();
                            }
                          });
                        },
                      ),
                    ),

                  // 删除按钮
                  if (widget.onDelete != null)
                    Container(
                      width: 36, // 减小尺寸
                      height: 36, // 减小尺寸
                      decoration: BoxDecoration(
                        color: AppColors.error.withAlpha(26),
                        borderRadius: BorderRadius.circular(18),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(10),
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.delete_outline),
                        color: AppColors.error,
                        iconSize: 16, // 减小图标尺寸
                        padding: EdgeInsets.zero,
                        onPressed: () {
                          // 先关闭滑动状态，再执行删除操作
                          setState(() {
                            _isOpen = false;
                          });
                          // 延迟执行删除操作，确保动画完成
                          Future.delayed(const Duration(milliseconds: 200), () {
                            if (widget.onDelete != null) {
                              widget.onDelete!();
                            }
                          });
                        },
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),

        // 可滑动的卡片
        GestureDetector(
          onHorizontalDragUpdate: (details) {
            if (details.delta.dx < 0) { // 只处理向左滑动
              setState(() {
                _isOpen = true;
              });
            } else if (details.delta.dx > 0 && _isOpen) { // 向右滑动关闭
              setState(() {
                _isOpen = false;
              });
            }
          },
          onTap: () {
            if (_isOpen) {
              setState(() {
                _isOpen = false;
              });
            }
          },
          child: AnimatedSlide(
            offset: _isOpen ? const Offset(-0.3, 0) : Offset.zero, // 滑动偏移
            duration: const Duration(milliseconds: 200),
            child: Card(
              elevation: 1, // 增加轻微的阴影
              margin: const EdgeInsets.only(bottom: 4.0, left: 2.0, right: 2.0), // 增加卡片间距和左右间距
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppSizes.radiusMedium), // 使用中等圆角
                side: BorderSide(
                  color: AppColors.divider,
                  width: 0.5,
                ),
              ),
              shadowColor: Colors.black.withAlpha(AppColors.alpha20), // 增强阴影效果
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 0.0),
                child: cardContent,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
