import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:table_calendar/table_calendar.dart';
import '../../../shared/theme/constants.dart';
import '../providers/schedule_state.dart';

/// 改进的日历组件
/// 使用 table_calendar 包实现更完善的日历功能
class ImprovedCalendarWidget extends ConsumerStatefulWidget {
  final bool isExpanded;
  final Function(bool) onExpandToggle;
  final Function(DateTime) onDateSelected;

  const ImprovedCalendarWidget({
    super.key,
    required this.isExpanded,
    required this.onExpandToggle,
    required this.onDateSelected,
  });

  @override
  ConsumerState<ImprovedCalendarWidget> createState() => _ImprovedCalendarWidgetState();
}

class _ImprovedCalendarWidgetState extends ConsumerState<ImprovedCalendarWidget> {
  late DateTime _focusedDay;
  late DateTime _selectedDay;
  late CalendarFormat _calendarFormat;
  final kFirstDay = DateTime(2000, 1, 1);
  final kLastDay = DateTime(2050, 12, 31);

  @override
  void initState() {
    super.initState();
    _focusedDay = DateTime.now();
    _selectedDay = DateTime.now();
    _calendarFormat = CalendarFormat.month;
  }

  // 格式化月份标题
  String _formatMonthYear(DateTime date) {
    return '${date.year}年${date.month}月';
  }

  // 构建收起状态下的周视图
  Widget _buildCollapsedWeekView() {
    // 计算当前选中日期所在的周的开始和结束
    final selectedWeekDay = _selectedDay.weekday;
    final startOfWeek = _selectedDay.subtract(Duration(days: selectedWeekDay - 1));

    // 生成当前周的日期列表
    final weekDays = List.generate(7, (index) => startOfWeek.add(Duration(days: index)));
    final weekdayLabels = ['一', '二', '三', '四', '五', '六', '日'];

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 星期标签行
        Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: weekdayLabels.map((label) => Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            )).toList(),
          ),
        ),

        // 日期行
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: weekDays.map((day) {
            final isSelected = isSameDay(day, _selectedDay);
            final isToday = isSameDay(day, DateTime.now());
            final isCurrentMonth = day.month == _focusedDay.month;

            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedDay = day;
                  _focusedDay = day;
                });
                widget.onDateSelected(day);
              },
              child: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.primary : Colors.transparent,
                  borderRadius: BorderRadius.circular(18),
                  border: isToday && !isSelected ? Border.all(color: AppColors.primary, width: 1) : null,
                ),
                child: Center(
                  child: Text(
                    day.day.toString(),
                    style: TextStyle(
                      color: isSelected ? Colors.white :
                             isToday ? AppColors.primary :
                             isCurrentMonth ? AppColors.text : AppColors.textSecondary,
                      fontWeight: (isSelected || isToday) ? FontWeight.bold : FontWeight.normal,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    // 获取当前选中的日期
    final selectedDate = ref.watch(scheduleStateProvider).selectedDate;
    if (selectedDate != null) {
      _selectedDay = selectedDate;
      _focusedDay = selectedDate;
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppShadows.low],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 月份标题、切换按钮和展开/收起按钮
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // 左侧切换按钮和月份标题
                Row(
                  children: [
                    // 上一月按钮
                    IconButton(
                      icon: const Icon(Icons.chevron_left, size: 20),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      onPressed: () {
                        // 计算上一个月的日期
                        final previousMonth = DateTime(_focusedDay.year, _focusedDay.month - 1, 1);

                        // 更新焦点日期和选中日期
                        setState(() {
                          _focusedDay = previousMonth;

                          // 确保选中日期在新的月份内
                          final lastDayOfMonth = DateTime(previousMonth.year, previousMonth.month + 1, 0).day;
                          int day = _selectedDay.day;
                          if (day > lastDayOfMonth) {
                            day = lastDayOfMonth;
                          }
                          _selectedDay = DateTime(previousMonth.year, previousMonth.month, day);
                        });

                        // 通知父组件日期已更改
                        widget.onDateSelected(_selectedDay);

                        // 调试信息
                        debugPrint('切换到上一月: ${previousMonth.year}-${previousMonth.month}');
                      },
                    ),

                    const SizedBox(width: 8),

                    // 月份标题
                    Text(
                      _formatMonthYear(_focusedDay),
                      style: AppTextStyles.headline3,
                    ),

                    const SizedBox(width: 8),

                    // 下一月按钮
                    IconButton(
                      icon: const Icon(Icons.chevron_right, size: 20),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      onPressed: () {
                        // 计算下一个月的日期
                        final nextMonth = DateTime(_focusedDay.year, _focusedDay.month + 1, 1);

                        // 更新焦点日期和选中日期
                        setState(() {
                          _focusedDay = nextMonth;

                          // 确保选中日期在新的月份内
                          final lastDayOfMonth = DateTime(nextMonth.year, nextMonth.month + 1, 0).day;
                          int day = _selectedDay.day;
                          if (day > lastDayOfMonth) {
                            day = lastDayOfMonth;
                          }
                          _selectedDay = DateTime(nextMonth.year, nextMonth.month, day);
                        });

                        // 通知父组件日期已更改
                        widget.onDateSelected(_selectedDay);

                        // 调试信息
                        debugPrint('切换到下一月: ${nextMonth.year}-${nextMonth.month}');
                      },
                    ),
                  ],
                ),

                // 展开/收起按钮
                IconButton(
                  icon: Icon(
                    widget.isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    color: AppColors.textSecondary,
                  ),
                  onPressed: () => widget.onExpandToggle(!widget.isExpanded),
                ),
              ],
            ),
          ),

          // 日历部分
          widget.isExpanded
              ? SizedBox(
                  height: 340, // 增加高度以适应有六行的月份
                  child: TableCalendar(
                    firstDay: kFirstDay,
                    lastDay: kLastDay,
                    focusedDay: _focusedDay,
                    calendarFormat: _calendarFormat,
                    headerVisible: false, // 隐藏默认的头部，因为我们有自己的头部
                    selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
                    startingDayOfWeek: StartingDayOfWeek.monday,
                    calendarStyle: CalendarStyle(
                      // 选中日期的样式
                      selectedDecoration: BoxDecoration(
                        color: AppColors.primary,
                        shape: BoxShape.circle,
                      ),
                      selectedTextStyle: const TextStyle(color: Colors.white),
                      // 今天的样式
                      todayDecoration: BoxDecoration(
                        color: Colors.transparent,
                        shape: BoxShape.circle,
                        border: Border.all(color: AppColors.primary, width: 1),
                      ),
                      todayTextStyle: TextStyle(color: AppColors.primary),
                      // 周末的样式
                      weekendTextStyle: TextStyle(color: AppColors.text),
                      // 不在当前月的日期样式
                      outsideTextStyle: TextStyle(color: AppColors.textSecondary.withAlpha(128)),
                      // 标记样式
                      markersMaxCount: 3,
                      markersAnchor: 1.0,
                      // 调整日期单元格的大小
                      cellMargin: const EdgeInsets.all(3),
                      cellPadding: const EdgeInsets.all(0),
                      // 设置日期单元格的最大宽度和高度
                      cellAlignment: Alignment.center,
                      // 确保显示所有日期
                      isTodayHighlighted: true,
                      outsideDaysVisible: true,
                    ),
                    daysOfWeekStyle: DaysOfWeekStyle(
                      weekdayStyle: AppTextStyles.labelMedium,
                      weekendStyle: AppTextStyles.labelMedium,
                      // 自定义星期标签，使用中文
                      dowTextFormatter: (date, locale) {
                        final weekdayLabels = ['日', '一', '二', '三', '四', '五', '六'];
                        return weekdayLabels[date.weekday % 7];
                      },
                    ),
                    onDaySelected: (selectedDay, focusedDay) {
                      setState(() {
                        _selectedDay = selectedDay;
                        _focusedDay = focusedDay;
                      });
                      widget.onDateSelected(selectedDay);
                    },
                    onPageChanged: (focusedDay) {
                      // 更新焦点日期
                      setState(() {
                        _focusedDay = focusedDay;

                        // 确保选中日期在新的月份内
                        if (_selectedDay.month != focusedDay.month || _selectedDay.year != focusedDay.year) {
                          final lastDayOfMonth = DateTime(focusedDay.year, focusedDay.month + 1, 0).day;
                          int day = _selectedDay.day;
                          if (day > lastDayOfMonth) {
                            day = lastDayOfMonth;
                          }
                          _selectedDay = DateTime(focusedDay.year, focusedDay.month, day);
                        }
                      });

                      // 通知父组件日期已更改
                      widget.onDateSelected(_selectedDay);

                      // 调试信息
                      debugPrint('页面滑动切换到: ${focusedDay.year}-${focusedDay.month}，选中日期: ${_selectedDay.year}-${_selectedDay.month}-${_selectedDay.day}');
                    },
                  ),
                )
              : SizedBox(
                  height: 80,
                  child: _buildCollapsedWeekView(),
                ),
        ],
      ),
    );
  }
}
