// 该文件定义了日程相关的状态管理Provider

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/schedule.dart';

// 日程状态数据类
class ScheduleState {
  final List<Schedule> schedules;
  final DateTime? selectedDate;
  final bool isLoading;
  final String? error;

  const ScheduleState({
    this.schedules = const [],
    this.selectedDate,
    this.isLoading = false,
    this.error,
  });

  // 创建副本
  ScheduleState copyWith({
    List<Schedule>? schedules,
    DateTime? selectedDate,
    bool? isLoading,
    String? error,
  }) {
    return ScheduleState(
      schedules: schedules ?? this.schedules,
      selectedDate: selectedDate ?? this.selectedDate,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

// 日程状态Notifier
class ScheduleStateNotifier extends StateNotifier<ScheduleState> {
  ScheduleStateNotifier() : super(const ScheduleState());

  // 设置所有日程
  void setSchedules(List<Schedule> schedules) {
    state = state.copyWith(schedules: schedules);
  }

  // 添加日程
  void addSchedule(Schedule schedule) {
    state = state.copyWith(schedules: [...state.schedules, schedule]);
  }

  // 更新日程
  void updateSchedule(Schedule schedule) {
    final index = state.schedules.indexWhere((s) => s.id == schedule.id);
    if (index >= 0) {
      final updatedSchedules = [...state.schedules];
      updatedSchedules[index] = schedule;
      state = state.copyWith(schedules: updatedSchedules);
    }
  }

  // 删除日程
  void deleteSchedule(String id) {
    state = state.copyWith(
      schedules: state.schedules.where((s) => s.id != id).toList(),
    );
  }

  // 设置选中日期
  void setSelectedDate(DateTime date) {
    state = state.copyWith(selectedDate: date);
  }

  // 设置加载状态
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  // 设置错误信息
  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  // 获取指定日期的日程
  List<Schedule> getSchedulesByDate(DateTime date) {
    return state.schedules.where((schedule) => schedule.isOnDate(date)).toList();
  }

  // 获取指定类型的日程
  List<Schedule> getSchedulesByType(ScheduleType type) {
    return state.schedules.where((schedule) => schedule.type == type).toList();
  }

  // 获取指定日期和类型的日程
  List<Schedule> getSchedulesByDateAndType(DateTime date, ScheduleType type) {
    return state.schedules.where((schedule) =>
      schedule.isOnDate(date) && schedule.type == type
    ).toList();
  }
}

// 日程状态Provider
final scheduleStateProvider = StateNotifierProvider<ScheduleStateNotifier, ScheduleState>((ref) {
  return ScheduleStateNotifier();
});
