import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import '../../../shared/widgets/app_card.dart';
import '../widgets/feature_card_grid.dart';
import '../../audio/screens/audio_select.dart';
import '../../exam/screens/exam_home_screen.dart';

/// 自定义卡片区域组件
/// 显示底部功能卡片区域
class CustomCardsArea extends StatelessWidget {
  const CustomCardsArea({super.key});

  @override
  Widget build(BuildContext context) {
    // 创建卡片数据
    final List<FeatureCardData> cards = [
      // 音频卡片
      FeatureCardData(
        title: '场景音频',
        description: '选择不同的场景音频提升专注力',
        icon: Icons.audiotrack,
        color: AppColors.secondary,
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AudioSelectScreen(),
            ),
          );
        },
      ),
      // 模拟考试卡片
      FeatureCardData(
        title: '模拟考试',
        description: '使用模拟考试功能进行练习和测试',
        icon: Icons.question_answer,
        color: Colors.blue,
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const ExamHomeScreen(),
            ),
          );
        },
      ),
      // 资源管理卡片
      FeatureCardData(
        title: '资源管理',
        description: '统一管理学习资源',
        icon: Icons.folder,
        color: Colors.orange,
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('资源管理功能开发中')),
          );
        },
      ),
      // 专注技巧卡片
      FeatureCardData(
        title: '专注技巧',
        description: '提升专注力的实用技巧和方法',
        icon: Icons.lightbulb,
        color: Colors.amber,
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('专注技巧功能开发中')),
          );
        },
      ),
      // 专注日志卡片
      FeatureCardData(
        title: '专注日志',
        description: '记录您的专注历程和心得体会',
        icon: Icons.book,
        color: Colors.green,
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('专注日志功能开发中')),
          );
        },
      ),
      // 专注模式卡片
      FeatureCardData(
        title: '专注模式',
        description: '自定义不同的专注模式和场景',
        icon: Icons.settings,
        color: Colors.purple,
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('专注模式功能开发中')),
          );
        },
      ),
    ];

    // 计算底部卡片区域的高度
    // 获取屏幕高度
    final screenHeight = MediaQuery.of(context).size.height;
    // 计算底部卡片区域的最小高度，至少为屏幕高度的一半
    final minHeight = screenHeight * 0.68;

    return AppCard(
      padding: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              left: AppSizes.paddingMedium,
              right: AppSizes.paddingMedium,
              top: AppSizes.paddingMedium
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  "其他功能",
                  style: AppTextStyles.headline3,
                ),
                // 添加一个小提示，表示可以滚动
                Row(
                  children: const [
                    Icon(
                      Icons.swipe,
                      size: AppSizes.iconSmall,
                      color: AppColors.textTertiary,
                    ),
                    SizedBox(width: AppSizes.paddingSmall / 2),
                    Text(
                      "滚动查看更多",
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textTertiary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: AppSizes.paddingMedium),
          // 使用滚动容器包裹卡片网格
          SizedBox(
            // 设置固定高度，确保内部可滚动
            height: minHeight - 70, // 减去标题和边距的高度
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium),
              child: FeatureCardGrid(
                cards: cards,
                crossAxisCount: 2,
                horizontalSpacing: AppSizes.paddingMedium,
                verticalSpacing: AppSizes.paddingMedium,
              ),
            ),
          ),
          // 底部留出一些空间
          const SizedBox(height: AppSizes.paddingMedium),
        ],
      ),
    );
  }
}
