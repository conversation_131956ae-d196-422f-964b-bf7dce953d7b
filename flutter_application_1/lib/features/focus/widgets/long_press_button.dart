import 'package:flutter/material.dart';

/// 长按按钮组件
/// 用于需要长按确认的操作，防止误触
class LongPressButton extends StatefulWidget {
  final VoidCallback onLongPressComplete; // 长按完成后的回调
  final String label; // 按钮文本
  final IconData icon; // 按钮图标
  final Color backgroundColor; // 按钮背景色
  final Duration longPressDuration; // 长按所需时间

  const LongPressButton({
    super.key,
    required this.onLongPressComplete,
    required this.label,
    required this.icon,
    required this.backgroundColor,
    this.longPressDuration = const Duration(seconds: 2), // 默认长按2秒
  });

  @override
  State<LongPressButton> createState() => _LongPressButtonState();
}

class _LongPressButtonState extends State<LongPressButton> with SingleTickerProviderStateMixin {
  // 动画控制器
  late AnimationController _animationController;
  
  // 是否正在长按
  bool _isLongPressing = false;

  @override
  void initState() {
    super.initState();
    
    // 初始化动画控制器
    _animationController = AnimationController(
      vsync: this,
      duration: widget.longPressDuration,
    );
    
    // 添加动画状态监听
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // 动画完成，触发回调
        widget.onLongPressComplete();
        
        // 重置状态
        setState(() {
          _isLongPressing = false;
        });
        _animationController.reset();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onLongPressStart: (_) {
        setState(() {
          _isLongPressing = true;
        });
        _animationController.forward();
      },
      onLongPressEnd: (_) {
        setState(() {
          _isLongPressing = false;
        });
        _animationController.reset();
      },
      onLongPressCancel: () {
        setState(() {
          _isLongPressing = false;
        });
        _animationController.reset();
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 长按进度条 - 仅在长按时显示
          if (_isLongPressing)
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: SizedBox(
                width: 120,
                height: 4,
                child: AnimatedBuilder(
                  animation: _animationController,
                  builder: (context, child) {
                    return LinearProgressIndicator(
                      value: _animationController.value,
                      backgroundColor: Colors.grey.withOpacity(0.3),
                      valueColor: AlwaysStoppedAnimation<Color>(widget.backgroundColor),
                      borderRadius: BorderRadius.circular(2),
                    );
                  },
                ),
              ),
            ),
          
          // 按钮
          ElevatedButton.icon(
            onPressed: () {
              // 点击时显示提示
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('长按以结束专注'),
                  duration: Duration(seconds: 1),
                ),
              );
            },
            icon: Icon(widget.icon),
            label: Text(widget.label),
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.backgroundColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
