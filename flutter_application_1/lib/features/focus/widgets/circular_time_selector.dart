import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../shared/theme/constants.dart';

/// 圆形时间选择器
/// 用于在专注启动页面选择倒计时时间
class CircularTimeSelector extends StatefulWidget {
  /// 当前选择的时间（分钟）
  final double value;

  /// 最小时间（分钟）
  final double min;

  /// 最大时间（分钟）
  final double max;

  /// 时间变化回调
  final ValueChanged<double> onChanged;

  /// 是否启用交互
  final bool enabled;

  /// 圆环大小
  final double size;

  /// 圆环宽度
  final double strokeWidth;

  /// 圆环颜色
  final Color ringColor;

  /// 填充颜色
  final Color fillColor;

  /// 背景颜色
  final Color backgroundColor;

  /// 文本样式
  final TextStyle? textStyle;

  /// 刻度数量
  final int tickCount;

  /// 主刻度间隔
  final int majorTickInterval;

  /// 圆弧起始角度（度）- 默认为135度（左下方）
  final double startAngle;

  /// 圆弧扫过的角度（度）- 默认为270度（不完整的圆弧）
  final double sweepAngle;

  const CircularTimeSelector({
    super.key,
    required this.value,
    required this.onChanged,
    this.min = 5,
    this.max = 120,
    this.enabled = true,
    this.size = 250,
    this.strokeWidth = 15,
    this.ringColor = Colors.white,
    this.fillColor = const Color(0x4D4CAF50), // AppColors.primary with 30% opacity
    this.backgroundColor = const Color(0x1AFFFFFF), // Colors.white with 10% opacity
    this.textStyle,
    this.tickCount = 24,
    this.majorTickInterval = 4,
    this.startAngle = 135.0,
    this.sweepAngle = 270.0,
  });

  @override
  State<CircularTimeSelector> createState() => _CircularTimeSelectorState();
}

class _CircularTimeSelectorState extends State<CircularTimeSelector> with TickerProviderStateMixin {
  // 是否正在拖动
  bool _isDragging = false;

  // 上一次触摸的角度（弧度）
  double _lastAngle = 0.0;

  // 动画控制器
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  // 滑块颜色动画控制器
  late AnimationController _colorAnimationController;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化缩放动画控制器
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    // 初始化颜色动画控制器
    _colorAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _colorAnimation = ColorTween(
      begin: AppColors.primary,
      end: AppColors.primary.withAlpha(204), // 0.8 * 255 = 204，稍微淡一点的颜色
    ).animate(
      CurvedAnimation(
        parent: _colorAnimationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _colorAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 计算当前角度（0-360度）
    final angle = (widget.value - widget.min) / (widget.max - widget.min) * 360.0;

    // 计算进度（0-1）
    final progress = (widget.value - widget.min) / (widget.max - widget.min);

    // 默认文本样式
    final defaultTextStyle = TextStyle(
      fontSize: widget.size * 0.16,
      fontWeight: FontWeight.bold,
      color: AppColors.text,
    );

    return GestureDetector(
      onPanStart: widget.enabled ? _onPanStart : null,
      onPanUpdate: widget.enabled ? _onPanUpdate : null,
      onPanEnd: widget.enabled ? _onPanEnd : null,
      // 使用 deferToChild 确保手势检测更精确
      behavior: HitTestBehavior.deferToChild,
      child: Container(
        width: widget.size,
        height: widget.size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: widget.backgroundColor,
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            // 刻度
            CustomPaint(
              size: Size(widget.size, widget.size),
              painter: TickPainter(
                tickCount: widget.tickCount,
                majorTickInterval: widget.majorTickInterval,
                ringColor: widget.ringColor,
                size: widget.size,
                strokeWidth: widget.strokeWidth,
              ),
            ),

            // 背景圆环 - 添加可点击区域
            Stack(
              alignment: Alignment.center,
              children: [
                // 实际的背景圆环
                Container(
                  width: widget.size,
                  height: widget.size,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: widget.ringColor.withAlpha(100),
                      width: widget.strokeWidth,
                    ),
                  ),
                ),
                // 透明的可点击区域，稍微大一点以增加点击容差
                if (widget.enabled)
                  Positioned.fill(
                    child: CustomPaint(
                      painter: RingHitAreaPainter(
                        ringRadius: widget.size / 2 - widget.strokeWidth / 2,
                        hitAreaWidth: widget.strokeWidth * 2, // 点击区域宽度是圆环宽度的2倍
                      ),
                    ),
                  ),
              ],
            ),

            // 进度圆环
            CustomPaint(
              size: Size(widget.size, widget.size),
              painter: CircularProgressPainter(
                progress: progress,
                color: widget.fillColor,
                strokeWidth: widget.strokeWidth,
              ),
            ),

            // 拖动手柄
            if (widget.enabled)
              AnimatedBuilder(
                animation: _scaleAnimation,
                builder: (context, child) {
                  return Transform.rotate(
                    angle: angle * pi / 180,
                    child: Transform.translate(
                      // 精确计算手柄位置，确保它位于圆环中心
                      offset: Offset(0, -widget.size / 2 + widget.strokeWidth / 2),
                      child: Container(
                        // 调整手柄大小，与圆环保持一致
                        width: widget.strokeWidth * 1.2 * _scaleAnimation.value,
                        height: widget.strokeWidth * 1.2 * _scaleAnimation.value,
                        decoration: BoxDecoration(
                          color: _isDragging
                              ? _colorAnimation.value ?? AppColors.primary
                              : AppColors.primary,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white,
                            width: 2,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(50),
                              blurRadius: _isDragging ? 6 : 4, // 拖动时增加阴影
                              offset: const Offset(0, 2),
                              spreadRadius: _isDragging ? 1 : 0, // 拖动时增加阴影扩散
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),

            // 时间文本 - 使用纯数字与分号格式
            Text(
              // 将分钟数格式化为 "MM:00"
              "${widget.value.toInt().toString().padLeft(2, '0')}:00",
              style: widget.textStyle ?? defaultTextStyle,
            ),
          ],
        ),
      ),
    );
  }

  // 开始拖动
  void _onPanStart(DragStartDetails details) {
    final center = Offset(widget.size / 2, widget.size / 2);
    final touchPosition = details.localPosition;

    // 计算当前滑块的位置
    // 首先计算当前值对应的角度（0-360度）
    final angle = (widget.value - widget.min) / (widget.max - widget.min) * 360.0;

    // 计算滑块在圆环上的位置
    final ringRadius = widget.size / 2 - widget.strokeWidth / 2;
    final knobPosition = _calculatePointOnRing(center, ringRadius, angle);

    // 计算触摸点到滑块的距离
    final distanceToKnob = (touchPosition - knobPosition).distance;

    // 只有当触摸点在滑块附近时才允许开始拖动
    // 计算允许的误差范围，考虑到人手指的大小和精确度
    final knobSize = widget.strokeWidth * 1.2; // 滑块大小，与显示的大小一致

    // 根据设备尺寸动态调整触摸区域
    // 在小屏幕上提供更大的触摸区域
    final screenWidth = MediaQuery.of(context).size.width;

    // 根据屏幕大小和像素密度调整触摸区域
    // 屏幕越小，触摸区域越大
    final sizeFactor = (400 / screenWidth).clamp(1.0, 1.5);
    final allowedError = knobSize * 1.5 * sizeFactor;

    if (distanceToKnob > allowedError) {
      // 如果触摸点不在滑块附近，不开始拖动
      return;
    }

    // 开始拖动时播放放大动画和颜色动画
    _animationController.forward();
    _colorAnimationController.forward();

    // 触觉反馈
    HapticFeedback.lightImpact();

    setState(() {
      _isDragging = true;
    });

    // 注意：这里不再立即更新滑块位置
    // 只有在拖动过程中才会更新滑块位置
  }

  // 滑动方向：1表示顺时针（值增加），-1表示逆时针（值减少）
  int _dragDirection = 0;

  // 根据触摸位置更新值
  void _updateValueFromPosition(Offset touchPosition) {
    final center = Offset(widget.size / 2, widget.size / 2);

    // 计算触摸点相对于圆心的角度
    final currentAngle = _calculateAngle(center, touchPosition);

    // 检测是否是第一次更新
    if (_isDragging && _lastAngle == 0.0) {
      _lastAngle = currentAngle;

      // 初始化滑动方向
      _dragDirection = 0;
    }

    // 计算角度差，确定滑动方向
    double angleDiff = (currentAngle - _lastAngle + 360) % 360;
    if (angleDiff > 180) {
      angleDiff = angleDiff - 360;
    }

    // 更新滑动方向
    if (angleDiff > 0) {
      // 顺时针滑动（值增加）
      _dragDirection = 1;
    } else if (angleDiff < 0) {
      // 逆时针滑动（值减少）
      _dragDirection = -1;
    }

    // 检测是否越过了0度/360度的边界
    const double topPointThreshold = 5.0; // 接近顶点的阈值（度）
    bool crossedTopPoint = false;

    // 如果上一次角度接近360度，当前角度接近0度，说明从右向左越过了顶点
    if (_lastAngle > (360 - topPointThreshold) && currentAngle < topPointThreshold) {
      crossedTopPoint = true;
    }

    // 如果上一次角度接近0度，当前角度接近360度，说明从左向右越过了顶点
    if (_lastAngle < topPointThreshold && currentAngle > (360 - topPointThreshold)) {
      crossedTopPoint = true;
    }

    // 处理边界情况
    if (crossedTopPoint) {
      // 如果当前值接近最小值且向左滑动，固定在最小值
      if (widget.value <= widget.min + 10 && _dragDirection < 0) {
        widget.onChanged(widget.min);
        _lastAngle = currentAngle;
        return;
      }

      // 如果当前值接近最大值且向右滑动，固定在最大值
      if (widget.value >= widget.max - 10 && _dragDirection > 0) {
        widget.onChanged(widget.max);
        _lastAngle = currentAngle;
        return;
      }
    }

    // 处理非越过边界的情况
    // 如果当前值已经是最小值且继续向左滑动，保持在最小值
    if (widget.value <= widget.min && _dragDirection < 0) {
      widget.onChanged(widget.min);
      _lastAngle = currentAngle;
      return;
    }

    // 如果当前值已经是最大值且继续向右滑动，保持在最大值
    if (widget.value >= widget.max && _dragDirection > 0) {
      widget.onChanged(widget.max);
      _lastAngle = currentAngle;
      return;
    }

    // 正常情况下，根据角度计算新值
    const angleRange = 360.0;
    final valueRange = widget.max - widget.min;

    // 计算新值
    final percentage = currentAngle / angleRange;
    final newValue = widget.min + percentage * valueRange;

    // 限制在范围内
    final clampedValue = newValue.clamp(widget.min, widget.max);

    // 四舍五入到5的倍数
    final roundedValue = (clampedValue / 5).round() * 5.0;

    // 只有当值发生变化时才触发回调
    if (roundedValue != widget.value) {
      widget.onChanged(roundedValue);
    }

    // 更新上一次的角度
    _lastAngle = currentAngle;
  }

  // 上次更新的时间戳，用于限制更新频率
  int _lastUpdateTimestamp = 0;

  // 拖动更新
  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isDragging) return;

    // 限制更新频率，避免过于频繁的更新导致性能问题
    // 每16毫秒（约60fps）更新一次
    final now = DateTime.now().millisecondsSinceEpoch;
    if (now - _lastUpdateTimestamp < 16) {
      return;
    }
    _lastUpdateTimestamp = now;

    final center = Offset(widget.size / 2, widget.size / 2);
    final touchPosition = details.localPosition;

    // 计算触摸点到圆心的距离
    final distance = (touchPosition - center).distance;

    // 计算圆环的半径
    final ringRadius = widget.size / 2 - widget.strokeWidth / 2;

    // 计算触摸点到圆环的距离
    final distanceToRing = (distance - ringRadius).abs();

    // 如果触摸点距离圆环太远，我们忽略这次更新
    // 但允许一定的容差，使操作更流畅
    // 容差范围比开始拖动时稍大，以便用户在拖动过程中有更大的操作空间
    final allowedError = widget.strokeWidth + 20;

    if (distanceToRing > allowedError) {
      // 如果触摸点不在圆环附近，不更新
      return;
    }

    // 更新值
    final oldValue = widget.value;
    _updateValueFromPosition(touchPosition);

    // 如果值发生变化，提供触觉反馈
    if (oldValue != widget.value) {
      HapticFeedback.selectionClick();
    }
  }

  // 结束拖动
  void _onPanEnd(DragEndDetails details) {
    // 结束拖动时播放缩小动画和颜色恢复动画
    _animationController.reverse();
    _colorAnimationController.reverse();

    // 触觉反馈
    HapticFeedback.lightImpact();

    setState(() {
      _isDragging = false;
      _lastAngle = 0.0; // 重置上一次的角度
      _dragDirection = 0; // 重置滑动方向
    });
  }

  // 计算角度（0-360度）
  double _calculateAngle(Offset center, Offset position) {
    final dx = position.dx - center.dx;
    final dy = position.dy - center.dy;

    // 计算弧度（-π到π）
    final radians = atan2(dy, dx);

    // 转换为角度（0-360），从顶部开始（12点钟方向）
    var degrees = radians * 180 / pi + 90;
    if (degrees < 0) {
      degrees += 360;
    }

    // 确保角度在0-360范围内
    degrees = degrees % 360;

    return degrees;
  }

  // 根据角度计算圆环上的点
  Offset _calculatePointOnRing(Offset center, double radius, double angleInDegrees) {
    // 将角度转换为弧度，并调整起始点为顶部（12点钟方向）
    final angleInRadians = (angleInDegrees - 90) * pi / 180;

    // 计算点的坐标
    final x = center.dx + radius * cos(angleInRadians);
    final y = center.dy + radius * sin(angleInRadians);

    return Offset(x, y);
  }
}

/// 刻度绘制器
class TickPainter extends CustomPainter {
  final int tickCount;
  final int majorTickInterval;
  final Color ringColor;
  final double size;
  final double strokeWidth;

  TickPainter({
    required this.tickCount,
    required this.majorTickInterval,
    required this.ringColor,
    required this.size,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // 计算刻度长度
    final majorTickLength = strokeWidth * 0.6;
    final minorTickLength = strokeWidth * 0.3;

    // 绘制刻度
    for (int i = 0; i < tickCount; i++) {
      final isMajorTick = i % majorTickInterval == 0;
      final tickLength = isMajorTick ? majorTickLength : minorTickLength;

      // 计算刻度角度
      final angle = (i / tickCount) * 2 * pi;

      // 计算刻度起点和终点
      final outerX = center.dx + (radius + strokeWidth / 2) * cos(angle);
      final outerY = center.dy + (radius + strokeWidth / 2) * sin(angle);
      final innerX = center.dx + (radius - tickLength) * cos(angle);
      final innerY = center.dy + (radius - tickLength) * sin(angle);

      // 绘制刻度线
      final paint = Paint()
        ..color = isMajorTick ? ringColor : ringColor.withAlpha(150)
        ..strokeWidth = isMajorTick ? 2.0 : 1.0
        ..strokeCap = StrokeCap.round;

      canvas.drawLine(
        Offset(outerX, outerY),
        Offset(innerX, innerY),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(TickPainter oldDelegate) {
    return oldDelegate.tickCount != tickCount ||
           oldDelegate.majorTickInterval != majorTickInterval ||
           oldDelegate.ringColor != ringColor ||
           oldDelegate.size != size ||
           oldDelegate.strokeWidth != strokeWidth;
  }
}

/// 圆环点击区域绘制器
/// 用于创建一个透明的可点击区域，使圆环更容易被点击
class RingHitAreaPainter extends CustomPainter {
  final double ringRadius;
  final double hitAreaWidth;

  RingHitAreaPainter({
    required this.ringRadius,
    required this.hitAreaWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 这是一个透明的绘制器，不需要实际绘制任何内容
    // 它只是为了创建一个可点击的区域
    // 在调试模式下，我们可以绘制一个半透明的区域来查看点击区域
    // final center = Offset(size.width / 2, size.height / 2);
    // final paint = Paint()
    //   ..color = Colors.red.withOpacity(0.1)
    //   ..style = PaintingStyle.stroke
    //   ..strokeWidth = hitAreaWidth;
    // canvas.drawCircle(center, ringRadius, paint);
  }

  @override
  bool shouldRepaint(RingHitAreaPainter oldDelegate) {
    return oldDelegate.ringRadius != ringRadius ||
           oldDelegate.hitAreaWidth != hitAreaWidth;
  }
}

/// 圆形进度绘制器
class CircularProgressPainter extends CustomPainter {
  final double progress;
  final Color color;
  final double strokeWidth;

  CircularProgressPainter({
    required this.progress,
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    // 绘制进度弧
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -pi / 2, // 从顶部开始
      progress * 2 * pi, // 进度对应的弧度
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(CircularProgressPainter oldDelegate) {
    return oldDelegate.progress != progress ||
           oldDelegate.color != color ||
           oldDelegate.strokeWidth != strokeWidth;
  }
}
