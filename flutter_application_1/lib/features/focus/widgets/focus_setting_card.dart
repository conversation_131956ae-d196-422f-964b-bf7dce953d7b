import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../shared/widgets/app_card.dart';
import '/shared/widgets/common_bottom_sheet.dart';
import '../../task/providers/subject_state.dart';
import '../screens/focus_screen.dart';

/// 专注设置卡片组件
/// 包含计时模式选择、时间设置和科目选择
class FocusSettingCard extends ConsumerStatefulWidget {
  const FocusSettingCard({super.key});

  @override
  ConsumerState<FocusSettingCard> createState() => _FocusSettingCardState();
}

class _FocusSettingCardState extends ConsumerState<FocusSettingCard> {
  // 是否为倒计时模式（true为倒计时，false为正计时）
  bool _isCountdown = true;

  // 倒计时时间（分钟）
  double _countdownMinutes = 100;

  @override
  Widget build(BuildContext context) {
    // 获取当前选中的科目和项目
    final subjectState = ref.watch(subjectStateProvider);
    final currentSubject = subjectState.currentSubject;
    final currentProject = subjectState.currentProject;

    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 计时模式切换
          LayoutBuilder(
            builder: (context, constraints) {
              // 计算容器宽度 - 使用父容器宽度的80%，但不超过300
              final containerWidth = constraints.maxWidth * 0.6 > 300
                  ? 300.0
                  : constraints.maxWidth * 0.6;
              // 计算每个选项的宽度
              final segmentWidth = containerWidth / 2;

              return Center(
                child: Container(
                  width: containerWidth,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.grey.withAlpha(25),
                    borderRadius: BorderRadius.circular(22),
                  ),
                  child: Stack(
                    children: [
                      // 滑动块
                      AnimatedPositioned(
                        duration: const Duration(milliseconds: 250),
                        curve: Curves.easeInOut,
                        left: _isCountdown ? segmentWidth : 0,
                        child: Container(
                          margin: const EdgeInsets.all(3),
                          width: segmentWidth - 6, // 减去边距
                          height: 34,
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(19),
                            boxShadow: [AppShadows.low],
                          ),
                        ),
                      ),
                      // 分段文本
                      Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  _isCountdown = false;
                                });
                              },
                              child: Center(
                                child: Text(
                                  "正计时",
                                  style: TextStyle(
                                    color: !_isCountdown ? Colors.white : AppColors.textAssist,
                                    fontWeight: !_isCountdown ? FontWeight.bold : FontWeight.normal,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  _isCountdown = true;
                                });
                              },
                              child: Center(
                                child: Text(
                                  "倒计时",
                                  style: TextStyle(
                                    color: _isCountdown ? Colors.white : AppColors.textAssist,
                                    fontWeight: _isCountdown ? FontWeight.bold : FontWeight.normal,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          ),

          // 倒计时模式下显示时间滑块
          if (_isCountdown) ...[
            const SizedBox(height: AppSizes.paddingMedium),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  "专注时长",
                  style: AppTextStyles.bodyLarge,
                ),
                Text(
                  "${_countdownMinutes.toInt()}分钟",
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSizes.paddingSmall),
            Slider(
              value: _countdownMinutes,
              min: 5,
              max: 120,
              divisions: 23,
              label: "${_countdownMinutes.toInt()}分钟",
              onChanged: (value) {
                setState(() {
                  _countdownMinutes = value;
                });
              },
              activeColor: AppColors.primary,
              inactiveColor: AppColors.primary.withAlpha(50),
            ),
          ],

          const SizedBox(height: AppSizes.paddingMedium),

          // 科目和项目选择
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      "科目",
                      style: AppTextStyles.bodyLarge,
                    ),
                    const SizedBox(height: AppSizes.paddingSmall),
                    InkWell(
                      onTap: _showSubjectSelector,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.border),
                          borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                currentSubject?.name ?? "选择科目",
                                style: currentSubject == null
                                    ? AppTextStyles.hint
                                    : AppTextStyles.bodyMedium,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(
                              Icons.arrow_drop_down,
                              color: AppColors.textTertiary,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: AppSizes.paddingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      "项目",
                      style: AppTextStyles.bodyLarge,
                    ),
                    const SizedBox(height: AppSizes.paddingSmall),
                    InkWell(
                      onTap: _showProjectSelector,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.border),
                          borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                currentProject?.name ?? "选择项目",
                                style: currentProject == null
                                    ? AppTextStyles.hint
                                    : AppTextStyles.bodyMedium,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(
                              Icons.arrow_drop_down,
                              color: AppColors.textTertiary,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: AppSizes.paddingLarge),

          // 开始专注按钮
          ElevatedButton(
            onPressed: () => _startFocus(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
              ),
              minimumSize: const Size(double.infinity, AppSizes.buttonHeightMedium),
            ),
            child: const Text(
              "开始专注",
              style: AppTextStyles.buttonLarge,
            ),
          )
        ],
      ),
    );
  }

  // 显示科目选择器
  void _showSubjectSelector() {
    final subjects = ref.read(subjectStateProvider).subjects;

    if (subjects.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('暂无科目，请先创建科目')),
      );
      return;
    }

    CommonBottomSheet.show(
      context,
      title: "选择科目",
      contentPadding: const EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 24.0),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const BouncingScrollPhysics(),
        itemCount: subjects.length,
        separatorBuilder: (context, index) => const SizedBox(height: 8),
        itemBuilder: (context, index) {
          final subject = subjects[index];
          final isSelected = ref.read(subjectStateProvider).currentSubject?.id == subject.id;
          final subjectColor = Color(subject.color);

          return AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              color: isSelected ? subjectColor.withAlpha(30) : Colors.white,
              borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
              border: Border.all(
                color: isSelected ? subjectColor : AppColors.border,
                width: isSelected ? 1.5 : 1.0,
              ),
              boxShadow: isSelected ? [AppShadows.low] : null,
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                onTap: () {
                  ref.read(subjectStateProvider.notifier).setCurrentSubject(subject);
                  Navigator.pop(context);
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  child: Row(
                    children: [
                      // 科目颜色标识
                      Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: subjectColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 12),
                      // 科目名称
                      Expanded(
                        child: Text(
                          subject.name,
                          style: AppTextStyles.bodyLarge.copyWith(
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            color: isSelected ? subjectColor : AppColors.textSecondary,
                          ),
                        ),
                      ),
                      // 选中标识
                      if (isSelected)
                        Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: subjectColor,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // 显示项目选择器
  void _showProjectSelector() {
    final currentSubject = ref.read(subjectStateProvider).currentSubject;

    if (currentSubject == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先选择科目')),
      );
      return;
    }

    final projects = ref.read(subjectStateProvider).projects
        .where((p) => p.subjectId == currentSubject.id && !p.isArchived)
        .toList();

    if (projects.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('当前科目暂无项目，请先创建项目')),
      );
      return;
    }

    // 获取当前科目的颜色
    final subjectColor = Color(currentSubject.color);

    CommonBottomSheet.show(
      context,
      title: "选择项目",
      contentPadding: const EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 24.0),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const BouncingScrollPhysics(),
        itemCount: projects.length,
        separatorBuilder: (context, index) => const SizedBox(height: 8),
        itemBuilder: (context, index) {
          final project = projects[index];
          final isSelected = ref.read(subjectStateProvider).currentProject?.id == project.id;

          // 计算项目进度
          final progress = project.progress * 100;
          final now = DateTime.now();
          final isExpired = project.endDate.isBefore(now);

          // 格式化日期
          final startMonth = project.startDate.month.toString().padLeft(2, '0');
          final startDay = project.startDate.day.toString().padLeft(2, '0');
          final endMonth = project.endDate.month.toString().padLeft(2, '0');
          final endDay = project.endDate.day.toString().padLeft(2, '0');
          final dateRange = "$startMonth/$startDay - $endMonth/$endDay";

          return AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              color: isSelected ? subjectColor.withAlpha(30) : Colors.white,
              borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
              border: Border.all(
                color: isSelected ? subjectColor : AppColors.border,
                width: isSelected ? 1.5 : 1.0,
              ),
              boxShadow: isSelected ? [AppShadows.low] : null,
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                onTap: () {
                  ref.read(subjectStateProvider.notifier).setCurrentProject(project);
                  Navigator.pop(context);
                },
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          // 项目名称
                          Expanded(
                            child: Text(
                              project.name,
                              style: AppTextStyles.bodyLarge.copyWith(
                                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                color: isSelected ? subjectColor : AppColors.textSecondary,
                              ),
                            ),
                          ),
                          // 选中标识
                          if (isSelected)
                            Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: subjectColor,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      // 日期范围
                      Row(
                        children: [
                          Icon(
                            Icons.calendar_today_outlined,
                            size: 14,
                            color: isExpired ? AppColors.error : AppColors.textTertiary,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            dateRange,
                            style: AppTextStyles.bodySmall.copyWith(
                              color: isExpired ? AppColors.error : AppColors.textTertiary,
                            ),
                          ),
                          if (isExpired) ...[
                            const SizedBox(width: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: AppColors.errorLight100,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                '已过期',
                                style: AppTextStyles.overline.copyWith(color: AppColors.error),
                              ),
                            ),
                          ],
                        ],
                      ),
                      // 如果开启了进度跟踪，显示进度条
                      if (project.isTrackingEnabled) ...[
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(2),
                                child: LinearProgressIndicator(
                                  value: project.progress,
                                  backgroundColor: subjectColor.withAlpha(50),
                                  valueColor: AlwaysStoppedAnimation<Color>(subjectColor),
                                  minHeight: 4,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              "${progress.toStringAsFixed(0)}%",
                              style: AppTextStyles.bodySmall.copyWith(
                                color: subjectColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // 开始专注
  void _startFocus(BuildContext context) {
    // 获取当前选中的科目和项目
    final currentSubject = ref.read(subjectStateProvider).currentSubject;
    final currentProject = ref.read(subjectStateProvider).currentProject;

    // 检查是否已选择科目和项目
    if (currentSubject == null || currentProject == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请先选择科目和项目'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    // 导航到专注页面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FocusScreen(
          isCountdown: _isCountdown,
          countdownMinutes: _isCountdown ? _countdownMinutes : null,
          subject: currentSubject,
          project: currentProject,
        ),
      ),
    );
  }
}
