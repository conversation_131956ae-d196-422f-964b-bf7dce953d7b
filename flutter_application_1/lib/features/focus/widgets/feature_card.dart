import 'package:flutter/material.dart';

/// 功能卡片组件
/// 用于在首页底部显示各种功能入口
/// 每个卡片宽度为屏幕宽度的一半减去边距
class FeatureCard extends StatelessWidget {
  /// 卡片标题
  final String title;

  /// 卡片描述
  final String? description;

  /// 卡片图标
  final IconData icon;

  /// 卡片主色调
  final Color color;

  /// 点击回调
  final VoidCallback onTap;

  /// 是否显示箭头
  final bool showArrow;

  const FeatureCard({
    super.key,
    required this.title,
    this.description,
    required this.icon,
    required this.color,
    required this.onTap,
    this.showArrow = true,
  });

  // 固定卡片高度
  static const double cardHeight = 144.0;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        height: cardHeight, // 设置固定高度
        decoration: BoxDecoration(
          color: color.withAlpha(25),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(5),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 图标和箭头
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withAlpha(50),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(icon, color: color, size: 20),
                ),
                if (showArrow)
                  const Icon(
                    Icons.arrow_forward_ios,
                    size: 14,
                    color: Color(0xFF999999),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            // 标题
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF333333),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4), // 始终保持这个间距，无论是否有描述
            // 描述文本或占位空间
            Expanded(
              child: description != null
                ? Text(
                    description!,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF666666),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  )
                : const SizedBox(), // 如果没有描述，使用空占位符保持高度
            ),
          ],
        ),
      ),
    );
  }
}
