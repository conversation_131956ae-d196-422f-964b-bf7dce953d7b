// // import 'package:flutter/material.dart';
// // import 'package:flutter_riverpod/flutter_riverpod.dart';
// // import '../../../shared/theme/constants.dart';
// // import 'package:limefocus/core/models/subject_project.dart';
// // import 'package:limefocus/shared/widgets/common_bottom_sheet.dart';
// // import 'package:limefocus/features/task/providers/goal_state.dart';
// // import 'package:limefocus/features/task/providers/subject_state.dart';
// // import 'package:limefocus/core/services/hive_service.dart';
// // import 'focus_screen.dart';

// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import '../../../shared/theme/constants.dart';
// import '../../../core/services/hive_service.dart';
// import '../../task/providers/goal_state.dart';
// import '../../task/providers/subject_state.dart';
// import '../widgets/countdown_card.dart';
// import '../widgets/focus_setting_card.dart';
// import '../widgets/custom_cards_area.dart';


// /// 专注启动页面
// /// 这是应用的主页面，用于显示用户的专注目标、倒计时以及专注设置
// /// 页面包含三个主要区域：
// /// 1. 顶部倒计时卡片：显示用户设定的目标与倒计时
// /// 2. 中间专注设置卡片：包含计时模式选择和时间设置
// /// 3. 底部自定义卡片区域：预留给用户自定义内容
// class FocusHomeScreen extends ConsumerStatefulWidget {
//   const FocusHomeScreen({super.key});

//   @override
//   ConsumerState<FocusHomeScreen> createState() => _FocusHomeScreenState();
// }

// class _FocusHomeScreenState extends ConsumerState<FocusHomeScreen> {
//   // Hive服务
//   final HiveService _hiveService = HiveService();

//   @override
//   void initState() {
//     super.initState();
//     // 从Hive加载数据
//     _loadData();
//   }

//   // 从Hive加载数据
//   Future<void> _loadData() async {
//     try {
//       // 确保Hive服务已初始化
//       await _hiveService.initHive();

//       // 获取所有目标
//       final goals = _hiveService.goalRepository.getAllGoals();

//       // 获取所有科目和项目
//       final subjects = _hiveService.subjectRepository.getAllSubjects();
//       final projects = _hiveService.subjectRepository.getAllProjects();

//       // 使用Future.microtask确保不在构建生命周期中直接修改状态
//       Future.microtask(() async {
//         // 设置加载状态
//         ref.read(goalStateProvider.notifier).setLoading(true);
//         ref.read(subjectStateProvider.notifier).setLoading(true);

//         // 如果有目标，设置第一个目标为当前目标
//         if (goals.isNotEmpty) {
//           final currentGoal = _hiveService.goalRepository.getGoalWithMilestones(goals.first.id);
//           if (currentGoal != null) {
//             ref.read(goalStateProvider.notifier).setCurrentGoal(currentGoal);
//             ref.read(goalStateProvider.notifier).setMilestones(currentGoal.milestones);
//           }
//         }

//         // 更新科目和项目状态
//         ref.read(subjectStateProvider.notifier).setSubjects(subjects);
//         ref.read(subjectStateProvider.notifier).setProjects(projects);

//         // 加载上次选择的科目和项目
//         await ref.read(subjectStateProvider.notifier).loadLastSelection();

//         // 设置加载完成
//         ref.read(goalStateProvider.notifier).setLoading(false);
//         ref.read(subjectStateProvider.notifier).setLoading(false);
//       });
//     } catch (e) {
//       debugPrint('加载数据出错: $e');
//       // 使用Future.microtask确保不在构建生命周期中直接修改状态
//       Future.microtask(() {
//         ref.read(goalStateProvider.notifier).setError('加载数据失败: $e');
//         ref.read(goalStateProvider.notifier).setLoading(false);
//         ref.read(subjectStateProvider.notifier).setError('加载数据失败: $e');
//         ref.read(subjectStateProvider.notifier).setLoading(false);
//       });
//     }
//   }




//   @override
//   Widget build(BuildContext context) {

//     return Scaffold(
//       // 使用渐变背景
//       body: Container(
//         decoration: const BoxDecoration(
//           gradient: AppColors.pageBackground,
//         ),
//         // 使用SingleChildScrollView使整个页面可滚动
//         child: SafeArea(
//           child: SingleChildScrollView(
//             child: Padding(
//               padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium),
//               // 使用ConstrainedBox确保内容至少占据屏幕高度
//               child: ConstrainedBox(
//                 constraints: BoxConstraints(
//                   minHeight: MediaQuery.of(context).size.height - MediaQuery.of(context).padding.top - MediaQuery.of(context).padding.bottom,
//                 ),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: const [
//                     SizedBox(height: AppSizes.paddingMedium),
//                     // 1. 顶部倒计时卡片
//                     CountdownCard(),
//                     SizedBox(height: AppSizes.paddingLarge),
//                     // 2. 中间专注设置卡片
//                     FocusSettingCard(),
//                     SizedBox(height: AppSizes.paddingLarge),
//                     // 3. 底部自定义卡片区域
//                     CustomCardsArea(),
//                     // 添加底部空间，确保有足够的安全距离
//                     SizedBox(height: AppSizes.paddingLarge),
//                   ],
//                 ),
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }






// }
