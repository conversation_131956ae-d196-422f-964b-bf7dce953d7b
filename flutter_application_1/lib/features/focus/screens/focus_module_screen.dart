import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/services/hive_service.dart';
import '../../../core/models/subject_project.dart';
import '../../task/providers/goal_state.dart';
import '../../task/providers/subject_state.dart';
import '../screens/focus_screen.dart';
import '../widgets/circular_time_selector.dart';

/// 专注模块页面
/// 作为专注功能的主入口，提供专注设置和启动功能
class FocusModuleScreen extends ConsumerStatefulWidget {
  const FocusModuleScreen({super.key});

  @override
  ConsumerState<FocusModuleScreen> createState() => _FocusModuleScreenState();
}

class _FocusModuleScreenState extends ConsumerState<FocusModuleScreen> {
  // Hive服务
  final HiveService _hiveService = HiveService();

  // 是否为倒计时模式（true为倒计时，false为正计时）
  bool _isCountdown = true;

  // 倒计时时间（分钟）
  double _countdownMinutes = 25;

  @override
  void initState() {
    super.initState();
    // 从Hive加载数据
    _loadData();
  }

  // 从Hive加载数据
  Future<void> _loadData() async {
    try {
      // 确保Hive服务已初始化
      await _hiveService.initHive();

      // 获取所有目标
      final goals = _hiveService.goalRepository.getAllGoals();

      // 获取所有科目和项目
      final subjects = _hiveService.subjectRepository.getAllSubjects();
      final projects = _hiveService.subjectRepository.getAllProjects();

      // 使用Future.microtask确保不在构建生命周期中直接修改状态
      Future.microtask(() async {
        // 设置加载状态
        ref.read(goalStateProvider.notifier).setLoading(true);
        ref.read(subjectStateProvider.notifier).setLoading(true);

        // 如果有目标，设置第一个目标为当前目标
        if (goals.isNotEmpty) {
          final currentGoal = _hiveService.goalRepository.getGoalWithMilestones(goals.first.id);
          if (currentGoal != null) {
            ref.read(goalStateProvider.notifier).setCurrentGoal(currentGoal);
            ref.read(goalStateProvider.notifier).setMilestones(currentGoal.milestones);
          }
        }

        // 更新科目和项目状态
        ref.read(subjectStateProvider.notifier).setSubjects(subjects);
        ref.read(subjectStateProvider.notifier).setProjects(projects);

        // 加载上次选择的科目和项目
        await ref.read(subjectStateProvider.notifier).loadLastSelection();

        // 设置加载完成
        ref.read(goalStateProvider.notifier).setLoading(false);
        ref.read(subjectStateProvider.notifier).setLoading(false);
      });
    } catch (e) {
      debugPrint('加载数据出错: $e');
      // 使用Future.microtask确保不在构建生命周期中直接修改状态
      Future.microtask(() {
        ref.read(goalStateProvider.notifier).setError('加载数据失败: $e');
        ref.read(goalStateProvider.notifier).setLoading(false);
        ref.read(subjectStateProvider.notifier).setError('加载数据失败: $e');
        ref.read(subjectStateProvider.notifier).setLoading(false);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取当前选中的科目和项目
    final subjectState = ref.watch(subjectStateProvider);
    final currentSubject = subjectState.currentSubject;
    final currentProject = subjectState.currentProject;

    // 获取屏幕尺寸
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.height < 700;

    // 检查是否选择了科目和项目
    final bool canStart = currentSubject != null && currentProject != null;

    return Scaffold(
      // 使用渐变背景
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color.fromARGB(255, 204, 255, 229), // 顶部颜色
              Color.fromARGB(255, 255, 250, 240), // 底部颜色
            ],
          ),
        ),
        // 使用SafeArea确保内容不会被系统UI遮挡
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingLarge),
            child: Column(
              children: [
                // 顶部区域
                _buildCompactTopBar(),

                // 主要内容区域 - 使用Expanded确保内容填充可用空间
                Expanded(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // 计时模式切换和时间选择
                        Card(
                          elevation: 0,
                          color: Colors.white.withAlpha(30),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              vertical: AppSizes.paddingMedium,
                              horizontal: AppSizes.paddingMedium,
                            ),
                            child: _buildCompactTimerControls(),
                          ),
                        ),

                        // 增加间距
                        SizedBox(height: isSmallScreen ? AppSizes.paddingLarge : AppSizes.paddingXLarge),

                        // 计时器预览 - 放在卡片中
                        Card(
                          elevation: 0,
                          color: Colors.white.withAlpha(20),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(AppSizes.radiusXLarge),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(AppSizes.paddingMedium),
                            child: _buildTimerPreview(isSmallScreen),
                          ),
                        ),

                        // 增加间距
                        SizedBox(height: isSmallScreen ? AppSizes.paddingLarge : AppSizes.paddingXLarge),

                        // 科目和项目选择区域 - 使用卡片包装
                        Card(
                          elevation: 0,
                          color: Colors.white.withAlpha(30),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              vertical: AppSizes.paddingMedium,
                              horizontal: AppSizes.paddingMedium,
                            ),
                            child: _buildCompactSubjectProjectSelector(currentSubject, currentProject),
                          ),
                        ),

                        // 增加间距
                        const Spacer(),

                        // 开始按钮 - 更美观
                        _buildCompactStartButton(canStart, currentSubject),

                        // 底部间距
                        SizedBox(height: isSmallScreen ? AppSizes.paddingLarge : AppSizes.paddingXLarge),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 构建紧凑的顶部栏
  Widget _buildCompactTopBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: AppSizes.paddingMedium,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 页面标题
          Row(
            children: [
              //先考虑保持一致性
              // Container(
              //   padding: const EdgeInsets.all(8),
              //   decoration: BoxDecoration(
              //     color: AppColors.primary.withAlpha(30),
              //     shape: BoxShape.circle,
              //   ),
              //   child: Icon(
              //     Icons.timer_outlined,
              //     color: AppColors.primary,
              //     size: 20,
              //   ),
              // ),
              // const SizedBox(width: 8),
              const Text(
                "专注",
                style: AppTextStyles.headline2,
              ),
            ],
          ),

          // 原专注启动页面右上角的设置按钮，当前取消
          // Container(
          //   decoration: BoxDecoration(
          //     color: Colors.white.withAlpha(50),
          //     borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
          //   ),
          //   child: IconButton(
          //     icon: const Icon(Icons.settings_outlined),
          //     onPressed: _showSettingsMenu,
          //     tooltip: '专注设置',
          //     color: AppColors.textSecondary,
          //   ),
          // ),
        ],
      ),
    );
  }

  // 构建计时器预览
  Widget _buildTimerPreview(bool isSmallScreen) {
    // 计算圆形计时器的大小 - 更小一些
    final timerSize = isSmallScreen ? 180.0 : 220.0;

    if (_isCountdown) {
      // 倒计时模式下使用可交互的圆形时间选择器
      return CircularTimeSelector(
        value: _countdownMinutes,
        min: 5,
        max: 120,
        size: timerSize,
        onChanged: (value) {
          setState(() {
            _countdownMinutes = value;
          });
        },
        textStyle: TextStyle(
          fontSize: isSmallScreen ? 32.0 : 36.0,
          fontWeight: FontWeight.bold,
          color: AppColors.text,
        ),
      );
    } else {
      // 正计时模式下显示静态计时器
      return Container(
        width: timerSize,
        height: timerSize,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white.withAlpha(25),
          border: Border.all(
            color: AppColors.primary.withAlpha(77),
            width: 12, // 稍微减小边框宽度
          ),
        ),
        child: Center(
          child: Text(
            "00:00",
            style: TextStyle(
              fontSize: isSmallScreen ? 32.0 : 36.0,
              fontWeight: FontWeight.bold,
              color: AppColors.text,
            ),
          ),
        ),
      );
    }
  }



  // 构建紧凑的科目和项目选择器
  Widget _buildCompactSubjectProjectSelector(Subject? currentSubject, Project? currentProject) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 标题
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.edit_note_outlined,
              size: 16,
              color: AppColors.textTertiary,
            ),
            const SizedBox(width: 4),
            Text(
              "选择专注内容",
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textTertiary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),

        const SizedBox(height: AppSizes.paddingSmall),

        // 选择器按钮
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 科目选择按钮 - 使用标签图标
            _buildEnhancedSelectorButton(
              label: currentSubject?.name ?? "选择科目",
              icon: Icons.local_offer_outlined, // 标签图标更适合表示科目/分类
              color: currentSubject != null ? Color(currentSubject.color) : AppColors.grey,
              onTap: _showSubjectSelector,
              showIndicator: currentSubject == null,
            ),

            // 分隔符
            Container(
              height: 24,
              width: 1,
              margin: const EdgeInsets.symmetric(horizontal: AppSizes.paddingSmall),
              color: AppColors.divider,
            ),

            // 项目选择按钮 - 使用任务图标
            _buildEnhancedSelectorButton(
              label: currentProject?.name ?? "选择项目",
              icon: Icons.assignment_outlined, // 任务/清单图标更适合表示具体项目
              color: currentSubject != null ? Color(currentSubject.color) : AppColors.grey,
              onTap: _showProjectSelector,
              showIndicator: currentProject == null,
              enabled: currentSubject != null,
            ),
          ],
        ),
      ],
    );
  }

  // 构建增强的选择器按钮
  Widget _buildEnhancedSelectorButton({
    required String label,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    bool showIndicator = false,
    bool enabled = true,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: enabled ? Colors.white.withAlpha(80) : Colors.white.withAlpha(30),
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        border: Border.all(
          color: enabled ? color.withAlpha(50) : AppColors.border,
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: enabled ? onTap : null,
          borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppSizes.paddingMedium,
              vertical: AppSizes.paddingSmall,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: 18,
                  color: enabled ? color : AppColors.textAssist,
                ),
                const SizedBox(width: 8),
                Container(
                  constraints: const BoxConstraints(maxWidth: 80),
                  child: Text(
                    label,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: enabled ? AppColors.text : AppColors.textAssist,
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
                if (showIndicator) ...[
                  const SizedBox(width: 6),
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: AppColors.error,
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }



  // 构建紧凑的计时控制
  Widget _buildCompactTimerControls() {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingSmall),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // 计算容器宽度 - 使用父容器宽度的60%，但不超过240
          final containerWidth = constraints.maxWidth * 0.6 > 240
              ? 240.0
              : constraints.maxWidth * 0.6;
          // 计算每个选项的宽度
          final segmentWidth = containerWidth / 2;

          return Center(
            child: Container(
              width: containerWidth,
              height: 36,
              decoration: BoxDecoration(
                color: Colors.grey.withAlpha(25),
                borderRadius: BorderRadius.circular(18),
              ),
              child: Stack(
                children: [
                  // 滑动块
                  AnimatedPositioned(
                    duration: const Duration(milliseconds: 250),
                    curve: Curves.easeInOut,
                    left: _isCountdown ? segmentWidth : 0,
                    child: Container(
                      margin: const EdgeInsets.all(3),
                      width: segmentWidth - 6, // 减去边距
                      height: 30,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [AppShadows.low],
                      ),
                    ),
                  ),
                  // 分段文本
                  Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _isCountdown = false;
                            });
                          },
                          child: Center(
                            child: Text(
                              "正计时",
                              style: TextStyle(
                                fontSize: 14,
                                color: !_isCountdown ? Colors.white : AppColors.textAssist,
                                fontWeight: !_isCountdown ? FontWeight.bold : FontWeight.normal,
                              ),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _isCountdown = true;
                            });
                          },
                          child: Center(
                            child: Text(
                              "倒计时",
                              style: TextStyle(
                                fontSize: 14,
                                color: _isCountdown ? Colors.white : AppColors.textAssist,
                                fontWeight: _isCountdown ? FontWeight.bold : FontWeight.normal,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // 构建紧凑的开始按钮
  Widget _buildCompactStartButton(bool canStart, Subject? currentSubject) {
    // 使用固定的 greenLight300 作为按钮颜色
    final Color buttonColor = AppColors.greenLight400;

    return Container(
      width: 180,
      height: 48,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24), // 更圆润的圆角
        boxShadow: canStart ? [
          BoxShadow(
            color: buttonColor.withAlpha(40),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: buttonColor.withAlpha(20),
            blurRadius: 2,
            offset: const Offset(0, 1),
            spreadRadius: 0,
          ),
        ] : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: canStart ? () => _startFocus(context) : null,
          borderRadius: BorderRadius.circular(24), // 更圆润的圆角
          child: Ink(
            decoration: BoxDecoration(
              color: canStart ? buttonColor : AppColors.grey.withAlpha(100),
              borderRadius: BorderRadius.circular(24), // 更圆润的圆角
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.play_circle_outlined,
                  size: 22,
                  color: Colors.white,
                ),
                const SizedBox(width: 8),
                Text(
                  "开始专注",
                  style: AppTextStyles.buttonMedium.copyWith(
                    fontSize: 16,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }



  // 显示科目选择器
  void _showSubjectSelector() {
    final subjects = ref.read(subjectStateProvider).subjects;

    if (subjects.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('暂无科目，请先创建科目')),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(AppSizes.radiusLarge)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSizes.paddingMedium),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  "选择科目",
                  style: AppTextStyles.headline3,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: AppSizes.paddingMedium),
            Expanded(
              child: ListView.separated(
                shrinkWrap: true,
                physics: const BouncingScrollPhysics(),
                itemCount: subjects.length,
                separatorBuilder: (context, index) => const SizedBox(height: 8),
                itemBuilder: (context, index) {
                  final subject = subjects[index];
                  final isSelected = ref.read(subjectStateProvider).currentSubject?.id == subject.id;
                  final subjectColor = Color(subject.color);

                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    decoration: BoxDecoration(
                      color: isSelected ? subjectColor.withAlpha(30) : Colors.white,
                      borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                      border: Border.all(
                        color: isSelected ? subjectColor : AppColors.border,
                        width: isSelected ? 1.5 : 1.0,
                      ),
                      boxShadow: isSelected ? [AppShadows.low] : null,
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                        onTap: () {
                          ref.read(subjectStateProvider.notifier).setCurrentSubject(subject);
                          Navigator.pop(context);
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                          child: Row(
                            children: [
                              // 科目颜色标识
                              Container(
                                width: 16,
                                height: 16,
                                decoration: BoxDecoration(
                                  color: subjectColor,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 12),
                              // 科目名称
                              Expanded(
                                child: Text(
                                  subject.name,
                                  style: AppTextStyles.bodyLarge.copyWith(
                                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                    color: isSelected ? subjectColor : AppColors.textSecondary,
                                  ),
                                ),
                              ),
                              // 选中标识
                              if (isSelected)
                                Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: subjectColor,
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.check,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示项目选择器
  void _showProjectSelector() {
    final currentSubject = ref.read(subjectStateProvider).currentSubject;

    if (currentSubject == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先选择科目')),
      );
      return;
    }

    final projects = ref.read(subjectStateProvider).projects
        .where((p) => p.subjectId == currentSubject.id && !p.isArchived)
        .toList();

    if (projects.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('当前科目暂无项目，请先创建项目')),
      );
      return;
    }

    // 获取当前科目的颜色
    final subjectColor = Color(currentSubject.color);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(AppSizes.radiusLarge)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSizes.paddingMedium),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  "选择项目",
                  style: AppTextStyles.headline3,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: AppSizes.paddingMedium),
            Expanded(
              child: ListView.separated(
                shrinkWrap: true,
                physics: const BouncingScrollPhysics(),
                itemCount: projects.length,
                separatorBuilder: (context, index) => const SizedBox(height: 8),
                itemBuilder: (context, index) {
                  final project = projects[index];
                  final isSelected = ref.read(subjectStateProvider).currentProject?.id == project.id;

                  // 计算项目进度
                  final progress = project.progress * 100;
                  final now = DateTime.now();
                  final isExpired = project.endDate.isBefore(now);

                  // 格式化日期
                  final startMonth = project.startDate.month.toString().padLeft(2, '0');
                  final startDay = project.startDate.day.toString().padLeft(2, '0');
                  final endMonth = project.endDate.month.toString().padLeft(2, '0');
                  final endDay = project.endDate.day.toString().padLeft(2, '0');
                  final dateRange = "$startMonth/$startDay - $endMonth/$endDay";

                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    decoration: BoxDecoration(
                      color: isSelected ? subjectColor.withAlpha(30) : Colors.white,
                      borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                      border: Border.all(
                        color: isSelected ? subjectColor : AppColors.border,
                        width: isSelected ? 1.5 : 1.0,
                      ),
                      boxShadow: isSelected ? [AppShadows.low] : null,
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                        onTap: () {
                          ref.read(subjectStateProvider.notifier).setCurrentProject(project);
                          Navigator.pop(context);
                        },
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  // 项目名称
                                  Expanded(
                                    child: Text(
                                      project.name,
                                      style: AppTextStyles.bodyLarge.copyWith(
                                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                        color: isSelected ? subjectColor : AppColors.textSecondary,
                                      ),
                                    ),
                                  ),
                                  // 选中标识
                                  if (isSelected)
                                    Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: subjectColor,
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.check,
                                        color: Colors.white,
                                        size: 16,
                                      ),
                                    ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              // 日期范围
                              Row(
                                children: [
                                  Icon(
                                    Icons.calendar_today_outlined,
                                    size: 14,
                                    color: isExpired ? AppColors.error : AppColors.textTertiary,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    dateRange,
                                    style: AppTextStyles.bodySmall.copyWith(
                                      color: isExpired ? AppColors.error : AppColors.textTertiary,
                                    ),
                                  ),
                                  if (isExpired) ...[
                                    const SizedBox(width: 4),
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: AppColors.errorLight100,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Text(
                                        '已过期',
                                        style: AppTextStyles.overline.copyWith(color: AppColors.error),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                              // 如果开启了进度跟踪，显示进度条
                              if (project.isTrackingEnabled) ...[
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    Expanded(
                                      child: LinearProgressIndicator(
                                        value: project.progress,
                                        backgroundColor: AppColors.divider,
                                        valueColor: AlwaysStoppedAnimation<Color>(subjectColor),
                                        minHeight: 6,
                                        borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      "${progress.toInt()}%",
                                      style: AppTextStyles.bodySmall.copyWith(
                                        color: AppColors.textTertiary,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 开始专注
  void _startFocus(BuildContext context) {
    final currentSubject = ref.read(subjectStateProvider).currentSubject;
    final currentProject = ref.read(subjectStateProvider).currentProject;

    // 检查是否选择了科目和项目
    if (currentSubject == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先选择科目')),
      );
      return;
    }

    if (currentProject == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先选择项目')),
      );
      return;
    }

    // 导航到专注页面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FocusScreen(
          isCountdown: _isCountdown,
          countdownMinutes: _isCountdown ? _countdownMinutes : null,
          subject: currentSubject,
          project: currentProject,
        ),
      ),
    );
  }

  // 显示设置菜单
  // void _showSettingsMenu() {
  //   showModalBottomSheet(
  //     context: context,
  //     isScrollControlled: true,
  //     shape: const RoundedRectangleBorder(
  //       borderRadius: BorderRadius.vertical(top: Radius.circular(AppSizes.radiusLarge)),
  //     ),
  //     builder: (context) => Container(
  //       padding: const EdgeInsets.all(AppSizes.paddingMedium),
  //       constraints: BoxConstraints(
  //         maxHeight: MediaQuery.of(context).size.height * 0.7,
  //       ),
  //       child: Column(
  //         mainAxisSize: MainAxisSize.min,
  //         children: [
  //           Row(
  //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //             children: [
  //               const Text(
  //                 "专注设置",
  //                 style: AppTextStyles.headline3,
  //               ),
  //               IconButton(
  //                 icon: const Icon(Icons.close),
  //                 onPressed: () => Navigator.pop(context),
  //               ),
  //             ],
  //           ),
  //           const SizedBox(height: AppSizes.paddingMedium),
  //           Expanded(
  //             child: ListView(
  //               children: [
  //                 // 完成提醒设置
  //                 ListTile(
  //                   leading: const Icon(Icons.notifications_outlined),
  //                   title: const Text('完成时提醒'),
  //                   trailing: Switch(
  //                     value: true, // 默认开启
  //                     onChanged: (value) {
  //                       // 处理提醒设置
  //                     },
  //                     activeColor: AppColors.primary,
  //                   ),
  //                 ),
  //                 const Divider(),

  //                 // 防打扰模式
  //                 ListTile(
  //                   leading: const Icon(Icons.do_not_disturb_on_outlined),
  //                   title: const Text('专注时防打扰'),
  //                   trailing: Switch(
  //                     value: false, // 默认关闭
  //                     onChanged: (value) {
  //                       // 处理防打扰设置
  //                     },
  //                     activeColor: AppColors.primary,
  //                   ),
  //                 ),
  //                 const Divider(),

  //                 // 自动开始下一轮
  //                 ListTile(
  //                   leading: const Icon(Icons.repeat_outlined),
  //                   title: const Text('自动开始下一轮'),
  //                   trailing: Switch(
  //                     value: false, // 默认关闭
  //                     onChanged: (value) {
  //                       // 处理自动开始设置
  //                     },
  //                     activeColor: AppColors.primary,
  //                   ),
  //                 ),
  //                 const Divider(),

  //                 // 数据统计
  //                 ListTile(
  //                   leading: const Icon(Icons.bar_chart_outlined),
  //                   title: const Text('专注数据统计'),
  //                   subtitle: const Text('查看历史专注记录和统计数据'),
  //                   onTap: () {
  //                     // 导航到数据统计页面
  //                     Navigator.pop(context);
  //                   },
  //                 ),
  //               ],
  //             ),
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  // 以下是原有的专注设置卡片方法，已不再使用，仅作参考
  /*
  Widget _buildFocusSettingCard(Subject? currentSubject, Project? currentProject) {
    return Card(
      margin: EdgeInsets.zero,
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 计时模式切换
            LayoutBuilder(
              builder: (context, constraints) {
                // 计算容器宽度 - 使用父容器宽度的80%，但不超过300
                final containerWidth = constraints.maxWidth * 0.6 > 300
                    ? 300.0
                    : constraints.maxWidth * 0.6;
                // 计算每个选项的宽度
                final segmentWidth = containerWidth / 2;

                return Center(
                  child: Container(
                    width: containerWidth,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.grey.withAlpha(25),
                      borderRadius: BorderRadius.circular(22),
                    ),
                    child: Stack(
                      children: [
                        // 滑动块
                        AnimatedPositioned(
                          duration: const Duration(milliseconds: 250),
                          curve: Curves.easeInOut,
                          left: _isCountdown ? segmentWidth : 0,
                          child: Container(
                            margin: const EdgeInsets.all(3),
                            width: segmentWidth - 6, // 减去边距
                            height: 34,
                            decoration: BoxDecoration(
                              color: AppColors.primary,
                              borderRadius: BorderRadius.circular(19),
                              boxShadow: [AppShadows.low],
                            ),
                          ),
                        ),
                        // 分段文本
                        Row(
                          children: [
                            Expanded(
                              child: GestureDetector(
                                onTap: () {
                                  setState(() {
                                    _isCountdown = false;
                                  });
                                },
                                child: Center(
                                  child: Text(
                                    "正计时",
                                    style: TextStyle(
                                      color: !_isCountdown ? Colors.white : AppColors.textAssist,
                                      fontWeight: !_isCountdown ? FontWeight.bold : FontWeight.normal,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            Expanded(
                              child: GestureDetector(
                                onTap: () {
                                  setState(() {
                                    _isCountdown = true;
                                  });
                                },
                                child: Center(
                                  child: Text(
                                    "倒计时",
                                    style: TextStyle(
                                      color: _isCountdown ? Colors.white : AppColors.textAssist,
                                      fontWeight: _isCountdown ? FontWeight.bold : FontWeight.normal,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),

            // 倒计时模式下显示时间滑块
            if (_isCountdown) ...[
              const SizedBox(height: AppSizes.paddingMedium),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    "专注时长",
                    style: AppTextStyles.bodyLarge,
                  ),
                  Text(
                    "${_countdownMinutes.toInt()}分钟",
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppSizes.paddingSmall),
              Slider(
                value: _countdownMinutes,
                min: 5,
                max: 120,
                divisions: 23,
                label: "${_countdownMinutes.toInt()}分钟",
                onChanged: (value) {
                  setState(() {
                    _countdownMinutes = value;
                  });
                },
                activeColor: AppColors.primary,
                inactiveColor: AppColors.primary.withAlpha(50),
              ),
            ],

            const SizedBox(height: AppSizes.paddingMedium),

            // 科目和项目选择
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        "科目",
                        style: AppTextStyles.bodyLarge,
                      ),
                      const SizedBox(height: AppSizes.paddingSmall),
                      InkWell(
                        onTap: _showSubjectSelector,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingMedium,
                            vertical: AppSizes.paddingSmall,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.border),
                            borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  currentSubject?.name ?? "选择科目",
                                  style: currentSubject == null
                                      ? AppTextStyles.hint
                                      : AppTextStyles.bodyMedium,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              const Icon(
                                Icons.arrow_drop_down,
                                color: AppColors.textTertiary,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: AppSizes.paddingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        "项目",
                        style: AppTextStyles.bodyLarge,
                      ),
                      const SizedBox(height: AppSizes.paddingSmall),
                      InkWell(
                        onTap: _showProjectSelector,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingMedium,
                            vertical: AppSizes.paddingSmall,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.border),
                            borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  currentProject?.name ?? "选择项目",
                                  style: currentProject == null
                                      ? AppTextStyles.hint
                                      : AppTextStyles.bodyMedium,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              const Icon(
                                Icons.arrow_drop_down,
                                color: AppColors.textTertiary,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppSizes.paddingLarge),

            // 开始专注按钮
            ElevatedButton(
              onPressed: () => _startFocus(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                ),
                minimumSize: const Size(double.infinity, AppSizes.buttonHeightMedium),
              ),
              child: const Text(
                "开始专注",
                style: AppTextStyles.buttonLarge,
              ),
            )
          ],
        ),
      ),
    );
  }

  // 显示科目选择器
  void _showSubjectSelector() {
    final subjects = ref.read(subjectStateProvider).subjects;

    if (subjects.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('暂无科目，请先创建科目')),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(AppSizes.radiusLarge)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSizes.paddingMedium),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  "选择科目",
                  style: AppTextStyles.headline3,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: AppSizes.paddingMedium),
            Expanded(
              child: ListView.separated(
                shrinkWrap: true,
                physics: const BouncingScrollPhysics(),
                itemCount: subjects.length,
                separatorBuilder: (context, index) => const SizedBox(height: 8),
                itemBuilder: (context, index) {
                  final subject = subjects[index];
                  final isSelected = ref.read(subjectStateProvider).currentSubject?.id == subject.id;
                  final subjectColor = Color(subject.color);

                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    decoration: BoxDecoration(
                      color: isSelected ? subjectColor.withAlpha(30) : Colors.white,
                      borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                      border: Border.all(
                        color: isSelected ? subjectColor : AppColors.border,
                        width: isSelected ? 1.5 : 1.0,
                      ),
                      boxShadow: isSelected ? [AppShadows.low] : null,
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                        onTap: () {
                          // 使用Future.microtask确保在构建完成后更新状态
                          Future.microtask(() {
                            ref.read(subjectStateProvider.notifier).setCurrentSubject(subject);
                          });
                          Navigator.pop(context);
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                          child: Row(
                            children: [
                              // 科目颜色标识
                              Container(
                                width: 16,
                                height: 16,
                                decoration: BoxDecoration(
                                  color: subjectColor,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 12),
                              // 科目名称
                              Expanded(
                                child: Text(
                                  subject.name,
                                  style: AppTextStyles.bodyLarge.copyWith(
                                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                    color: isSelected ? subjectColor : AppColors.textSecondary,
                                  ),
                                ),
                              ),
                              // 选中标识
                              if (isSelected)
                                Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: subjectColor,
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.check,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示项目选择器
  void _showProjectSelector() {
    final currentSubject = ref.read(subjectStateProvider).currentSubject;

    if (currentSubject == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先选择科目')),
      );
      return;
    }

    final projects = ref.read(subjectStateProvider).projects
        .where((p) => p.subjectId == currentSubject.id)
        .toList();

    if (projects.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('当前科目暂无项目，请先创建项目')),
      );
      return;
    }

    // 获取当前科目的颜色
    final subjectColor = Color(currentSubject.color);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(AppSizes.radiusLarge)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSizes.paddingMedium),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  "选择项目",
                  style: AppTextStyles.headline3,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: AppSizes.paddingMedium),
            Expanded(
              child: ListView.separated(
                shrinkWrap: true,
                physics: const BouncingScrollPhysics(),
                itemCount: projects.length,
                separatorBuilder: (context, index) => const SizedBox(height: 8),
                itemBuilder: (context, index) {
                  final project = projects[index];
                  final isSelected = ref.read(subjectStateProvider).currentProject?.id == project.id;

                  // 计算项目进度
                  final progress = project.progress * 100;
                  final now = DateTime.now();
                  final isExpired = project.endDate.isBefore(now);

                  // 格式化日期
                  final startMonth = project.startDate.month.toString().padLeft(2, '0');
                  final startDay = project.startDate.day.toString().padLeft(2, '0');
                  final endMonth = project.endDate.month.toString().padLeft(2, '0');
                  final endDay = project.endDate.day.toString().padLeft(2, '0');
                  final dateRange = "$startMonth/$startDay - $endMonth/$endDay";

                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    decoration: BoxDecoration(
                      color: isSelected ? subjectColor.withAlpha(30) : Colors.white,
                      borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                      border: Border.all(
                        color: isSelected ? subjectColor : AppColors.border,
                        width: isSelected ? 1.5 : 1.0,
                      ),
                      boxShadow: isSelected ? [AppShadows.low] : null,
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                        onTap: () {
                          // 使用Future.microtask确保在构建完成后更新状态
                          Future.microtask(() {
                            ref.read(subjectStateProvider.notifier).setCurrentProject(project);
                          });
                          Navigator.pop(context);
                        },
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  // 项目名称
                                  Expanded(
                                    child: Text(
                                      project.name,
                                      style: AppTextStyles.bodyLarge.copyWith(
                                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                        color: isSelected ? subjectColor : AppColors.textSecondary,
                                      ),
                                    ),
                                  ),
                                  // 选中标识
                                  if (isSelected)
                                    Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: subjectColor,
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.check,
                                        color: Colors.white,
                                        size: 16,
                                      ),
                                    ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              // 日期范围
                              Row(
                                children: [
                                  Icon(
                                    Icons.calendar_today_outlined,
                                    size: 14,
                                    color: isExpired ? AppColors.error : AppColors.textTertiary,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    dateRange,
                                    style: AppTextStyles.bodySmall.copyWith(
                                      color: isExpired ? AppColors.error : AppColors.textTertiary,
                                    ),
                                  ),
                                  if (isExpired) ...[
                                    const SizedBox(width: 4),
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: AppColors.errorLight100,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Text(
                                        '已过期',
                                        style: AppTextStyles.overline.copyWith(color: AppColors.error),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                              // 如果开启了进度跟踪，显示进度条
                              if (project.isTrackingEnabled) ...[
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    Expanded(
                                      child: LinearProgressIndicator(
                                        value: project.progress,
                                        backgroundColor: AppColors.divider,
                                        valueColor: AlwaysStoppedAnimation<Color>(subjectColor),
                                        minHeight: 6,
                                        borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      "${progress.toInt()}%",
                                      style: AppTextStyles.bodySmall.copyWith(
                                        color: AppColors.textTertiary,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 开始专注
  void _startFocus(BuildContext context) {
    final currentSubject = ref.read(subjectStateProvider).currentSubject;
    final currentProject = ref.read(subjectStateProvider).currentProject;

    // 检查是否选择了科目和项目
    if (currentSubject == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先选择科目')),
      );
      return;
    }

    if (currentProject == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先选择项目')),
      );
      return;
    }

    // 导航到专注页面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FocusScreen(
          isCountdown: _isCountdown,
          countdownMinutes: _isCountdown ? _countdownMinutes : null,
          subject: currentSubject,
          project: currentProject,
        ),
      ),
    );
  }
}
*/
}
