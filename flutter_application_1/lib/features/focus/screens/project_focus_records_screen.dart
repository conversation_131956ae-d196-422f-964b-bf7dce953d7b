import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/models/focus_record.dart';
import '../../../core/services/enhanced_hive_service.dart';

/// 项目专注记录页面
/// 显示特定项目的所有专注记录
class ProjectFocusRecordsScreen extends StatefulWidget {
  final String projectId;
  final String projectName;

  const ProjectFocusRecordsScreen({
    super.key,
    required this.projectId,
    required this.projectName,
  });

  @override
  State<ProjectFocusRecordsScreen> createState() => _ProjectFocusRecordsScreenState();
}

class _ProjectFocusRecordsScreenState extends State<ProjectFocusRecordsScreen> {
  final EnhancedHiveService _hiveService = EnhancedHiveService();
  List<FocusRecord> _records = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadRecords();
  }

  // 加载专注记录
  Future<void> _loadRecords() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 初始化Hive服务
      await _hiveService.initHive();

      // 获取项目的专注记录
      final records = _hiveService.focusRecordRepository.getFocusRecordsByProjectId(widget.projectId);

      // 按时间倒序排序
      records.sort((a, b) => b.startTime.compareTo(a.startTime));

      setState(() {
        _records = records;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('加载专注记录失败: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // 使用渐变背景，与应用其他页面保持一致
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.pageBackground,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 自定义顶部栏
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // 返回按钮
                    IconButton(
                      icon: const Icon(Icons.arrow_back),
                      onPressed: () => Navigator.pop(context),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                    // 标题
                    Expanded(
                      child: Center(
                        child: Text(
                          '${widget.projectName}的专注记录',
                          style: AppTextStyles.headline3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                    // 为了平衡布局的空白区域
                    const SizedBox(width: 24),
                  ],
                ),
              ),

              // 内容区域
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _records.isEmpty
                        ? _buildEmptyState()
                        : _buildRecordsList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.timer_off,
            size: 64,
            color: AppColors.textTertiary,
          ),
          const SizedBox(height: 16),
          Text(
            '暂无专注记录',
            style: AppTextStyles.headline3.copyWith(
              color: AppColors.textTertiary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '开始专注后，记录将显示在这里',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textTertiary,
            ),
          ),
        ],
      ),
    );
  }

  // 构建记录列表
  Widget _buildRecordsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _records.length,
      itemBuilder: (context, index) {
        final record = _records[index];
        return _buildRecordCard(record);
      },
    );
  }

  // 构建记录卡片
  Widget _buildRecordCard(FocusRecord record) {
    final durationMinutes = record.durationSeconds ~/ 60;
    final subject = _hiveService.subjectRepository.getSubjectById(record.subjectId);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 日期和时长
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  DateFormat('yyyy-MM-dd HH:mm').format(record.startTime),
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withAlpha(26),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '$durationMinutes分钟',
                    style: AppTextStyles.bodySmall.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // 科目
            if (subject != null)
              Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Color(subject.color),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    subject.name,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textTertiary,
                    ),
                  ),
                ],
              ),
            const SizedBox(height: 8),

            // 状态
            Row(
              children: [
                Icon(
                  record.status == FocusRecordStatus.completed
                      ? Icons.check_circle_outline
                      : Icons.error_outline,
                  size: 16,
                  color: record.status == FocusRecordStatus.completed
                      ? AppColors.success
                      : AppColors.warning,
                ),
                const SizedBox(width: 8),
                Text(
                  record.status == FocusRecordStatus.completed ? '已完成' : '已中断',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: record.status == FocusRecordStatus.completed
                        ? AppColors.success
                        : AppColors.warning,
                  ),
                ),
                if (record.isCountdown)
                  Padding(
                    padding: const EdgeInsets.only(left: 16),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.timer,
                          size: 16,
                          color: AppColors.textTertiary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '倒计时模式',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.textTertiary,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
