import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import '../models/onboarding_page_model.dart';

/// 引导页组件
/// 显示单个引导页的内容
class OnboardingPageWidget extends StatelessWidget {
  final OnboardingPageModel page;

  const OnboardingPageWidget({
    super.key,
    required this.page,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: page.color,
      padding: const EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 图标
          Icon(
            page.icon,
            size: 150,
            color: AppColors.primary.withOpacity(0.8),
          ),
          const SizedBox(height: 40),
          
          // 标题
          Text(
            page.title,
            style: const TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppColors.text,
            ),
          ),
          const SizedBox(height: 20),
          
          // 描述
          Text(
            page.description,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 18,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}
