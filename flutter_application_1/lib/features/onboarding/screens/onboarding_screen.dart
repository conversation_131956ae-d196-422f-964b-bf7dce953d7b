import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/providers/app_settings_provider.dart';
import '../models/onboarding_page_model.dart';
import '../widgets/onboarding_page.dart';

/// 引导页屏幕
/// 显示应用功能介绍，引导用户了解应用的主要功能
class OnboardingScreen extends ConsumerStatefulWidget {
  const OnboardingScreen({super.key});

  @override
  ConsumerState<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends ConsumerState<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  final int _totalPages = 4;

  // 引导页数据
  final List<OnboardingPageModel> _pages = [
    OnboardingPageModel(
      title: '专注学习',
      description: '通过科学的专注方法，提高学习效率',
      icon: Icons.timer,
      color: Colors.blue.shade50,
    ),
    OnboardingPageModel(
      title: '目标管理',
      description: '设定目标，分解任务，逐步实现',
      icon: Icons.flag,
      color: Colors.green.shade50,
    ),
    OnboardingPageModel(
      title: '数据分析',
      description: '可视化学习数据，了解自己的学习情况',
      icon: Icons.bar_chart,
      color: Colors.orange.shade50,
    ),
    OnboardingPageModel(
      title: '开始使用',
      description: '立即开始你的高效学习之旅',
      icon: Icons.rocket_launch,
      color: Colors.purple.shade50,
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// 页面变化处理
  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
  }

  /// 下一页
  void _nextPage() {
    if (_currentPage < _totalPages - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _finishOnboarding();
    }
  }

  /// 完成引导
  void _finishOnboarding() {
    // 标记引导页已完成
    ref.read(appSettingsServiceProvider).markOnboardingCompleted();

    // 如果是最后一页，显示登录/注册/跳过选项
    if (_currentPage == _totalPages - 1) {
      _showLoginOptions();
    } else {
      // 不是最后一页，直接导航到主页
      Navigator.of(context).pushReplacementNamed('/');
    }
  }

  /// 显示登录选项对话框
  void _showLoginOptions() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('开始使用'),
        content: const Text('您可以选择登录或注册账号，也可以直接跳过进入应用。登录账号可以同步数据，在多个设备上使用。'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // 关闭对话框
              Navigator.pushReplacementNamed(context, '/auth/login');
            },
            child: const Text('登录'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context); // 关闭对话框
              Navigator.pushReplacementNamed(context, '/auth/register');
            },
            child: const Text('注册'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context); // 关闭对话框
              Navigator.pushReplacementNamed(context, '/');
            },
            child: const Text('跳过'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // 页面内容
          PageView.builder(
            controller: _pageController,
            onPageChanged: _onPageChanged,
            itemCount: _totalPages,
            itemBuilder: (context, index) {
              return OnboardingPageWidget(page: _pages[index]);
            },
          ),

          // 跳过按钮
          Positioned(
            top: 50,
            right: 20,
            child: TextButton(
              onPressed: _finishOnboarding,
              child: const Text('跳过'),
            ),
          ),

          // 底部导航
          Positioned(
            bottom: 50,
            left: 0,
            right: 0,
            child: Column(
              children: [
                // 页面指示器
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    _totalPages,
                    (index) => Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      width: 10,
                      height: 10,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _currentPage == index
                            ? AppColors.primary
                            : Colors.grey.shade300,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 30),
                // 下一步按钮
                ElevatedButton(
                  onPressed: _nextPage,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: Text(
                    _currentPage == _totalPages - 1 ? '完成' : '下一步',
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
