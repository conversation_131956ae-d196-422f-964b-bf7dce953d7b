import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:io';
import 'package:image_picker/image_picker.dart';
import '../../../core/services/user_service.dart';
import '../../../core/utils/storage_utils.dart';
import '../../../shared/theme/constants.dart';
import '../../profile/providers/user_provider.dart';

/// 认证测试页面
class AuthTestScreen extends ConsumerStatefulWidget {
  const AuthTestScreen({super.key});

  @override
  ConsumerState<AuthTestScreen> createState() => _AuthTestScreenState();
}

class _AuthTestScreenState extends ConsumerState<AuthTestScreen> {
  final UserService _userService = UserService();

  bool _isLoading = false;
  String _result = '';
  String? _token;
  String? _refreshToken;
  String? _userInfo;
  File? _avatarFile;

  final TextEditingController _phoneController = TextEditingController(text: '13800138000');
  final TextEditingController _passwordController = TextEditingController(text: 'Password123!');
  final TextEditingController _nicknameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadAuthData();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _passwordController.dispose();
    _nicknameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  // 加载认证数据
  Future<void> _loadAuthData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final token = await StorageUtils.getToken();
      final refreshToken = await StorageUtils.getRefreshToken();
      final userInfo = await StorageUtils.getUserInfo();

      debugPrint('加载认证数据成功:');
      debugPrint('令牌: ${token ?? '无'}');
      debugPrint('刷新令牌: ${refreshToken ?? '无'}');
      debugPrint('用户信息: ${userInfo != null ? '有' : '无'}');

      if (!mounted) return;

      setState(() {
        _token = token;
        _refreshToken = refreshToken;
        _userInfo = userInfo;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('加载认证数据失败: $e');

      if (!mounted) return;

      setState(() {
        _result = '加载认证数据失败: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final userAsync = ref.watch(userProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('认证测试'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 认证状态
            const Text('认证状态', style: AppTextStyles.headline3),
            const SizedBox(height: 8),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('令牌: ${_token != null ? '已存在' : '不存在'}'),
                    if (_token != null)
                      Text('令牌值: ${_token!.substring(0, 10)}...'),
                    const SizedBox(height: 4),
                    Text('刷新令牌: ${_refreshToken != null ? '已存在' : '不存在'}'),
                    const SizedBox(height: 4),
                    Text('用户信息: ${_userInfo != null ? '已存在' : '不存在'}'),
                    if (_userInfo != null)
                      Text('用户信息: ${_userInfo!.length > 100 ? '${_userInfo!.substring(0, 100)}...' : _userInfo}'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // 用户信息
            const Text('用户信息', style: AppTextStyles.headline3),
            const SizedBox(height: 8),
            userAsync.when(
              data: (user) => Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('ID: ${user.id}'),
                      Text('用户名: ${user.username}'),
                      Text('昵称: ${user.nickname}'),
                      Text('手机: ${user.phone}'),
                      Text('邮箱: ${user.email ?? '无'}'),
                      Text('头像: ${user.avatar ?? '无'}'),
                      if (user.avatar != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 8),
                          child: ClipOval(
                            child: Image.network(
                              user.avatar!,
                              width: 60,
                              height: 60,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  width: 60,
                                  height: 60,
                                  color: Colors.grey.shade200,
                                  child: const Icon(Icons.error),
                                );
                              },
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text('加载失败: $error'),
            ),

            const SizedBox(height: 24),

            // 登录表单
            const Text('登录', style: AppTextStyles.headline3),
            const SizedBox(height: 8),
            TextField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: '手机号',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _passwordController,
              decoration: const InputDecoration(
                labelText: '密码',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton(
                  onPressed: _login,
                  child: const Text('登录'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _logout,
                  child: const Text('登出'),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // 更新个人信息
            const Text('更新个人信息', style: AppTextStyles.headline3),
            const SizedBox(height: 8),
            TextField(
              controller: _nicknameController,
              decoration: const InputDecoration(
                labelText: '昵称',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: '邮箱',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _updateProfile,
              child: const Text('更新个人信息'),
            ),

            const SizedBox(height: 24),

            // 头像上传
            const Text('头像上传', style: AppTextStyles.headline3),
            const SizedBox(height: 8),
            Row(
              children: [
                ElevatedButton(
                  onPressed: _pickAvatar,
                  child: const Text('选择头像'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _avatarFile != null ? _uploadAvatar : null,
                  child: const Text('上传头像'),
                ),
              ],
            ),
            if (_avatarFile != null)
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: ClipOval(
                  child: Image.file(
                    _avatarFile!,
                    width: 100,
                    height: 100,
                    fit: BoxFit.cover,
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // 测试结果
            const Text('测试结果', style: AppTextStyles.headline3),
            const SizedBox(height: 8),
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(_result),
              ),
          ],
        ),
      ),
    );
  }

  // 登录
  Future<void> _login() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _result = '正在登录...';
    });

    try {
      final phone = _phoneController.text.trim();
      final password = _passwordController.text.trim();

      if (phone.isEmpty || password.isEmpty) {
        if (!mounted) return;
        setState(() {
          _isLoading = false;
          _result = '手机号和密码不能为空';
        });
        return;
      }

      debugPrint('开始登录: phone=$phone, password=${password.substring(0, 3)}***');

      final result = await _userService.login(
        phone: phone,
        password: password,
      );

      debugPrint('登录成功，获取到令牌: ${result['accessToken'] ?? '无'}');

      // 刷新用户数据
      debugPrint('刷新用户数据...');
      final _ = await ref.refresh(userProvider.future);

      // 刷新认证数据
      debugPrint('刷新认证数据...');
      await _loadAuthData();

      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _result = '登录成功!\n访问令牌: ${result['accessToken']}\n刷新令牌: ${result['refreshToken']}';
      });
    } catch (e) {
      debugPrint('登录失败: $e');

      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _result = '登录失败: $e';
      });
    }
  }

  // 登出
  Future<void> _logout() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _result = '正在登出...';
    });

    try {
      debugPrint('开始登出...');

      await StorageUtils.clearAuthData();

      debugPrint('认证数据已清除');

      // 刷新认证数据
      debugPrint('刷新认证数据...');
      await _loadAuthData();

      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _result = '登出成功!';
      });
    } catch (e) {
      debugPrint('登出失败: $e');

      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _result = '登出失败: $e';
      });
    }
  }

  // 更新个人信息
  Future<void> _updateProfile() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _result = '正在更新个人信息...';
    });

    try {
      final nickname = _nicknameController.text.trim();
      final email = _emailController.text.trim();

      if (nickname.isEmpty && email.isEmpty) {
        if (!mounted) return;
        setState(() {
          _isLoading = false;
          _result = '昵称和邮箱至少填写一项';
        });
        return;
      }

      debugPrint('开始更新个人信息: nickname=$nickname, email=$email');

      final user = await _userService.updateUserProfile(
        nickname: nickname.isNotEmpty ? nickname : null,
        email: email.isNotEmpty ? email : null,
      );

      debugPrint('个人信息更新成功: ${user.nickname}, ${user.email}');

      // 刷新用户数据
      debugPrint('刷新用户数据...');
      final _ = await ref.refresh(userProvider.future);

      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _result = '个人信息更新成功!\n昵称: ${user.nickname}\n邮箱: ${user.email}';
      });
    } catch (e) {
      debugPrint('个人信息更新失败: $e');

      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _result = '个人信息更新失败: $e';
      });
    }
  }

  // 选择头像
  Future<void> _pickAvatar() async {
    if (!mounted) return;

    try {
      debugPrint('开始选择头像...');

      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null) {
        debugPrint('已选择头像: ${image.path}');

        if (!mounted) return;

        setState(() {
          _avatarFile = File(image.path);
          _result = '已选择头像: ${image.path}';
        });
      } else {
        debugPrint('未选择头像');
      }
    } catch (e) {
      debugPrint('选择头像失败: $e');

      if (!mounted) return;

      setState(() {
        _result = '选择头像失败: $e';
      });
    }
  }

  // 上传头像
  Future<void> _uploadAvatar() async {
    if (_avatarFile == null) return;
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _result = '正在上传头像...';
    });

    try {
      debugPrint('开始上传头像: ${_avatarFile!.path}');

      final avatarUrl = await _userService.uploadAvatar(_avatarFile!);

      debugPrint('头像上传成功: $avatarUrl');

      // 刷新用户数据
      debugPrint('刷新用户数据...');
      final _ = await ref.refresh(userProvider.future);

      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _result = '头像上传成功!\n头像URL: $avatarUrl';
      });
    } catch (e) {
      debugPrint('头像上传失败: $e');

      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _result = '头像上传失败: $e';
      });
    }
  }
}
