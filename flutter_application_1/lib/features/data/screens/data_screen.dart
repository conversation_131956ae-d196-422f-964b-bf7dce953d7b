import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:math' as math;
import '../../../shared/theme/constants.dart';
import '../../../core/services/hive_service.dart';
import '../../../core/models/focus_stats.dart';
import '../../../core/models/focus_record.dart';
import 'focus_data_simulator.dart';
import 'data_detail_screen.dart';

// 科目分布数据类
class SubjectDistribution {
  final String id;
  final String name;
  final double hours;
  final double percentage;

  SubjectDistribution({
    required this.id,
    required this.name,
    required this.hours,
    required this.percentage,
  });
}

// 圆环图绘制器
class PieChartPainter extends CustomPainter {
  final List<SubjectDistribution> data;
  final List<Color> colors;

  PieChartPainter(this.data, this.colors);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 3, size.height / 2);
    final radius = size.width / 4;

    double startAngle = -90 * (math.pi / 180); // 从上方开始（-90度）

    for (int i = 0; i < data.length; i++) {
      final sweepAngle = data[i].percentage * 2 * math.pi;

      final paint = Paint()
        ..style = PaintingStyle.fill
        ..color = colors[i % colors.length];

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );

      startAngle += sweepAngle;
    }

    // 绘制中心的白色圆形（形成环形）
    final centerPaint = Paint()
      ..style = PaintingStyle.fill
      ..color = Colors.white;

    canvas.drawCircle(center, radius * 0.6, centerPaint);
  }

  @override
  bool shouldRepaint(PieChartPainter oldDelegate) => true;
}

/// 数据页面
/// 显示用户的学习数据统计
class DataScreen extends ConsumerStatefulWidget {
  const DataScreen({super.key});

  @override
  ConsumerState<DataScreen> createState() => _DataScreenState();
}

class _DataScreenState extends ConsumerState<DataScreen> {
  // Hive服务
  final HiveService _hiveService = HiveService();

  // 是否已初始化
  bool _isInitialized = false;

  // 总专注时长（小时）
  double _totalFocusHours = 0;

  // 本周专注时长（小时）
  double _weeklyFocusHours = 0;

  // 连续打卡天数
  int _streakDays = 0;

  @override
  void initState() {
    super.initState();
    // 从Hive加载数据
    _loadData();
  }

  // 从Hive加载数据
  Future<void> _loadData() async {
    try {
      // 确保Hive服务已初始化
      await _hiveService.initHive();

      // 从FocusRecordRepository加载专注数据
      final focusStats = _hiveService.focusRecordRepository.getFocusStats();

      setState(() {
        _totalFocusHours = focusStats.totalDurationHours;
        _weeklyFocusHours = focusStats.getWeeklyDuration();
        // 计算连续打卡天数
        _streakDays = _calculateStreakDays(focusStats);
        _isInitialized = true;
      });
    } catch (e) {
      debugPrint('加载数据出错: $e');
      // 如果加载失败，使用默认值
      setState(() {
        _totalFocusHours = 0;
        _weeklyFocusHours = 0;
        _streakDays = 0;
        _isInitialized = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    return Scaffold(
      // 使用渐变背景，与应用其他页面保持一致
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.pageBackground,
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 顶部栏
                Padding(
                  padding: const EdgeInsets.fromLTRB(AppSizes.paddingMedium, AppSizes.paddingMedium, AppSizes.paddingMedium, 0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // 返回按钮和页面标题
                      Row(
                        children: [
                          // 返回按钮
                          Container(
                            height: 32,
                            width: 32,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              color: AppColors.background,
                            ),
                            child: IconButton(
                              icon: const Icon(Icons.arrow_back, size: 16),
                              tooltip: "返回",
                              color: AppColors.textSecondary,
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                              onPressed: () => Navigator.of(context).pop(),
                            ),
                          ),
                          const SizedBox(width: 12),
                          // 页面标题
                          const Text(
                            '数据分析',
                            style: AppTextStyles.headline2,
                          ),
                        ],
                      ),
                      // 数据模拟器入口 - 参考目标页面卡片编辑按钮样式
                      Container(
                        height: 32, // 保持高度一致
                        width: 32, // 保持宽度一致
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8), // 保持圆角一致
                          color: AppColors.background, // 使用背景色，不使用边框
                        ),
                        child: IconButton(
                          icon: const Icon(Icons.science_outlined, size: 16),
                          tooltip: "数据模拟器",
                          color: AppColors.textSecondary,
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const FocusDataSimulatorScreen(),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),

                // 内容区域
                Padding(
                  padding: const EdgeInsets.all(AppSizes.paddingMedium),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 副标题
                      const Text(
                        '学习数据',
                        style: AppTextStyles.headline3,
                      ),
                      const SizedBox(height: 24),

                      // 总览卡片
                      _buildOverviewCard(),
                      const SizedBox(height: AppSizes.paddingLarge),

                      // 每日专注时长图表
                      _buildDailyFocusChart(),
                      const SizedBox(height: AppSizes.paddingLarge),

                      // 科目分布图表
                      _buildSubjectDistributionChart(),
                      const SizedBox(height: AppSizes.paddingLarge),

                      // 专注数据分析入口
                      _buildFocusDataAnalysisCard(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 构建总览卡片
  Widget _buildOverviewCard() {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      decoration: AppDecorations.standardCard(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '总览',
            style: AppTextStyles.headline3,
          ),
          const SizedBox(height: 16),

          // 数据指标行
          Row(
            children: [
              // 总专注时长
              Expanded(
                child: _buildMetricItem(
                  icon: Icons.timer,
                  value: '${_totalFocusHours.toStringAsFixed(1)}h',
                  label: '总专注时长',
                  color: AppColors.primary,
                ),
              ),

              // 本周专注时长
              Expanded(
                child: _buildMetricItem(
                  icon: Icons.calendar_today,
                  value: '${_weeklyFocusHours.toStringAsFixed(1)}h',
                  label: '本周专注',
                  color: AppColors.secondary,
                ),
              ),

              // 连续打卡天数
              Expanded(
                child: _buildMetricItem(
                  icon: Icons.local_fire_department,
                  value: '$_streakDays天',
                  label: '连续打卡',
                  color: AppColors.accent,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建指标项
  Widget _buildMetricItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withAlpha(26), // 10% opacity (255 * 0.1 = 25.5)
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: AppTextStyles.headline3,
        ),
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  // 构建每日专注时长图表
  Widget _buildDailyFocusChart() {
    // 获取过去7天的专注数据
    final weeklyData = _getWeeklyFocusData();
    final weekdayNames = List.generate(7, (index) => _getWeekdayShortName(index));

    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      decoration: AppDecorations.standardCard(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '每日专注时长',
            style: AppTextStyles.headline3,
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: Row(
              children: [
                // Y轴刻度
                Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('${_getMaxValue(weeklyData).toInt()}h', style: AppTextStyles.bodySmall),
                    Text('${(_getMaxValue(weeklyData) / 2).toInt()}h', style: AppTextStyles.bodySmall),
                    Text('0h', style: AppTextStyles.bodySmall),
                    const SizedBox(height: 20), // 留出空间给X轴刻度
                  ],
                ),
                const SizedBox(width: 8),
                // 图表主体
                Expanded(
                  child: Column(
                    children: [
                      // 图表区域
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: List.generate(7, (index) {
                            final height = weeklyData[index] / _getMaxValue(weeklyData) * 150;
                            return Tooltip(
                              message: '${_getWeekdayName(index)}\n${weeklyData[index].toStringAsFixed(1)}小时',
                              child: Container(
                                width: 20,
                                height: height.clamp(0.0, 150.0),
                                decoration: BoxDecoration(
                                  color: AppColors.primary,
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(4),
                                    topRight: Radius.circular(4),
                                  ),
                                ),
                              ),
                            );
                          }),
                        ),
                      ),
                      // X轴刻度
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: List.generate(
                          7,
                          (index) => Text(
                            weekdayNames[index],
                            style: AppTextStyles.bodySmall,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 获取过去7天的专注数据
  List<double> _getWeeklyFocusData() {
    final List<double> data = List.filled(7, 0.0);
    final now = DateTime.now();

    try {
      // 获取所有专注记录
      final records = _hiveService.focusRecordRepository.getAllFocusRecords();

      // 遍历过去7天
      for (int i = 0; i < 7; i++) {
        final day = now.subtract(Duration(days: i));
        final dayStart = DateTime(day.year, day.month, day.day);
        final dayEnd = DateTime(day.year, day.month, day.day, 23, 59, 59);

        // 计算当天的专注时长
        double dayHours = 0.0;
        for (final record in records) {
          if (record.startTime.isAfter(dayStart) && record.startTime.isBefore(dayEnd)) {
            dayHours += record.durationSeconds / 3600.0;
          }
        }

        // 存储数据（倒序，最近的日期在右侧）
        data[6 - i] = dayHours;
      }
    } catch (e) {
      debugPrint('获取每日专注数据出错: $e');
    }

    return data;
  }

  // 获取最大值
  double _getMaxValue(List<double> data) {
    if (data.isEmpty) return 1.0;
    return data.reduce((a, b) => a > b ? a : b).clamp(1.0, double.infinity);
  }

  // 获取星期名称
  String _getWeekdayName(int index) {
    final now = DateTime.now();
    final day = now.subtract(Duration(days: 6 - index));

    switch (day.weekday) {
      case 1: return '星期一';
      case 2: return '星期二';
      case 3: return '星期三';
      case 4: return '星期四';
      case 5: return '星期五';
      case 6: return '星期六';
      case 7: return '星期日';
      default: return '';
    }
  }

  // 获取星期简称
  String _getWeekdayShortName(int index) {
    final now = DateTime.now();
    final day = now.subtract(Duration(days: 6 - index));

    switch (day.weekday) {
      case 1: return '一';
      case 2: return '二';
      case 3: return '三';
      case 4: return '四';
      case 5: return '五';
      case 6: return '六';
      case 7: return '日';
      default: return '';
    }
  }

  // 构建科目分布图表
  Widget _buildSubjectDistributionChart() {
    // 获取科目分布数据
    final subjectData = _getSubjectDistributionData();

    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      decoration: AppDecorations.standardCard(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '科目分布',
            style: AppTextStyles.headline3,
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: subjectData.isEmpty
                ? const Center(child: Text('暂无科目数据'))
                : Column(
                    children: [
                      // 图表区域
                      Expanded(
                        child: Row(
                          children: [
                            // 圆环图
                            Expanded(
                              flex: 3,
                              child: CustomPaint(
                                painter: PieChartPainter(subjectData, _getSubjectColors()),
                                child: Container(),
                              ),
                            ),
                            // 图例
                            Expanded(
                              flex: 2,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: List.generate(
                                  subjectData.length,
                                  (index) => Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 4),
                                    child: Row(
                                      children: [
                                        Container(
                                          width: 12,
                                          height: 12,
                                          decoration: BoxDecoration(
                                            color: _getSubjectColor(index),
                                            shape: BoxShape.circle,
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            '${subjectData[index].name} (${(subjectData[index].percentage * 100).toInt()}%)',
                                            style: AppTextStyles.bodySmall,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  // 获取科目分布数据
  List<SubjectDistribution> _getSubjectDistributionData() {
    final List<SubjectDistribution> data = [];

    try {
      // 获取所有专注记录
      final records = _hiveService.focusRecordRepository.getAllFocusRecords();
      if (records.isEmpty) return data;

      // 按科目分组
      final Map<String, double> subjectHours = {};
      double totalHours = 0;

      // 计算每个科目的专注时长
      for (final record in records) {
        final subjectId = record.subjectId;
        final hours = record.durationSeconds / 3600.0;

        if (!subjectHours.containsKey(subjectId)) {
          subjectHours[subjectId] = 0;
        }

        subjectHours[subjectId] = (subjectHours[subjectId] ?? 0) + hours;
        totalHours += hours;
      }

      // 获取科目名称
      for (final entry in subjectHours.entries) {
        final subject = _hiveService.subjectRepository.getSubjectById(entry.key);
        if (subject != null) {
          data.add(SubjectDistribution(
            id: subject.id,
            name: subject.name,
            hours: entry.value,
            percentage: totalHours > 0 ? entry.value / totalHours : 0,
          ));
        }
      }

      // 按百分比降序排序
      data.sort((a, b) => b.percentage.compareTo(a.percentage));

      // 只取前5个，其余的合并为“其他”
      if (data.length > 5) {
        double otherHours = 0;
        double otherPercentage = 0;

        for (int i = 5; i < data.length; i++) {
          otherHours += data[i].hours;
          otherPercentage += data[i].percentage;
        }

        final subList = data.sublist(0, 5);
        subList.add(SubjectDistribution(
          id: 'other',
          name: '其他',
          hours: otherHours,
          percentage: otherPercentage,
        ));

        return subList;
      }
    } catch (e) {
      debugPrint('获取科目分布数据出错: $e');
    }

    return data;
  }

  // 获取科目颜色
  Color _getSubjectColor(int index) {
    final colors = _getSubjectColors();
    return colors[index % colors.length];
  }

  // 获取科目颜色列表
  List<Color> _getSubjectColors() {
    return [
      AppColors.primary,
      Colors.blue,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.grey,
    ];
  }



  // 计算连续打卡天数
  int _calculateStreakDays(FocusStats stats) {
    // 获取所有专注记录
    final records = _hiveService.focusRecordRepository.getAllFocusRecords();
    if (records.isEmpty) return 0;

    // 按日期分组记录
    final Map<String, List<FocusRecord>> recordsByDate = {};
    for (final record in records) {
      final date = '${record.startTime.year}-${record.startTime.month.toString().padLeft(2, '0')}-${record.startTime.day.toString().padLeft(2, '0')}';
      if (!recordsByDate.containsKey(date)) {
        recordsByDate[date] = [];
      }
      recordsByDate[date]!.add(record);
    }

    // 获取所有有专注记录的日期，并按时间降序排序
    final dates = recordsByDate.keys.toList();
    dates.sort((a, b) => b.compareTo(a)); // 降序排序，最近的日期在前

    if (dates.isEmpty) return 0;

    // 直接从最近的日期开始计算

    // 开始计算连续天数
    int streakDays = 0;

    for (int i = 0; i < dates.length; i++) {
      final dateStr = dates[i];
      final date = DateTime.parse(dateStr);

      // 如果是连续的日期，增加连续天数
      if (i == 0 || _isConsecutiveDay(DateTime.parse(dates[i-1]), date)) {
        streakDays++;
      } else {
        // 如果不是连续的，结束计算
        break;
      }
    }

    return streakDays;
  }

  // 检查两个日期是否连续
  bool _isConsecutiveDay(DateTime date1, DateTime date2) {
    // 计算两个日期之间的差距（天数）
    final difference = date1.difference(date2).inDays;
    return difference == 1; // 如果相差1天，则是连续的
  }

  // 构建专注数据分析入口卡片
  Widget _buildFocusDataAnalysisCard() {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const DataDetailScreen()),
        );
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [AppShadows.low],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: const BoxDecoration(
                color: Color(0x1A4CAF50), // AppColors.primary with 10% opacity
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.analytics,
                color: AppColors.primary,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '专注数据分析',
                    style: AppTextStyles.headline3,
                  ),
                  Text(
                    '查看详细的专注数据统计和分析',
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              color: AppColors.textSecondary,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
