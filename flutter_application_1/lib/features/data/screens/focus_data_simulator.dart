import 'dart:math';

import 'package:flutter/material.dart';
import 'package:limefocus/core/models/focus_record.dart';
import 'package:limefocus/core/models/subject_project.dart';
import 'package:limefocus/core/services/hive_service.dart';
// 使用标准颜色
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

/// 专注数据模拟器页面
/// 用于快速生成测试数据，以便开发和测试数据分析页面
class FocusDataSimulatorScreen extends ConsumerStatefulWidget {
  const FocusDataSimulatorScreen({super.key});

  @override
  ConsumerState<FocusDataSimulatorScreen> createState() => _FocusDataSimulatorScreenState();
}

class _FocusDataSimulatorScreenState extends ConsumerState<FocusDataSimulatorScreen> {
  final HiveService _hiveService = HiveService();

  // 表单控制器
  final TextEditingController _durationController = TextEditingController(text: '25');
  final TextEditingController _countController = TextEditingController(text: '10');

  // 日期选择
  DateTime _selectedDate = DateTime.now();

  // 选择的项目和科目
  Project? _selectedProject;
  Subject? _selectedSubject;

  // 所有项目和科目
  List<Project> _projects = [];
  List<Subject> _subjects = [];

  // 生成的记录
  List<FocusRecord> _generatedRecords = [];

  // 加载状态
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _durationController.dispose();
    _countController.dispose();
    super.dispose();
  }

  // 加载项目和科目数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _hiveService.initHive();

      // 加载所有科目
      final subjects = _hiveService.subjectRepository.getAllSubjects();

      // 加载所有项目
      final List<Project> allProjects = [];
      for (final subject in subjects) {
        final projects = _hiveService.subjectRepository.getProjectsBySubjectId(subject.id);
        allProjects.addAll(projects);
      }

      setState(() {
        _subjects = subjects;
        _projects = allProjects;

        // 默认选择第一个科目和项目（如果有）
        if (_subjects.isNotEmpty) {
          _selectedSubject = _subjects.first;
        }

        if (_projects.isNotEmpty) {
          _selectedProject = _projects.first;
        }

        _isLoading = false;
      });
    } catch (e) {
      debugPrint('加载数据出错: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 生成单条专注记录
  Future<void> _generateSingleRecord() async {
    if (_selectedProject == null || _selectedSubject == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先选择项目和科目')),
      );
      return;
    }

    final int durationMinutes = int.tryParse(_durationController.text) ?? 25;

    // 创建专注记录
    final focusRecord = FocusRecord(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      projectId: _selectedProject!.id,
      subjectId: _selectedSubject!.id,
      startTime: _selectedDate,
      endTime: _selectedDate.add(Duration(minutes: durationMinutes)),
      durationSeconds: durationMinutes * 60,
      isCountdown: true,
      plannedDurationMinutes: durationMinutes,
      status: FocusRecordStatus.completed,
      interruptionCount: 0,
      notes: '模拟生成的专注记录',
    );

    // 保存记录
    await _hiveService.focusRecordRepository.saveFocusRecord(focusRecord);

    // 更新项目的专注时间
    final double focusHours = durationMinutes / 60.0;

    // 如果项目开启了进度追踪且是专注时间模式，更新进度
    if (_selectedProject!.isTrackingEnabled &&
        _selectedProject!.trackingMode == ProgressTrackingMode.focusTime) {
      final double newCurrentFocusHours = (_selectedProject!.currentFocusHours ?? 0) + focusHours;
      double newProgress = 0.0;

      if (_selectedProject!.totalFocusHours != null && _selectedProject!.totalFocusHours! > 0) {
        newProgress = (newCurrentFocusHours / _selectedProject!.totalFocusHours!).clamp(0.0, 1.0);
      }

      final updatedProject = _selectedProject!.copyWith(
        focusedHours: _selectedProject!.focusedHours + focusHours,
        currentFocusHours: newCurrentFocusHours,
        progress: newProgress,
      );

      await _hiveService.subjectRepository.saveProject(updatedProject);
    } else {
      // 只更新总专注时间
      final updatedProject = _selectedProject!.copyWith(
        focusedHours: _selectedProject!.focusedHours + focusHours,
      );

      await _hiveService.subjectRepository.saveProject(updatedProject);
    }

    setState(() {
      _generatedRecords.add(focusRecord);
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('已生成 $durationMinutes 分钟的专注记录')),
      );
    }
  }

  // 批量生成专注记录
  Future<void> _generateBatchRecords() async {
    if (_selectedProject == null || _selectedSubject == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先选择项目和科目')),
      );
      return;
    }

    final int count = int.tryParse(_countController.text) ?? 10;
    final Random random = Random();

    // 显示进度对话框
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在生成专注记录...'),
            ],
          ),
        ),
      );
    }

    // 生成记录
    final List<FocusRecord> records = [];
    double totalFocusHours = 0;

    // 从选择的日期开始，向前生成记录
    DateTime currentDate = _selectedDate;

    for (int i = 0; i < count; i++) {
      // 生成更真实的专注时长，通常是25「30「45「60分钟
      final List<int> commonDurations = [25, 30, 45, 60];
      final int durationMinutes = commonDurations[random.nextInt(commonDurations.length)];

      // 生成更真实的时间分布，根据一天中的不同时段调整概率
      int hour;
      final timeDistribution = random.nextDouble();

      if (timeDistribution < 0.2) {
        // 早上（6-9点）
        hour = random.nextInt(4) + 6;
      } else if (timeDistribution < 0.5) {
        // 上午（9-12点）
        hour = random.nextInt(3) + 9;
      } else if (timeDistribution < 0.7) {
        // 下午（14-18点）
        hour = random.nextInt(4) + 14;
      } else {
        // 晚上（19-23点）
        hour = random.nextInt(5) + 19;
      }

      final int minute = random.nextInt(60);

      final DateTime startTime = DateTime(
        currentDate.year,
        currentDate.month,
        currentDate.day,
        hour,
        minute,
      );

      final DateTime endTime = startTime.add(Duration(minutes: durationMinutes));

      // 创建专注记录，大部分是完成的，少部分是中断的
      final focusRecord = FocusRecord(
        id: '${DateTime.now().millisecondsSinceEpoch}_$i',
        projectId: _selectedProject!.id,
        subjectId: _selectedSubject!.id,
        startTime: startTime,
        endTime: endTime,
        durationSeconds: durationMinutes * 60,
        isCountdown: true, // 大多数专注都是倒计时的
        plannedDurationMinutes: durationMinutes,
        status: random.nextDouble() > 0.15 // 只有15%的概率是中断的
            ? FocusRecordStatus.completed
            : FocusRecordStatus.interrupted,
        interruptionCount: random.nextDouble() > 0.8 ? random.nextInt(2) + 1 : 0, // 大多数没有中断
        notes: '模拟专注记录 #$i',
      );

      records.add(focusRecord);

      // 累计专注时间
      final double focusHours = durationMinutes / 60.0;
      totalFocusHours += focusHours;

      // 生成更真实的日期分布，周末的专注记录少一些
      if ((i + 1) % (random.nextInt(2) + 2) == 0) {
        currentDate = currentDate.subtract(const Duration(days: 1));

        // 如果是周末，有更高的概率再往前跳一天
        if (currentDate.weekday == 6 || currentDate.weekday == 7) {
          if (random.nextDouble() < 0.7) {
            currentDate = currentDate.subtract(const Duration(days: 1));
          }
        }
      }
    }

    // 保存所有记录
    for (final record in records) {
      await _hiveService.focusRecordRepository.saveFocusRecord(record);
    }

    // 更新项目的专注时间
    if (_selectedProject!.isTrackingEnabled &&
        _selectedProject!.trackingMode == ProgressTrackingMode.focusTime) {
      final double newCurrentFocusHours = (_selectedProject!.currentFocusHours ?? 0) + totalFocusHours;
      double newProgress = 0.0;

      if (_selectedProject!.totalFocusHours != null && _selectedProject!.totalFocusHours! > 0) {
        newProgress = (newCurrentFocusHours / _selectedProject!.totalFocusHours!).clamp(0.0, 1.0);
      }

      final updatedProject = _selectedProject!.copyWith(
        focusedHours: _selectedProject!.focusedHours + totalFocusHours,
        currentFocusHours: newCurrentFocusHours,
        progress: newProgress,
      );

      await _hiveService.subjectRepository.saveProject(updatedProject);
    } else {
      // 只更新总专注时间
      final updatedProject = _selectedProject!.copyWith(
        focusedHours: _selectedProject!.focusedHours + totalFocusHours,
      );

      await _hiveService.subjectRepository.saveProject(updatedProject);
    }

    // 关闭进度对话框
    if (mounted) {
      Navigator.of(context).pop();
    }

    setState(() {
      _generatedRecords = records;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('已生成 $count 条专注记录，总时长 ${totalFocusHours.toStringAsFixed(2)} 小时')),
      );
    }
  }

  // 生成更真实的专注记录
  Future<void> _generateRealisticRecords() async {
    if (_selectedProject == null || _selectedSubject == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先选择项目和科目')),
      );
      return;
    }

    // 生成更多的记录，以模拟真实的使用情况
    final Random random = Random();

    // 显示进度对话框
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在生成真实专注数据...'),
            ],
          ),
        ),
      );
    }

    // 生成记录
    final List<FocusRecord> records = [];
    double totalFocusHours = 0;

    // 从选择的日期开始，向前生成记录
    DateTime currentDate = _selectedDate;

    // 模拟一个月的专注记录
    for (int day = 0; day < 30; day++) {
      // 周末的专注记录少一些
      int dailyRecordsCount;
      if (currentDate.weekday == 6 || currentDate.weekday == 7) { // 周六或周日
        dailyRecordsCount = random.nextInt(2); // 0-1条记录
      } else {
        dailyRecordsCount = random.nextInt(3) + 1; // 1-3条记录
      }

      // 生成当天的专注记录
      for (int i = 0; i < dailyRecordsCount; i++) {
        // 生成更真实的专注时长，通常是25「30「45「60分钟
        final List<int> commonDurations = [25, 25, 30, 30, 45, 60]; // 增加了25和30分钟的概率
        final int durationMinutes = commonDurations[random.nextInt(commonDurations.length)];

        // 生成更真实的时间分布，根据一天中的不同时段调整概率
        int hour;
        final timeDistribution = random.nextDouble();

        if (timeDistribution < 0.15) {
          // 早上（6-9点）
          hour = random.nextInt(4) + 6;
        } else if (timeDistribution < 0.45) {
          // 上午（9-12点）
          hour = random.nextInt(3) + 9;
        } else if (timeDistribution < 0.75) {
          // 下午（14-18点）
          hour = random.nextInt(4) + 14;
        } else {
          // 晚上（19-23点）
          hour = random.nextInt(5) + 19;
        }

        final int minute = random.nextInt(60);

        final DateTime startTime = DateTime(
          currentDate.year,
          currentDate.month,
          currentDate.day,
          hour,
          minute,
        );

        final DateTime endTime = startTime.add(Duration(minutes: durationMinutes));

        // 创建专注记录，大部分是完成的，少部分是中断的
        final focusRecord = FocusRecord(
          id: '${DateTime.now().millisecondsSinceEpoch}_${day}_$i',
          projectId: _selectedProject!.id,
          subjectId: _selectedSubject!.id,
          startTime: startTime,
          endTime: endTime,
          durationSeconds: durationMinutes * 60,
          isCountdown: true, // 大多数专注都是倒计时的
          plannedDurationMinutes: durationMinutes,
          status: random.nextDouble() > 0.1 // 只有10%的概率是中断的
              ? FocusRecordStatus.completed
              : FocusRecordStatus.interrupted,
          interruptionCount: random.nextDouble() > 0.85 ? random.nextInt(2) + 1 : 0, // 大多数没有中断
          notes: '真实模式专注 #${day}_$i',
        );

        records.add(focusRecord);

        // 累计专注时间
        final double focusHours = durationMinutes / 60.0;
        totalFocusHours += focusHours;
      }

      // 往前推一天
      currentDate = currentDate.subtract(const Duration(days: 1));
    }

    // 保存所有记录
    for (final record in records) {
      await _hiveService.focusRecordRepository.saveFocusRecord(record);
    }

    // 更新项目的专注时间
    if (_selectedProject!.isTrackingEnabled && _selectedProject!.trackingMode == ProgressTrackingMode.focusTime) {
      final updatedProject = _selectedProject!.copyWith(
        focusedHours: _selectedProject!.focusedHours + totalFocusHours,
      );

      await _hiveService.subjectRepository.saveProject(updatedProject);
    }

    // 关闭进度对话框
    if (mounted && Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    }

    setState(() {
      _generatedRecords = records;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('已生成 ${records.length} 条专注记录，总时长 ${totalFocusHours.toStringAsFixed(2)} 小时')),
      );
    }
  }

  // 清除所有生成的记录
  Future<void> _clearAllRecords() async {
    // 显示确认对话框
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清除所有记录'),
        content: const Text('确定要清除所有专注记录吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    // 显示进度对话框
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在清除专注记录...'),
            ],
          ),
        ),
      );
    }

    try {
      // 清除所有专注记录
      await _hiveService.focusRecordRepository.clearAllRecords();

      // 重置所有项目的专注时间
      for (final project in _projects) {
        final updatedProject = project.copyWith(
          focusedHours: 0,
          currentFocusHours: 0,
          progress: 0,
        );

        await _hiveService.subjectRepository.saveProject(updatedProject);
      }

      // 关闭进度对话框
      if (mounted) {
        Navigator.of(context).pop();
      }

      setState(() {
        _generatedRecords = [];
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('已清除所有专注记录')),
        );
      }
    } catch (e) {
      // 关闭进度对话框
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('清除记录失败: $e')),
        );
      }
    }
  }

  // 选择日期
  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('专注数据模拟器'),
        // 添加返回按钮
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _clearAllRecords,
            tooltip: '清除所有记录',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 参数设置卡片
                  Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('参数设置', style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                          const SizedBox(height: 16),

                          // 日期选择
                          Row(
                            children: [
                              const Text('日期: '),
                              TextButton(
                                onPressed: _selectDate,
                                child: Text(
                                  DateFormat('yyyy-MM-dd').format(_selectedDate),
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 8),

                          // 科目选择
                          DropdownButtonFormField<Subject>(
                            decoration: const InputDecoration(
                              labelText: '选择科目',
                              border: OutlineInputBorder(),
                            ),
                            value: _selectedSubject,
                            items: _subjects.map((subject) {
                              return DropdownMenuItem<Subject>(
                                value: subject,
                                child: Text(subject.name),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedSubject = value;

                                // 更新项目列表为该科目下的项目
                                if (value != null) {
                                  // 从所有项目中筛选出当前科目的项目
                                  final filteredProjects = _projects.where((p) => p.subjectId == value.id).toList();
                                  setState(() {
                                    _selectedProject = filteredProjects.isNotEmpty ? filteredProjects.first : null;
                                  });
                                }
                              });
                            },
                          ),

                          const SizedBox(height: 16),

                          // 项目选择
                          DropdownButtonFormField<Project>(
                            decoration: const InputDecoration(
                              labelText: '选择项目',
                              border: OutlineInputBorder(),
                            ),
                            value: _selectedProject,
                            items: _projects.map((project) {
                              return DropdownMenuItem<Project>(
                                value: project,
                                child: Text(project.name),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedProject = value;
                              });
                            },
                          ),

                          const SizedBox(height: 16),

                          // 单条记录参数
                          Row(
                            children: [
                              Expanded(
                                child: TextField(
                                  controller: _durationController,
                                  decoration: const InputDecoration(
                                    labelText: '专注时长(分钟)',
                                    border: OutlineInputBorder(),
                                  ),
                                  keyboardType: TextInputType.number,
                                ),
                              ),
                              const SizedBox(width: 16),
                              ElevatedButton(
                                onPressed: _generateSingleRecord,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.green,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                ),
                                child: const Text('生成单条记录'),
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),

                          // 批量生成参数
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              TextField(
                                controller: _countController,
                                decoration: const InputDecoration(
                                  labelText: '生成数量',
                                  border: OutlineInputBorder(),
                                ),
                                keyboardType: TextInputType.number,
                              ),
                              const SizedBox(height: 16),
                              Row(
                                children: [
                                  Expanded(
                                    child: ElevatedButton(
                                      onPressed: _generateBatchRecords,
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.green,
                                        foregroundColor: Colors.white,
                                        padding: const EdgeInsets.symmetric(vertical: 16),
                                      ),
                                      child: const Text('批量生成'),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: ElevatedButton(
                                      onPressed: _generateRealisticRecords,
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.blue,
                                        foregroundColor: Colors.white,
                                        padding: const EdgeInsets.symmetric(vertical: 16),
                                      ),
                                      child: const Text('真实模式'),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  // 生成的记录列表
                  Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('生成的记录', style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                          const SizedBox(height: 16),

                          _generatedRecords.isEmpty
                              ? const Center(
                                  child: Padding(
                                    padding: EdgeInsets.all(16.0),
                                    child: Text('暂无记录，请先生成数据'),
                                  ),
                                )
                              : Container(
                                  constraints: const BoxConstraints(maxHeight: 300),
                                  child: ListView.builder(
                                    shrinkWrap: true,
                                    itemCount: _generatedRecords.length > 10 ? 10 : _generatedRecords.length,
                                    itemBuilder: (context, index) {
                                      final record = _generatedRecords[index];
                                      final durationMinutes = record.durationSeconds ~/ 60;

                                      return Card(
                                        margin: const EdgeInsets.only(bottom: 8),
                                        child: ListTile(
                                          title: Text(
                                            '${DateFormat('yyyy-MM-dd HH:mm').format(record.startTime)} - $durationMinutes分钟',
                                            style: const TextStyle(fontWeight: FontWeight.bold),
                                          ),
                                          subtitle: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text('项目: ${_projects.firstWhere((p) => p.id == record.projectId).name}'),
                                              Text('状态: ${record.status == FocusRecordStatus.completed ? '完成' : '中断'}'),
                                              if (record.interruptionCount > 0)
                                                Text('中断次数: ${record.interruptionCount}'),
                                            ],
                                          ),
                                          trailing: Text(
                                            '$durationMinutes分钟',
                                            style: TextStyle(
                                              color: record.status == FocusRecordStatus.completed
                                                  ? Colors.green
                                                  : Colors.orange,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),

                          if (_generatedRecords.length > 10) ...[
                            const SizedBox(height: 8),
                            Center(
                              child: Text('共生成 ${_generatedRecords.length} 条记录，只显示前10条',
                                style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
