import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'dart:math' as math;
import '../../../shared/theme/constants.dart';
import '../../../core/services/hive_service.dart';
import '../../../core/models/focus_record.dart';
import '../../../core/models/subject_project.dart';
import '../widgets/data_chart_widget.dart';

// 网格线绘制器
class GridPainter extends CustomPainter {
  final int horizontalLinesCount;
  final int verticalLinesCount;

  GridPainter({this.horizontalLinesCount = 4, this.verticalLinesCount = 6});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.shade200
      ..strokeWidth = 1;

    // 绘制水平线
    for (int i = 0; i <= horizontalLinesCount; i++) {
      final y = size.height - (i * size.height / horizontalLinesCount);
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }

    // 绘制垂直线
    for (int i = 0; i <= verticalLinesCount; i++) {
      final x = i * size.width / verticalLinesCount;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }
  }

  @override
  bool shouldRepaint(GridPainter oldDelegate) =>
      oldDelegate.horizontalLinesCount != horizontalLinesCount ||
      oldDelegate.verticalLinesCount != verticalLinesCount;
}

// 时间周期类型
enum TimePeriod { day, week, month, year }

/// 数据详情页面
/// 显示特定维度（总体/科目/项目）的详细数据分析
class DataDetailScreen extends ConsumerStatefulWidget {
  final String? subjectId; // 如果为null，则显示总体数据
  final String? projectId; // 如果为null，则显示科目数据或总体数据

  const DataDetailScreen({
    super.key,
    this.subjectId,
    this.projectId,
  });

  @override
  ConsumerState<DataDetailScreen> createState() => _DataDetailScreenState();
}

class _DataDetailScreenState extends ConsumerState<DataDetailScreen> with SingleTickerProviderStateMixin {
  final HiveService _hiveService = HiveService();

  // 标签页控制器
  late TabController _tabController;

  // 数据
  List<FocusRecord> _allRecords = [];
  List<FocusRecord> _filteredRecords = [];

  // 标题
  String _title = '总体数据';

  // 科目或项目对象
  Subject? _subject;
  Project? _project;

  // 时间范围
  String _timeRange = '全部';
  final List<String> _timeRanges = ['今日', '本周', '本月', '全部'];

  // 加载状态
  bool _isLoading = true;

  // 记录当前标签页索引
  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);

    // 添加标签页切换监听
    _tabController.addListener(_handleTabChange);

    _loadData();
  }

  // 处理标签页切换
  void _handleTabChange() {
    // 如果标签页确实发生了变化
    if (_tabController.index != _currentTabIndex) {
      // 如果是切换到专注标签页（索引为1）
      if (_tabController.index == 1) {
        // 重置初始化标记，使得下次进入时可以重新初始化
        setState(() {
          _trendTabInitialized = false;
        });
      }

      // 更新当前标签页索引
      _currentTabIndex = _tabController.index;
    }
  }

  @override
  void dispose() {
    // 移除监听器
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  // 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 初始化Hive
      await _hiveService.initHive();

      // 获取所有专注记录
      _allRecords = _hiveService.focusRecordRepository.getAllFocusRecords();

      // 如果有科目ID，获取科目信息
      if (widget.subjectId != null) {
        _subject = _hiveService.subjectRepository.getSubjectById(widget.subjectId!);
        if (_subject != null) {
          _title = '${_subject!.name}的数据';

          // 过滤该科目的记录
          _allRecords = _allRecords.where((record) => record.subjectId == widget.subjectId).toList();
        }
      }

      // 如果有项目ID，获取项目信息
      if (widget.projectId != null && widget.subjectId != null) {
        _project = _hiveService.subjectRepository.getProjectById(widget.projectId!);
        if (_project != null) {
          _title = '${_project!.name}的数据';

          // 过滤该项目的记录
          _allRecords = _allRecords.where((record) => record.projectId == widget.projectId).toList();
        }
      }

      // 默认显示所有记录
      _filteredRecords = List.from(_allRecords);

      // 按时间排序（最新的在前）
      _filteredRecords.sort((a, b) => b.startTime.compareTo(a.startTime));

    } catch (e) {
      debugPrint('加载数据出错: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 过滤记录
  void _filterRecords(String timeRange) {
    setState(() {
      _timeRange = timeRange;

      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      switch (timeRange) {
        case '今日':
          _filteredRecords = _allRecords.where((record) {
            final recordDate = DateTime(
              record.startTime.year,
              record.startTime.month,
              record.startTime.day,
            );
            return recordDate.isAtSameMomentAs(today);
          }).toList();
          break;

        case '本周':
          // 计算本周的开始（周一）
          final weekStart = today.subtract(Duration(days: today.weekday - 1));

          _filteredRecords = _allRecords.where((record) {
            final recordDate = DateTime(
              record.startTime.year,
              record.startTime.month,
              record.startTime.day,
            );
            return recordDate.isAfter(weekStart.subtract(const Duration(days: 1))) &&
                   recordDate.isBefore(weekStart.add(const Duration(days: 7)));
          }).toList();
          break;

        case '本月':
          // 本月的开始
          final monthStart = DateTime(now.year, now.month, 1);
          // 下个月的开始
          final nextMonth = now.month < 12
              ? DateTime(now.year, now.month + 1, 1)
              : DateTime(now.year + 1, 1, 1);

          _filteredRecords = _allRecords.where((record) {
            final recordDate = record.startTime;
            return recordDate.isAfter(monthStart.subtract(const Duration(days: 1))) &&
                   recordDate.isBefore(nextMonth);
          }).toList();
          break;

        case '全部':
        default:
          _filteredRecords = List.from(_allRecords);
          break;
      }

      // 按时间排序（最新的在前）
      _filteredRecords.sort((a, b) => b.startTime.compareTo(a.startTime));
    });
  }

  // 计算总专注时长（小时）
  double _calculateTotalHours() {
    if (_filteredRecords.isEmpty) return 0;

    final totalSeconds = _filteredRecords.fold<int>(
      0, (sum, record) => sum + record.durationSeconds);

    return totalSeconds / 3600.0;
  }

  // 计算平均专注时长（分钟）
  double _calculateAverageMinutes() {
    if (_filteredRecords.isEmpty) return 0;

    final totalSeconds = _filteredRecords.fold<int>(
      0, (sum, record) => sum + record.durationSeconds);

    return (totalSeconds / 60.0) / _filteredRecords.length;
  }

  // 计算专注次数
  int _calculateSessionCount() {
    return _filteredRecords.length;
  }

  // 计算中断次数
  int _calculateInterruptionCount() {
    return _filteredRecords.where((record) =>
      record.status == FocusRecordStatus.interrupted).length;
  }

  // 计算完成率
  double _calculateCompletionRate() {
    if (_filteredRecords.isEmpty) return 0;

    final completedCount = _filteredRecords.where((record) =>
      record.status == FocusRecordStatus.completed).length;

    return completedCount / _filteredRecords.length;
  }

  // 获取最佳专注时段
  int _getBestFocusHour() {
    if (_filteredRecords.isEmpty) return 0;

    // 按小时统计专注时长
    final Map<int, int> hourlyDuration = {};

    for (final record in _filteredRecords) {
      final hour = record.startTime.hour;

      if (!hourlyDuration.containsKey(hour)) {
        hourlyDuration[hour] = 0;
      }

      hourlyDuration[hour] = hourlyDuration[hour]! + record.durationSeconds;
    }

    // 找出专注时长最长的小时
    int bestHour = 0;
    int maxDuration = 0;

    hourlyDuration.forEach((hour, duration) {
      if (duration > maxDuration) {
        maxDuration = duration;
        bestHour = hour;
      }
    });

    return bestHour;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        title: Text(
          _title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        actions: [
          // 时间范围选择
          PopupMenuButton<String>(
            icon: const Icon(Icons.calendar_today, color: Colors.white),
            tooltip: '选择时间范围',
            onSelected: _filterRecords,
            itemBuilder: (context) => _timeRanges.map((range) {
              return PopupMenuItem<String>(
                value: range,
                child: Text(
                  range,
                  style: TextStyle(
                    fontWeight: _timeRange == range ? FontWeight.bold : FontWeight.normal,
                    color: _timeRange == range ? AppColors.primary : null,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          labelStyle: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          unselectedLabelStyle: const TextStyle(fontSize: 16),
          tabs: const [
            Tab(text: '概览'),
            Tab(text: '专注'),
            Tab(text: '项目'),
            Tab(text: '记录'),
            Tab(text: '分析'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                // 概览标签页
                _buildOverviewTab(),

                // 趋势标签页
                _buildTrendTab(),

                // 项目标签页
                _buildProjectsTab(),

                // 记录标签页
                _buildRecordsTab(),

                // 分析标签页
                _buildAnalysisTab(),
              ],
            ),
    );
  }

  // 构建概览标签页
  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 时间范围提示
          Text(
            '$_timeRange数据概览',
            style: AppTextStyles.headline2,
          ),
          const SizedBox(height: 16),

          // 数据卡片网格
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            childAspectRatio: 1.5,
            children: [
              // 总专注时长卡片
              _buildDataCard(
                title: '总专注时长',
                value: '${_calculateTotalHours().toStringAsFixed(1)}小时',
                icon: Icons.access_time,
                color: AppColors.primary,
              ),

              // 平均专注时长卡片
              _buildDataCard(
                title: '平均专注时长',
                value: '${_calculateAverageMinutes().toStringAsFixed(1)}分钟',
                icon: Icons.timer,
                color: Colors.orange,
              ),

              // 专注次数卡片
              _buildDataCard(
                title: '专注次数',
                value: '${_calculateSessionCount()}次',
                icon: Icons.repeat,
                color: Colors.blue,
              ),

              // 中断次数卡片
              _buildDataCard(
                title: '中断次数',
                value: '${_calculateInterruptionCount()}次',
                icon: Icons.pause_circle_outline,
                color: Colors.red,
              ),
            ],
          ),

          const SizedBox(height: 24),

          // 完成率卡片
          _buildCompletionRateCard(),

          const SizedBox(height: 24),

          // 最佳专注时段卡片
          _buildBestFocusTimeCard(),
        ],
      ),
    );
  }

  // 当前选择的时间周期
  TimePeriod _selectedPeriod = TimePeriod.week;

  // 当前选择的日期
  DateTime _selectedDate = DateTime.now();

  // 记录是否已经初始化过标签页
  bool _trendTabInitialized = false;

  // 构建专注标签页
  Widget _buildTrendTab() {
    // 只在首次进入标签页时自动跳转回今天
    if (!_trendTabInitialized) {
      _trendTabInitialized = true;
      // 使用延迟执行，确保在构建完成后跳转
      Future.microtask(() {
        if (mounted) {
          setState(() {
            _selectedDate = DateTime.now();
            _updateFilteredRecords();
          });
        }
      });
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 时间周期选择器
          _buildPeriodSelector(),
          const SizedBox(height: 16),

          // 时间选择器
          _buildDateSelector(),
          const SizedBox(height: 24),

          // 专注统计数据
          _buildFocusStatistics(),
          const SizedBox(height: 24),

          // 专注时长趋势图
          if (_selectedPeriod != TimePeriod.day) ...[
            Text(
              '专注时长趋势',
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: 16),
            Container(
              height: 250,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [AppShadows.low],
              ),
              child: _filteredRecords.isEmpty
                  ? const Center(child: Text('暂无数据'))
                  : DataChartWidget(records: _filteredRecords),
            ),
            const SizedBox(height: 24),
          ],

          // 日视图下的小时分布图
          if (_selectedPeriod == TimePeriod.day) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '当日专注时段分布',
                  style: AppTextStyles.headline3,
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.info_outline, size: 14, color: Colors.grey.shade600),
                      const SizedBox(width: 4),
                      Text('每格2小时', style: TextStyle(fontSize: 12, color: Colors.grey.shade600)),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              height: 280, // 增加高度，以容纳标签
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [AppShadows.low],
              ),
              child: _buildDailyHourlyChart(),
            ),
            const SizedBox(height: 24),
          ],

          // 最佳专注时段分析
          _buildBestFocusTimeAnalysis(),
        ],
      ),
    );
  }

  // 构建日视图下的小时分布图
  Widget _buildDailyHourlyChart() {
    // 获取当天每小时的专注时长
    final Map<int, int> hourlyData = {};

    // 初始化所有小时为0
    for (int i = 0; i < 24; i++) {
      hourlyData[i] = 0;
    }

    // 统计当天每小时的专注时长
    for (final record in _filteredRecords) {
      final hour = record.startTime.hour;
      hourlyData[hour] = (hourlyData[hour] ?? 0) + record.durationSeconds;
    }

    // 检查是否有专注数据
    final hasData = hourlyData.values.any((duration) => duration > 0);

    if (!hasData) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bar_chart, size: 48, color: Colors.grey.shade300),
            const SizedBox(height: 16),
            Text(
              '暂无专注数据',
              style: TextStyle(color: Colors.grey.shade600),
            ),
          ],
        ),
      );
    }

    // 每小时最大专注时长为60分钟
    final maxPossibleMinutes = 60;
    final maxPossibleSeconds = maxPossibleMinutes * 60;

    // 将数据按照两小时为一段进行分组
    final Map<int, int> twoHourlyData = {};
    final List<String> timeLabels = [];

    // 初始化分组数据
    for (int i = 0; i < 12; i++) {
      twoHourlyData[i] = 0;
      final startHour = i * 2;
      final endHour = startHour + 2;
      timeLabels.add('$startHour-$endHour');
    }

    // 将小时数据合并为两小时一组
    for (int hour = 0; hour < 24; hour++) {
      final groupIndex = hour ~/ 2;
      twoHourlyData[groupIndex] = (twoHourlyData[groupIndex] ?? 0) + (hourlyData[hour] ?? 0);
    }

    return Column(
      children: [
        // 图表区域
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Y轴刻度
              Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text('120分钟', style: TextStyle(fontSize: 12, color: Colors.grey.shade700)),
                    Text('90分钟', style: TextStyle(fontSize: 12, color: Colors.grey.shade700)),
                    Text('60分钟', style: TextStyle(fontSize: 12, color: Colors.grey.shade700)),
                    Text('30分钟', style: TextStyle(fontSize: 12, color: Colors.grey.shade700)),
                    Text('0', style: TextStyle(fontSize: 12, color: Colors.grey.shade700)),
                  ],
                ),
              ),

              // 柱状图
              Expanded(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    final barWidth = 16.0; // 更宽的柱子，因为现在只有12个分组
                    final groupWidth = constraints.maxWidth / 12;
                    final maxTwoHourSeconds = maxPossibleSeconds * 2; // 两小时的最大秒数

                    return Stack(
                      children: [
                        // 背景网格
                        Positioned.fill(
                          child: CustomPaint(
                            painter: GridPainter(horizontalLinesCount: 5), // 增加水平线数量
                          ),
                        ),

                        // 所有分组的背景柱子（表示120分钟上限）
                        ...List.generate(12, (groupIndex) {
                          return Positioned(
                            left: groupIndex * groupWidth + (groupWidth - barWidth) / 2,
                            bottom: 0,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                // 60分钟标记线
                                Container(
                                  width: barWidth + 4,
                                  height: 1,
                                  color: Colors.grey.shade300,
                                  margin: EdgeInsets.only(bottom: constraints.maxHeight * 0.95 / 2),
                                ),
                                // 背景柱子
                                Container(
                                  width: barWidth,
                                  height: constraints.maxHeight * 0.95,
                                  decoration: BoxDecoration(
                                    color: Colors.grey.shade100,
                                    borderRadius: const BorderRadius.only(
                                      topLeft: Radius.circular(4),
                                      topRight: Radius.circular(4),
                                    ),
                                    border: Border.all(color: Colors.grey.shade200, width: 1),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }),

                        // 实际专注时长的柱子
                        ...List.generate(12, (groupIndex) {
                          final duration = twoHourlyData[groupIndex] ?? 0;
                          final minutes = duration / 60;
                          final heightPercentage = duration / maxTwoHourSeconds;
                          final height = constraints.maxHeight * 0.95 * heightPercentage;

                          // 计算颜色 - 使用分组的第一个小时的颜色
                          final Color barColor = _getBarColor(groupIndex * 2);

                          // 根据时间段调整颜色饱和度
                          final Color adjustedColor = HSLColor.fromColor(barColor)
                              .withSaturation(math.min(1.0, HSLColor.fromColor(barColor).saturation + 0.2))
                              .withLightness(math.max(0.3, HSLColor.fromColor(barColor).lightness - 0.1))
                              .toColor();

                          final startHour = groupIndex * 2;
                          final endHour = startHour + 2;

                          // 判断是否是当天最高的专注时长
                          final isHighest = duration == twoHourlyData.values.reduce((a, b) => a > b ? a : b) && duration > 0;

                          return Positioned(
                            left: groupIndex * groupWidth + (groupWidth - barWidth) / 2,
                            bottom: 0,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // 如果是最高值，显示标签
                                if (isHighest && minutes > 0)
                                  Transform.translate(
                                    offset: const Offset(0, -24),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: adjustedColor,
                                        borderRadius: BorderRadius.circular(10),
                                        boxShadow: [
                                          BoxShadow(
                                            color: adjustedColor.withAlpha(100),
                                            blurRadius: 3,
                                            offset: const Offset(0, 1),
                                          ),
                                        ],
                                      ),
                                      child: Text(
                                        '${minutes.round()}分钟',
                                        style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                  ),

                                // 专注时长柱子
                                Tooltip(
                                  message: '$startHour:00 - $endHour:00\n${minutes.round()}分钟',
                                  child: Container(
                                    width: barWidth,
                                    height: height > 0 ? math.max(height, 2) : 0,
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.bottomCenter,
                                        end: Alignment.topCenter,
                                        colors: [
                                          adjustedColor,
                                          adjustedColor.withAlpha(200),
                                        ],
                                      ),
                                      borderRadius: const BorderRadius.only(
                                        topLeft: Radius.circular(4),
                                        topRight: Radius.circular(4),
                                      ),
                                      boxShadow: height > 10 ? [
                                        BoxShadow(
                                          color: adjustedColor.withAlpha(100),
                                          blurRadius: 3,
                                          offset: const Offset(0, 1),
                                        ),
                                      ] : null,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }),

                        // 添加分组标记线
                        ...List.generate(13, (i) {
                          return Positioned(
                            left: i * groupWidth,
                            bottom: 0,
                            top: 0,
                            child: Container(
                              width: 1,
                              color: i % 3 == 0 ? Colors.grey.shade300 : Colors.transparent,
                            ),
                          );
                        }),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),

        // X轴刻度
        const SizedBox(height: 8),
        SizedBox(
          height: 20,
          child: Row(
            children: [
              // 留出与Y轴对齐的空间
              const SizedBox(width: 40),

              // X轴刻度
              Expanded(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: List.generate(7, (i) {
                        final hour = i * 4;
                        return Text(
                          '$hour:00',
                          style: TextStyle(fontSize: 11, color: Colors.grey.shade700),
                        );
                      }),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 获取柱状图颜色
  Color _getBarColor(int hour) {
    // 根据不同时段返回不同颜色
    if (hour >= 0 && hour < 6) {
      // 凌晨（深蓝色）
      return Colors.indigo;
    } else if (hour >= 6 && hour < 12) {
      // 上午（绿色）
      return AppColors.primary;
    } else if (hour >= 12 && hour < 18) {
      // 下午（橙色）
      return Colors.orange;
    } else {
      // 晚上（紫色）
      return Colors.purple;
    }
  }



  // 构建时间周期选择器
  Widget _buildPeriodSelector() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppShadows.low],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildPeriodButton(TimePeriod.day, '日'),
          _buildPeriodButton(TimePeriod.week, '周'),
          _buildPeriodButton(TimePeriod.month, '月'),
          _buildPeriodButton(TimePeriod.year, '年'),
        ],
      ),
    );
  }

  // 构建周期按钮
  Widget _buildPeriodButton(TimePeriod period, String label) {
    final isSelected = _selectedPeriod == period;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPeriod = period;
          _updateFilteredRecords();
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  // 构建日期选择器
  Widget _buildDateSelector() {
    String dateText = '';
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selectedDay = DateTime(_selectedDate.year, _selectedDate.month, _selectedDate.day);

    // 检查是否是当前日期
    bool isCurrentPeriod = false;

    switch (_selectedPeriod) {
      case TimePeriod.day:
        dateText = DateFormat('yyyy年MM月dd日').format(_selectedDate);
        isCurrentPeriod = selectedDay.isAtSameMomentAs(today);
        break;
      case TimePeriod.week:
        final weekStart = _getWeekStart(_selectedDate);
        final weekEnd = weekStart.add(const Duration(days: 6));
        dateText = '${DateFormat('MM月dd日').format(weekStart)} - ${DateFormat('MM月dd日').format(weekEnd)}';

        final currentWeekStart = _getWeekStart(now);
        isCurrentPeriod = weekStart.isAtSameMomentAs(currentWeekStart);
        break;
      case TimePeriod.month:
        dateText = DateFormat('yyyy年MM月').format(_selectedDate);
        isCurrentPeriod = _selectedDate.year == now.year && _selectedDate.month == now.month;
        break;
      case TimePeriod.year:
        dateText = DateFormat('yyyy年').format(_selectedDate);
        isCurrentPeriod = _selectedDate.year == now.year;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppShadows.low],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 上一个时间段按钮
          IconButton(
            icon: const Icon(Icons.arrow_back_ios, size: 20),
            tooltip: '上一${_getPeriodName()}',
            onPressed: () {
              setState(() {
                _selectedDate = _getPreviousPeriod(_selectedDate, _selectedPeriod);
                _updateFilteredRecords();
              });
            },
          ),

          // 当前日期文本
          GestureDetector(
            onTap: () {
              // 点击日期文本时跳转到今天
              if (!isCurrentPeriod) {
                setState(() {
                  _selectedDate = DateTime.now();
                  _updateFilteredRecords();
                });
              }
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Row(
                children: [
                  Text(
                    dateText,
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  if (!isCurrentPeriod) ...[
                    const SizedBox(width: 4),
                    const Icon(Icons.home, size: 16, color: AppColors.primary),
                  ],
                ],
              ),
            ),
          ),

          // 下一个时间段按钮
          IconButton(
            icon: Icon(Icons.arrow_forward_ios,
              size: 20,
              color: isCurrentPeriod ? Colors.grey.shade300 : null,
            ),
            tooltip: '下一${_getPeriodName()}',
            onPressed: isCurrentPeriod
                ? null // 如果是当前日期，禁用按钮
                : () {
                    final nextDate = _getNextPeriod(_selectedDate, _selectedPeriod);
                    // 不允许选择超过当前日期的时间
                    if (nextDate.isBefore(now) ||
                        DateFormat('yyyy-MM-dd').format(nextDate) == DateFormat('yyyy-MM-dd').format(now)) {
                      setState(() {
                        _selectedDate = nextDate;
                        _updateFilteredRecords();
                      });
                    }
                  },
          ),
        ],
      ),
    );
  }

  // 获取当前周期的名称
  String _getPeriodName() {
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return '天';
      case TimePeriod.week:
        return '周';
      case TimePeriod.month:
        return '月';
      case TimePeriod.year:
        return '年';
    }
  }

  // 构建专注统计数据
  Widget _buildFocusStatistics() {
    // 计算当前周期的专注统计数据
    final totalHours = _calculateTotalHours();
    final sessionCount = _calculateSessionCount();
    final avgMinutes = _calculateAverageMinutes();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppShadows.low],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _getPeriodTitle(),
            style: AppTextStyles.headline3,
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('总时长', '${totalHours.toStringAsFixed(1)}小时', Icons.access_time),
              _buildStatItem('专注次数', '$sessionCount次', Icons.repeat),
              _buildStatItem('平均时长', '${avgMinutes.toStringAsFixed(1)}分钟', Icons.timer),
            ],
          ),
        ],
      ),
    );
  }

  // 构建统计项
  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: AppColors.primary.withAlpha(26),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: AppColors.primary),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        Text(
          label,
          style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
        ),
      ],
    );
  }

  // 构建最佳专注时段分析
  Widget _buildBestFocusTimeAnalysis() {
    if (_filteredRecords.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [AppShadows.low],
        ),
        child: const Center(
          child: Padding(
            padding: EdgeInsets.all(16.0),
            child: Text('暂无数据，无法分析最佳专注时段'),
          ),
        ),
      );
    }

    final bestHour = _getBestFocusHour();
    final bestHourDuration = _getHourlyDuration()[bestHour] ?? 0;
    final bestHourMinutes = bestHourDuration ~/ 60;

    return Container(
      width: double.infinity, // 确保容器占据全宽
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppShadows.low],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '最佳专注时段分析',
            style: AppTextStyles.headline3,
          ),
          const SizedBox(height: 16),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primary.withAlpha(26),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.access_time,
                  color: AppColors.primary,
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '$bestHour:00 - ${(bestHour + 1) % 24}:00',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '在这个时段内，您总共专注了 $bestHourMinutes 分钟',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            width: double.infinity, // 确保容器占据全宽
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary.withAlpha(10),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Text(
              '建议：在最佳专注时段安排重要任务，可以提高工作效率。',
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
          ),
        ],
      ),
    );
  }



  // 获取小时分布数据
  Map<int, int> _getHourlyDuration() {
    final Map<int, int> hourlyDuration = {};

    for (final record in _filteredRecords) {
      final hour = record.startTime.hour;

      if (!hourlyDuration.containsKey(hour)) {
        hourlyDuration[hour] = 0;
      }

      hourlyDuration[hour] = hourlyDuration[hour]! + record.durationSeconds;
    }

    return hourlyDuration;
  }

  // 获取周期标题
  String _getPeriodTitle() {
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return '当日专注统计';
      case TimePeriod.week:
        return '本周专注统计';
      case TimePeriod.month:
        return '本月专注统计';
      case TimePeriod.year:
        return '全年专注统计';
    }
  }

  // 获取周的开始日期（周一）
  DateTime _getWeekStart(DateTime date) {
    final weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }

  // 获取上一个周期
  DateTime _getPreviousPeriod(DateTime date, TimePeriod period) {
    switch (period) {
      case TimePeriod.day:
        return date.subtract(const Duration(days: 1));
      case TimePeriod.week:
        return date.subtract(const Duration(days: 7));
      case TimePeriod.month:
        // 上一个月
        if (date.month == 1) {
          return DateTime(date.year - 1, 12, 1);
        } else {
          return DateTime(date.year, date.month - 1, 1);
        }
      case TimePeriod.year:
        return DateTime(date.year - 1);
    }
  }

  // 获取下一个周期
  DateTime _getNextPeriod(DateTime date, TimePeriod period) {
    switch (period) {
      case TimePeriod.day:
        return date.add(const Duration(days: 1));
      case TimePeriod.week:
        return date.add(const Duration(days: 7));
      case TimePeriod.month:
        // 下一个月
        if (date.month == 12) {
          return DateTime(date.year + 1, 1, 1);
        } else {
          return DateTime(date.year, date.month + 1, 1);
        }
      case TimePeriod.year:
        return DateTime(date.year + 1);
    }
  }

  // 更新过滤的记录
  void _updateFilteredRecords() {

    switch (_selectedPeriod) {
      case TimePeriod.day:
        final dayStart = DateTime(_selectedDate.year, _selectedDate.month, _selectedDate.day);
        final dayEnd = dayStart.add(const Duration(days: 1)).subtract(const Duration(seconds: 1));

        _filteredRecords = _allRecords.where((record) {
          return record.startTime.isAfter(dayStart) && record.startTime.isBefore(dayEnd);
        }).toList();
        break;

      case TimePeriod.week:
        final weekStart = _getWeekStart(_selectedDate);
        final weekEnd = weekStart.add(const Duration(days: 7)).subtract(const Duration(seconds: 1));

        _filteredRecords = _allRecords.where((record) {
          return record.startTime.isAfter(weekStart) && record.startTime.isBefore(weekEnd);
        }).toList();
        break;

      case TimePeriod.month:
        final monthStart = DateTime(_selectedDate.year, _selectedDate.month, 1);
        final monthEnd = _selectedDate.month < 12
            ? DateTime(_selectedDate.year, _selectedDate.month + 1, 1).subtract(const Duration(seconds: 1))
            : DateTime(_selectedDate.year + 1, 1, 1).subtract(const Duration(seconds: 1));

        _filteredRecords = _allRecords.where((record) {
          return record.startTime.isAfter(monthStart) && record.startTime.isBefore(monthEnd);
        }).toList();
        break;

      case TimePeriod.year:
        final yearStart = DateTime(_selectedDate.year, 1, 1);
        final yearEnd = DateTime(_selectedDate.year + 1, 1, 1).subtract(const Duration(seconds: 1));

        _filteredRecords = _allRecords.where((record) {
          return record.startTime.isAfter(yearStart) && record.startTime.isBefore(yearEnd);
        }).toList();
        break;
    }

    // 按时间排序（最新的在前）
    _filteredRecords.sort((a, b) => b.startTime.compareTo(a.startTime));
  }

  // 构建项目标签页
  Widget _buildProjectsTab() {
    // 获取当前科目的所有项目
    List<Project> projects = [];

    if (widget.subjectId != null) {
      // 如果有科目 ID，获取该科目的项目
      projects = _hiveService.subjectRepository.getProjectsBySubjectId(widget.subjectId!);
    } else {
      // 如果没有科目 ID，获取所有项目
      projects = _hiveService.subjectRepository.getAllProjects();
    }

    // 计算每个项目的专注时长
    final Map<String, double> projectHours = {};
    for (final record in _filteredRecords) {
      if (!projectHours.containsKey(record.projectId)) {
        projectHours[record.projectId] = 0;
      }
      projectHours[record.projectId] = (projectHours[record.projectId] ?? 0) + record.durationSeconds / 3600.0;
    }

    // 按专注时长排序项目
    projects.sort((a, b) {
      final aHours = projectHours[a.id] ?? 0;
      final bHours = projectHours[b.id] ?? 0;
      return bHours.compareTo(aHours); // 降序排列
    });

    return projects.isEmpty
        ? const Center(child: Text('暂无项目数据'))
        : SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '项目专注时长排行',
                  style: AppTextStyles.headline3,
                ),
                const SizedBox(height: 16),

                // 项目列表
                ...List.generate(
                  projects.length,
                  (index) {
                    final project = projects[index];
                    final hours = projectHours[project.id] ?? 0;
                    final subject = _hiveService.subjectRepository.getSubjectById(project.subjectId);

                    return Card(
                      margin: const EdgeInsets.only(bottom: 12),
                      child: InkWell(
                        onTap: () {
                          // 如果已经在项目详情页面，不跳转
                          if (widget.projectId != null) return;

                          // 跳转到项目详情页面
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => DataDetailScreen(
                                subjectId: project.subjectId,
                                projectId: project.id,
                              ),
                            ),
                          );
                        },
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // 项目名称和科目
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          project.name,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                                        if (subject != null)
                                          Text(
                                            subject.name,
                                            style: TextStyle(
                                              color: Colors.grey.shade600,
                                              fontSize: 14,
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                    decoration: BoxDecoration(
                                      color: AppColors.primary.withAlpha(26),
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    child: Text(
                                      '${hours.toStringAsFixed(1)}小时',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.primary,
                                      ),
                                    ),
                                  ),
                                ],
                              ),

                              const SizedBox(height: 12),

                              // 进度条
                              if (project.isTrackingEnabled)
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          '项目进度',
                                          style: TextStyle(
                                            color: Colors.grey.shade600,
                                            fontSize: 14,
                                          ),
                                        ),
                                        Text(
                                          '${(project.progress * 100).toInt()}%',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: AppColors.primary,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(4),
                                      child: LinearProgressIndicator(
                                        value: project.progress,
                                        backgroundColor: Colors.grey.shade200,
                                        color: AppColors.primary,
                                        minHeight: 8,
                                      ),
                                    ),
                                  ],
                                ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 24),

                // 专注记录列表
                Text(
                  '专注记录',
                  style: AppTextStyles.headline3,
                ),
                const SizedBox(height: 16),

                _filteredRecords.isEmpty
                    ? const Center(child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Text('暂无专注记录'),
                      ))
                    : ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _filteredRecords.length > 5 ? 5 : _filteredRecords.length,
                        itemBuilder: (context, index) {
                          final record = _filteredRecords[index];
                          final durationMinutes = record.durationSeconds ~/ 60;

                          // 获取项目和科目名称
                          String projectName = '未知项目';
                          String subjectName = '未知科目';

                          final project = _hiveService.subjectRepository.getProjectById(record.projectId);
                          if (project != null) {
                            projectName = project.name;
                          }

                          final subject = _hiveService.subjectRepository.getSubjectById(record.subjectId);
                          if (subject != null) {
                            subjectName = subject.name;
                          }

                          return Card(
                            margin: const EdgeInsets.only(bottom: 12),
                            child: ListTile(
                              title: Text(
                                DateFormat('yyyy-MM-dd HH:mm').format(record.startTime),
                                style: const TextStyle(fontWeight: FontWeight.bold),
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('$subjectName - $projectName'),
                                  Text(
                                    '状态: ${record.status == FocusRecordStatus.completed ? '完成' : '中断'}',
                                    style: TextStyle(
                                      color: record.status == FocusRecordStatus.completed
                                          ? AppColors.success
                                          : AppColors.warning,
                                    ),
                                  ),
                                ],
                              ),
                              trailing: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withAlpha(26),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Text(
                                  '$durationMinutes分钟',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.primary,
                                  ),
                                ),
                              ),
                              onTap: () {
                                _showRecordDetail(record);
                              },
                            ),
                          );
                        },
                      ),

                // 查看更多按钮
                if (_filteredRecords.length > 5)
                  Center(
                    child: TextButton.icon(
                      onPressed: () {
                        // 切换到记录标签页
                        _tabController.animateTo(3); // 记录标签页的索引是3
                      },
                      icon: const Icon(Icons.list),
                      label: const Text('查看更多记录'),
                    ),
                  ),
              ],
            ),
          );
  }

  // 构建记录标签页
  Widget _buildRecordsTab() {
    return _filteredRecords.isEmpty
        ? const Center(child: Text('暂无专注记录'))
        : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _filteredRecords.length,
            itemBuilder: (context, index) {
              final record = _filteredRecords[index];
              final durationMinutes = record.durationSeconds ~/ 60;

              // 获取项目和科目名称
              String projectName = '未知项目';
              String subjectName = '未知科目';

              final project = _hiveService.subjectRepository.getProjectById(record.projectId);
              if (project != null) {
                projectName = project.name;
              }

              final subject = _hiveService.subjectRepository.getSubjectById(record.subjectId);
              if (subject != null) {
                subjectName = subject.name;
              }

              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                child: ListTile(
                  title: Text(
                    DateFormat('yyyy-MM-dd HH:mm').format(record.startTime),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('$subjectName - $projectName'),
                      Text(
                        '状态: ${record.status == FocusRecordStatus.completed ? '完成' : '中断'}',
                        style: TextStyle(
                          color: record.status == FocusRecordStatus.completed
                              ? AppColors.success
                              : AppColors.warning,
                        ),
                      ),
                    ],
                  ),
                  trailing: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withAlpha(26),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      '$durationMinutes分钟',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                  onTap: () {
                    _showRecordDetail(record);
                  },
                ),
              );
            },
          );
  }

  // 构建分析标签页
  Widget _buildAnalysisTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '专注行为分析',
            style: AppTextStyles.headline3,
          ),
          const SizedBox(height: 16),

          // 专注时长分布
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [AppShadows.low],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '专注时长分布',
                  style: AppTextStyles.headline3,
                ),
                const SizedBox(height: 16),
                _filteredRecords.isEmpty
                    ? const Center(child: Text('暂无数据'))
                    : const Center(child: Text('专注时长分布图 - 开发中')),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // 专注效率分析
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [AppShadows.low],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '专注效率分析',
                  style: AppTextStyles.headline3,
                ),
                const SizedBox(height: 16),
                _filteredRecords.isEmpty
                    ? const Center(child: Text('暂无数据'))
                    : Column(
                        children: [
                          _buildEfficiencyItem(
                            title: '平均每次专注时长',
                            value: '${_calculateAverageMinutes().toStringAsFixed(1)}分钟',
                          ),
                          _buildEfficiencyItem(
                            title: '完成率',
                            value: '${(_calculateCompletionRate() * 100).toStringAsFixed(1)}%',
                          ),
                          _buildEfficiencyItem(
                            title: '最佳专注时段',
                            value: '${_getBestFocusHour()}:00 - ${(_getBestFocusHour() + 1) % 24}:00',
                          ),
                        ],
                      ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // 专注习惯建议
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [AppShadows.low],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '专注习惯建议',
                  style: AppTextStyles.headline3,
                ),
                const SizedBox(height: 16),
                _buildSuggestionItem(
                  icon: Icons.access_time,
                  title: '选择最佳时间',
                  content: '根据数据分析，您在${_getBestFocusHour()}:00 - ${(_getBestFocusHour() + 1) % 24}:00时段专注效果最好，建议安排重要任务在此时段。',
                ),
                const Divider(),
                _buildSuggestionItem(
                  icon: Icons.timer,
                  title: '合理安排时长',
                  content: '您的平均专注时长为${_calculateAverageMinutes().toStringAsFixed(1)}分钟，建议将任务分解为25-30分钟的小块，提高完成率。',
                ),
                if (_calculateCompletionRate() < 0.8) ...[
                  const Divider(),
                  _buildSuggestionItem(
                    icon: Icons.warning_amber_rounded,
                    title: '减少中断',
                    content: '您的专注完成率为${(_calculateCompletionRate() * 100).toStringAsFixed(1)}%，建议减少干扰，提高专注质量。',
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建数据卡片
  Widget _buildDataCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppShadows.low],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: AppTextStyles.bodyMedium,
              ),
              Icon(
                icon,
                color: color,
                size: 20,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  // 构建完成率卡片
  Widget _buildCompletionRateCard() {
    final completionRate = _calculateCompletionRate();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppShadows.low],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '专注完成率',
            style: AppTextStyles.headline3,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              // 进度条
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: LinearProgressIndicator(
                    value: completionRate,
                    backgroundColor: Colors.grey.shade200,
                    color: AppColors.primary,
                    minHeight: 20,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // 百分比
              Text(
                '${(completionRate * 100).toStringAsFixed(1)}%',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建最佳专注时段卡片
  Widget _buildBestFocusTimeCard() {
    final bestHour = _getBestFocusHour();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppShadows.low],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '最佳专注时段',
            style: AppTextStyles.headline3,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primary.withAlpha(26),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.access_time,
                  color: AppColors.primary,
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                '$bestHour:00 - ${(bestHour + 1) % 24}:00',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '在此时段，您的专注效果最好，建议安排重要任务在这个时间段进行。',
            style: AppTextStyles.bodyMedium.copyWith(color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  // 构建效率分析项
  Widget _buildEfficiencyItem({
    required String title,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: AppTextStyles.bodyMedium),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  // 构建建议项
  Widget _buildSuggestionItem({
    required IconData icon,
    required String title,
    required String content,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primary.withAlpha(26),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: AppColors.primary,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                content,
                style: AppTextStyles.bodyMedium.copyWith(color: Colors.grey.shade600),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 显示记录详情
  void _showRecordDetail(FocusRecord record) {
    final durationMinutes = record.durationSeconds ~/ 60;
    final subject = _hiveService.subjectRepository.getSubjectById(record.subjectId);
    final project = _hiveService.subjectRepository.getProjectById(record.projectId);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    '专注记录详情',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const Divider(),

              // 时间
              ListTile(
                leading: const Icon(Icons.access_time, color: AppColors.primary),
                title: const Text('开始时间'),
                subtitle: Text(DateFormat('yyyy-MM-dd HH:mm:ss').format(record.startTime)),
              ),

              // 时长
              ListTile(
                leading: const Icon(Icons.timer, color: AppColors.primary),
                title: const Text('专注时长'),
                subtitle: Text('$durationMinutes分钟'),
              ),

              // 科目
              if (subject != null)
                ListTile(
                  leading: const Icon(Icons.category, color: AppColors.primary),
                  title: const Text('科目'),
                  subtitle: Text(subject.name),
                ),

              // 项目
              if (project != null)
                ListTile(
                  leading: const Icon(Icons.assignment, color: AppColors.primary),
                  title: const Text('项目'),
                  subtitle: Text(project.name),
                ),

              // 状态
              ListTile(
                leading: Icon(
                  record.status == FocusRecordStatus.completed
                      ? Icons.check_circle
                      : Icons.cancel,
                  color: record.status == FocusRecordStatus.completed
                      ? Colors.green
                      : Colors.orange,
                ),
                title: const Text('状态'),
                subtitle: Text(
                  record.status == FocusRecordStatus.completed ? '已完成' : '已中断',
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
