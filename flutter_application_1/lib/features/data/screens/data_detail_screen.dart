import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/services/hive_service.dart';
import '../../../core/models/focus_record.dart';
import '../../../core/models/subject_project.dart';

import '../widgets/tabs/overview_tab.dart';
import '../widgets/tabs/trend_tab.dart';
import '../widgets/tabs/projects_tab.dart';
import '../widgets/record_detail_dialog.dart';

/// 数据详情页面
/// 显示特定维度（总体/科目/项目）的详细数据分析
class DataDetailScreen extends ConsumerStatefulWidget {
  final String? subjectId; // 如果为null，则显示总体数据
  final String? projectId; // 如果为null，则显示科目数据或总体数据

  const DataDetailScreen({
    super.key,
    this.subjectId,
    this.projectId,
  });

  @override
  ConsumerState<DataDetailScreen> createState() => _DataDetailScreenState();
}

class _DataDetailScreenState extends ConsumerState<DataDetailScreen> with SingleTickerProviderStateMixin {
  final HiveService _hiveService = HiveService();

  // 标签页控制器
  late TabController _tabController;

  // 数据
  List<FocusRecord> _allRecords = [];
  List<FocusRecord> _filteredRecords = [];

  // 科目或项目对象
  Subject? _subject;
  Project? _project;

  // 时间范围
  final String _timeRange = '全部';

  // 加载状态
  bool _isLoading = true;

  // 记录当前标签页索引
  int _currentTabIndex = 0;


  @override
  void initState() {
    super.initState();

    // 创建新的TabController，确保长度为3
    _tabController = TabController(length: 3, vsync: this);

    // 添加标签页切换监听
    _tabController.addListener(_handleTabChange);

    _loadData();
  }

  // 处理标签页切换
  void _handleTabChange() {
    // 如果标签页确实发生了变化
    if (_tabController.index != _currentTabIndex) {
      // 如果是切换到专注标签页（索引为1）
      // 在这里可以添加特定的处理逻辑

      // 更新当前标签页索引并刷新UI
      setState(() {
        _currentTabIndex = _tabController.index;
      });
    }
  }

  @override
  void dispose() {
    // 移除监听器
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  // 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 初始化Hive
      await _hiveService.initHive();

      // 获取所有专注记录
      _allRecords = _hiveService.focusRecordRepository.getAllFocusRecords();

      // 如果有科目ID，获取科目信息
      if (widget.subjectId != null) {
        _subject = _hiveService.subjectRepository.getSubjectById(widget.subjectId!);
        if (_subject != null) {
          // 过滤该科目的记录
          _allRecords = _allRecords.where((record) => record.subjectId == widget.subjectId).toList();
        }
      }

      // 如果有项目ID，获取项目信息
      if (widget.projectId != null && widget.subjectId != null) {
        _project = _hiveService.subjectRepository.getProjectById(widget.projectId!);
        if (_project != null) {
          // 过滤该项目的记录
          _allRecords = _allRecords.where((record) => record.projectId == widget.projectId).toList();
        }
      }

      // 默认显示所有记录
      _filteredRecords = List.from(_allRecords);

      // 按时间排序（最新的在前）
      _filteredRecords.sort((a, b) => b.startTime.compareTo(a.startTime));

    } catch (e) {
      debugPrint('加载数据出错: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 注意：此方法已被移除，但保留相关逻辑注释以供参考
  // 过滤记录功能已移至其他位置，但后台记录数据仍然保留

  // 更新趋势标签页的过滤记录
  void _updateTrendFilteredRecords(List<FocusRecord> records) {
    setState(() {
      _filteredRecords = records;
    });
  }

  // 显示记录详情
  void _showRecordDetail(FocusRecord record) {
    RecordDetailDialog.show(context, record, _hiveService);
  }

  // 构建iOS风格的标签项
  Widget _buildTabItem(int index, String label) {
    final isSelected = _tabController.index == index;

    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _tabController.animateTo(index);
          });
        },
        child: Container(
          decoration: BoxDecoration(
            color: isSelected
                ? AppColors.primary.withAlpha(AppColors.alpha30)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8.0),
          ),
          margin: const EdgeInsets.all(3.0),
          child: Center(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 15,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected ? AppColors.primary : AppColors.text,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // 使用渐变背景，与应用其他页面保持一致
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.pageBackground,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 顶部栏
              Container(
                height: 56, // 固定高度
                padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium),
                margin: const EdgeInsets.only(top: AppSizes.paddingMedium),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // 返回按钮（左侧）
                    IconButton(
                      icon: const Icon(Icons.arrow_back),
                      tooltip: "返回",
                      color: AppColors.text,
                      onPressed: () => Navigator.of(context).pop(),
                    ),

                    // 页面标题（居中）
                    const Expanded(
                      child: Center(
                        child: Text(
                          "数据分析",
                          style: AppTextStyles.headline2,
                        ),
                      ),
                    ),

                    // 占位，保持布局平衡
                    SizedBox(width: 48),
                  ],
                ),
              ),

              // iOS风格标签页栏
              Container(
                margin: const EdgeInsets.fromLTRB(AppSizes.paddingMedium, 16, AppSizes.paddingMedium, 16),
                height: 44, // 固定高度
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(15),
                      blurRadius: 2,
                      spreadRadius: 0,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    _buildTabItem(0, '概览'),
                    _buildTabItem(1, '专注'),
                    _buildTabItem(2, '项目'),
                  ],
                ),
              ),

              // 主要内容区域
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : TabBarView(
                        controller: _tabController,
                        children: [
                          // 概览标签页
                          OverviewTab(
                            records: _filteredRecords,
                            timeRange: _timeRange,
                          ),

                          // 专注标签页 - 临时移除付费限制
                          TrendTab(
                            allRecords: _allRecords,
                            onRecordsFiltered: _updateTrendFilteredRecords,
                          ),

                          // 项目标签页 - 临时移除付费限制
                          ProjectsTab(
                            records: _filteredRecords,
                            subjectId: widget.subjectId,
                            projectId: widget.projectId,
                            onRecordTap: _showRecordDetail,
                            hiveService: _hiveService,
                          ),
                        ],
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
