import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/focus_record.dart';
import '../../../core/models/focus_stats.dart';
import '../../../core/services/hive_service.dart';
import '../../../shared/theme/constants.dart';

/// 专注数据分析页面
class FocusDataScreen extends ConsumerStatefulWidget {
  const FocusDataScreen({super.key});

  @override
  ConsumerState<FocusDataScreen> createState() => _FocusDataScreenState();
}

class _FocusDataScreenState extends ConsumerState<FocusDataScreen> with SingleTickerProviderStateMixin {
  // Hive服务
  final HiveService _hiveService = HiveService();

  // 专注统计数据
  FocusStats? _focusStats;

  // 加载状态
  bool _isLoading = true;

  // 选择的时间范围
  String _selectedTimeRange = '全部';
  final List<String> _timeRanges = ['今天', '本周', '本月', '全部'];

  // 选择的维度
  String _selectedDimension = '总体';
  final List<String> _dimensions = ['总体', '科目', '项目'];

  // Tab控制器
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadFocusStats();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // 加载专注统计数据
  Future<void> _loadFocusStats() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 初始化Hive服务
      await _hiveService.initHive();

      // 根据选择的时间范围获取统计数据
      switch (_selectedTimeRange) {
        case '今天':
          final records = _hiveService.focusRecordRepository.getTodayFocusRecords();
          _focusStats = _calculateStats(records);
          break;
        case '本周':
          final records = _hiveService.focusRecordRepository.getWeeklyFocusRecords();
          _focusStats = _calculateStats(records);
          break;
        case '本月':
          final records = _hiveService.focusRecordRepository.getMonthlyFocusRecords();
          _focusStats = _calculateStats(records);
          break;
        case '全部':
        default:
          _focusStats = _hiveService.focusRecordRepository.getFocusStats();
          break;
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载数据失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 计算统计数据
  FocusStats _calculateStats(List<FocusRecord> records) {
    final stats = FocusStats();
    for (var record in records) {
      stats.addRecord(record);
    }
    return stats;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('专注数据分析'),
        // 添加返回按钮
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: '概览'),
            Tab(text: '日'),
            Tab(text: '周'),
            Tab(text: '月'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // 筛选条件
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      // 时间范围选择
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: '时间范围',
                            border: OutlineInputBorder(),
                          ),
                          value: _selectedTimeRange,
                          items: _timeRanges.map((range) {
                            return DropdownMenuItem<String>(
                              value: range,
                              child: Text(range),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null && value != _selectedTimeRange) {
                              setState(() {
                                _selectedTimeRange = value;
                              });
                              _loadFocusStats();
                            }
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      // 维度选择
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: '维度',
                            border: OutlineInputBorder(),
                          ),
                          value: _selectedDimension,
                          items: _dimensions.map((dimension) {
                            return DropdownMenuItem<String>(
                              value: dimension,
                              child: Text(dimension),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null && value != _selectedDimension) {
                              setState(() {
                                _selectedDimension = value;
                              });
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),

                // 主要内容区域
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      // 概览页
                      _buildOverviewTab(),
                      // 日页
                      _buildDailyTab(),
                      // 周页
                      _buildWeeklyTab(),
                      // 月页
                      _buildMonthlyTab(),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  // 构建概览标签页
  Widget _buildOverviewTab() {
    if (_focusStats == null) {
      return const Center(child: Text('暂无数据'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 总体数据卡片
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '总体数据',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildStatRow('总专注时长', '${_focusStats!.totalDurationHours.toStringAsFixed(2)} 小时'),
                  _buildStatRow('专注会话次数', '${_focusStats!.totalFocusSessions} 次'),
                  _buildStatRow('完成率', '${(_focusStats!.completionRate * 100).toStringAsFixed(1)}%'),
                  _buildStatRow('平均专注时长', '${(_focusStats!.averageDurationSeconds / 60).toStringAsFixed(1)} 分钟'),
                  _buildStatRow('平均中断次数', '${_focusStats!.averageInterruptionsPerSession.toStringAsFixed(1)} 次/会话'),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 科目分布卡片
          if (_selectedDimension == '科目' || _selectedDimension == '总体')
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '科目分布',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildSubjectDistribution(),
                  ],
                ),
              ),
            ),

          const SizedBox(height: 16),

          // 项目分布卡片
          if (_selectedDimension == '项目' || _selectedDimension == '总体')
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '项目分布',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildProjectDistribution(),
                  ],
                ),
              ),
            ),

          const SizedBox(height: 16),

          // 最佳专注时段卡片
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '最佳专注时段',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildBestFocusHour(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建日标签页
  Widget _buildDailyTab() {
    if (_focusStats == null || _focusStats!.dailyDurations.isEmpty) {
      return const Center(child: Text('暂无数据'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 日专注时长卡片
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '日专注时长',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildDailyDurationChart(),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 日专注会话次数卡片
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '日专注会话次数',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildDailySessionsChart(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建周标签页
  Widget _buildWeeklyTab() {
    if (_focusStats == null || _focusStats!.weeklyDurations.isEmpty) {
      return const Center(child: Text('暂无数据'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 周专注时长卡片
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '周专注时长',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildWeeklyDurationChart(),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 周专注会话次数卡片
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '周专注会话次数',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildWeeklySessionsChart(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建月标签页
  Widget _buildMonthlyTab() {
    if (_focusStats == null || _focusStats!.monthlyDurations.isEmpty) {
      return const Center(child: Text('暂无数据'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 月专注时长卡片
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '月专注时长',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildMonthlyDurationChart(),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 月专注会话次数卡片
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '月专注会话次数',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildMonthlySessionsChart(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建统计行
  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  // 构建科目分布
  Widget _buildSubjectDistribution() {
    if (_focusStats == null || _focusStats!.subjectDurations.isEmpty) {
      return const Center(child: Text('暂无数据'));
    }

    final subjectRanking = _focusStats!.getSubjectDurationRanking();

    return Column(
      children: subjectRanking.take(5).map((entry) {
        final subjectId = entry.key;
        final durationSeconds = entry.value;
        final durationHours = durationSeconds / 3600;
        final percentage = durationSeconds / _focusStats!.totalDurationSeconds * 100;

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    subjectId, // 实际应用中应该显示科目名称
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    '${durationHours.toStringAsFixed(1)}小时 (${percentage.toStringAsFixed(1)}%)',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              LinearProgressIndicator(
                value: percentage / 100,
                backgroundColor: Colors.grey[200],
                valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  // 构建项目分布
  Widget _buildProjectDistribution() {
    if (_focusStats == null || _focusStats!.projectDurations.isEmpty) {
      return const Center(child: Text('暂无数据'));
    }

    final projectRanking = _focusStats!.getProjectDurationRanking();

    return Column(
      children: projectRanking.take(5).map((entry) {
        final projectId = entry.key;
        final durationSeconds = entry.value;
        final durationHours = durationSeconds / 3600;
        final percentage = durationSeconds / _focusStats!.totalDurationSeconds * 100;

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    projectId, // 实际应用中应该显示项目名称
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    '${durationHours.toStringAsFixed(1)}小时 (${percentage.toStringAsFixed(1)}%)',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              LinearProgressIndicator(
                value: percentage / 100,
                backgroundColor: Colors.grey[200],
                valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  // 构建最佳专注时段
  Widget _buildBestFocusHour() {
    if (_focusStats == null || _focusStats!.hourlyDurations.isEmpty) {
      return const Center(child: Text('暂无数据'));
    }

    final bestHour = _focusStats!.getBestFocusHour();
    final bestHourDuration = _focusStats!.hourlyDurations[bestHour] ?? 0;
    final bestHourDurationHours = bestHourDuration / 3600;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '最佳专注时段: $bestHour:00 - ${(bestHour + 1) % 24}:00',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '该时段专注时长: ${bestHourDurationHours.toStringAsFixed(1)} 小时',
          style: const TextStyle(
            fontSize: 16,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  // 构建日专注时长图表
  Widget _buildDailyDurationChart() {
    // 简单实现，实际应用中应该使用图表库
    return const Center(
      child: Text('此处应显示日专注时长图表'),
    );
  }

  // 构建日专注会话次数图表
  Widget _buildDailySessionsChart() {
    // 简单实现，实际应用中应该使用图表库
    return const Center(
      child: Text('此处应显示日专注会话次数图表'),
    );
  }

  // 构建周专注时长图表
  Widget _buildWeeklyDurationChart() {
    // 简单实现，实际应用中应该使用图表库
    return const Center(
      child: Text('此处应显示周专注时长图表'),
    );
  }

  // 构建周专注会话次数图表
  Widget _buildWeeklySessionsChart() {
    // 简单实现，实际应用中应该使用图表库
    return const Center(
      child: Text('此处应显示周专注会话次数图表'),
    );
  }

  // 构建月专注时长图表
  Widget _buildMonthlyDurationChart() {
    // 简单实现，实际应用中应该使用图表库
    return const Center(
      child: Text('此处应显示月专注时长图表'),
    );
  }

  // 构建月专注会话次数图表
  Widget _buildMonthlySessionsChart() {
    // 简单实现，实际应用中应该使用图表库
    return const Center(
      child: Text('此处应显示月专注会话次数图表'),
    );
  }
}
