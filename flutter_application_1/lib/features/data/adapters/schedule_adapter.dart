import 'package:flutter/material.dart';
import '../../../core/models/schedule.dart';

/// Schedule适配器，用于将Schedule模型适配为热力图所需的数据格式
class ScheduleAdapter {
  final Schedule schedule;

  ScheduleAdapter(this.schedule);

  /// 获取任务名称
  String get name => schedule.title;

  /// 获取任务描述
  String get description => schedule.description ?? '';

  /// 获取任务颜色
  Color getColor() {
    // 使用预定义的颜色，或者从Schedule中获取颜色
    return Colors.green;
  }

  /// 获取任务开始日期
  DateTime get startDate => schedule.date ?? DateTime.now();

  /// 获取任务结束日期
  DateTime? get endDate => schedule.endDate;

  /// 获取已打卡天数
  int get checkedInDays => schedule.completedDates?.length ?? 0;

  /// 获取总天数
  int get totalDays {
    if (schedule.date == null) return 0;

    final start = schedule.date!;
    final end = schedule.endDate ?? DateTime.now();

    return end.difference(start).inDays + 1;
  }

  /// 获取打卡进度
  double get progress {
    if (totalDays == 0) return 0.0;
    return checkedInDays / totalDays;
  }

  /// 获取今天是否已打卡
  bool get isTodayCheckedIn {
    final today = DateTime.now();
    return isDateCheckedIn(today);
  }

  /// 获取指定日期是否已打卡
  bool isDateCheckedIn(DateTime date) {
    if (schedule.completedDates == null || schedule.completedDates!.isEmpty) {
      return false;
    }

    // 检查日期是否在完成日期列表中
    return schedule.completedDates!.any((completedDate) =>
      completedDate.year == date.year &&
      completedDate.month == date.month &&
      completedDate.day == date.day
    );
  }

  /// 获取指定月份的打卡记录
  Map<DateTime, bool> getMonthlyCheckInRecords(DateTime month) {
    final Map<DateTime, bool> records = {};

    // 获取月份的最后一天
    final lastDay = DateTime(month.year, month.month + 1, 0);

    // 遍历月份中的每一天
    for (int day = 1; day <= lastDay.day; day++) {
      final date = DateTime(month.year, month.month, day);
      records[date] = isDateCheckedIn(date);
    }

    return records;
  }

  /// 判断日期是否在任务范围内
  bool isDateInRange(DateTime date) {
    if (schedule.date == null) return false;

    final start = schedule.date!;
    final end = schedule.endDate ?? DateTime.now();

    return date.isAtSameMomentAs(start) ||
           date.isAtSameMomentAs(end) ||
           (date.isAfter(start) && date.isBefore(end));
  }
}
