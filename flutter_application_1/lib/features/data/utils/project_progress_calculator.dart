import 'package:flutter/material.dart';
import '../../../core/models/subject_project.dart';
import '../../../core/models/focus_record.dart';
import '../../../core/models/project_progress_change.dart';
import '../../../core/services/enhanced_hive_service.dart';
import 'project_progress_analyzer.dart';

/// 项目进度推进数据模型
class ProjectProgressData {
  final Project project;
  final Subject subject;
  final double previousProgress; // 之前的进度（0-1）
  final double currentProgress; // 当前进度（0-1）
  final double progressChange; // 进度变化（百分比）
  final double? valueChange; // 数值变化（自定义模式）
  final double? hoursChange; // 时间变化（专注时间模式）

  ProjectProgressData({
    required this.project,
    required this.subject,
    required this.previousProgress,
    required this.currentProgress,
    required this.progressChange,
    this.valueChange,
    this.hoursChange,
  });

  /// 获取进度变化的百分比文本
  String get progressChangeText {
    // 显示正负号
    final sign = progressChange >= 0 ? '+' : '';
    return '$sign${(progressChange * 100).toStringAsFixed(1)}%';
  }

  /// 获取数值变化文本
  String get valueChangeText {
    if (project.trackingMode == ProgressTrackingMode.custom) {
      // 自定义模式：显示 数值变化 单位
      double displayValue = valueChange ?? 0;
      // 显示正负号
      final sign = displayValue >= 0 ? '+' : '';
      return '$sign${displayValue.toStringAsFixed(0)} ${project.customUnit ?? '单位'}';
    } else if (project.trackingMode == ProgressTrackingMode.focusTime) {
      // 专注时间模式：显示 小时数变化h
      double displayHours = hoursChange ?? 0;
      // 显示正负号
      final sign = displayHours >= 0 ? '+' : '';
      return '$sign${displayHours.toStringAsFixed(1)}h';
    }
    return '';
  }

  /// 获取当前进度百分比文本
  String get currentProgressText => '${(currentProgress * 100).toStringAsFixed(0)}%';
}

/// 项目进度计算工具类
class ProjectProgressCalculator {
  /// 计算今日项目进度推进数据
  static List<ProjectProgressData> calculateTodayProjectProgress({
    required List<Project> projects,
    required List<Subject> subjects,
    required List<FocusRecord> todayRecords,
    required List<FocusRecord> yesterdayRecords,
    List<ProjectProgressChange>? progressChanges,
  }) {
    // 调试信息
    debugPrint('calculateTodayProjectProgress: 开始计算今日项目进度推进数据');
    debugPrint('calculateTodayProjectProgress: 项目数量: ${projects.length}');
    debugPrint('calculateTodayProjectProgress: 科目数量: ${subjects.length}');
    debugPrint('calculateTodayProjectProgress: 今日记录数量: ${todayRecords.length}');
    debugPrint('calculateTodayProjectProgress: 昨日记录数量: ${yesterdayRecords.length}');
    debugPrint('calculateTodayProjectProgress: 进度变化记录数量: ${progressChanges?.length ?? 0}');

    // 如果没有提供进度变化记录，则尝试从Hive获取
    final List<ProjectProgressChange> allProgressChanges = progressChanges ?? [];
    if (allProgressChanges.isEmpty) {
      try {
        final hiveService = EnhancedHiveService();
        final repository = hiveService.projectProgressRepository;
        allProgressChanges.addAll(repository.getAllProgressChanges());
        debugPrint('calculateTodayProjectProgress: 从Hive获取进度变化记录: ${allProgressChanges.length}条');
      } catch (e) {
        debugPrint('calculateTodayProjectProgress: 从Hive获取进度变化记录失败: $e');
      }
    }


    // 创建项目ID到项目的映射
    final Map<String, Project> projectMap = {};
    for (final project in projects) {
      projectMap[project.id] = project;
    }

    // 创建科目ID到科目的映射
    final Map<String, Subject> subjectMap = {};
    for (final subject in subjects) {
      subjectMap[subject.id] = subject;
    }

    // 计算今日每个项目的专注时长
    final Map<String, double> todayProjectHours = {};
    for (final record in todayRecords) {
      if (!todayProjectHours.containsKey(record.projectId)) {
        todayProjectHours[record.projectId] = 0;
      }
      todayProjectHours[record.projectId] = (todayProjectHours[record.projectId] ?? 0) +
          record.durationSeconds / 3600.0;
    }

    // 获取开启了进度追踪的项目
    final trackedProjects = projects.where((p) => p.isTrackingEnabled).toList();
    debugPrint('calculateTodayProjectProgress: 开启进度追踪的项目数量: ${trackedProjects.length}');

    // 输出每个开启进度追踪的项目的信息
    for (final project in trackedProjects) {
      debugPrint('项目: ${project.name}, 进度: ${project.progress}, 追踪模式: ${project.trackingMode}, 目标时间: ${project.targetHours}h, 已专注: ${project.focusedHours}h');
    }

    // 计算每个项目的进度变化
    final List<ProjectProgressData> progressDataList = [];

    for (final project in trackedProjects) {
      // 获取项目对应的科目
      final subject = subjectMap[project.subjectId];
      if (subject == null) continue;

      // 获取今日专注时长
      final todayHours = todayProjectHours[project.id] ?? 0;
      debugPrint('项目: ${project.name}, 今日专注时长: ${todayHours}h');

      // 初始化进度变化相关变量
      double previousProgress = 0;
      double currentProgress = project.progress;
      double progressChange = 0;
      double? valueChange;
      double? hoursChange;

      // 如果今日没有专注时长，但仍然需要显示项目
      if (todayHours <= 0) {
        // 对于专注时间模式，如果有进度但今日没有专注，我们仍然显示项目
        if (project.trackingMode == ProgressTrackingMode.focusTime && project.progress > 0) {
          debugPrint('项目: ${project.name}, 今日无专注时长，但有进度，仍然显示');
          // 设置hoursChange为0，表示今日没有专注时间变化
          hoursChange = 0;
          // 设置一个小的进度变化，确保项目会被添加到列表中
          progressChange = 0.001;

          // 添加到列表并继续处理下一个项目
          progressDataList.add(ProjectProgressData(
            project: project,
            subject: subject,
            previousProgress: previousProgress,
            currentProgress: currentProgress,
            progressChange: progressChange,
            valueChange: valueChange,
            hoursChange: hoursChange,
          ));
          continue; // 继续处理下一个项目
        } else if (project.trackingMode == ProgressTrackingMode.focusTime) {
          debugPrint('项目: ${project.name}, 今日无专注时长，且无进度，跳过');
          continue; // 跳过这个项目
        }
        // 对于自定义模式，即使今日没有专注，也可能有进度变化，所以不跳过
      }

      // 获取今日的日期
      final today = DateTime.now();
      final todayStart = DateTime(today.year, today.month, today.day);

      // 使用ProjectProgressAnalyzer计算今日进度变化
      // 注意：现在包含负值变化，包括所有来源（专注完成和手动调整）
      final todayProgressChange = ProjectProgressAnalyzer.getProgressChangeOnDate(
        project,
        allProgressChanges,
        today,
      );

      debugPrint('项目: ${project.name}, 今日进度变化: ${(todayProgressChange * 100).toStringAsFixed(1)}%');

      // 筛选今日的进度变化记录，用于计算值变化
      final todayChanges = allProgressChanges.where(
        (change) =>
            change.projectId == project.id &&
            change.timestamp.isAfter(todayStart) &&
            change.timestamp.isBefore(DateTime(today.year, today.month, today.day, 23, 59, 59))
      ).toList();

      // 计算今日的总值变化（包括所有来源）
      double todayValueChange = 0;
      for (final change in todayChanges) {
        todayValueChange += change.valueChange;
      }

      debugPrint('项目: ${project.name}, 今日值变化: $todayValueChange');

      // 根据追踪模式计算其他数据
      if (project.trackingMode == ProgressTrackingMode.focusTime) {
        // 专注时间模式
        hoursChange = todayHours;
        debugPrint('项目: ${project.name}, 专注时间模式, 今日专注: ${todayHours}h');

        // 获取目标时间（优先使用totalFocusHours）
        final targetHours = project.totalFocusHours ?? project.targetHours;

        if (targetHours > 0) {
          // 计算基于今日专注时间的进度变化
          final todayProgressFromHours = todayHours / targetHours;

          // 如果有进度变化记录，使用记录中的数据；否则使用专注时间计算
          if (todayProgressChange > 0) {
            progressChange = todayProgressChange;
          } else {
            progressChange = todayProgressFromHours;
          }

          previousProgress = (currentProgress - progressChange).clamp(0.0, 1.0);

          debugPrint('项目: ${project.name}, 目标时间: ${targetHours}h, 已专注: ${project.focusedHours}h, 今日专注: ${todayHours}h, 昨日进度: ${(previousProgress * 100).toStringAsFixed(1)}%, 今日进度: ${(currentProgress * 100).toStringAsFixed(1)}%, 进度变化: ${(progressChange * 100).toStringAsFixed(1)}%');
        } else {
          // 即使目标时间为0，也显示专注时间的变化
          debugPrint('项目: ${project.name}, 目标时间为0，但仍显示专注时间变化');
          // 设置一个默认的进度变化，确保项目会被添加到列表中
          progressChange = todayHours > 0 ? 0.01 : 0; // 如果今日有专注，设置一个小的正值
        }

        // 值变化就是专注时间变化
        valueChange = todayHours;
      } else {
        // 自定义模式 - 使用实际的进度变化记录
        debugPrint('项目: ${project.name}, 自定义模式，使用实际进度变化记录');

        // 使用之前计算的今日进度变化和值变化
        progressChange = todayProgressChange;
        valueChange = todayValueChange;

        // 计算昨天的进度
        previousProgress = (currentProgress - progressChange).clamp(0.0, 1.0);

        // 专注时间变化
        hoursChange = todayHours;

        debugPrint('项目: ${project.name}, 自定义模式, 今日值变化: $valueChange, 进度变化: ${(progressChange * 100).toStringAsFixed(1)}%, 专注时间变化: ${hoursChange}h');
      }

      // 输出详细的进度变化信息
      debugPrint('项目: ${project.name}, 进度变化: ${(progressChange * 100).toStringAsFixed(2)}%, 值变化: $valueChange, 小时变化: $hoursChange');

      // 判断是否有值变化或进度变化
      bool hasValueChange = (valueChange > 0);
      bool hasHoursChange = (hoursChange > 0);
      bool hasProgressChange = progressChange > 0;

      // 如果有值变化、时间变化或进度变化，则添加到列表
      if (hasProgressChange || hasValueChange || hasHoursChange) {
        debugPrint('项目: ${project.name}, 有变化，添加到列表 (进度变化: $hasProgressChange, 值变化: $hasValueChange, 时间变化: $hasHoursChange)');

        // 如果进度变化为0但有值变化，设置一个最小的进度变化
        double effectiveProgressChange = progressChange;
        if (progressChange <= 0 && (hasValueChange || hasHoursChange)) {
          effectiveProgressChange = 0.001; // 设置一个很小的进度变化，确保能显示
          debugPrint('项目: ${project.name}, 进度变化为0但有值变化，设置最小进度变化');
        }

        progressDataList.add(ProjectProgressData(
          project: project,
          subject: subject,
          previousProgress: previousProgress,
          currentProgress: currentProgress,
          progressChange: effectiveProgressChange,
          valueChange: valueChange,
          hoursChange: hoursChange,
        ));
      } else {
        debugPrint('项目: ${project.name}, 无任何变化，不添加到列表');

        // 尝试添加一个小的进度变化，用于测试
        if (project.isTrackingEnabled && project.progress > 0) {
          debugPrint('项目: ${project.name}, 尝试添加一个小的进度变化用于测试');
          progressDataList.add(ProjectProgressData(
            project: project,
            subject: subject,
            previousProgress: project.progress - 0.01,
            currentProgress: project.progress,
            progressChange: 0.01, // 1%的进度变化
            valueChange: 1.0, // 假设值变化为1
            hoursChange: project.trackingMode == ProgressTrackingMode.focusTime ? 0.1 : null, // 假设时间变化为0.1小时
          ));
        }
      }
    }

    // 按进度变化从大到小排序
    progressDataList.sort((a, b) => b.progressChange.compareTo(a.progressChange));

    return progressDataList;
  }
}
