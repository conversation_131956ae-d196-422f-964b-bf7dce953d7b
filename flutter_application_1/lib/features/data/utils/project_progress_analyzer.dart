
import 'package:flutter/material.dart';
import '../../../core/models/subject_project.dart';
import '../../../core/models/focus_record.dart';
import '../../../core/models/project_progress_change.dart';

/// 项目进度分析工具类
/// 用于分析项目进度变化和专注时间的关系
class ProjectProgressAnalyzer {
  /// 计算单位专注时间的进度推进
  ///
  /// [project] 项目
  /// [records] 专注记录列表
  /// [progressChanges] 进度变化记录列表
  ///
  /// 返回每小时专注时间的进度推进百分比
  static double calculateProgressPerHour(
    Project project,
    List<FocusRecord> records,
    List<ProjectProgressChange> progressChanges,
  ) {
    // 如果是专注时间模式，直接计算
    if (project.trackingMode == ProgressTrackingMode.focusTime) {
      return project.targetHours > 0 ? (1 / project.targetHours) : 0;
    }

    // 如果是自定义模式，基于专注完成后的进度调整
    double totalProgressChange = 0;
    double totalFocusHours = 0;

    // 筛选专注完成后的进度变化
    final focusCompletionChanges = progressChanges.where(
      (change) => change.source == ProgressChangeSource.focusCompletion
    ).toList();

    // 计算总进度变化
    for (final change in focusCompletionChanges) {
      totalProgressChange += change.progressChange;
    }

    // 计算总专注时长
    for (final record in records) {
      if (record.projectId == project.id) {
        totalFocusHours += record.durationSeconds / 3600;
      }
    }

    // 计算单位专注时间的进度推进
    return totalFocusHours > 0 ? (totalProgressChange / totalFocusHours) : 0;
  }

  /// 获取项目在指定日期范围内的进度变化
  ///
  /// [project] 项目
  /// [progressChanges] 进度变化记录列表
  /// [startDate] 开始日期
  /// [endDate] 结束日期
  /// [includeNegative] 是否包含负值变化，默认为false（负值视为0）
  ///
  /// 返回日期范围内的进度变化
  static double getProgressChangeInDateRange(
    Project project,
    List<ProjectProgressChange> progressChanges,
    DateTime startDate,
    DateTime endDate,
    {bool includeNegative = false}
  ) {
    // 转换为日期（不包含时间）
    final start = DateTime(startDate.year, startDate.month, startDate.day);
    final end = DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);

    // 筛选日期范围内的进度变化（包括所有来源）
    final changesInRange = progressChanges.where(
      (change) =>
          change.projectId == project.id &&
          change.timestamp.isAfter(start) &&
          change.timestamp.isBefore(end)
    ).toList();

    // 计算总进度变化
    double totalProgressChange = 0;
    for (final change in changesInRange) {
      // 始终使用原始的进度变化值，包括负值
      totalProgressChange += change.progressChange;
    }

    return totalProgressChange;
  }

  /// 获取项目在指定日期的进度变化
  ///
  /// [project] 项目
  /// [progressChanges] 进度变化记录列表
  /// [date] 日期
  ///
  /// 返回指定日期的进度变化（包括负值）
  static double getProgressChangeOnDate(
    Project project,
    List<ProjectProgressChange> progressChanges,
    DateTime date,
  ) {
    // 转换为日期（不包含时间）
    final start = DateTime(date.year, date.month, date.day);
    final end = DateTime(date.year, date.month, date.day, 23, 59, 59);

    // 添加调试日志
    debugPrint('getProgressChangeOnDate: 项目: ${project.name}, 日期: ${date.toString().substring(0, 10)}, 进度变化记录数量: ${progressChanges.length}');

    // 筛选该项目在指定日期的进度变化记录
    final projectChanges = progressChanges.where(
      (change) =>
          change.projectId == project.id &&
          change.timestamp.isAfter(start) &&
          change.timestamp.isBefore(end)
    ).toList();

    debugPrint('getProgressChangeOnDate: 项目: ${project.name}, 日期: ${date.toString().substring(0, 10)}, 筛选后的进度变化记录数量: ${projectChanges.length}');

    // 输出每条进度变化记录的详细信息
    for (final change in projectChanges) {
      debugPrint('getProgressChangeOnDate: 项目: ${project.name}, 来源: ${change.source}, 值变化: ${change.valueChange}, 进度变化: ${change.progressChange}');
    }

    final result = getProgressChangeInDateRange(
      project,
      progressChanges,
      start,
      end,
    );

    debugPrint('getProgressChangeOnDate: 项目: ${project.name}, 日期: ${date.toString().substring(0, 10)}, 计算结果: $result');

    return result;
  }

  /// 获取项目在指定日期范围内的专注时长
  ///
  /// [project] 项目
  /// [records] 专注记录列表
  /// [startDate] 开始日期
  /// [endDate] 结束日期
  ///
  /// 返回日期范围内的专注时长（小时）
  static double getFocusHoursInDateRange(
    Project project,
    List<FocusRecord> records,
    DateTime startDate,
    DateTime endDate,
  ) {
    // 转换为日期（不包含时间）
    final start = DateTime(startDate.year, startDate.month, startDate.day);
    final end = DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);

    // 筛选日期范围内的专注记录
    final recordsInRange = records.where(
      (record) =>
          record.projectId == project.id &&
          record.startTime.isAfter(start) &&
          record.startTime.isBefore(end)
    ).toList();

    // 计算总专注时长
    double totalFocusHours = 0;
    for (final record in recordsInRange) {
      totalFocusHours += record.durationSeconds / 3600;
    }

    return totalFocusHours;
  }

  /// 获取项目在指定日期的专注时长
  ///
  /// [project] 项目
  /// [records] 专注记录列表
  /// [date] 日期
  ///
  /// 返回指定日期的专注时长（小时）
  static double getFocusHoursOnDate(
    Project project,
    List<FocusRecord> records,
    DateTime date,
  ) {
    // 转换为日期（不包含时间）
    final start = DateTime(date.year, date.month, date.day);
    final end = DateTime(date.year, date.month, date.day, 23, 59, 59);

    return getFocusHoursInDateRange(project, records, start, end);
  }
}
