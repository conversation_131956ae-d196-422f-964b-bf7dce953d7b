import '../../../../core/models/focus_record.dart';

/// 专注数据计算工具类
class FocusDataCalculator {
  /// 计算总专注时长（小时）
  static double calculateTotalHours(List<FocusRecord> records) {
    if (records.isEmpty) return 0;

    final totalSeconds = records.fold<int>(
      0, (sum, record) => sum + record.durationSeconds);

    return totalSeconds / 3600.0;
  }

  /// 计算专注天数（有过专注行为的天数）
  static int calculateFocusDays(List<FocusRecord> records) {
    if (records.isEmpty) return 0;

    // 按日期分组记录
    final Set<String> uniqueDates = {};

    for (final record in records) {
      final date = '${record.startTime.year}-${record.startTime.month.toString().padLeft(2, '0')}-${record.startTime.day.toString().padLeft(2, '0')}';
      uniqueDates.add(date);
    }

    return uniqueDates.length;
  }

  /// 计算平均每天专注时长（小时）
  static double calculateAverageDailyHours(List<FocusRecord> records) {
    if (records.isEmpty) return 0;

    final totalHours = calculateTotalHours(records);
    final focusDays = calculateFocusDays(records);

    if (focusDays == 0) return 0;

    return totalHours / focusDays;
  }

  /// 计算平均专注时长（分钟）
  static double calculateAverageMinutes(List<FocusRecord> records) {
    if (records.isEmpty) return 0;

    final totalSeconds = records.fold<int>(
      0, (sum, record) => sum + record.durationSeconds);

    return (totalSeconds / 60.0) / records.length;
  }

  /// 计算专注次数
  static int calculateSessionCount(List<FocusRecord> records) {
    return records.length;
  }

  /// 计算中断次数
  static int calculateInterruptionCount(List<FocusRecord> records) {
    return records.where((record) =>
      record.status == FocusRecordStatus.interrupted).length;
  }

  /// 计算完成率
  static double calculateCompletionRate(List<FocusRecord> records) {
    if (records.isEmpty) return 0;

    final completedCount = records.where((record) =>
      record.status == FocusRecordStatus.completed).length;

    return completedCount / records.length;
  }

  /// 获取最佳专注时段
  static int getBestFocusHour(List<FocusRecord> records) {
    if (records.isEmpty) return 0;

    // 按小时统计专注时长
    final Map<int, int> hourlyDuration = {};

    for (final record in records) {
      final hour = record.startTime.hour;

      if (!hourlyDuration.containsKey(hour)) {
        hourlyDuration[hour] = 0;
      }

      hourlyDuration[hour] = hourlyDuration[hour]! + record.durationSeconds;
    }

    // 找出专注时长最长的小时
    int bestHour = 0;
    int maxDuration = 0;

    hourlyDuration.forEach((hour, duration) {
      if (duration > maxDuration) {
        maxDuration = duration;
        bestHour = hour;
      }
    });

    return bestHour;
  }

  /// 获取小时分布数据
  static Map<int, int> getHourlyDuration(List<FocusRecord> records) {
    final Map<int, int> hourlyDuration = {};

    // 初始化所有小时为0
    for (int i = 0; i < 24; i++) {
      hourlyDuration[i] = 0;
    }

    for (final record in records) {
      final hour = record.startTime.hour;
      hourlyDuration[hour] = (hourlyDuration[hour] ?? 0) + record.durationSeconds;
    }

    return hourlyDuration;
  }
}
