import 'package:intl/intl.dart';

/// 时间周期类型
enum TimePeriod { day, week, month, year }

/// 时间周期工具类
class TimePeriodUtils {
  /// 获取周期名称
  static String getPeriodName(TimePeriod period) {
    switch (period) {
      case TimePeriod.day:
        return '天';
      case TimePeriod.week:
        return '周';
      case TimePeriod.month:
        return '月';
      case TimePeriod.year:
        return '年';
    }
  }

  /// 获取周期标题
  static String getPeriodTitle(TimePeriod period) {
    switch (period) {
      case TimePeriod.day:
        return '当日专注统计';
      case TimePeriod.week:
        return '本周专注统计';
      case TimePeriod.month:
        return '本月专注统计';
      case TimePeriod.year:
        return '全年专注统计';
    }
  }

  /// 获取周的开始日期（周一）
  static DateTime getWeekStart(DateTime date) {
    final weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }

  /// 获取上一个周期
  static DateTime getPreviousPeriod(DateTime date, TimePeriod period) {
    switch (period) {
      case TimePeriod.day:
        return date.subtract(const Duration(days: 1));
      case TimePeriod.week:
        return date.subtract(const Duration(days: 7));
      case TimePeriod.month:
        // 上一个月
        if (date.month == 1) {
          return DateTime(date.year - 1, 12, 1);
        } else {
          return DateTime(date.year, date.month - 1, 1);
        }
      case TimePeriod.year:
        return DateTime(date.year - 1);
    }
  }

  /// 获取下一个周期
  static DateTime getNextPeriod(DateTime date, TimePeriod period) {
    switch (period) {
      case TimePeriod.day:
        return date.add(const Duration(days: 1));
      case TimePeriod.week:
        return date.add(const Duration(days: 7));
      case TimePeriod.month:
        // 下一个月
        if (date.month == 12) {
          return DateTime(date.year + 1, 1, 1);
        } else {
          return DateTime(date.year, date.month + 1, 1);
        }
      case TimePeriod.year:
        return DateTime(date.year + 1);
    }
  }

  /// 格式化日期范围文本
  static String formatDateRange(DateTime date, TimePeriod period) {
    switch (period) {
      case TimePeriod.day:
        return DateFormat('yyyy年MM月dd日').format(date);
      case TimePeriod.week:
        final weekStart = getWeekStart(date);
        final weekEnd = weekStart.add(const Duration(days: 6));
        return '${DateFormat('MM月dd日').format(weekStart)} - ${DateFormat('MM月dd日').format(weekEnd)}';
      case TimePeriod.month:
        return DateFormat('yyyy年MM月').format(date);
      case TimePeriod.year:
        return DateFormat('yyyy年').format(date);
    }
  }

  /// 检查是否是当前周期
  static bool isCurrentPeriod(DateTime date, TimePeriod period) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selectedDay = DateTime(date.year, date.month, date.day);

    switch (period) {
      case TimePeriod.day:
        return selectedDay.isAtSameMomentAs(today);
      case TimePeriod.week:
        final currentWeekStart = getWeekStart(now);
        final selectedWeekStart = getWeekStart(date);
        return selectedWeekStart.isAtSameMomentAs(currentWeekStart);
      case TimePeriod.month:
        return date.year == now.year && date.month == now.month;
      case TimePeriod.year:
        return date.year == now.year;
    }
  }
}
