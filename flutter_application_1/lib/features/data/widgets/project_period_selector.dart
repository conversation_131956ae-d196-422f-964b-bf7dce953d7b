import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import 'time_period_selector.dart';

/// 项目标签页的时间周期选择器
/// 用于选择日/周/月视图，参考专注tab的设计
class ProjectPeriodSelector extends StatelessWidget {
  final TimePeriodType selectedPeriod;
  final Function(TimePeriodType) onPeriodChanged;

  const ProjectPeriodSelector({
    super.key,
    required this.selectedPeriod,
    required this.onPeriodChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48, // 固定高度
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppShadows.low],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildPeriodButton(TimePeriodType.day, '日'),
          _buildPeriodButton(TimePeriodType.week, '周'),
          _buildPeriodButton(TimePeriodType.month, '月'),
        ],
      ),
    );
  }

  // 构建周期按钮
  Widget _buildPeriodButton(TimePeriodType period, String label) {
    final isSelected = selectedPeriod == period;

    return GestureDetector(
      onTap: () {
        onPeriodChanged(period);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
