import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/models/focus_record.dart';
import '../../../shared/theme/constants.dart';

/// 数据图表组件
/// 用于显示专注数据的趋势图
class DataChartWidget extends StatefulWidget {
  final List<FocusRecord> records;
  
  const DataChartWidget({
    super.key,
    required this.records,
  });

  @override
  State<DataChartWidget> createState() => _DataChartWidgetState();
}

class _DataChartWidgetState extends State<DataChartWidget> {
  // 图表数据
  late List<DailyFocusData> _chartData;
  
  // 最大值
  late double _maxValue;
  
  @override
  void initState() {
    super.initState();
    _prepareChartData();
  }
  
  @override
  void didUpdateWidget(DataChartWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.records != oldWidget.records) {
      _prepareChartData();
    }
  }
  
  // 准备图表数据
  void _prepareChartData() {
    // 按日期分组记录
    final Map<String, List<FocusRecord>> recordsByDate = {};
    
    for (final record in widget.records) {
      final dateStr = DateFormat('yyyy-MM-dd').format(record.startTime);
      
      if (!recordsByDate.containsKey(dateStr)) {
        recordsByDate[dateStr] = [];
      }
      
      recordsByDate[dateStr]!.add(record);
    }
    
    // 计算每天的总专注时长
    final List<DailyFocusData> data = [];
    
    recordsByDate.forEach((dateStr, records) {
      final totalSeconds = records.fold<int>(
        0, (sum, record) => sum + record.durationSeconds);
      
      final hours = totalSeconds / 3600.0;
      
      data.add(DailyFocusData(
        date: DateTime.parse(dateStr),
        hours: hours,
      ));
    });
    
    // 按日期排序
    data.sort((a, b) => a.date.compareTo(b.date));
    
    // 如果数据超过7条，只取最近7条
    if (data.length > 7) {
      _chartData = data.sublist(data.length - 7);
    } else {
      _chartData = data;
    }
    
    // 计算最大值
    _maxValue = _chartData.isEmpty 
        ? 1.0 
        : _chartData.map((e) => e.hours).reduce((a, b) => a > b ? a : b);
    
    // 确保最大值至少为1
    _maxValue = _maxValue < 1.0 ? 1.0 : _maxValue;
    
    // 为了美观，将最大值向上取整
    _maxValue = (_maxValue * 1.2).ceilToDouble();
  }
  
  @override
  Widget build(BuildContext context) {
    return _chartData.isEmpty
        ? const Center(child: Text('暂无数据'))
        : Column(
            children: [
              // Y轴标签
              Row(
                children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('${_maxValue.toInt()}h', style: AppTextStyles.bodySmall),
                      const SizedBox(height: 60),
                      Text('${(_maxValue / 2).toInt()}h', style: AppTextStyles.bodySmall),
                      const SizedBox(height: 60),
                      Text('0h', style: AppTextStyles.bodySmall),
                    ],
                  ),
                  const SizedBox(width: 8),
                  // 图表主体
                  Expanded(
                    child: SizedBox(
                      height: 180,
                      child: CustomPaint(
                        painter: ChartPainter(
                          data: _chartData,
                          maxValue: _maxValue,
                        ),
                        child: Container(),
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // X轴标签
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: List.generate(
                  _chartData.length,
                  (index) => Text(
                    DateFormat('MM/dd').format(_chartData[index].date),
                    style: AppTextStyles.bodySmall,
                  ),
                ),
              ),
            ],
          );
  }
}

/// 每日专注数据
class DailyFocusData {
  final DateTime date;
  final double hours;
  
  DailyFocusData({
    required this.date,
    required this.hours,
  });
}

/// 图表绘制器
class ChartPainter extends CustomPainter {
  final List<DailyFocusData> data;
  final double maxValue;
  
  ChartPainter({
    required this.data,
    required this.maxValue,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // 绘制网格线
    _drawGrid(canvas, size);
    
    // 绘制折线
    _drawLine(canvas, size);
    
    // 绘制数据点
    _drawPoints(canvas, size);
  }
  
  // 绘制网格线
  void _drawGrid(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withOpacity(0.3)
      ..strokeWidth = 1;
    
    // 横线（Y轴刻度线）
    for (int i = 0; i <= 2; i++) {
      final y = size.height - (i * size.height / 2);
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }
  
  // 绘制折线
  void _drawLine(Canvas canvas, Size size) {
    if (data.isEmpty) return;
    
    final paint = Paint()
      ..color = AppColors.primary
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    
    final path = Path();
    
    for (int i = 0; i < data.length; i++) {
      final x = i * (size.width / (data.length - 1));
      final y = size.height - (data[i].hours / maxValue * size.height);
      
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    
    canvas.drawPath(path, paint);
    
    // 绘制渐变区域
    final gradientPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          AppColors.primary.withOpacity(0.3),
          AppColors.primary.withOpacity(0.05),
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height))
      ..style = PaintingStyle.fill;
    
    final fillPath = Path.from(path)
      ..lineTo(size.width, size.height)
      ..lineTo(0, size.height)
      ..close();
    
    canvas.drawPath(fillPath, gradientPaint);
  }
  
  // 绘制数据点
  void _drawPoints(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.primary
      ..style = PaintingStyle.fill;
    
    final whitePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    
    for (int i = 0; i < data.length; i++) {
      final x = i * (size.width / (data.length - 1));
      final y = size.height - (data[i].hours / maxValue * size.height);
      
      // 外圆
      canvas.drawCircle(Offset(x, y), 6, paint);
      
      // 内圆（白色）
      canvas.drawCircle(Offset(x, y), 3, whitePaint);
    }
  }
  
  @override
  bool shouldRepaint(ChartPainter oldDelegate) => 
    data != oldDelegate.data || maxValue != oldDelegate.maxValue;
}
