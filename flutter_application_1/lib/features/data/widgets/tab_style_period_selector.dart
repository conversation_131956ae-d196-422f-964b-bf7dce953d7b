import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import '../utils/time_period_utils.dart';
import 'time_period_selector.dart';

/// Tab样式的时间周期选择器
/// 样式参考数据分析页面顶部的tab切换器
class TabStylePeriodSelector<T> extends StatelessWidget {
  /// 选中的时间周期
  final T selectedPeriod;

  /// 时间周期变化回调
  final Function(T) onPeriodChanged;

  /// 是否显示年选项
  final bool showYearOption;

  /// 使用的周期类型（TimePeriod 或 TimePeriodType）
  final bool useTimePeriodType;

  const TabStylePeriodSelector({
    super.key,
    required this.selectedPeriod,
    required this.onPeriodChanged,
    this.showYearOption = true,
    this.useTimePeriodType = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 44, // 增加高度，为底部指示器留出空间
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        boxShadow: [AppShadows.low],
      ),
      child: Row(
        children: [
          _buildPeriodTab('日', _getPeriodValue(true)),
          _buildPeriodTab('周', _getPeriodValue(false)),
          _buildPeriodTab('月', _getPeriodValue(null)),
          if (showYearOption)
            _buildPeriodTab('年', _getPeriodValue(false, isYear: true)),
        ],
      ),
    );
  }

  /// 获取周期值
  T _getPeriodValue(bool? isDay, {bool isYear = false}) {
    if (isYear) {
      return (useTimePeriodType ? TimePeriodType.month : TimePeriod.year) as T;
    } else if (isDay == true) {
      return (useTimePeriodType ? TimePeriodType.day : TimePeriod.day) as T;
    } else if (isDay == false) {
      return (useTimePeriodType ? TimePeriodType.week : TimePeriod.week) as T;
    } else {
      return (useTimePeriodType ? TimePeriodType.month : TimePeriod.month) as T;
    }
  }

  /// 构建单个周期选项卡
  Widget _buildPeriodTab(String label, T period) {
    final isSelected = selectedPeriod == period;

    return Expanded(
      child: InkWell(
        onTap: () => onPeriodChanged(period),
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 文本
            Text(
              label,
              style: TextStyle(
                color: isSelected ? AppColors.primary : AppColors.textSecondary,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 14,
              ),
            ),

            // 底部指示器 - 与文字同宽
            const SizedBox(height: 4),
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: label.length * 20.0, // 根据文字长度动态计算宽度
              height: 3,
              decoration: BoxDecoration(
                color: isSelected ? AppColors.primary : Colors.transparent,
                borderRadius: BorderRadius.circular(1.5),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
