import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import '../utils/time_period_utils.dart';

/// 日期选择器
/// 用于在特定时间周期内选择日期
class DateSelector extends StatelessWidget {
  final DateTime selectedDate;
  final TimePeriod selectedPeriod;
  final Function(DateTime) onDateChanged;

  const DateSelector({
    super.key,
    required this.selectedDate,
    required this.selectedPeriod,
    required this.onDateChanged,
  });

  @override
  Widget build(BuildContext context) {
    final dateText = TimePeriodUtils.formatDateRange(selectedDate, selectedPeriod);

    // 重新计算是否是当前周期，确保逻辑正确
    final now = DateTime.now();
    bool isCurrentPeriod;

    // 根据不同的周期类型，使用不同的比较逻辑
    switch (selectedPeriod) {
      case TimePeriod.day:
        final today = DateTime(now.year, now.month, now.day);
        final selectedDay = DateTime(selectedDate.year, selectedDate.month, selectedDate.day);
        isCurrentPeriod = selectedDay.isAtSameMomentAs(today);
        break;
      case TimePeriod.week:
        // 重新实现周视图的判断逻辑，确保只考虑日期部分，忽略时间部分
        final nowDate = DateTime(now.year, now.month, now.day);
        final selectedDateNoTime = DateTime(selectedDate.year, selectedDate.month, selectedDate.day);

        final currentWeekStart = TimePeriodUtils.getWeekStart(nowDate);
        final selectedWeekStart = TimePeriodUtils.getWeekStart(selectedDateNoTime);

        // 比较年、月、日，忽略时间部分
        isCurrentPeriod = currentWeekStart.year == selectedWeekStart.year &&
                          currentWeekStart.month == selectedWeekStart.month &&
                          currentWeekStart.day == selectedWeekStart.day;

        // 详细调试信息
        debugPrint('周视图判断:');
        debugPrint('  当前周开始日期: ${currentWeekStart.toString()}');
        debugPrint('  选择周开始日期: ${selectedWeekStart.toString()}');
        debugPrint('  是否相同: $isCurrentPeriod');
        break;
      case TimePeriod.month:
        isCurrentPeriod = selectedDate.year == now.year && selectedDate.month == now.month;
        break;
      case TimePeriod.year:
        isCurrentPeriod = selectedDate.year == now.year;
        break;
    }

    // 调试输出，帮助排查问题
    debugPrint('当前周期: $isCurrentPeriod, 周期类型: ${selectedPeriod.toString()}, 选择日期: ${selectedDate.toString()}, 当前日期: ${now.toString()}');

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppShadows.low],
      ),
      child: Row(
        children: [
          // 上一个时间段按钮 - 使用更紧凑的按钮
          IconButton(
            icon: const Icon(Icons.arrow_back_ios, size: 18),
            tooltip: '上一${TimePeriodUtils.getPeriodName(selectedPeriod)}',
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(),
            onPressed: () {
              final previousDate = TimePeriodUtils.getPreviousPeriod(selectedDate, selectedPeriod);
              onDateChanged(previousDate);
            },
          ),

          // 中间部分：日期文本和返回当前按钮
          Expanded(
            child: Stack(
              alignment: Alignment.center,
              children: [
                // 日期文本 - 居中显示
                Align(
                  alignment: Alignment.center,
                  child: Text(
                    dateText,
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                // 返回当前按钮 - 使用简单的图标按钮，放在右侧
                if (!isCurrentPeriod)
                  Positioned(
                    right: 0,
                    child: IconButton(
                      icon: const Icon(Icons.refresh, size: 16),
                      tooltip: '返回当前${TimePeriodUtils.getPeriodName(selectedPeriod)}',
                      padding: const EdgeInsets.all(4),
                      constraints: const BoxConstraints(),
                      color: AppColors.primary,
                      onPressed: () {
                        onDateChanged(DateTime.now());
                      },
                    ),
                  ),
              ],
            ),
          ),

          // 下一个时间段按钮 - 使用更紧凑的按钮
          IconButton(
            icon: Icon(
              Icons.arrow_forward_ios,
              size: 18,
              // 确保在当前周期时显示灰色
              color: isCurrentPeriod ? Colors.grey.shade300 : null,
            ),
            tooltip: '下一${TimePeriodUtils.getPeriodName(selectedPeriod)}',
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(),
            // 确保在当前周期时禁用按钮
            onPressed: isCurrentPeriod
                ? null // 如果是当前周期，禁用按钮
                : () {
                    final nextDate = TimePeriodUtils.getNextPeriod(selectedDate, selectedPeriod);
                    // 不允许选择超过当前日期的时间
                    if (nextDate.isBefore(now) ||
                        nextDate.year == now.year && nextDate.month == now.month && nextDate.day == now.day) {
                      onDateChanged(nextDate);
                    }
                  },
          ),
        ],
      ),
    );
  }
}
