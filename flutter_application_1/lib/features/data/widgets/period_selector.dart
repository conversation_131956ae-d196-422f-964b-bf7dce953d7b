import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import '../utils/time_period_utils.dart';

/// 时间周期选择器
/// 用于选择日/周/月/年视图
class PeriodSelector extends StatelessWidget {
  final TimePeriod selectedPeriod;
  final Function(TimePeriod) onPeriodChanged;

  const PeriodSelector({
    super.key,
    required this.selectedPeriod,
    required this.onPeriodChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppShadows.low],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildPeriodButton(TimePeriod.day, '日'),
          _buildPeriodButton(TimePeriod.week, '周'),
          _buildPeriodButton(TimePeriod.month, '月'),
          _buildPeriodButton(TimePeriod.year, '年'),
        ],
      ),
    );
  }

  // 构建周期按钮
  Widget _buildPeriodButton(TimePeriod period, String label) {
    final isSelected = selectedPeriod == period;

    return GestureDetector(
      onTap: () {
        onPeriodChanged(period);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
