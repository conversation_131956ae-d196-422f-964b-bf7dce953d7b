import 'package:flutter/material.dart';
import '../../../../core/models/focus_record.dart';
import '../../../../core/utils/date_util.dart';
import '../../../../shared/theme/constants.dart';
import '../../../../shared/widgets/tooltip_button.dart';

/// 周专注对比卡片
/// 对比本周与上周的专注情况
class WeeklyComparisonCard extends StatelessWidget {
  final List<FocusRecord> records;
  final DateTime selectedDate;

  const WeeklyComparisonCard({
    super.key,
    required this.records,
    required this.selectedDate,
  });

  @override
  Widget build(BuildContext context) {
    // 计算本周和上周的日期范围
    final currentWeekRange = DateUtil.getWeekRange(selectedDate);
    final lastWeekRange = DateUtil.getWeekRange(
      currentWeekRange.start.subtract(const Duration(days: 7))
    );

    // 筛选本周和上周的记录
    final currentWeekRecords = records.where((record) {
      return record.startTime.isAfter(currentWeekRange.start.subtract(const Duration(seconds: 1))) &&
             record.startTime.isBefore(currentWeekRange.end.add(const Duration(seconds: 1)));
    }).toList();

    final lastWeekRecords = records.where((record) {
      return record.startTime.isAfter(lastWeekRange.start.subtract(const Duration(seconds: 1))) &&
             record.startTime.isBefore(lastWeekRange.end.add(const Duration(seconds: 1)));
    }).toList();

    // 计算本周和上周的专注数据
    final currentWeekData = _calculateFocusData(currentWeekRecords);
    final lastWeekData = _calculateFocusData(lastWeekRecords);

    // 计算环比变化
    final totalTimeChange = _calculateChange(currentWeekData.totalMinutes, lastWeekData.totalMinutes);
    final sessionCountChange = _calculateChange(currentWeekData.sessionCount.toDouble(), lastWeekData.sessionCount.toDouble());
    final avgSessionChange = _calculateChange(currentWeekData.avgSessionMinutes, lastWeekData.avgSessionMinutes);
    final avgDailyChange = _calculateChange(currentWeekData.avgDailyMinutes, lastWeekData.avgDailyMinutes);

    // 判断本周是否已结束
    final now = DateTime.now();
    final isCurrentWeekComplete = now.isAfter(currentWeekRange.end);

    // 如果本周未结束，计算预测值
    double predictedTotalMinutes = currentWeekData.totalMinutes;
    int predictedSessionCount = currentWeekData.sessionCount;
    double predictedAvgDailyMinutes = currentWeekData.avgDailyMinutes;

    // 计算本周已经过去的天数
    final daysPassed = isCurrentWeekComplete
        ? 7
        : (now.difference(currentWeekRange.start).inDays + 1).clamp(1, 7);

    if (!isCurrentWeekComplete && daysPassed > 0) {
      // 根据已过去的天数预测本周总值
      final multiplier = 7 / daysPassed;
      predictedTotalMinutes = currentWeekData.totalMinutes * multiplier;
      predictedSessionCount = (currentWeekData.sessionCount * multiplier).round();

      // 平均日专注时间不需要预测，因为它已经是平均值
      // 但如果只有一天数据，可能需要调整
      if (daysPassed == 1) {
        // 如果只有一天数据，可以稍微保守一些预测
        predictedAvgDailyMinutes = currentWeekData.avgDailyMinutes * 0.9;
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题和周进度
        Row(
          children: [
            // 标题图标
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.infoLight100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.compare_arrows,
                color: AppColors.info,
                size: 18,
              ),
            ),
            const SizedBox(width: 12),

            // 标题文本
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        isCurrentWeekComplete ? '本周与上周对比' : '本周预测与上周对比',
                        style: AppTextStyles.headline3.copyWith(
                          fontSize: 16,
                        ),
                      ),

                      if(!isCurrentWeekComplete)...[
                      const SizedBox(width: 6),
                      // 添加提示按钮
                      const TooltipButton(
                        message: '基于已完成天数平均值预测',
                        iconSize: 14,
                        iconColor: AppColors.info,
                        preferBelow: false,
                      ),
                    ],
                    ],
                  ),
                  if (!isCurrentWeekComplete) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        // 分段式进度条 - 七个分开的长条
                        SizedBox(
                          width: 140, // 缩短进度条长度
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: List.generate(7, (index) {
                              // 判断当前段是否已完成
                              final isCompleted = index < daysPassed;
                              return Container(
                                width: 16,
                                height: 6,
                                decoration: BoxDecoration(
                                  color: isCompleted ? AppColors.info : AppColors.divider,
                                  borderRadius: BorderRadius.circular(3),
                                ),
                              );
                            }),
                          ),
                        ),
                        const SizedBox(width: 12),
                        // 已过天数放在右边
                        Text(
                          '已过 $daysPassed/7 天',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColors.textTertiary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // 对比数据 - 第一行
        Row(
          children: [
            // 总专注时长
            Expanded(
              child: _buildGraphicalComparisonItem(
                title: '总专注时长',
                icon: Icons.access_time,
                currentValue: predictedTotalMinutes / 60,
                lastValue: lastWeekData.totalMinutes / 60,
                changePercentage: totalTimeChange,
                valueFormatter: (value) => _formatTimeValue(value, unit: 'h'),
                color: AppColors.info,
              ),
            ),
            const SizedBox(width: 16),
            // 专注次数
            Expanded(
              child: _buildGraphicalComparisonItem(
                title: '专注次数',
                icon: Icons.repeat,
                currentValue: predictedSessionCount.toDouble(),
                lastValue: lastWeekData.sessionCount.toDouble(),
                changePercentage: sessionCountChange,
                valueFormatter: (value) => '${value.round()}次',
                color: AppColors.success,
              ),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // 对比数据 - 第二行
        Row(
          children: [
            // 平均日专注
            Expanded(
              child: _buildGraphicalComparisonItem(
                title: '平均日专注',
                icon: Icons.today,
                currentValue: predictedAvgDailyMinutes,
                lastValue: lastWeekData.avgDailyMinutes,
                changePercentage: avgDailyChange,
                valueFormatter: (value) => _formatTimeValue(value, unit: 'min'),
                color: AppColors.amber,
              ),
            ),
            const SizedBox(width: 16),
            // 平均单次专注
            Expanded(
              child: _buildGraphicalComparisonItem(
                title: '平均单次专注',
                icon: Icons.timer,
                currentValue: currentWeekData.avgSessionMinutes,
                lastValue: lastWeekData.avgSessionMinutes,
                changePercentage: avgSessionChange,
                valueFormatter: (value) => _formatTimeValue(value, unit: 'min'),
                color: AppColors.purple,
              ),
            ),
          ],
        ),

        // 日期范围 - 使用更简洁的样式，放在同一行
        Padding(
          padding: const EdgeInsets.only(top: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 分隔线
              Container(
                height: 1,
                color: AppColors.divider,
                margin: const EdgeInsets.only(bottom: 12),
              ),
              // 日期范围文本 - 放在同一行
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildDateRangeChip(
                    label: '本周',
                    dateRange: '${_formatDate(currentWeekRange.start)} - ${_formatDate(currentWeekRange.end)}',
                    color: Colors.transparent,
                    textColor: AppColors.textSecondary,
                  ),
                  const SizedBox(width: 16),
                  _buildDateRangeChip(
                    label: '上周',
                    dateRange: '${_formatDate(lastWeekRange.start)} - ${_formatDate(lastWeekRange.end)}',
                    color: Colors.transparent,
                    textColor: AppColors.textSecondary,
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 构建图形化对比项
  Widget _buildGraphicalComparisonItem({
    required String title,
    required IconData icon,
    required double currentValue,
    required double lastValue,
    required double changePercentage,
    required String Function(double) valueFormatter,
    required Color color,
  }) {
    // 确定变化的颜色和图标 - 统一使用红色表示下降，绿色表示上升
    Color changeColor;
    IconData changeIcon;
    String changeText;

    if (changePercentage > 0) {
      // 上升使用绿色
      changeColor = AppColors.success;
      changeIcon = Icons.arrow_upward;
      changeText = '+${changePercentage.abs().toStringAsFixed(1)}%';
    } else if (changePercentage < 0) {
      // 下降使用红色
      changeColor = AppColors.error;
      changeIcon = Icons.arrow_downward;
      changeText = '-${changePercentage.abs().toStringAsFixed(1)}%';
    } else {
      // 无变化使用灰色
      changeColor = AppColors.textTertiary;
      changeIcon = Icons.remove;
      changeText = '0%';
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppColors.divider.withAlpha(180)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(8),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题和图标
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withAlpha(25), // 约等于10%透明度
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  icon,
                  size: 14,
                  color: color,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textSecondary,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // 变化百分比 - 放在最显眼的位置
          if (changePercentage != 0) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
              decoration: BoxDecoration(
                color: changeColor.withAlpha(25), // 约等于10%透明度
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    changeIcon,
                    size: 16,
                    color: changeColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    changeText,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: changeColor,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 10),
          ] else ...[
            // 如果没有变化，显示一个占位符
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.divider,
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Text(
                '无变化',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textTertiary,
                ),
              ),
            ),
            const SizedBox(height: 10),
          ],

          // 当前值和上周值的对比 - 优化布局避免溢出
          Row(
            children: [
              // 当前值
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '本周',
                      style: TextStyle(
                        fontSize: 11,
                        color: AppColors.textTertiary,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      valueFormatter(currentValue),
                      style: TextStyle(
                        fontSize: _getAdaptiveFontSize(valueFormatter(currentValue)),
                        fontWeight: FontWeight.bold,
                        color: AppColors.text,
                        height: 1.1, // 减小行高
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ],
                ),
              ),

              // 箭头
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: Icon(
                  Icons.arrow_forward,
                  size: 12,
                  color: AppColors.divider,
                ),
              ),

              // 上周值
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '上周',
                      style: TextStyle(
                        fontSize: 11,
                        color: AppColors.textTertiary,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      valueFormatter(lastValue),
                      style: TextStyle(
                        fontSize: _getAdaptiveFontSize(valueFormatter(lastValue)),
                        color: AppColors.textSecondary,
                        height: 1.1, // 减小行高
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }



  // 构建日期范围文本
  Widget _buildDateRangeChip({
    required String label,
    required String dateRange,
    required Color color,
    required Color textColor,
  }) {
    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: '$label: ',
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          TextSpan(
            text: dateRange,
            style: TextStyle(
              fontSize: 11,
              color: AppColors.textTertiary,
            ),
          ),
        ],
      ),
    );
  }

  // 计算专注数据
  _FocusData _calculateFocusData(List<FocusRecord> records) {
    if (records.isEmpty) {
      return _FocusData(
        totalMinutes: 0,
        sessionCount: 0,
        interruptCount: 0,
        avgSessionMinutes: 0,
        avgDailyMinutes: 0,
      );
    }

    double totalMinutes = 0;
    int interruptCount = 0;

    // 获取记录中的日期范围
    final dates = <DateTime>{};
    DateTime? firstDate;
    DateTime? lastDate;

    for (final record in records) {
      totalMinutes += record.durationSeconds / 60;
      interruptCount += record.interruptionCount;

      // 记录日期（只考虑年月日，不考虑时分秒）
      final recordDate = DateTime(
        record.startTime.year,
        record.startTime.month,
        record.startTime.day,
      );
      dates.add(recordDate);

      // 更新最早和最晚日期
      if (firstDate == null || recordDate.isBefore(firstDate)) {
        firstDate = recordDate;
      }
      if (lastDate == null || recordDate.isAfter(lastDate)) {
        lastDate = recordDate;
      }
    }

    // 计算有专注记录的天数
    final activeDays = dates.length;

    // 计算平均每日专注时间
    final avgDailyMinutes = activeDays > 0 ? totalMinutes / activeDays : 0.0;

    return _FocusData(
      totalMinutes: totalMinutes,
      sessionCount: records.length,
      interruptCount: interruptCount,
      avgSessionMinutes: records.isEmpty ? 0 : totalMinutes / records.length,
      avgDailyMinutes: avgDailyMinutes,
    );
  }

  // 计算变化百分比
  double _calculateChange(double current, double previous) {
    if (previous == 0) {
      return current > 0 ? 100 : 0;
    }
    return ((current - previous) / previous) * 100;
  }

  // 格式化日期
  String _formatDate(DateTime date) {
    return '${date.month}/${date.day}';
  }

  // 智能格式化时间值
  String _formatTimeValue(double value, {required String unit}) {
    // 根据数值大小和单位类型调整显示格式
    if (unit == 'h') {
      // 小时格式
      if (value < 10) {
        // 小于10小时，保留一位小数
        return '${value.toStringAsFixed(1)}h';
      } else {
        // 大于等于10小时，不显示小数
        return '${value.round()}h';
      }
    } else if (unit == 'min') {
      // 分钟格式
      if (value >= 60) {
        // 大于等于60分钟，转换为小时显示
        final hours = value / 60;
        if (hours < 10) {
          return '${hours.toStringAsFixed(1)}h';
        } else {
          return '${hours.round()}h';
        }
      } else if (value < 1) {
        // 小于1分钟，显示为"<1min"
        return '<1m';
      } else {
        // 正常显示分钟
        return '${value.round()}m';
      }
    } else {
      // 其他单位，直接返回
      return '${value.toStringAsFixed(1)}$unit';
    }
  }

  // 根据文本长度获取自适应字体大小
  double _getAdaptiveFontSize(String text) {
    // 基础字体大小
    const double baseFontSize = 14.0;

    // 根据文本长度调整字体大小
    if (text.length <= 3) {
      // 短文本，使用基础字体大小
      return baseFontSize;
    } else if (text.length <= 5) {
      // 中等长度文本，稍微减小字体
      return baseFontSize - 1;
    } else {
      // 长文本，显著减小字体
      return baseFontSize - 2;
    }
  }
}

/// 专注数据类
class _FocusData {
  final double totalMinutes;
  final int sessionCount;
  final int interruptCount;
  final double avgSessionMinutes;
  final double avgDailyMinutes; // 平均每日专注时间

  _FocusData({
    required this.totalMinutes,
    required this.sessionCount,
    required this.interruptCount,
    required this.avgSessionMinutes,
    required this.avgDailyMinutes,
  });
}
