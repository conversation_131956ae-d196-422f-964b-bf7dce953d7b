import 'package:flutter/material.dart';
import '../../../../shared/theme/constants.dart';

/// 完成率卡片组件
/// 显示专注完成率的进度条和百分比
class CompletionRateCard extends StatelessWidget {
  final double completionRate;

  const CompletionRateCard({
    super.key,
    required this.completionRate,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppShadows.low],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '专注完成率',
            style: AppTextStyles.headline3,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              // 进度条
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: LinearProgressIndicator(
                    value: completionRate,
                    backgroundColor: Colors.grey.shade200,
                    color: AppColors.primary,
                    minHeight: 20,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // 百分比
              Text(
                '${(completionRate * 100).toStringAsFixed(1)}%',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
