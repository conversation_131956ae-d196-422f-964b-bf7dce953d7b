import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/models/focus_record.dart';
import '../../../../shared/theme/constants.dart';
import '../../../../shared/widgets/tooltip_button.dart';

/// 月度专注对比卡片（新版）
/// 对比本月与上月的专注情况，采用与周专注对比卡片相同的设计思路
class MonthlyComparisonCardNew extends StatelessWidget {
  final List<FocusRecord> records;
  final DateTime selectedDate;

  const MonthlyComparisonCardNew({
    super.key,
    required this.records,
    required this.selectedDate,
  });

  @override
  Widget build(BuildContext context) {
    // 计算本月和上月的日期范围
    final currentMonthStart = DateTime(selectedDate.year, selectedDate.month, 1);
    final currentMonthEnd = DateTime(selectedDate.year, selectedDate.month + 1, 0);
    
    final lastMonthStart = DateTime(currentMonthStart.year, currentMonthStart.month - 1, 1);
    final lastMonthEnd = DateTime(currentMonthStart.year, currentMonthStart.month, 0);

    // 检查当月是否已结束
    final now = DateTime.now();
    final isCurrentMonthEnded = now.isAfter(currentMonthEnd);

    // 计算当月已过天数和总天数
    final totalDaysInCurrentMonth = currentMonthEnd.day;
    final daysPassedInCurrentMonth = isCurrentMonthEnded 
        ? totalDaysInCurrentMonth 
        : now.day;

    // 筛选本月和上月的记录
    final currentMonthRecords = records.where((record) {
      return record.startTime.isAfter(currentMonthStart.subtract(const Duration(seconds: 1))) &&
             record.startTime.isBefore(currentMonthEnd.add(const Duration(days: 1)));
    }).toList();

    final lastMonthRecords = records.where((record) {
      return record.startTime.isAfter(lastMonthStart.subtract(const Duration(seconds: 1))) &&
             record.startTime.isBefore(lastMonthEnd.add(const Duration(days: 1)));
    }).toList();

    // 计算本月和上月的专注数据
    final currentMonthData = _calculateMonthlyStats(currentMonthRecords, daysPassedInCurrentMonth);
    final lastMonthData = _calculateMonthlyStats(lastMonthRecords, lastMonthEnd.day);

    // 计算环比变化
    final totalHoursChange = _calculateChange(
      isCurrentMonthEnded ? currentMonthData.totalHours : _getProjectedValue(currentMonthData.totalHours, daysPassedInCurrentMonth, totalDaysInCurrentMonth),
      lastMonthData.totalHours
    );
    
    final sessionCountChange = _calculateChange(
      isCurrentMonthEnded ? currentMonthData.sessionCount.toDouble() : _getProjectedValue(currentMonthData.sessionCount.toDouble(), daysPassedInCurrentMonth, totalDaysInCurrentMonth),
      lastMonthData.sessionCount.toDouble()
    );
    
    final avgSessionChange = _calculateChange(
      currentMonthData.avgSessionMinutes,
      lastMonthData.avgSessionMinutes
    );
    
    final avgDailyChange = _calculateChange(
      currentMonthData.avgDailyMinutes,
      lastMonthData.avgDailyMinutes
    );

    // 如果本月未结束，计算预测值
    double predictedTotalHours = currentMonthData.totalHours;
    int predictedSessionCount = currentMonthData.sessionCount;
    double predictedAvgDailyMinutes = currentMonthData.avgDailyMinutes;

    if (!isCurrentMonthEnded && daysPassedInCurrentMonth > 0) {
      // 根据已过去的天数预测本月总值
      final multiplier = totalDaysInCurrentMonth / daysPassedInCurrentMonth;
      predictedTotalHours = _getProjectedValue(currentMonthData.totalHours, daysPassedInCurrentMonth, totalDaysInCurrentMonth);
      predictedSessionCount = _getProjectedValue(currentMonthData.sessionCount.toDouble(), daysPassedInCurrentMonth, totalDaysInCurrentMonth).round();

      // 平均日专注时间不需要预测，因为它已经是平均值
      // 但如果只有一天数据，可能需要调整
      if (daysPassedInCurrentMonth == 1) {
        // 如果只有一天数据，可以稍微保守一些预测
        predictedAvgDailyMinutes = currentMonthData.avgDailyMinutes * 0.9;
      }
    }

    // 获取当月和上月的名称
    final currentMonthName = DateFormat('yyyy年M月').format(selectedDate);
    final lastMonthDate = DateTime(selectedDate.year, selectedDate.month - 1, 1);
    final lastMonthName = DateFormat('yyyy年M月').format(lastMonthDate);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题和月进度
        Row(
          children: [
            // 标题图标
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.infoLight100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.compare_arrows,
                color: AppColors.info,
                size: 18,
              ),
            ),
            const SizedBox(width: 12),

            // 标题文本
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        isCurrentMonthEnded ? '本月与上月对比' : '本月预测与上月对比',
                        style: AppTextStyles.headline3.copyWith(
                          fontSize: 16,
                        ),
                      ),

                      if (!isCurrentMonthEnded) ...[                   
                      const SizedBox(width: 6),
                      // 添加提示按钮
                      const TooltipButton(
                        message: '基于已完成天数平均值预测',
                        iconSize: 14,
                        iconColor: AppColors.info,
                        preferBelow: false,
                      ),
                     ],
                    ],
                  ),
                  if (!isCurrentMonthEnded) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        // 连续进度条
                        SizedBox(
                          width: 140, // 与周专注对比卡片保持一致
                          child: Stack(
                            children: [
                              // 背景条
                              Container(
                                height: 6,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: AppColors.divider,
                                  borderRadius: BorderRadius.circular(3),
                                ),
                              ),
                              // 进度条
                              FractionallySizedBox(
                                widthFactor: daysPassedInCurrentMonth / totalDaysInCurrentMonth,
                                child: Container(
                                  height: 6,
                                  decoration: BoxDecoration(
                                    color: AppColors.info,
                                    borderRadius: BorderRadius.circular(3),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 12),
                        // 已过天数放在右边
                        Text(
                          '已过 $daysPassedInCurrentMonth/$totalDaysInCurrentMonth 天',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColors.textTertiary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // 对比数据 - 第一行
        Row(
          children: [
            // 总专注时长
            Expanded(
              child: _buildGraphicalComparisonItem(
                title: '总专注时长',
                icon: Icons.access_time,
                currentValue: predictedTotalHours,
                lastValue: lastMonthData.totalHours,
                changePercentage: totalHoursChange,
                valueFormatter: (value) => _formatTimeValue(value, unit: 'h'),
                color: AppColors.info,
              ),
            ),
            const SizedBox(width: 16),
            // 专注次数
            Expanded(
              child: _buildGraphicalComparisonItem(
                title: '专注次数',
                icon: Icons.repeat,
                currentValue: predictedSessionCount.toDouble(),
                lastValue: lastMonthData.sessionCount.toDouble(),
                changePercentage: sessionCountChange,
                valueFormatter: (value) => '${value.round()}次',
                color: AppColors.success,
              ),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // 对比数据 - 第二行
        Row(
          children: [
            // 平均日专注
            Expanded(
              child: _buildGraphicalComparisonItem(
                title: '平均日专注',
                icon: Icons.today,
                currentValue: predictedAvgDailyMinutes,
                lastValue: lastMonthData.avgDailyMinutes,
                changePercentage: avgDailyChange,
                valueFormatter: (value) => _formatTimeValue(value, unit: 'min'),
                color: AppColors.amber,
              ),
            ),
            const SizedBox(width: 16),
            // 平均单次专注
            Expanded(
              child: _buildGraphicalComparisonItem(
                title: '平均单次专注',
                icon: Icons.timer,
                currentValue: currentMonthData.avgSessionMinutes,
                lastValue: lastMonthData.avgSessionMinutes,
                changePercentage: avgSessionChange,
                valueFormatter: (value) => _formatTimeValue(value, unit: 'min'),
                color: AppColors.purple,
              ),
            ),
          ],
        ),

        // 日期范围 - 使用更简洁的样式，放在同一行
        Padding(
          padding: const EdgeInsets.only(top: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 分隔线
              Container(
                height: 1,
                color: AppColors.divider,
                margin: const EdgeInsets.only(bottom: 12),
              ),
              // 日期范围文本 - 放在同一行
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildDateRangeChip(
                    label: '本月',
                    dateRange: currentMonthName,
                    color: Colors.transparent,
                    textColor: AppColors.textSecondary,
                  ),
                  const SizedBox(width: 16),
                  _buildDateRangeChip(
                    label: '上月',
                    dateRange: lastMonthName,
                    color: Colors.transparent,
                    textColor: AppColors.textSecondary,
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 构建图形化对比项
  Widget _buildGraphicalComparisonItem({
    required String title,
    required IconData icon,
    required double currentValue,
    required double lastValue,
    required double changePercentage,
    required String Function(double) valueFormatter,
    required Color color,
  }) {
    // 确定变化的颜色和图标 - 统一使用红色表示下降，绿色表示上升
    Color changeColor;
    IconData changeIcon;
    String changeText;

    if (changePercentage > 0) {
      // 上升使用绿色
      changeColor = AppColors.success;
      changeIcon = Icons.arrow_upward;
      changeText = '+${changePercentage.abs().toStringAsFixed(1)}%';
    } else if (changePercentage < 0) {
      // 下降使用红色
      changeColor = AppColors.error;
      changeIcon = Icons.arrow_downward;
      changeText = '-${changePercentage.abs().toStringAsFixed(1)}%';
    } else {
      // 无变化使用灰色
      changeColor = AppColors.textTertiary;
      changeIcon = Icons.remove;
      changeText = '0%';
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppColors.divider.withAlpha(180)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(8),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题和图标
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withAlpha(25), // 约等于10%透明度
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  icon,
                  size: 14,
                  color: color,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textSecondary,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // 变化百分比 - 放在最显眼的位置
          if (changePercentage != 0) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
              decoration: BoxDecoration(
                color: changeColor.withAlpha(25), // 约等于10%透明度
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    changeIcon,
                    size: 16,
                    color: changeColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    changeText,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: changeColor,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 10),
          ] else ...[
            // 如果没有变化，显示一个占位符
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.divider,
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Text(
                '无变化',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textTertiary,
                ),
              ),
            ),
            const SizedBox(height: 10),
          ],

          // 当前值和上月值的对比 - 优化布局避免溢出
          Row(
            children: [
              // 当前值
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '本月',
                      style: TextStyle(
                        fontSize: 11,
                        color: AppColors.textTertiary,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      valueFormatter(currentValue),
                      style: TextStyle(
                        fontSize: _getAdaptiveFontSize(valueFormatter(currentValue)),
                        fontWeight: FontWeight.bold,
                        color: AppColors.text,
                        height: 1.1, // 减小行高
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ],
                ),
              ),

              // 箭头
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: Icon(
                  Icons.arrow_forward,
                  size: 12,
                  color: AppColors.divider,
                ),
              ),

              // 上月值
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '上月',
                      style: TextStyle(
                        fontSize: 11,
                        color: AppColors.textTertiary,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      valueFormatter(lastValue),
                      style: TextStyle(
                        fontSize: _getAdaptiveFontSize(valueFormatter(lastValue)),
                        color: AppColors.textSecondary,
                        height: 1.1, // 减小行高
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建日期范围文本
  Widget _buildDateRangeChip({
    required String label,
    required String dateRange,
    required Color color,
    required Color textColor,
  }) {
    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: '$label: ',
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          TextSpan(
            text: dateRange,
            style: TextStyle(
              fontSize: 11,
              color: AppColors.textTertiary,
            ),
          ),
        ],
      ),
    );
  }

  // 计算月度统计数据
  MonthlyFocusStats _calculateMonthlyStats(List<FocusRecord> records, int daysPassed) {
    // 总专注时长（小时）
    final totalSeconds = records.fold<int>(0, (sum, record) => sum + record.durationSeconds);
    final totalHours = totalSeconds / 3600;

    // 专注次数
    final sessionCount = records.length;

    // 打断次数
    final interruptionCount = records.fold<int>(0, (sum, record) => sum + record.interruptionCount);

    // 平均每次专注时长（分钟）
    final avgSessionMinutes = sessionCount > 0
        ? totalSeconds / sessionCount / 60
        : 0.0;

    // 平均每天专注时长（分钟）
    final avgDailyMinutes = daysPassed > 0
        ? totalSeconds / daysPassed / 60
        : 0.0;

    return MonthlyFocusStats(
      totalHours: totalHours,
      sessionCount: sessionCount,
      interruptionCount: interruptionCount,
      avgSessionMinutes: avgSessionMinutes,
      avgDailyMinutes: avgDailyMinutes,
    );
  }

  // 计算变化百分比
  double _calculateChange(double current, double previous) {
    if (previous == 0) {
      return current > 0 ? 100 : 0;
    }
    return ((current - previous) / previous) * 100;
  }

  // 获取预测值
  double _getProjectedValue(double value, int daysPassed, int totalDays) {
    if (daysPassed == 0) return value;
    
    // 如果已过天数少于总天数的1/3，则预测系数更保守
    double projectionFactor;
    if (daysPassed < totalDays / 3) {
      projectionFactor = totalDays / daysPassed * 0.8;
    } else {
      projectionFactor = totalDays / daysPassed;
    }
    
    return value * projectionFactor;
  }

  // 智能格式化时间值
  String _formatTimeValue(double value, {required String unit}) {
    // 根据数值大小和单位类型调整显示格式
    if (unit == 'h') {
      // 小时格式
      if (value < 10) {
        // 小于10小时，保留一位小数
        return '${value.toStringAsFixed(1)}h';
      } else {
        // 大于等于10小时，不显示小数
        return '${value.round()}h';
      }
    } else if (unit == 'min') {
      // 分钟格式
      if (value >= 60) {
        // 大于等于60分钟，转换为小时显示
        final hours = value / 60;
        if (hours < 10) {
          return '${hours.toStringAsFixed(1)}h';
        } else {
          return '${hours.round()}h';
        }
      } else if (value < 1) {
        // 小于1分钟，显示为"<1m"
        return '<1m';
      } else {
        // 正常显示分钟
        return '${value.round()}m';
      }
    } else {
      // 其他单位，直接返回
      return '${value.toStringAsFixed(1)}$unit';
    }
  }
  
  // 根据文本长度获取自适应字体大小
  double _getAdaptiveFontSize(String text) {
    // 基础字体大小
    const double baseFontSize = 14.0;
    
    // 根据文本长度调整字体大小
    if (text.length <= 3) {
      // 短文本，使用基础字体大小
      return baseFontSize;
    } else if (text.length <= 5) {
      // 中等长度文本，稍微减小字体
      return baseFontSize - 1;
    } else {
      // 长文本，显著减小字体
      return baseFontSize - 2;
    }
  }
}

/// 月度专注统计数据
class MonthlyFocusStats {
  final double totalHours;
  final int sessionCount;
  final int interruptionCount;
  final double avgSessionMinutes;
  final double avgDailyMinutes;

  MonthlyFocusStats({
    required this.totalHours,
    required this.sessionCount,
    required this.interruptionCount,
    required this.avgSessionMinutes,
    required this.avgDailyMinutes,
  });
}
