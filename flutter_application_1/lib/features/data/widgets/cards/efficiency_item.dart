import 'package:flutter/material.dart';
import '../../../../shared/theme/constants.dart';

/// 效率分析项组件
/// 用于显示效率分析中的单个指标
class EfficiencyItem extends StatelessWidget {
  final String title;
  final String value;

  const EfficiencyItem({
    super.key,
    required this.title,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: AppTextStyles.bodyMedium),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }
}
