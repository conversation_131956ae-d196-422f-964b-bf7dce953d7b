import 'package:flutter/material.dart';
import '../../../../shared/theme/constants.dart';
import '../../../../core/models/subject_project.dart';
import '../../../../core/models/focus_record.dart';
import '../../../../core/models/project_progress_change.dart';
import '../../utils/project_progress_calculator.dart';

/// 今日项目进度推进卡片
class TodayProjectProgressCard extends StatelessWidget {
  final List<Project> projects;
  final List<Subject> subjects;
  final List<FocusRecord> todayRecords;
  final List<FocusRecord> yesterdayRecords;
  final List<ProjectProgressChange>? progressChanges;
  final VoidCallback? onTap;

  const TodayProjectProgressCard({
    super.key,
    required this.projects,
    required this.subjects,
    required this.todayRecords,
    required this.yesterdayRecords,
    this.progressChanges,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // 计算今日项目进度推进数据
    final progressDataList = ProjectProgressCalculator.calculateTodayProjectProgress(
      projects: projects,
      subjects: subjects,
      todayRecords: todayRecords,
      yesterdayRecords: yesterdayRecords,
      progressChanges: progressChanges,
    );

    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      decoration: AppDecorations.standardCard(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 卡片标题
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '今日项目进度',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.text,
                ),
              ),
              if (progressDataList.length > 5)
                GestureDetector(
                  onTap: onTap,
                  child: Text(
                    '查看全部',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.primary,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),

          // 项目进度列表
          if (progressDataList.isEmpty)
            _buildEmptyState()
          else
            Column(
              children: [
                // 最多显示5个项目
                for (int i = 0; i < progressDataList.length && i < 5; i++)
                  Column(
                    children: [
                      _buildProgressItem(progressDataList[i]),
                      // 如果不是最后一个项目，添加分隔线
                      if (i < progressDataList.length - 1 && i < 4)
                        const Divider(
                          color: AppColors.divider,
                          height: 16,
                          thickness: 1,
                        ),
                    ],
                  ),
              ],
            ),
        ],
      ),
    );
  }

  // 构建空状态
  Widget _buildEmptyState() {
    return Container(
      height: 70,
      alignment: Alignment.center,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.info_outline,
            size: 14,
            color: AppColors.textSecondary,
          ),
          const SizedBox(width: 6),
          Text(
            '今日暂无项目进度推进',
            style: TextStyle(
              fontSize: 13,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  // 构建进度项
  Widget _buildProgressItem(ProjectProgressData data) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          // 项目颜色标记
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: Color(data.subject.color),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),

          // 项目名称和科目
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  data.project.name,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: AppColors.text,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  data.subject.name,
                  style: TextStyle(
                    fontSize: 11,
                    color: AppColors.textSecondary,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),

          // 进度变化
          SizedBox(
            width: 70, // 固定宽度
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 根据进度变化的正负值显示不同的图标和颜色
                    Icon(
                      data.progressChange >= 0 ? Icons.arrow_upward : Icons.arrow_downward,
                      size: 12,
                      color: data.progressChange >= 0 ? AppColors.primary : Colors.red,
                    ),
                    const SizedBox(width: 2),
                    Text(
                      data.progressChangeText,
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        color: data.progressChange >= 0 ? AppColors.primary : Colors.red,
                      ),
                    ),
                  ],
                ),
                Text(
                  data.valueChangeText,
                  style: const TextStyle(
                    fontSize: 11,
                    color: AppColors.textSecondary,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),

          // 当前进度圆环
          SizedBox(
            width: 40,
            height: 40,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // 进度圆环
                SizedBox(
                  width: 32,
                  height: 32,
                  child: CircularProgressIndicator(
                    value: data.currentProgress,
                    strokeWidth: 3,
                    backgroundColor: AppColors.divider,
                    valueColor: AlwaysStoppedAnimation<Color>(Color(data.subject.color)),
                  ),
                ),
                // 进度文本
                Text(
                  data.currentProgressText,
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: AppColors.text,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
