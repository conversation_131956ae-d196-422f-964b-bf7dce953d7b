import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../shared/theme/constants.dart';
import '../../../../core/models/focus_record.dart';

/// 最佳专注时段卡片（新版）
/// 计算指定时间周期内，专注时间最长的三个连续小时
/// 支持周视图和月视图，并且只在已完成的周期显示
class BestFocusPeriodCardNew extends StatelessWidget {
  final List<FocusRecord> records;
  final DateTime selectedDate;
  final bool isWeekView; // 是否为周视图，false表示月视图

  const BestFocusPeriodCardNew({
    super.key,
    required this.records,
    required this.selectedDate,
    required this.isWeekView,
  });

  @override
  Widget build(BuildContext context) {
    // 计算最佳专注时段
    final bestPeriod = _calculateBestFocusPeriod(records);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppShadows.low],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (bestPeriod != null) ...[
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.info.withAlpha(26),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.access_time,
                    color: AppColors.info,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 小标题：具体时间 + "最佳"
                      Text(
                        '${bestPeriod.startHour}:00 - ${(bestPeriod.startHour + 3) % 24}:00 最佳',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '在这个时段内，您平均每天专注了 ${(bestPeriod.averageMinutes).toStringAsFixed(1)} 分钟',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ] else
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Text('暂无数据'),
              ),
            ),
        ],
      ),
    );
  }



  /// 计算最佳专注时段（连续三小时）
  BestFocusPeriod? _calculateBestFocusPeriod(List<FocusRecord> records) {
    if (records.isEmpty) return null;

    // 按小时统计专注时长
    final Map<int, List<int>> hourlyDurationsByDay = {};

    // 初始化24小时的数据
    for (int hour = 0; hour < 24; hour++) {
      hourlyDurationsByDay[hour] = [];
    }

    // 按日期分组记录
    final Map<String, List<FocusRecord>> recordsByDate = {};

    for (final record in records) {
      final dateStr = DateFormat('yyyy-MM-dd').format(record.startTime);

      if (!recordsByDate.containsKey(dateStr)) {
        recordsByDate[dateStr] = [];
      }

      recordsByDate[dateStr]!.add(record);
    }

    // 计算每天每小时的专注时长
    recordsByDate.forEach((date, dayRecords) {
      // 初始化当天24小时的数据
      final Map<int, int> hourlyDurations = {};
      for (int hour = 0; hour < 24; hour++) {
        hourlyDurations[hour] = 0;
      }

      // 统计当天每小时的专注时长
      for (final record in dayRecords) {
        final hour = record.startTime.hour;
        hourlyDurations[hour] = (hourlyDurations[hour] ?? 0) + record.durationSeconds;
      }

      // 将当天的数据添加到按小时统计的列表中
      for (int hour = 0; hour < 24; hour++) {
        hourlyDurationsByDay[hour]!.add(hourlyDurations[hour] ?? 0);
      }
    });

    // 计算每小时的平均专注时长（分钟）
    final Map<int, double> averageMinutesByHour = {};

    for (int hour = 0; hour < 24; hour++) {
      final durations = hourlyDurationsByDay[hour]!;
      if (durations.isEmpty) {
        averageMinutesByHour[hour] = 0;
      } else {
        final totalSeconds = durations.reduce((a, b) => a + b);
        final averageSeconds = totalSeconds / durations.length;
        averageMinutesByHour[hour] = averageSeconds / 60;
      }
    }

    // 计算连续三小时的总平均专注时长
    double maxAverageMinutes = 0;
    int bestStartHour = 0;

    for (int startHour = 0; startHour < 24; startHour++) {
      double totalMinutes = 0;

      for (int i = 0; i < 3; i++) {
        final hour = (startHour + i) % 24;
        totalMinutes += averageMinutesByHour[hour] ?? 0;
      }

      if (totalMinutes > maxAverageMinutes) {
        maxAverageMinutes = totalMinutes;
        bestStartHour = startHour;
      }
    }

    // 如果最大平均时长为0，表示没有足够的数据
    if (maxAverageMinutes == 0) return null;

    return BestFocusPeriod(
      startHour: bestStartHour,
      averageMinutes: maxAverageMinutes,
    );
  }
}

/// 最佳专注时段数据
class BestFocusPeriod {
  final int startHour;
  final double averageMinutes;

  BestFocusPeriod({
    required this.startHour,
    required this.averageMinutes,
  });
}
