import 'package:flutter/material.dart';
import '../../../../shared/theme/constants.dart';
import '../../utils/time_period_utils.dart';
import '../../utils/focus_data_calculator.dart';
import '../../../../core/models/focus_record.dart';

/// 专注统计卡片工厂类
/// 根据不同的时间周期生成适合的专注统计卡片内容
class FocusStatisticsCardFactory {
  /// 创建专注统计卡片内容
  static Widget createContent({
    required TimePeriod selectedPeriod,
    required double totalHours,
    required int sessionCount,
    required double avgMinutes,
    required List<FocusRecord> records,
  }) {
    // 计算打断次数
    final interruptCount = records.fold<int>(
        0, (sum, record) => sum + record.interruptionCount);

    // 计算平均日专注时间（小时）
    final focusDays = FocusDataCalculator.calculateFocusDays(records);
    final avgDailyHours = focusDays > 0
        ? totalHours / focusDays
        : 0.0;

    // 根据不同的时间周期返回不同的内容
    switch (selectedPeriod) {
      case TimePeriod.day:
        // 日视图只显示总专注时长、专注次数和打断次数
        return _buildDayContent(totalHours, sessionCount, interruptCount);
      case TimePeriod.week:
      case TimePeriod.month:
      case TimePeriod.year:
        // 周、月、年视图显示所有统计数据
        return _buildFullContent(
          totalHours, 
          sessionCount, 
          interruptCount, 
          avgDailyHours, 
          avgMinutes
        );
      default:
        return _buildFullContent(
          totalHours, 
          sessionCount, 
          interruptCount, 
          avgDailyHours, 
          avgMinutes
        );
    }
  }

  /// 构建日视图内容
  static Widget _buildDayContent(
    double totalHours, 
    int sessionCount, 
    int interruptCount
  ) {
    return Row(
      children: [
        // 总专注时长
        Expanded(
          child: _buildStatItem(
            '专注时长',
            '${totalHours.toStringAsFixed(1)}h',
            Icons.access_time,
            AppColors.info,
          ),
        ),

        // 专注次数
        Expanded(
          child: _buildStatItem(
            '专注次数',
            '$sessionCount次',
            Icons.repeat,
            AppColors.success,
          ),
        ),

        // 打断次数
        Expanded(
          child: _buildStatItem(
            '打断次数',
            '$interruptCount次',
            Icons.block,
            AppColors.error,
          ),
        ),
      ],
    );
  }

  /// 构建完整内容（周、月、年视图）
  static Widget _buildFullContent(
    double totalHours, 
    int sessionCount, 
    int interruptCount, 
    double avgDailyHours, 
    double avgMinutes
  ) {
    return Column(
      children: [
        // 第一行：总专注时长、专注次数、打断次数
        Row(
          children: [
            // 总专注时长
            Expanded(
              child: _buildStatItem(
                '总专注时长',
                '${totalHours.toStringAsFixed(1)}h',
                Icons.access_time,
                AppColors.info,
              ),
            ),

            // 专注次数
            Expanded(
              child: _buildStatItem(
                '专注次数',
                '$sessionCount次',
                Icons.repeat,
                AppColors.success,
              ),
            ),

            // 打断次数
            Expanded(
              child: _buildStatItem(
                '打断次数',
                '$interruptCount次',
                Icons.block,
                AppColors.error,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // 第二行：平均日专注、平均单次专注 - 对称居中布局
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 左侧空白
            const SizedBox(width: 20),

            // 平均日专注
            Expanded(
              child: _buildStatItem(
                '平均日专注',
                '${avgDailyHours.toStringAsFixed(1)}h',
                Icons.today,
                AppColors.amber,
              ),
            ),

            // 平均单次专注
            Expanded(
              child: _buildStatItem(
                '平均单次专注',
                '${avgMinutes.toStringAsFixed(0)}min',
                Icons.timer,
                AppColors.purple,
              ),
            ),

            // 右侧空白
            const SizedBox(width: 20),
          ],
        ),
      ],
    );
  }

  /// 构建统计项
  static Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // 图标
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withAlpha(26), // 10% 透明度
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(height: 8),

        // 数值
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.text,
          ),
        ),
        const SizedBox(height: 4),

        // 标签
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
