import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';

/// 时间段类型
enum TimePeriodType {
  day,    // 日
  week,   // 周
  month,  // 月
}

/// 时间段选择器
/// 用于在日、周、月之间切换
class TimePeriodSelector extends StatelessWidget {
  final TimePeriodType selectedPeriod;
  final Function(TimePeriodType) onPeriodChanged;
  
  const TimePeriodSelector({
    super.key,
    required this.selectedPeriod,
    required this.onPeriodChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildPeriodButton(context, TimePeriodType.day, '日'),
          _buildPeriodButton(context, TimePeriodType.week, '周'),
          _buildPeriodButton(context, TimePeriodType.month, '月'),
        ],
      ),
    );
  }
  
  Widget _buildPeriodButton(BuildContext context, TimePeriodType type, String label) {
    final isSelected = selectedPeriod == type;
    
    return GestureDetector(
      onTap: () => onPeriodChanged(type),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ]
              : null,
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            color: isSelected ? AppColors.primary : Colors.grey.shade700,
          ),
        ),
      ),
    );
  }
}
