import 'package:flutter/material.dart';

/// 网格线绘制器
/// 用于在图表背景绘制网格线
class GridPainter extends CustomPainter {
  final int horizontalLinesCount;
  final int verticalLinesCount;

  GridPainter({this.horizontalLinesCount = 4, this.verticalLinesCount = 6});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.shade200
      ..strokeWidth = 1;

    // 绘制水平线
    for (int i = 0; i <= horizontalLinesCount; i++) {
      final y = size.height - (i * size.height / horizontalLinesCount);
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }

    // 绘制垂直线
    for (int i = 0; i <= verticalLinesCount; i++) {
      final x = i * size.width / verticalLinesCount;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }
  }

  @override
  bool shouldRepaint(GridPainter oldDelegate) =>
      oldDelegate.horizontalLinesCount != horizontalLinesCount ||
      oldDelegate.verticalLinesCount != verticalLinesCount;
}
