import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../shared/theme/constants.dart';
import '../../../../core/models/focus_record.dart';

import '../../../../core/services/hive_service.dart';

/// 专注记录详情弹窗
/// 显示单条专注记录的详细信息
class RecordDetailDialog extends StatelessWidget {
  final FocusRecord record;
  final HiveService hiveService;

  const RecordDetailDialog({
    super.key,
    required this.record,
    required this.hiveService,
  });

  @override
  Widget build(BuildContext context) {
    final durationMinutes = record.durationSeconds ~/ 60;
    final subject = hiveService.subjectRepository.getSubjectById(record.subjectId);
    final project = hiveService.subjectRepository.getProjectById(record.projectId);

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '专注记录详情',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const Divider(),

          // 时间
          ListTile(
            leading: const Icon(Icons.access_time, color: AppColors.primary),
            title: const Text('开始时间'),
            subtitle: Text(DateFormat('yyyy-MM-dd HH:mm:ss').format(record.startTime)),
          ),

          // 时长
          ListTile(
            leading: const Icon(Icons.timer, color: AppColors.primary),
            title: const Text('专注时长'),
            subtitle: Text('$durationMinutes分钟'),
          ),

          // 科目
          if (subject != null)
            ListTile(
              leading: const Icon(Icons.category, color: AppColors.primary),
              title: const Text('科目'),
              subtitle: Text(subject.name),
            ),

          // 项目
          if (project != null)
            ListTile(
              leading: const Icon(Icons.assignment, color: AppColors.primary),
              title: const Text('项目'),
              subtitle: Text(project.name),
            ),

          // 状态
          ListTile(
            leading: Icon(
              record.status == FocusRecordStatus.completed
                  ? Icons.check_circle
                  : Icons.cancel,
              color: record.status == FocusRecordStatus.completed
                  ? Colors.green
                  : Colors.orange,
            ),
            title: const Text('状态'),
            subtitle: Text(
              record.status == FocusRecordStatus.completed ? '已完成' : '已中断',
            ),
          ),
        ],
      ),
    );
  }

  /// 显示记录详情弹窗
  static void show(BuildContext context, FocusRecord record, HiveService hiveService) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => RecordDetailDialog(record: record, hiveService: hiveService),
    );
  }
}
