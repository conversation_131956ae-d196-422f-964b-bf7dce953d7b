import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../../../shared/theme/constants.dart';
import '../painters/grid_painter.dart';
import '../../../../core/models/focus_record.dart';

/// 日视图下的小时分布柱状图
/// 显示当天每小时的专注时长分布，使用细柱状图呈现每小时的专注情况
class DailyHourlyLineChart extends StatelessWidget {
  final List<FocusRecord> records;

  const DailyHourlyLineChart({
    super.key,
    required this.records,
  });

  @override
  Widget build(BuildContext context) {
    // 获取当天每小时的专注时长（分钟）
    final Map<int, double> hourlyData = {};

    // 初始化所有小时为0
    for (int i = 0; i < 24; i++) {
      hourlyData[i] = 0.0;
    }

    // 统计当天每小时的专注时长
    for (final record in records) {
      final hour = record.startTime.hour;
      hourlyData[hour] = (hourlyData[hour] ?? 0.0) + (record.durationSeconds / 60.0);
    }

    // 检查是否有专注数据
    final hasData = hourlyData.values.any((duration) => duration > 0);

    if (!hasData) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bar_chart, size: 48, color: Colors.grey.shade300),
            const SizedBox(height: 16),
            Text(
              '暂无专注数据',
              style: TextStyle(color: Colors.grey.shade600),
            ),
          ],
        ),
      );
    }

    // 设置最大值为60分钟，或者实际最大值（如果超过60分钟）
    final actualMax = hourlyData.values.reduce(math.max);
    final maxMinutes = actualMax > 60 ? actualMax : 60.0;

    return Column(
      children: [
        // 图表区域
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Y轴刻度 - 竖向排列并只保留数字
              Padding(
                padding: const EdgeInsets.only(right: 4.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    RotatedBox(
                      quarterTurns: 3,
                      child: Text('分钟', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    ),
                    const SizedBox(height: 4),
                    Text('${maxMinutes.round()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    Text('${(maxMinutes * 0.75).round()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    Text('${(maxMinutes * 0.5).round()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    Text('${(maxMinutes * 0.25).round()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    Text('0', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                  ],
                ),
              ),

              // 柱状图
              Expanded(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    return Stack(
                      children: [
                        // 背景网格
                        Positioned.fill(
                          child: CustomPaint(
                            painter: GridPainter(horizontalLinesCount: 4, verticalLinesCount: 24),
                          ),
                        ),

                        // 绘制柱状图
                        CustomPaint(
                          size: Size(constraints.maxWidth, constraints.maxHeight),
                          painter: HourlyBarChartPainter(
                            hourlyData: hourlyData,
                            maxMinutes: maxMinutes,
                            constraints: constraints,
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),

        // X轴标签 - 只显示0点、6点、12点、18点、23点
        const SizedBox(height: 8),
        SizedBox(
          height: 20,
          child: Row(
            children: [
              const SizedBox(width: 40), // 对应Y轴标签的宽度
              Expanded(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    return Stack(
                      children: [
                        // 0点
                        Positioned(
                          left: 0,
                          child: Text(
                            '0',
                            style: TextStyle(fontSize: 10, color: Colors.grey.shade700),
                          ),
                        ),
                        // 6点
                        Positioned(
                          left: (6 / 24) * constraints.maxWidth - 5,
                          child: Text(
                            '6',
                            style: TextStyle(fontSize: 10, color: Colors.grey.shade700),
                          ),
                        ),
                        // 12点
                        Positioned(
                          left: (12 / 24) * constraints.maxWidth - 8,
                          child: Text(
                            '12',
                            style: TextStyle(fontSize: 10, color: Colors.grey.shade700),
                          ),
                        ),
                        // 18点
                        Positioned(
                          left: (18 / 24) * constraints.maxWidth - 8,
                          child: Text(
                            '18',
                            style: TextStyle(fontSize: 10, color: Colors.grey.shade700),
                          ),
                        ),
                        // 23点
                        Positioned(
                          right: 0,
                          child: Text(
                            '23',
                            style: TextStyle(fontSize: 10, color: Colors.grey.shade700),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// 每小时柱状图绘制器
class HourlyBarChartPainter extends CustomPainter {
  final Map<int, double> hourlyData;
  final double maxMinutes;
  final BoxConstraints constraints;

  HourlyBarChartPainter({
    required this.hourlyData,
    required this.maxMinutes,
    required this.constraints,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 计算柱子宽度 - 使用很细的柱子
    final barWidth = size.width / 24 * 0.4; // 每小时宽度的40%

    for (int hour = 0; hour < 24; hour++) {
      final minutes = hourlyData[hour] ?? 0.0;

      if (minutes > 0) {
        // 计算柱子位置和高度
        final x = hour * size.width / 24 + (size.width / 24 - barWidth) / 2;
        final height = (minutes / maxMinutes) * size.height;
        final y = size.height - height;

        // 使用蓝色主题
        final paint = Paint()
          ..color = AppColors.info
          ..style = PaintingStyle.fill;

        // 绘制柱子
        canvas.drawRRect(
          RRect.fromRectAndRadius(
            Rect.fromLTWH(x, y, barWidth, height),
            const Radius.circular(2),
          ),
          paint,
        );

        // 绘制柱子边框（可选）
        final borderPaint = Paint()
          ..color = AppColors.info.withAlpha(200)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.5;

        canvas.drawRRect(
          RRect.fromRectAndRadius(
            Rect.fromLTWH(x, y, barWidth, height),
            const Radius.circular(2),
          ),
          borderPaint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(HourlyBarChartPainter oldDelegate) =>
      oldDelegate.hourlyData != hourlyData ||
      oldDelegate.maxMinutes != maxMinutes ||
      oldDelegate.constraints != constraints;
}
