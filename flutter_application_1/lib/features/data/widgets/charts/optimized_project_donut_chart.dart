import 'dart:math';
import 'package:flutter/material.dart';
import '../../../../core/models/focus_record.dart';
import '../../../../core/models/subject_project.dart';

/// 优化版项目专注时间占比圆环图
/// 解决溢出问题，优化项目标签布局
class OptimizedProjectDonutChart extends StatelessWidget {
  final List<FocusRecord> records;
  final List<Project> projects;
  final Subject subject;

  const OptimizedProjectDonutChart({
    super.key,
    required this.records,
    required this.projects,
    required this.subject,
  });

  @override
  Widget build(BuildContext context) {
    // 计算每个项目的专注时长
    final Map<String, double> projectHours = {};
    double totalHours = 0;

    for (final record in records) {
      if (record.subjectId != subject.id) continue;

      if (!projectHours.containsKey(record.projectId)) {
        projectHours[record.projectId] = 0;
      }
      final hours = record.durationSeconds / 3600.0;
      projectHours[record.projectId] = (projectHours[record.projectId] ?? 0) + hours;
      totalHours += hours;
    }

    // 按专注时长排序项目
    final sortedProjects = List<Project>.from(projects);
    sortedProjects.sort((a, b) {
      final aHours = projectHours[a.id] ?? 0;
      final bHours = projectHours[b.id] ?? 0;
      return bHours.compareTo(aHours); // 降序排列
    });

    // 如果没有数据，显示空状态
    if (totalHours == 0) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('暂无专注数据'),
        ),
      );
    }

    // 项目颜色系统 - 20个颜色的调色板
    // 使用更和谐的颜色组合，避免过强对比
    final List<Color> projectColorPalette = [
      // 蓝色系 - 使用更柔和的色调
      const Color(0xFF5B8FF9), // 柔和蓝
      const Color(0xFF61CDBB), // 薄荷绿
      const Color(0xFF65789B), // 灰蓝
      const Color(0xFF8EC6C5), // 浅青

      // 暖色系 - 避免过于鲜艳的色调
      const Color(0xFFF6BD16), // 金黄
      const Color(0xFFF6C3B7), // 浅粉
      const Color(0xFFD5695D), // 砖红
      const Color(0xFFE8684A), // 橙红

      // 紫色系 - 使用更柔和的紫色
      const Color(0xFF9270CA), // 淡紫
      const Color(0xFF7B6B8D), // 灰紫
      const Color(0xFFBA9BC9), // 浅紫
      const Color(0xFF9D96F5), // 薰衣草

      // 绿色系 - 使用更自然的绿色
      const Color(0xFF5AD8A6), // 薄荷绿
      const Color(0xFF6DC8EC), // 天蓝
      const Color(0xFF86C166), // 草绿
      const Color(0xFF5D7092), // 青灰

      // 中性色系 - 提供更多选择
      const Color(0xFFCCD6EB), // 浅灰蓝
      const Color(0xFFD7A9E3), // 浅紫粉
      const Color(0xFFFFD8B8), // 浅橙
      const Color(0xFFAAD8D8), // 浅青绿
    ];

    // 为项目分配颜色 - 使用算法确保相邻项目颜色有适当区分度，但不过于对比
    final Map<String, Color> projectColors = {};
    final List<Color> usedColors = [];

    // 定义理想的颜色差异范围
    const double idealMinDifference = 80.0;  // 最小差异，确保颜色不会太相似
    const double idealMaxDifference = 200.0; // 最大差异，确保颜色不会对比过强

    // 为每个项目分配颜色
    for (final project in sortedProjects) {
      if (projectHours[project.id] == null || projectHours[project.id]! <= 0) continue;

      // 如果没有使用过的颜色，使用第一个颜色
      if (usedColors.isEmpty) {
        final firstColor = projectColorPalette[0];
        projectColors[project.id] = firstColor;
        usedColors.add(firstColor);
        continue;
      }

      // 尝试找到与已使用颜色有适当区分度的颜色
      Color bestColor = projectColorPalette[0];
      double bestScore = double.negativeInfinity;

      // 遍历调色板中的所有颜色
      for (final color in projectColorPalette) {
        // 如果颜色已经使用过，跳过
        if (usedColors.contains(color)) continue;

        // 计算与所有已使用颜色的差异
        List<double> differences = [];
        for (final usedColor in usedColors) {
          differences.add(_calculateColorDifference(color, usedColor));
        }

        // 计算最小差异（与最相似的颜色的差异）
        double minDifference = differences.reduce(min);

        // 计算颜色的适合度分数
        // 当差异在理想范围内时，分数最高
        // 当差异太小或太大时，分数降低
        double score;
        if (minDifference < idealMinDifference) {
          // 差异太小，颜色太相似
          score = minDifference / idealMinDifference;
        } else if (minDifference > idealMaxDifference) {
          // 差异太大，对比过强
          score = 2.0 - (minDifference / idealMaxDifference);
        } else {
          // 差异在理想范围内，分数最高
          score = 1.0;
        }

        // 如果这个颜色的分数更高，选择这个颜色
        if (score > bestScore) {
          bestScore = score;
          bestColor = color;
        }
      }

      projectColors[project.id] = bestColor;
      usedColors.add(bestColor);
    }

    return Column(
      children: [
        // 圆环图 - 固定高度
        SizedBox(
          height: 180,
          child: CustomPaint(
            painter: OptimizedProjectDonutChartPainter(
              projects: sortedProjects,
              projectHours: projectHours,
              projectColors: projectColors,
              totalHours: totalHours,
              strokeWidthFactor: 0.25, // 更粗的圆环
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 只保留数字与单位，使用更小的"h"，确保不会溢出
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: totalHours.toStringAsFixed(1),
                          style: const TextStyle(
                            fontSize: 18, // 进一步减小字体
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF212121),
                          ),
                        ),
                        TextSpan(
                          text: 'h', // 使用小写h替代小时
                          style: const TextStyle(
                            fontSize: 10, // 更小的字体
                            fontWeight: FontWeight.normal, // 使用普通字重
                            color: Color(0xFF757575), // 使用更浅的颜色
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),


        const SizedBox(height: 12),

        // 项目标签列表 - 使用卡片分隔，更紧凑的布局
        Expanded(
          child: ListView.separated(
            shrinkWrap: true,
            physics: const BouncingScrollPhysics(),
            padding: EdgeInsets.zero,
            itemCount: sortedProjects.where((p) => (projectHours[p.id] ?? 0) > 0).length,
            separatorBuilder: (context, index) => Divider(
              indent:25,
              endIndent: 25,
              height: 1,
              thickness: 1,
              color: Colors.grey.shade100,
            ),
            itemBuilder: (context, index) {
              // 过滤掉专注时间为0的项目
              final filteredProjects = sortedProjects.where((p) => (projectHours[p.id] ?? 0) > 0).toList();
              if (index >= filteredProjects.length) return const SizedBox.shrink();

              final project = filteredProjects[index];
              final hours = projectHours[project.id] ?? 0;
              final percentage = totalHours > 0 ? (hours / totalHours * 100) : 0;
              final color = projectColors[project.id] ?? Colors.grey;

              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 30),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // 颜色圆点
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 12),

                    // 项目名称 - 限制最大宽度，确保不会挤压右侧内容
                    Expanded(
                      child: Text(
                        project.name,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                    const SizedBox(width: 16),

                    // 专注时长和占比 - 使用固定宽度的容器
                    SizedBox(
                      width: 120, // 固定宽度，确保不会溢出
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          // 专注时长
                          RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: hours.toStringAsFixed(1),
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xFF212121),
                                  ),
                                ),
                                TextSpan(
                                  text: 'h',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.normal,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(width: 12),

                          // 占比
                          Text(
                            '${percentage.toStringAsFixed(1)}%',
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// 计算两个颜色之间的差异值
  /// 使用加权RGB差异计算，更符合人眼感知
  /// 参考CIEDE2000颜色差异算法的简化版本
  double _calculateColorDifference(Color a, Color b) {
    // 人眼对绿色最敏感，对蓝色最不敏感
    const double rWeight = 0.3;
    const double gWeight = 0.59;
    const double bWeight = 0.11;

    // 计算加权RGB差异
    final rDiff = (a.r - b.r) * rWeight;
    final gDiff = (a.g - b.g) * gWeight;
    final bDiff = (a.b - b.b) * bWeight;

    // 计算亮度差异
    final aLuminance = (a.r * rWeight + a.g * gWeight + a.b * bWeight);
    final bLuminance = (b.r * rWeight + b.g * gWeight + b.b * bWeight);
    final luminanceDiff = (aLuminance - bLuminance).abs() * 1.5; // 增加亮度差异的权重

    // 计算色相差异（简化版）
    final aHue = atan2(a.r - a.g, a.g - a.b);
    final bHue = atan2(b.r - b.g, b.g - b.b);
    final hueDiff = (aHue - bHue).abs() * 30; // 增加色相差异的权重

    // 综合考虑RGB差异、亮度差异和色相差异
    return sqrt(rDiff * rDiff + gDiff * gDiff + bDiff * bDiff) + luminanceDiff + hueDiff;
  }
}

/// 优化版项目圆环图绘制器
class OptimizedProjectDonutChartPainter extends CustomPainter {
  final List<Project> projects;
  final Map<String, double> projectHours;
  final Map<String, Color> projectColors;
  final double totalHours;
  final double strokeWidthFactor; // 圆环粗细因子

  OptimizedProjectDonutChartPainter({
    required this.projects,
    required this.projectHours,
    required this.projectColors,
    required this.totalHours,
    this.strokeWidthFactor = 0.2, // 默认值
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 2 * 0.85; // 增大半径比例
    final strokeWidth = radius * strokeWidthFactor; // 圆环粗细

    // 绘制背景圆环
    final bgPaint = Paint()
      ..color = Colors.grey.shade200
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    canvas.drawCircle(center, radius - strokeWidth / 2, bgPaint);

    // 如果没有数据，直接返回
    if (totalHours == 0) return;

    // 绘制各项目扇形
    double startAngle = -pi / 2; // 从12点钟方向开始

    for (final project in projects) {
      final hours = projectHours[project.id] ?? 0;
      if (hours <= 0) continue;

      final sweepAngle = 2 * pi * (hours / totalHours);
      final color = projectColors[project.id] ?? Colors.grey;

      final paint = Paint()
        ..color = color
        ..style = PaintingStyle.stroke
        ..strokeWidth = strokeWidth
        ..strokeCap = StrokeCap.butt;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius - strokeWidth / 2),
        startAngle,
        sweepAngle,
        false,
        paint,
      );

      startAngle += sweepAngle;
    }
  }

  @override
  bool shouldRepaint(OptimizedProjectDonutChartPainter oldDelegate) =>
      projects != oldDelegate.projects ||
      projectHours != oldDelegate.projectHours ||
      projectColors != oldDelegate.projectColors ||
      totalHours != oldDelegate.totalHours ||
      strokeWidthFactor != oldDelegate.strokeWidthFactor;
}
