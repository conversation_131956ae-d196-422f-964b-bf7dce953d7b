import 'dart:math';
import 'package:flutter/material.dart';
import '../../../../core/models/focus_record.dart';
import '../../../../core/models/subject_project.dart';

/// 自定义项目专注时间占比圆环图 - 更大的圆环，中间只保留专注时长
class CustomProjectDonutChart extends StatelessWidget {
  final List<FocusRecord> records;
  final List<Project> projects;
  final Subject subject;
  
  const CustomProjectDonutChart({
    super.key,
    required this.records,
    required this.projects,
    required this.subject,
  });

  @override
  Widget build(BuildContext context) {
    // 计算每个项目的专注时长
    final Map<String, double> projectHours = {};
    double totalHours = 0;

    for (final record in records) {
      if (record.subjectId != subject.id) continue;

      if (!projectHours.containsKey(record.projectId)) {
        projectHours[record.projectId] = 0;
      }
      final hours = record.durationSeconds / 3600.0;
      projectHours[record.projectId] = (projectHours[record.projectId] ?? 0) + hours;
      totalHours += hours;
    }

    // 按专注时长排序项目
    final sortedProjects = List<Project>.from(projects);
    sortedProjects.sort((a, b) {
      final aHours = projectHours[a.id] ?? 0;
      final bHours = projectHours[b.id] ?? 0;
      return bHours.compareTo(aHours); // 降序排列
    });

    // 如果没有数据，显示空状态
    if (totalHours == 0) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('暂无专注数据'),
        ),
      );
    }

    // 为项目生成颜色
    final Map<String, Color> projectColors = {};
    final List<Color> colorPalette = [
      const Color(0xFF4285F4), // 蓝色
      const Color(0xFF34A853), // 绿色
      const Color(0xFFFBBC05), // 黄色
      const Color(0xFFEA4335), // 红色
      const Color(0xFF9C27B0), // 紫色
      const Color(0xFF00BCD4), // 青色
      const Color(0xFFFF9800), // 橙色
      const Color(0xFF795548), // 棕色
      const Color(0xFF607D8B), // 蓝灰色
      const Color(0xFF3F51B5), // 靛蓝色
    ];

    // 为每个项目分配颜色
    for (int i = 0; i < sortedProjects.length; i++) {
      final project = sortedProjects[i];
      projectColors[project.id] = colorPalette[i % colorPalette.length];
    }

    return Column(
      children: [
        // 圆环图 - 更大的尺寸
        SizedBox(
          height: 200, // 增加高度
          child: CustomPaint(
            painter: CustomProjectDonutChartPainter(
              projects: sortedProjects,
              projectHours: projectHours,
              projectColors: projectColors,
              totalHours: totalHours,
              strokeWidthFactor: 0.25, // 更粗的圆环
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 只保留专注时长，移除"总专注时长"标签
                  Text(
                    '${totalHours.toStringAsFixed(1)}小时',
                    style: const TextStyle(
                      fontSize: 24, // 增大字体
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF212121),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        const SizedBox(height: 16),

        // 项目图例 - 优化布局，确保不会溢出
        Align(
          alignment: Alignment.center,
          child: Wrap(
            alignment: WrapAlignment.center,
            spacing: 8, // 减小间距
            runSpacing: 8, // 减小行间距
            children: sortedProjects.map((project) {
              final hours = projectHours[project.id] ?? 0;
              // 过滤掉专注时间为0的项目
              if (hours <= 0) return const SizedBox.shrink();

              final percentage = totalHours > 0 ? (hours / totalHours * 100) : 0;

              return Container(
                margin: const EdgeInsets.only(bottom: 4),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6), // 减小内边距
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: const Color(0xFFEEEEEE)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 10, // 减小图标大小
                      height: 10, // 减小图标大小
                      decoration: BoxDecoration(
                        color: projectColors[project.id],
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 6), // 减小间距
                    // 限制文本宽度，防止过长
                    ConstrainedBox(
                      constraints: const BoxConstraints(maxWidth: 100),
                      child: Text(
                        project.name,
                        style: const TextStyle(
                          fontSize: 12, // 减小字体大小
                          fontWeight: FontWeight.w500,
                        ),
                        overflow: TextOverflow.ellipsis, // 文本溢出时显示省略号
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${percentage.toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontSize: 11, // 减小字体大小
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              );
            }).where((widget) => widget != const SizedBox.shrink()).toList(), // 过滤掉空部件
          ),
        ),
      ],
    );
  }
}

/// 自定义项目圆环图绘制器 - 更粗的圆环
class CustomProjectDonutChartPainter extends CustomPainter {
  final List<Project> projects;
  final Map<String, double> projectHours;
  final Map<String, Color> projectColors;
  final double totalHours;
  final double strokeWidthFactor; // 圆环粗细因子

  CustomProjectDonutChartPainter({
    required this.projects,
    required this.projectHours,
    required this.projectColors,
    required this.totalHours,
    this.strokeWidthFactor = 0.2, // 默认值
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 2 * 0.85; // 增大半径比例
    final strokeWidth = radius * strokeWidthFactor; // 圆环粗细
    final innerRadius = radius - strokeWidth; // 内圆半径

    // 绘制背景圆环
    final bgPaint = Paint()
      ..color = Colors.grey.shade200
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    canvas.drawCircle(center, radius - strokeWidth / 2, bgPaint);

    // 如果没有数据，直接返回
    if (totalHours == 0) return;

    // 绘制各项目扇形
    double startAngle = -pi / 2; // 从12点钟方向开始

    for (final project in projects) {
      final hours = projectHours[project.id] ?? 0;
      if (hours <= 0) continue;

      final sweepAngle = 2 * pi * (hours / totalHours);
      final color = projectColors[project.id] ?? Colors.grey;

      final paint = Paint()
        ..color = color
        ..style = PaintingStyle.stroke
        ..strokeWidth = strokeWidth
        ..strokeCap = StrokeCap.butt;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius - strokeWidth / 2),
        startAngle,
        sweepAngle,
        false,
        paint,
      );

      startAngle += sweepAngle;
    }
  }

  @override
  bool shouldRepaint(CustomProjectDonutChartPainter oldDelegate) =>
      projects != oldDelegate.projects ||
      projectHours != oldDelegate.projectHours ||
      projectColors != oldDelegate.projectColors ||
      totalHours != oldDelegate.totalHours ||
      strokeWidthFactor != oldDelegate.strokeWidthFactor;
}
