import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/models/focus_record.dart';

/// 月视图专注热力图
/// 使用不同颜色表示每天的专注情况
class MonthlyHeatmap extends StatefulWidget {
  final List<FocusRecord> records;
  final DateTime selectedDate;

  const MonthlyHeatmap({
    super.key,
    required this.records,
    required this.selectedDate,
  });

  @override
  State<MonthlyHeatmap> createState() => _MonthlyHeatmapState();
}

/// 热力图显示模式
enum HeatmapDisplayMode {
  /// 简洁模式 - 类似GitHub热力图
  compact,

  /// 详细模式 - 显示具体专注时间
  detailed
}

class _MonthlyHeatmapState extends State<MonthlyHeatmap> {
  // 热力图数据
  late List<DailyFocusData> _heatmapData;

  // 最大专注时长（小时）
  late double _maxHours;

  // 颜色级别数量
  static const int _colorLevels = 5;

  // 当前显示模式
  HeatmapDisplayMode _displayMode = HeatmapDisplayMode.compact;

  @override
  void initState() {
    super.initState();
    _prepareHeatmapData();
  }

  @override
  void didUpdateWidget(MonthlyHeatmap oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.records != oldWidget.records ||
        widget.selectedDate != oldWidget.selectedDate) {
      _prepareHeatmapData();
    }
  }

  // 准备热力图数据
  void _prepareHeatmapData() {
    // 获取当月的第一天
    final firstDayOfMonth = DateTime(widget.selectedDate.year, widget.selectedDate.month, 1);

    // 获取当月的天数
    final daysInMonth = DateTime(widget.selectedDate.year, widget.selectedDate.month + 1, 0).day;

    // 获取当月第一天是星期几（0是星期日，1是星期一，...）
    final firstWeekday = firstDayOfMonth.weekday;

    // 按日期分组记录
    final Map<String, List<FocusRecord>> recordsByDate = {};

    // 初始化当月每天的记录列表
    for (int day = 1; day <= daysInMonth; day++) {
      final date = DateTime(widget.selectedDate.year, widget.selectedDate.month, day);
      final dateStr = DateFormat('yyyy-MM-dd').format(date);
      recordsByDate[dateStr] = [];
    }

    // 将记录按日期分组
    for (final record in widget.records) {
      final dateStr = DateFormat('yyyy-MM-dd').format(record.startTime);

      if (recordsByDate.containsKey(dateStr)) {
        recordsByDate[dateStr]!.add(record);
      }
    }

    // 计算每天的总专注时长
    final List<DailyFocusData> data = [];

    // 添加上个月的占位日期（用于日历格式的排列）
    final int daysFromPrevMonth = (firstWeekday == 7) ? 0 : firstWeekday;
    for (int i = 0; i < daysFromPrevMonth; i++) {
      data.add(DailyFocusData(
        date: firstDayOfMonth.subtract(Duration(days: daysFromPrevMonth - i)),
        hours: 0,
        isCurrentMonth: false,
      ));
    }

    // 添加当月的日期
    for (int day = 1; day <= daysInMonth; day++) {
      final date = DateTime(widget.selectedDate.year, widget.selectedDate.month, day);
      final dateStr = DateFormat('yyyy-MM-dd').format(date);
      final dayRecords = recordsByDate[dateStr] ?? [];

      final totalSeconds = dayRecords.fold<int>(
        0, (sum, record) => sum + record.durationSeconds);

      final hours = totalSeconds / 3600.0;

      data.add(DailyFocusData(
        date: date,
        hours: hours,
        isCurrentMonth: true,
      ));
    }

    // 添加下个月的占位日期（用于填充最后一行）
    final int totalDays = data.length;
    final int remainingDays = 7 - (totalDays % 7);
    if (remainingDays < 7) {
      final lastDate = DateTime(widget.selectedDate.year, widget.selectedDate.month, daysInMonth);
      for (int i = 1; i <= remainingDays; i++) {
        data.add(DailyFocusData(
          date: lastDate.add(Duration(days: i)),
          hours: 0,
          isCurrentMonth: false,
        ));
      }
    }

    _heatmapData = data;

    // 计算最大专注时长
    _maxHours = _heatmapData
        .where((data) => data.isCurrentMonth)
        .map((e) => e.hours)
        .fold(0.0, (max, hours) => hours > max ? hours : max);

    // 确保最大值至少为1
    _maxHours = _maxHours < 1.0 ? 1.0 : _maxHours;
  }

  // 获取热力图颜色
  Color _getHeatmapColor(double hours) {
    if (hours <= 0) {

      return const Color.fromARGB(255, 241, 241, 241);
      //return const Color(0xFFF8F9FA); // 无专注时间，使用更浅的背景色
    }

    // 计算颜色级别（0-4）
    final level = (hours / _maxHours * (_colorLevels - 1)).floor();

    // 使用更现代的色谱，从浅蓝色到深蓝色
    switch (level) {
      case 0:
        return const Color(0xFFE3F2FD); // 浅蓝色
      case 1:
        return const Color(0xFF90CAF9);
      case 2:
        return const Color(0xFF42A5F5);
      case 3:
        return const Color(0xFF1E88E5);
      case 4:
        return const Color(0xFF0D47A1); // 深蓝色
      default:
        return const Color(0xFFE3F2FD);
    }
  }

  // 格式化专注时长
  String _formatHours(double hours) {
    if (hours < 0.1) return '';
    if (hours < 1.0) return '${(hours * 60).round()}分钟';
    return '${hours.toStringAsFixed(1)}h';
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 月份标题和模式切换按钮
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              DateFormat('yyyy年MM月').format(widget.selectedDate),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: Color(0xFF424242),
              ),
            ),
            // 模式切换按钮
            InkWell(
              onTap: () {
                setState(() {
                  _displayMode = _displayMode == HeatmapDisplayMode.detailed
                      ? HeatmapDisplayMode.compact
                      : HeatmapDisplayMode.detailed;
                });
              },
              borderRadius: BorderRadius.circular(20),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: const Color(0xFFEEEEEE),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _displayMode == HeatmapDisplayMode.detailed
                          ? Icons.grid_view_rounded
                          : Icons.view_agenda_rounded,
                      size: 16,
                      color: const Color(0xFF616161),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _displayMode == HeatmapDisplayMode.detailed ? '详细' : '简洁',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF616161),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // 星期标题
        Padding(
          padding: const EdgeInsets.only(left: 4, right: 4, bottom: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: const [
              Text('一', style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: Color(0xFF757575))),
              Text('二', style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: Color(0xFF757575))),
              Text('三', style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: Color(0xFF757575))),
              Text('四', style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: Color(0xFF757575))),
              Text('五', style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: Color(0xFF757575))),
              Text('六', style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: Color(0xFF757575))),
              Text('日', style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: Color(0xFF757575))),
            ],
          ),
        ),

        // 热力图网格 - 根据显示模式选择不同的呈现方式
        _displayMode == HeatmapDisplayMode.detailed
            ? _buildDetailedHeatmap()
            : _buildCompactHeatmap(),

        const SizedBox(height: 12),

        // 图例
        _buildLegend(),

        const SizedBox(height: 12),

        // 最佳专注日和最长专注时间
        _buildBestFocusDay(),
      ],
    );
  }

  // 构建详细模式热力图
  Widget _buildDetailedHeatmap() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 7,
            childAspectRatio: 1.0,
            crossAxisSpacing: 4.0,
            mainAxisSpacing: 4.0,
          ),
          itemCount: _heatmapData.length,
          itemBuilder: (context, index) {
            final data = _heatmapData[index];
            final color = _getHeatmapColor(data.hours);

            // 判断是否是今天
            final isToday = data.date.year == DateTime.now().year &&
                            data.date.month == DateTime.now().month &&
                            data.date.day == DateTime.now().day;

            return Container(
              decoration: BoxDecoration(
                color: color,
                border: isToday
                    ? Border.all(color: const Color(0xFF1E88E5), width: 4)
                    : null,
              ),
              child: data.isCurrentMonth ? Stack(
                children: [
                  // if (isToday)
                  //   Positioned(
                  //     top: 0,
                  //     right: 0,
                  //     child: Container(
                  //       width: 6,
                  //       height: 6,
                  //       decoration: const BoxDecoration(
                  //         color: Color(0xFF1E88E5),
                  //         shape: BoxShape.circle,
                  //       ),
                  //     ),
                  //   ),
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '${data.date.day}',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                            color: _getTextColor(data.hours, isToday),
                          ),
                        ),
                        if (data.hours > 0)
                          Text(
                            _formatHours(data.hours),
                            style: TextStyle(
                              fontSize: 11,
                              color: _getTextColor(data.hours, isToday),
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ) : null,
            );
          },
        ),
      ),
    );
  }

  // 构建简洁模式热力图（类似GitHub风格）
  Widget _buildCompactHeatmap() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(8),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 7,
          childAspectRatio: 1.0,
          crossAxisSpacing: 8.0,
          mainAxisSpacing: 8.0,
        ),
        itemCount: _heatmapData.length,
        itemBuilder: (context, index) {
          final data = _heatmapData[index];
          final color = _getHeatmapColor(data.hours);

          // 判断是否是今天
          final isToday = data.date.year == DateTime.now().year &&
                          data.date.month == DateTime.now().month &&
                          data.date.day == DateTime.now().day;

          // 如果不是当月的日期，显示一个淡色的方块
          if (!data.isCurrentMonth) {
            return Container(
              decoration: BoxDecoration(
                color: const Color.fromARGB(255, 255, 255, 255),
                //color: const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(4),
              ),
            );
          }

          return Tooltip(
            message: '${DateFormat('MM月dd日').format(data.date)}\n专注时长: ${_formatHours(data.hours)}',
            child: Container(
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(4),
                border: isToday
                    ? Border.all(color: const Color(0xFF1E88E5), width: 2)
                    : null,
              ),
              child: Center(
                child: data.hours > 0
                    ? Text(
                        '${data.date.day}',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                          color: _getTextColor(data.hours, isToday),
                        ),
                      )
                    : null,
              ),
            ),
          );
        },
      ),
    );
  }

  // 构建图例
  Widget _buildLegend() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text('专注强度: ', style: TextStyle(fontSize: 12, color: Color(0xFF757575))),
        ...List.generate(
          _colorLevels,
          (index) => Container(
            width: 16,
            height: 16,
            margin: const EdgeInsets.symmetric(horizontal: 2),
            decoration: BoxDecoration(
              color: _getHeatmapColor(index == 0 ? 0.1 : _maxHours * index / (_colorLevels - 1)),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
        // const SizedBox(width: 8),
        // Text('最长: ${_formatHours(_maxHours)}', style: const TextStyle(fontSize: 12, color: Color(0xFF757575))),
      ],
    );
  }

  // 根据背景颜色深浅决定文本颜色
  Color _getTextColor(double hours, bool isToday) {
    if (isToday) {
      return Colors.white;
    }

    if (hours <= 0) {
      return const Color(0xFF9E9E9E);
    }

    final level = (hours / _maxHours * (_colorLevels - 1)).floor();

    // 深色背景用白色文本，浅色背景用深色文本
    return level >= 3 ? Colors.white : const Color(0xFF424242);
  }

  // 构建最佳专注日和最长专注时间
  Widget _buildBestFocusDay() {
    // 找出专注时间最长的一天
    final bestDay = _heatmapData
        .where((data) => data.isCurrentMonth)
        .reduce((a, b) => a.hours > b.hours ? a : b);

    if (bestDay.hours <= 0) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 最佳专注日
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF1E88E5).withAlpha(26),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.calendar_today,
                color: Color(0xFF1E88E5),
                size: 16,
              ),
            ),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '最佳专注日',
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF757575),
                  ),
                ),
                Text(
                  DateFormat('MM月dd日').format(bestDay.date),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF424242),
                  ),
                ),
              ],
            ),
          ],
        ),

        // 最长专注时间
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF1E88E5).withAlpha(26),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.access_time,
                color: Color(0xFF1E88E5),
                size: 16,
              ),
            ),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '最长专注时间',
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF757575),
                  ),
                ),
                Text(
                  _formatHours(bestDay.hours),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF424242),
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }
}

/// 每日专注数据
class DailyFocusData {
  final DateTime date;
  final double hours;
  final bool isCurrentMonth;

  DailyFocusData({
    required this.date,
    required this.hours,
    required this.isCurrentMonth,
  });
}
