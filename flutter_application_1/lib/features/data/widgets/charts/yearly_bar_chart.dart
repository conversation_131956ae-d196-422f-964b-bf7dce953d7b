import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import '../../../../core/models/focus_record.dart';

/// 年视图专注时长柱状图
/// 显示当年每月的专注时长数据，使用柱状图
class YearlyBarChart extends StatefulWidget {
  final List<FocusRecord> records;
  final DateTime selectedDate;

  const YearlyBarChart({
    super.key,
    required this.records,
    required this.selectedDate,
  });

  @override
  State<YearlyBarChart> createState() => _YearlyBarChartState();
}

class _YearlyBarChartState extends State<YearlyBarChart> {
  // 图表数据
  late List<MonthlyFocusData> _chartData;

  // 最大值
  late double _maxValue;

  @override
  void initState() {
    super.initState();
    _prepareChartData();
  }

  @override
  void didUpdateWidget(YearlyBarChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.records != oldWidget.records ||
        widget.selectedDate != oldWidget.selectedDate) {
      _prepareChartData();
    }
  }

  // 准备图表数据
  void _prepareChartData() {
    // 获取当年的年份
    final year = widget.selectedDate.year;

    // 按月分组记录
    final Map<int, List<FocusRecord>> recordsByMonth = {};

    // 初始化每月的记录列表
    for (int month = 1; month <= 12; month++) {
      recordsByMonth[month] = [];
    }

    // 将记录按月分组
    for (final record in widget.records) {
      if (record.startTime.year == year) {
        final month = record.startTime.month;
        recordsByMonth[month]!.add(record);
      }
    }

    // 计算每月的总专注时长
    final List<MonthlyFocusData> data = [];

    for (int month = 1; month <= 12; month++) {
      final monthRecords = recordsByMonth[month] ?? [];

      final totalSeconds = monthRecords.fold<int>(
        0, (sum, record) => sum + record.durationSeconds);

      final hours = totalSeconds / 3600.0;

      data.add(MonthlyFocusData(
        month: month,
        hours: hours,
        monthName: _getMonthName(month),
      ));
    }

    _chartData = data;

    // 计算最大值
    _maxValue = _chartData.isEmpty
        ? 1.0
        : _chartData.map((e) => e.hours).reduce((a, b) => a > b ? a : b);

    // 确保最大值至少为1
    _maxValue = _maxValue < 1.0 ? 1.0 : _maxValue;

    // 为了美观，将最大值向上取整
    _maxValue = (_maxValue * 1.2).ceilToDouble();
  }

  // 获取月份名称 - 使用数字表示
  String _getMonthName(int month) {
    return month.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Y轴标签和图表主体
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Y轴标签 - 竖向排列并只保留数字
              Padding(
                padding: const EdgeInsets.only(right: 4.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    RotatedBox(
                      quarterTurns: 3,
                      child: Text('小时', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    ),
                    const SizedBox(height: 4),
                    Text('${_maxValue.toInt()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    Text('${(_maxValue * 0.75).toInt()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    Text('${(_maxValue * 0.5).toInt()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    Text('${(_maxValue * 0.25).toInt()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    Text('0', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                  ],
                ),
              ),

              // 图表主体
              Expanded(
                child: CustomPaint(
                  painter: YearlyBarChartPainter(
                    data: _chartData,
                    maxValue: _maxValue,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 8),

        // X轴标签 - 使用固定布局确保对齐
        SizedBox(
          height: 20,
          child: Row(
            children: List.generate(
              _chartData.length,
              (index) => Expanded(
                child: Center(
                  child: Text(
                    _chartData[index].monthName,
                    style: TextStyle(fontSize: 10, color: Colors.grey.shade700),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// 每月专注数据
class MonthlyFocusData {
  final int month;
  final double hours;
  final String monthName;

  MonthlyFocusData({
    required this.month,
    required this.hours,
    required this.monthName,
  });
}

/// 年视图柱状图绘制器
class YearlyBarChartPainter extends CustomPainter {
  final List<MonthlyFocusData> data;
  final double maxValue;

  YearlyBarChartPainter({
    required this.data,
    required this.maxValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 绘制网格线
    _drawGrid(canvas, size);

    // 绘制柱状图
    _drawBars(canvas, size);

    // 绘制数据标签
    _drawLabels(canvas, size);
  }

  // 绘制网格线
  void _drawGrid(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withAlpha(40)
      ..strokeWidth = 1;

    // 横线（Y轴刻度线）
    for (int i = 0; i <= 4; i++) {
      final y = size.height - (i * size.height / 4);
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // 竖线（分隔每月）
    for (int i = 0; i <= 12; i++) {
      final x = i * size.width / 12;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }
  }

  // 绘制柱状图
  void _drawBars(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final barWidth = size.width / 12 * 0.6; // 柱宽为每月宽度的60%

    for (int i = 0; i < data.length; i++) {
      final x = i * size.width / 12 + (size.width / 12 - barWidth) / 2;
      final height = data[i].hours / maxValue * size.height;
      final y = size.height - height;

      // 统一使用蓝色主题
      const color = Color(0xFF2196F3); // 使用蓝色

      // 柱状图画笔
      final paint = Paint()
        ..color = color.withAlpha(100)
        ..style = PaintingStyle.fill;

      // 绘制柱状图
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(x, y, barWidth, height),
          const Radius.circular(4),
        ),
        paint,
      );

      // 绘制柱状图边框
      final borderPaint = Paint()
        ..color = color
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1;

      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(x, y, barWidth, height),
          const Radius.circular(4),
        ),
        borderPaint,
      );
    }
  }

  // 绘制数据标签
  void _drawLabels(Canvas canvas, Size size) {
    final textStyle = TextStyle(
      color: Colors.grey.shade700,
      fontSize: 9,
      fontWeight: FontWeight.bold,
    );

    for (int i = 0; i < data.length; i++) {
      if (data[i].hours <= 0) continue;

      final x = i * size.width / 12 + size.width / 24;
      final y = size.height - (data[i].hours / maxValue * size.height) - 15;

      final textSpan = TextSpan(
        text: data[i].hours.toStringAsFixed(1),
        style: textStyle,
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: ui.TextDirection.ltr,
        textAlign: TextAlign.center,
      );

      textPainter.layout();

      // 确保标签不超出顶部边界
      final adjustedY = y < textPainter.height ? 0 : y - textPainter.height / 2;

      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, adjustedY.toDouble()),
      );
    }
  }

  @override
  bool shouldRepaint(YearlyBarChartPainter oldDelegate) =>
    data != oldDelegate.data || maxValue != oldDelegate.maxValue;
}
