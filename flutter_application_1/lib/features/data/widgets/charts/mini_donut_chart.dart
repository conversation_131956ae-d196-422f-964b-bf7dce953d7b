import 'dart:math';
import 'package:flutter/material.dart';
import '../../models/subject_distribution.dart';

/// 迷你圆环图组件，用于今日科目分布卡片
class MiniDonutChart extends StatelessWidget {
  final List<SubjectDistribution> data;
  final double size;
  final List<Color> colors;
  final double strokeWidth;

  const MiniDonutChart({
    super.key,
    required this.data,
    this.size = 60,
    required this.colors,
    this.strokeWidth = 8,
  });

  @override
  Widget build(BuildContext context) {
    // 计算总专注时长
    double totalHours = 0;
    for (final item in data) {
      totalHours += item.hours;
    }

    return SizedBox(
      width: size,
      height: size,
      child: CustomPaint(
        painter: MiniDonutChartPainter(
          data: data,
          colors: colors,
          strokeWidth: strokeWidth,
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${totalHours.toStringAsFixed(1)}h',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF424242),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 迷你圆环图绘制器
class MiniDonutChartPainter extends CustomPainter {
  final List<SubjectDistribution> data;
  final List<Color> colors;
  final double strokeWidth;

  MiniDonutChartPainter({
    required this.data,
    required this.colors,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 2;

    // 计算总时长
    double totalHours = 0;
    for (final item in data) {
      totalHours += item.hours;
    }

    // 如果没有数据，绘制空圆环
    if (totalHours <= 0) {
      final bgPaint = Paint()
        ..color = Colors.grey.shade200
        ..style = PaintingStyle.stroke
        ..strokeWidth = strokeWidth;

      canvas.drawCircle(center, radius - strokeWidth / 2, bgPaint);
      return;
    }

    // 绘制各科目扇形
    double startAngle = -pi / 2; // 从12点钟方向开始

    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      if (item.hours <= 0) continue;

      final sweepAngle = 2 * pi * (item.hours / totalHours);
      final color = colors[i % colors.length];

      final paint = Paint()
        ..color = color
        ..style = PaintingStyle.stroke
        ..strokeWidth = strokeWidth
        ..strokeCap = StrokeCap.butt;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius - strokeWidth / 2),
        startAngle,
        sweepAngle,
        false,
        paint,
      );

      startAngle += sweepAngle;
    }
  }

  @override
  bool shouldRepaint(MiniDonutChartPainter oldDelegate) =>
      data != oldDelegate.data ||
      colors != oldDelegate.colors ||
      strokeWidth != oldDelegate.strokeWidth;
}
