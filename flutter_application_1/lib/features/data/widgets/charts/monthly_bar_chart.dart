import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/models/focus_record.dart';
import '../../../../shared/theme/constants.dart';
import '../../utils/time_period_utils.dart';

/// 月视图专注时长柱状图
/// 显示当月每周的专注时长数据，使用柱状图
class MonthlyBarChart extends StatefulWidget {
  final List<FocusRecord> records;
  final DateTime selectedDate;

  const MonthlyBarChart({
    super.key,
    required this.records,
    required this.selectedDate,
  });

  @override
  State<MonthlyBarChart> createState() => _MonthlyBarChartState();
}

class _MonthlyBarChartState extends State<MonthlyBarChart> {
  // 图表数据
  late List<WeeklyFocusData> _chartData;

  // 最大值
  late double _maxValue;

  @override
  void initState() {
    super.initState();
    _prepareChartData();
  }

  @override
  void didUpdateWidget(MonthlyBarChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.records != oldWidget.records ||
        widget.selectedDate != oldWidget.selectedDate) {
      _prepareChartData();
    }
  }

  // 准备图表数据
  void _prepareChartData() {
    // 获取当月的第一天
    final firstDayOfMonth = DateTime(widget.selectedDate.year, widget.selectedDate.month, 1);

    // 获取当月的最后一天
    final lastDayOfMonth = DateTime(widget.selectedDate.year, widget.selectedDate.month + 1, 0);

    // 获取当月第一天所在周的周一
    final firstWeekStart = TimePeriodUtils.getWeekStart(firstDayOfMonth);

    // 计算当月有多少周
    final int weeksCount = ((lastDayOfMonth.difference(firstWeekStart).inDays) / 7).ceil();

    // 创建每周的开始日期列表
    final List<DateTime> weekStarts = List.generate(
      weeksCount,
      (index) => firstWeekStart.add(Duration(days: index * 7))
    );

    // 按周分组记录
    final Map<String, List<FocusRecord>> recordsByWeek = {};

    // 初始化每周的记录列表
    for (final weekStart in weekStarts) {
      final weekEnd = weekStart.add(const Duration(days: 6));
      final weekKey = '${DateFormat('MM/dd').format(weekStart)}-${DateFormat('MM/dd').format(weekEnd)}';
      recordsByWeek[weekKey] = [];
    }

    // 将记录按周分组
    for (final record in widget.records) {
      for (final weekStart in weekStarts) {
        final weekEnd = weekStart.add(const Duration(days: 6));

        if (record.startTime.isAfter(weekStart.subtract(const Duration(seconds: 1))) &&
            record.startTime.isBefore(weekEnd.add(const Duration(days: 1)))) {
          final weekKey = '${DateFormat('MM/dd').format(weekStart)}-${DateFormat('MM/dd').format(weekEnd)}';
          recordsByWeek[weekKey]!.add(record);
          break;
        }
      }
    }

    // 计算每周的总专注时长
    final List<WeeklyFocusData> data = [];

    for (final weekStart in weekStarts) {
      final weekEnd = weekStart.add(const Duration(days: 6));
      final weekKey = '${DateFormat('MM/dd').format(weekStart)}-${DateFormat('MM/dd').format(weekEnd)}';
      final weekRecords = recordsByWeek[weekKey] ?? [];

      final totalSeconds = weekRecords.fold<int>(
        0, (sum, record) => sum + record.durationSeconds);

      final hours = totalSeconds / 3600.0;

      // 计算这一周是当月的第几周
      final weekNumber = ((weekStart.difference(firstWeekStart).inDays) / 7).floor() + 1;

      data.add(WeeklyFocusData(
        startDate: weekStart,
        endDate: weekEnd,
        hours: hours,
        label: '第$weekNumber周',
        dateRange: weekKey,
      ));
    }

    _chartData = data;

    // 计算最大值
    _maxValue = _chartData.isEmpty
        ? 1.0
        : _chartData.map((e) => e.hours).reduce((a, b) => a > b ? a : b);

    // 确保最大值至少为1
    _maxValue = _maxValue < 1.0 ? 1.0 : _maxValue;

    // 为了美观，将最大值向上取整
    _maxValue = (_maxValue * 1.2).ceilToDouble();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Y轴标签和图表主体
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Y轴标签 - 竖向排列并只保留数字
              Padding(
                padding: const EdgeInsets.only(right: 4.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Text('/h',style: TextStyle(fontSize: 12, color: Colors.grey.shade700)),
                    RotatedBox(
                      quarterTurns: 3,
                      child: Text('小时', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    ),
                    const SizedBox(height: 4),
                    Text('${_maxValue.toInt()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    Text('${(_maxValue * 0.75).toInt()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    Text('${(_maxValue * 0.5).toInt()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    Text('${(_maxValue * 0.25).toInt()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    Text('0', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                  ],
                ),
              ),

              // 图表主体
              Expanded(
                child: CustomPaint(
                  painter: MonthlyBarChartPainter(
                    data: _chartData,
                    maxValue: _maxValue,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 8),

        // X轴标签
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(
            _chartData.length,
            (index) => Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _chartData[index].label,
                  style: TextStyle(fontSize: 10, color: Colors.grey.shade700),
                ),
                Text(
                  _chartData[index].dateRange,
                  style: TextStyle(fontSize: 8, color: Colors.grey.shade500),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

/// 每周专注数据
class WeeklyFocusData {
  final DateTime startDate;
  final DateTime endDate;
  final double hours;
  final String label;
  final String dateRange;

  WeeklyFocusData({
    required this.startDate,
    required this.endDate,
    required this.hours,
    required this.label,
    required this.dateRange,
  });
}

/// 月视图柱状图绘制器
class MonthlyBarChartPainter extends CustomPainter {
  final List<WeeklyFocusData> data;
  final double maxValue;

  MonthlyBarChartPainter({
    required this.data,
    required this.maxValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 绘制网格线
    _drawGrid(canvas, size);

    // 绘制柱状图
    _drawBars(canvas, size);

    // 绘制数据标签
    _drawLabels(canvas, size);
  }

  // 绘制网格线
  void _drawGrid(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withAlpha(40)
      ..strokeWidth = 1;

    // 横线（Y轴刻度线）
    for (int i = 0; i <= 4; i++) {
      final y = size.height - (i * size.height / 4);
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // 竖线（分隔每周）
    final int weeksCount = data.length;
    for (int i = 0; i <= weeksCount; i++) {
      final x = i * size.width / weeksCount;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }
  }

  // 绘制柱状图
  void _drawBars(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final int weeksCount = data.length;
    final barWidth = size.width / weeksCount * 0.6; // 柱宽为每周宽度的60%

    for (int i = 0; i < data.length; i++) {
      final x = i * size.width / weeksCount + (size.width / weeksCount - barWidth) / 2;
      final height = data[i].hours / maxValue * size.height;
      final y = size.height - height;

      // 柱状图画笔
      final paint = Paint()
        ..color = AppColors.info.withAlpha(100)
        ..style = PaintingStyle.fill;

      // 绘制柱状图
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(x, y, barWidth, height),
          const Radius.circular(4),
        ),
        paint,
      );

      // 绘制柱状图边框
      final borderPaint = Paint()
        ..color = AppColors.info
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1;

      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(x, y, barWidth, height),
          const Radius.circular(4),
        ),
        borderPaint,
      );
    }
  }

  // 绘制数据标签
  void _drawLabels(Canvas canvas, Size size) {
    final textStyle = TextStyle(
      color: Colors.grey.shade700,
      fontSize: 10,
      fontWeight: FontWeight.bold,
    );

    for (int i = 0; i < data.length; i++) {
      if (data[i].hours <= 0) continue;

      final int weeksCount = data.length;
      final x = i * size.width / weeksCount + size.width / (weeksCount * 2);
      final y = size.height - (data[i].hours / maxValue * size.height) - 15;

      final textSpan = TextSpan(
        text: '${data[i].hours.toStringAsFixed(1)}h',
        style: textStyle,
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: ui.TextDirection.ltr,
        textAlign: TextAlign.center,
      );

      textPainter.layout();

      // 确保标签不超出顶部边界
      final adjustedY = y < textPainter.height ? 0 : y - textPainter.height / 2;

      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, adjustedY.toDouble()),
      );
    }
  }

  @override
  bool shouldRepaint(MonthlyBarChartPainter oldDelegate) =>
    data != oldDelegate.data || maxValue != oldDelegate.maxValue;
}
