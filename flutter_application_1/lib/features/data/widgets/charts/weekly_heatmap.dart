import 'package:flutter/material.dart';
import '../../../../core/models/focus_record.dart';
import '../../../../core/utils/date_util.dart';

/// 周专注热力图
/// 显示一周内不同时段的专注情况
class WeeklyHeatmap extends StatelessWidget {
  final List<FocusRecord> records;
  final DateTime selectedDate;

  const WeeklyHeatmap({
    super.key,
    required this.records,
    required this.selectedDate,
  });

  @override
  Widget build(BuildContext context) {
    // 计算本周的日期范围
    final weekRange = DateUtil.getWeekRange(selectedDate);

    // 筛选本周的记录
    final weekRecords = records.where((record) {
      return record.startTime.isAfter(weekRange.start.subtract(const Duration(seconds: 1))) &&
             record.startTime.isBefore(weekRange.end.add(const Duration(seconds: 1)));
    }).toList();

    // 计算热力图数据
    final heatmapData = _calculateHeatmapData(weekRecords, weekRange);

    // 找出最大专注时长，用于颜色渐变
    double maxMinutes = 0;
    for (final dayData in heatmapData) {
      for (final timeSlot in dayData) {
        if (timeSlot > maxMinutes) {
          maxMinutes = timeSlot;
        }
      }
    }

    // 如果没有数据，设置一个默认最大值
    if (maxMinutes == 0) {
      maxMinutes = 60; // 默认最大值为60分钟
    }

    return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 热力图
            _buildHeatmap(context, heatmapData, maxMinutes),

            const SizedBox(height: 16),

            // 图例
            _buildLegend(context, maxMinutes),

            // 日期范围
            Padding(
              padding: const EdgeInsets.only(top: 16),
              child: Text(
                '${_formatDate(weekRange.start)} - ${_formatDate(weekRange.end)}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ),
          ],
        );
  }

  // 构建热力图
  Widget _buildHeatmap(BuildContext context, List<List<double>> heatmapData, double maxMinutes) {
    // 星期标签
    final weekdayLabels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    // 时间段标签
    final timeSlotLabels = ['上午', '下午', '晚上'];

    return LayoutBuilder(
      builder: (context, constraints) {
        // 计算可用宽度
        final labelWidth = 50.0;
        final availableWidth = constraints.maxWidth - labelWidth;
        final cellSize = (availableWidth - 14) / 7; // 减去间距，7个格子平分

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 星期标签行
            Row(
              children: [
                // 左上角空白
                SizedBox(width: labelWidth, height: 24),

                // 星期标签
                SizedBox(
                  width: availableWidth,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: List.generate(7, (index) {
                      return SizedBox(
                        width: cellSize,
                        child: Center(
                          child: Text(
                            weekdayLabels[index],
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ),
                      );
                    }),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // 热力图主体
            ...List.generate(3, (rowIndex) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  children: [
                    // 时间段标签
                    SizedBox(
                      width: labelWidth,
                      height: cellSize,
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: Text(
                            timeSlotLabels[rowIndex],
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ),
                      ),
                    ),

                    // 热力格子
                    SizedBox(
                      width: availableWidth,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: List.generate(7, (colIndex) {
                          final minutes = heatmapData[colIndex][rowIndex];
                          final intensity = minutes / maxMinutes;

                          return SizedBox(
                            width: cellSize,
                            height: cellSize,
                            child: Tooltip(
                              message: '${minutes.toStringAsFixed(0)}分钟',
                              child: Container(
                                margin: const EdgeInsets.all(1),
                                decoration: BoxDecoration(
                                  color: _getHeatmapColor(intensity),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                              ),
                            ),
                          );
                        }),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        );
      },
    );
  }

  // 构建图例
  Widget _buildLegend(BuildContext context, double maxMinutes) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text(
          '专注强度',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
        const SizedBox(width: 8),
        ...List.generate(5, (index) {
          final intensity = index / 4;
          return Container(
            width: 20,
            height: 20,
            margin: const EdgeInsets.symmetric(horizontal: 2),
            decoration: BoxDecoration(
              color: _getHeatmapColor(intensity),
              borderRadius: BorderRadius.circular(2),
            ),
          );
        }),
        const SizedBox(width: 8),
        // Text(
        //   '较多 (${maxMinutes.toStringAsFixed(0)}分钟)',
        //   style: const TextStyle(
        //     fontSize: 12,
        //     color: Colors.grey,
        //   ),
        // ),
      ],
    );
  }

  // 计算热力图数据
  List<List<double>> _calculateHeatmapData(List<FocusRecord> records, DateTimeRange weekRange) {
    // 初始化数据结构：7天 x 3个时间段
    final heatmapData = List.generate(7, (_) => List.filled(3, 0.0));

    // 时间段划分：早上(6-12)、下午(12-18)、晚上(18-24)
    for (final record in records) {
      // 计算记录所在的星期几（0-6，对应周一到周日）
      final dayIndex = record.startTime.difference(weekRange.start).inDays;
      if (dayIndex < 0 || dayIndex >= 7) continue;

      // 计算记录所在的时间段
      int timeSlotIndex;
      final hour = record.startTime.hour;
      if (hour >= 6 && hour < 12) {
        timeSlotIndex = 0; // 早上
      } else if (hour >= 12 && hour < 18) {
        timeSlotIndex = 1; // 下午
      } else {
        timeSlotIndex = 2; // 晚上
      }

      // 累加专注时长（分钟）
      heatmapData[dayIndex][timeSlotIndex] += record.durationSeconds / 60;
    }

    return heatmapData;
  }

  // 获取热力图颜色
  Color _getHeatmapColor(double intensity) {
    // 使用GitHub风格的热力图颜色
    if (intensity <= 0) {
      return const Color(0xFFE3F2FD); // 最浅的颜色
    } else if (intensity < 0.25) {
      return const Color(0xFF90CAF9); // 浅绿色
    } else if (intensity < 0.5) {
      return const Color(0xFF42A5F5); // 中绿色
    } else if (intensity < 0.75) {
      return const Color(0xFF1E88E5); // 深绿色
    } else {
      return const Color(0xFF0D47A1); // 最深的绿色
    }
  }

  // 格式化日期
  String _formatDate(DateTime date) {
    return '${date.month}月${date.day}日';
  }
}
