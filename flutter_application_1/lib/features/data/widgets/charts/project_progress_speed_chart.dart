import 'package:flutter/material.dart';
import '../../../../core/models/focus_record.dart';
import '../../../../core/models/subject_project.dart';
import '../../../../shared/theme/constants.dart';

/// 项目专注推进速度计算模式
enum ProgressSpeedMode {
  byFocusTime, // 按专注时间计算
  byDay,       // 按天计算
}

/// 项目专注推进速度图表
/// 展示项目完成进度的速率
class ProjectProgressSpeedChart extends StatefulWidget {
  final List<FocusRecord> records;
  final List<Project> projects;
  final List<Subject> subjects;

  const ProjectProgressSpeedChart({
    super.key,
    required this.records,
    required this.projects,
    required this.subjects,
  });

  @override
  State<ProjectProgressSpeedChart> createState() => _ProjectProgressSpeedChartState();
}

class _ProjectProgressSpeedChartState extends State<ProjectProgressSpeedChart> {
  // 当前计算模式
  ProgressSpeedMode _mode = ProgressSpeedMode.byFocusTime;

  // 是否显示所有项目（包括非活跃项目）
  bool _showAllProjects = false;

  // 控制过滤选项浮窗的显示
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  // 控制提示信息浮窗的显示
  final LayerLink _infoLayerLink = LayerLink();
  OverlayEntry? _infoOverlayEntry;

  @override
  void dispose() {
    // 确保在组件销毁时关闭浮窗
    _hideFilterOptions();
    _hideInfoTooltip();
    super.dispose();
  }

  // 显示过滤选项浮窗
  void _showFilterOptions(BuildContext context) {
    // 先关闭已存在的浮窗
    _hideFilterOptions();

    // 获取按钮的位置信息
    final RenderBox? buttonBox = context.findRenderObject() as RenderBox?;
    if (buttonBox == null) return;

    // 获取按钮在屏幕上的位置
    final buttonPosition = buttonBox.localToGlobal(Offset.zero);

    // 创建浮窗
    _overlayEntry = OverlayEntry(
      builder: (context) {
        // 使用全屏覆盖以便捕获点击事件
        return Stack(
          children: [
            // 透明层，用于捕获点击事件关闭浮窗
            Positioned.fill(
              child: GestureDetector(
                onTap: _hideFilterOptions,
                behavior: HitTestBehavior.translucent,
                child: Container(
                  color: Colors.transparent,
                ),
              ),
            ),
            // 过滤选项菜单 - 位置对齐卡片右侧，底部刚好遮挡按钮
            Positioned(
              right: 16, // 与卡片右侧对齐
              top: buttonPosition.dy + 20, // 顶部位置调整，使底部刚好遮挡住按钮
              width: 200,
              child: GestureDetector(
                // 防止点击菜单时关闭浮窗
                onTap: () {},
                child: Material(
                  elevation: 4,
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 标题
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          child: Row(
                            children: [
                              const Icon(Icons.filter_list, size: 16),
                              const SizedBox(width: 8),
                              const Text(
                                '过滤项目',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const Divider(height: 1),
                        // 显示所有项目选项
                        ListTile(
                          dense: true,
                          title: const Text('显示所有项目'),
                          leading: Icon(
                            _showAllProjects ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                            color: _showAllProjects ? AppColors.primary : Colors.grey,
                            size: 18,
                          ),
                          onTap: () {
                            setState(() {
                              _showAllProjects = true;
                            });
                            _hideFilterOptions();
                          },
                        ),
                        // 仅显示活跃项目选项
                        ListTile(
                          dense: true,
                          title: const Text('仅显示活跃项目'),
                          subtitle: const Text('隐藏已归档/过期项目', style: TextStyle(fontSize: 12)),
                          leading: Icon(
                            !_showAllProjects ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                            color: !_showAllProjects ? AppColors.primary : Colors.grey,
                            size: 18,
                          ),
                          onTap: () {
                            setState(() {
                              _showAllProjects = false;
                            });
                            _hideFilterOptions();
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );

    // 显示浮窗
    Overlay.of(context).insert(_overlayEntry!);
  }

  // 隐藏过滤选项浮窗
  void _hideFilterOptions() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  // 显示信息提示浮窗
  void _showInfoTooltip(BuildContext context) {
    // 先关闭已存在的浮窗
    _hideInfoTooltip();

    // 获取按钮的位置信息
    final RenderBox? buttonBox = context.findRenderObject() as RenderBox?;
    if (buttonBox == null) return;

    // 获取按钮在屏幕上的位置
    final buttonPosition = buttonBox.localToGlobal(Offset.zero);

    // 创建浮窗
    _infoOverlayEntry = OverlayEntry(
      builder: (context) {
        // 使用全屏覆盖以便捕获点击事件
        return Stack(
          children: [
            // 透明层，用于捕获点击事件关闭浮窗
            Positioned.fill(
              child: GestureDetector(
                onTap: _hideInfoTooltip,
                behavior: HitTestBehavior.translucent,
                child: Container(
                  color: Colors.transparent,
                ),
              ),
            ),
            // 信息提示浮窗
            Positioned(
              right: 16, // 与卡片右侧对齐
              top: buttonPosition.dy + 20, // 顶部位置调整
              width: 250,
              child: Material(
                elevation: 4,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '推进速度计算方式',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '单位时间速度：项目每小时专注时间的进度推进百分比',
                        style: TextStyle(fontSize: 12),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        '日均推进速度：项目每天的进度推进百分比',
                        style: TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );

    // 显示浮窗
    Overlay.of(context).insert(_infoOverlayEntry!);
  }

  // 隐藏信息提示浮窗
  void _hideInfoTooltip() {
    _infoOverlayEntry?.remove();
    _infoOverlayEntry = null;
  }

  @override
  Widget build(BuildContext context) {
    // 筛选出开启了进度追踪的项目
    final trackedProjects = widget.projects.where((p) => p.isTrackingEnabled).toList();

    // 如果没有开启进度追踪的项目，显示空状态
    if (trackedProjects.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('暂无开启进度追踪的项目'),
        ),
      );
    }

    // 计算每个项目的专注时长
    final Map<String, double> projectHours = {};
    final Map<String, int> projectDays = {}; // 项目活跃天数
    final Map<String, DateTime> projectFirstDate = {}; // 项目首次专注日期
    final Map<String, DateTime> projectLastDate = {}; // 项目最后专注日期

    // 项目ID到科目ID的映射
    final Map<String, String> projectToSubject = {};
    for (final project in widget.projects) {
      projectToSubject[project.id] = project.subjectId;
    }

    // 计算每个项目的专注时长和活跃天数
    for (final record in widget.records) {
      // 专注时长
      if (!projectHours.containsKey(record.projectId)) {
        projectHours[record.projectId] = 0;
      }
      final hours = record.durationSeconds / 3600.0;
      projectHours[record.projectId] = (projectHours[record.projectId] ?? 0) + hours;

      // 记录项目首次和最后专注日期
      final recordDate = DateTime(record.startTime.year, record.startTime.month, record.startTime.day);

      if (!projectFirstDate.containsKey(record.projectId) ||
          recordDate.isBefore(projectFirstDate[record.projectId]!)) {
        projectFirstDate[record.projectId] = recordDate;
      }

      if (!projectLastDate.containsKey(record.projectId) ||
          recordDate.isAfter(projectLastDate[record.projectId]!)) {
        projectLastDate[record.projectId] = recordDate;
      }
    }

    // 计算项目活跃天数
    for (final project in trackedProjects) {
      if (projectFirstDate.containsKey(project.id) && projectLastDate.containsKey(project.id)) {
        final firstDate = projectFirstDate[project.id]!;
        final lastDate = projectLastDate[project.id]!;
        final difference = lastDate.difference(firstDate).inDays + 1; // 包括首尾两天
        projectDays[project.id] = difference;
      } else {
        projectDays[project.id] = 1; // 默认为1天
      }
    }

    // 计算项目推进速度
    final Map<String, double> projectSpeeds = {};
    final Map<String, double> projectDisplaySpeeds = {}; // 用于图表显示的速度（负值视为0）

    for (final project in trackedProjects) {
      double speed;
      if (_mode == ProgressSpeedMode.byFocusTime) {
        // 按专注时间计算：进度 / 专注时长
        final hours = projectHours[project.id] ?? 0;
        speed = hours > 0 ? (project.progress * 100) / hours : 0;
      } else {
        // 按天计算：进度 / 活跃天数
        final days = projectDays[project.id] ?? 1;
        speed = (project.progress * 100) / days;
      }

      // 存储原始速度值（可能为负）
      projectSpeeds[project.id] = speed;

      // 存储用于图表显示的速度值（负值视为0）
      projectDisplaySpeeds[project.id] = speed < 0 ? 0 : speed;
    }

    // 判断项目是否活跃
    bool isProjectActive(Project project) {
      // 已归档的项目不活跃
      if (project.isArchived) {
        return false;
      }

      final now = DateTime.now();
      // 未开启进度追踪的项目视为活跃
      if (!project.isTrackingEnabled) {
        return true;
      }

      // 在时间范围内且未完成的项目视为活跃
      return now.isAfter(project.startDate) &&
             now.isBefore(project.endDate) &&
             project.progress < 1.0;
    }

    // 获取项目状态文本
    String getProjectStatusText(Project project) {
      if (project.isArchived) {
        return '已归档';
      }

      final now = DateTime.now();
      if (project.progress >= 1.0) {
        return '已完成';
      } else if (now.isBefore(project.startDate)) {
        return '未开始';
      } else if (now.isAfter(project.endDate)) {
        return '已过期';
      } else {
        return '';
      }
    }

    // 过滤项目
    final filteredProjects = trackedProjects.where((project) {
      return _showAllProjects || isProjectActive(project);
    }).toList();

    // 按推进速度排序项目，但在单位专注时间模式下，将专注时间模式的项目放在最底部
    if (_mode == ProgressSpeedMode.byFocusTime) {
      // 分离专注时间模式项目和其他项目
      final timeTrackingProjects = filteredProjects.where(
        (project) => project.trackingMode == ProgressTrackingMode.focusTime
      ).toList();

      final otherProjects = filteredProjects.where(
        (project) => project.trackingMode != ProgressTrackingMode.focusTime
      ).toList();

      // 分别排序
      otherProjects.sort((a, b) {
        final aSpeed = projectSpeeds[a.id] ?? 0;
        final bSpeed = projectSpeeds[b.id] ?? 0;
        return bSpeed.compareTo(aSpeed); // 降序排列
      });

      timeTrackingProjects.sort((a, b) {
        final aSpeed = projectSpeeds[a.id] ?? 0;
        final bSpeed = projectSpeeds[b.id] ?? 0;
        return bSpeed.compareTo(aSpeed); // 降序排列
      });

      // 合并列表，专注时间模式项目放在最底部
      filteredProjects.clear();
      filteredProjects.addAll(otherProjects);
      filteredProjects.addAll(timeTrackingProjects);
    } else {
      // 日均推进速度模式下，正常排序
      filteredProjects.sort((a, b) {
        final aSpeed = projectSpeeds[a.id] ?? 0;
        final bSpeed = projectSpeeds[b.id] ?? 0;
        return bSpeed.compareTo(aSpeed); // 降序排列
      });
    }

    // 获取科目颜色映射
    final Map<String, Color> subjectColors = {};
    for (final subject in widget.subjects) {
      subjectColors[subject.id] = Color(subject.color);
    }

    // 计算最大推进速度，用于确定柱状图的最大宽度
    // 使用 projectDisplaySpeeds（负值视为0）
    final maxSpeed = filteredProjects.fold<double>(
      0, (max, project) => (projectDisplaySpeeds[project.id] ?? 0) > max ? (projectDisplaySpeeds[project.id] ?? 0) : max);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题和过滤按钮
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '项目推进速度',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            // 过滤按钮 - 使用图标按钮，与项目专注时间排行卡片一致
            CompositedTransformTarget(
              link: _layerLink,
              child: IconButton(
                onPressed: () {
                  _showFilterOptions(context);
                },
                icon: Icon(
                  Icons.filter_list,
                  size: 20,
                  color: _showAllProjects ? AppColors.primary : Colors.grey.shade600,
                ),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                splashRadius: 20,
                tooltip: '过滤项目',
              ),
            ),
          ],
        ),

        // 第二排：模式切换和信息按钮 - 使用Stack实现精确居中
        Padding(
          padding: const EdgeInsets.only(top: 8, bottom: 16),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // 模式切换按钮（居中）
              Center(
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF5F5F5),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildModeButton(ProgressSpeedMode.byFocusTime, '单位时间速度'),
                      _buildModeButton(ProgressSpeedMode.byDay, '日均推进速度'),
                    ],
                  ),
                ),
              ),

              // 信息提示按钮（右侧）
              Positioned(
                right: 0,
                child: CompositedTransformTarget(
                  link: _infoLayerLink,
                  child: GestureDetector(
                    onLongPress: () {
                      _showInfoTooltip(context);
                    },
                    child: IconButton(
                      onPressed: () {
                        _showInfoTooltip(context);
                      },
                      icon: Icon(
                        Icons.info_outline,
                        size: 16,
                        color: Colors.grey.shade600,
                      ),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      splashRadius: 16,
                      tooltip: '查看说明',
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // 项目列表滚动区域
        Expanded(
          child: filteredProjects.isEmpty
              ? Center(
                  child: Text(
                    '暂无项目数据',
                    style: TextStyle(color: Colors.grey.shade600),
                  ),
                )
              : SingleChildScrollView(
                  child: Column(
                    children: [
                      // 柱状图
                      ...filteredProjects.map((project) {
          // 使用原始速度值（可能为负）用于显示数字
          final speed = projectSpeeds[project.id] ?? 0;
          // 使用处理后的速度值（负值视为0）用于图表渲染
          final displaySpeed = projectDisplaySpeeds[project.id] ?? 0;
          final subjectId = projectToSubject[project.id] ?? '';
          final color = subjectColors[subjectId] ?? Colors.grey;

          // 查找项目对应的科目
          final subject = widget.subjects.firstWhere(
            (s) => s.id == subjectId,
            orElse: () => Subject(id: '', name: '未知科目', color: 0xFF9E9E9E),
          );

          // 对于追踪模式为专注时间的项目，在按专注时间模式下使用灰色显示
          final isTimeTracking = project.trackingMode == ProgressTrackingMode.focusTime;
          final useGrayColor = _mode == ProgressSpeedMode.byFocusTime && isTimeTracking;
          final barColor = useGrayColor ? Colors.grey.shade400 : color;

          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 第一排：项目名称和推进速度
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              project.name,
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                                color: isProjectActive(project) ? Colors.black87 : Colors.grey.shade600,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          // 项目状态标签
                          if (!isProjectActive(project))
                            Container(
                              margin: const EdgeInsets.only(left: 8),
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade200,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                getProjectStatusText(project),
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    // 推进速度
                    // RichText(
                    //   textAlign: TextAlign.right,
                    //   text: TextSpan(
                    //     children: [
                    //       TextSpan(
                    //         text: '${speed.toStringAsFixed(1)}%',
                    //         style: TextStyle(
                    //           fontSize: 15,
                    //           fontWeight: FontWeight.bold,
                    //           color: useGrayColor ? Colors.grey.shade600 : Colors.black87,
                    //         ),
                    //       ),
                    //       TextSpan(
                    //         text: ' ${_mode == ProgressSpeedMode.byFocusTime ? '每小时' : '每天'}',
                    //         style: TextStyle(
                    //           fontSize: 11,
                    //           color: Colors.grey.shade500,
                    //         ),
                    //       ),
                    //     ],
                    //   ),
                    // ),
                  ],
                ),

                const SizedBox(height: 2),

                // 第二排：科目颜色标记和科目名称
                Row(
                  children: [
                    // 科目颜色标记
                    Container(
                      width: 10,
                      height: 10,
                      decoration: BoxDecoration(
                        color: useGrayColor ? Colors.grey.shade400 : color,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 6),
                    // 科目名称
                    Expanded(
                      child: Text(
                        subject.name,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                        RichText(
                      textAlign: TextAlign.right,
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: '${speed.toStringAsFixed(1)}%',
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                              color: useGrayColor ? Colors.grey.shade600 : Colors.black87,
                            ),
                          ),
                          TextSpan(
                            text: ' ${_mode == ProgressSpeedMode.byFocusTime ? '每小时' : '每天'}',
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.grey.shade500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 4),

                // 对比柱状图 - 调整颜色使其更弱化
                Container(
                  height: 10,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100, // 更灰的背景色
                    borderRadius: BorderRadius.circular(5),
                  ),
                  child: Stack(
                    children: [
                      // 对比条 - 使用弱化的颜色
                      FractionallySizedBox(
                        widthFactor: maxSpeed > 0 ? (displaySpeed / maxSpeed).clamp(0.0, 1.0) : 0,
                        child: Container(
                          height: 10,
                          decoration: BoxDecoration(
                            // 使用弱化的科目颜色
                            color: barColor.withAlpha((0.6 * 255).toInt()), // 降低透明度
                            borderRadius: BorderRadius.circular(5),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // 如果是专注时间模式的项目，在按专注时间计算时显示提示
                if (useGrayColor)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          size: 12,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '自动同步专注时间',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey.shade600,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          );
        }),
                    ],
                  ),
                ),
        ),
      ],
    );
  }

  // 构建模式切换按钮
  Widget _buildModeButton(ProgressSpeedMode mode, String label) {
    final isSelected = _mode == mode;

    return GestureDetector(
      onTap: () {
        setState(() {
          _mode = mode;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: Colors.black.withAlpha(13),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ]
              : null,
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 11, // 减小字体大小以适应更长的文本
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            color: isSelected ? AppColors.primary : Colors.grey.shade700, // 使用绿色主色系
          ),
        ),
      ),
    );
  }
}
