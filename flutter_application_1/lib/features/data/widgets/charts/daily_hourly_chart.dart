import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../../../shared/theme/constants.dart';
import '../painters/grid_painter.dart';
import '../../../../core/models/focus_record.dart';

/// 日视图下的小时分布图
/// 显示当天每小时的专注时长分布
class DailyHourlyChart extends StatelessWidget {
  final List<FocusRecord> records;

  const DailyHourlyChart({
    super.key,
    required this.records,
  });

  @override
  Widget build(BuildContext context) {
    // 获取当天每小时的专注时长
    final Map<int, int> hourlyData = {};

    // 初始化所有小时为0
    for (int i = 0; i < 24; i++) {
      hourlyData[i] = 0;
    }

    // 统计当天每小时的专注时长
    for (final record in records) {
      final hour = record.startTime.hour;
      hourlyData[hour] = (hourlyData[hour] ?? 0) + record.durationSeconds;
    }

    // 检查是否有专注数据
    final hasData = hourlyData.values.any((duration) => duration > 0);

    if (!hasData) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bar_chart, size: 48, color: Colors.grey.shade300),
            const SizedBox(height: 16),
            Text(
              '暂无专注数据',
              style: TextStyle(color: Colors.grey.shade600),
            ),
          ],
        ),
      );
    }

    // 每小时最大专注时长为60分钟
    final maxPossibleMinutes = 60;
    final maxPossibleSeconds = maxPossibleMinutes * 60;

    // 将数据按照两小时为一段进行分组
    final Map<int, int> twoHourlyData = {};
    final List<String> timeLabels = [];

    // 初始化分组数据
    for (int i = 0; i < 12; i++) {
      twoHourlyData[i] = 0;
      final startHour = i * 2;
      final endHour = startHour + 2;
      timeLabels.add('$startHour-$endHour');
    }

    // 将小时数据合并为两小时一组
    for (int hour = 0; hour < 24; hour++) {
      final groupIndex = hour ~/ 2;
      twoHourlyData[groupIndex] = (twoHourlyData[groupIndex] ?? 0) + (hourlyData[hour] ?? 0);
    }

    return Column(
      children: [
        // 图表区域
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Y轴刻度
              Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text('120分钟', style: TextStyle(fontSize: 12, color: Colors.grey.shade700)),
                    Text('90分钟', style: TextStyle(fontSize: 12, color: Colors.grey.shade700)),
                    Text('60分钟', style: TextStyle(fontSize: 12, color: Colors.grey.shade700)),
                    Text('30分钟', style: TextStyle(fontSize: 12, color: Colors.grey.shade700)),
                    Text('0', style: TextStyle(fontSize: 12, color: Colors.grey.shade700)),
                  ],
                ),
              ),

              // 柱状图
              Expanded(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    final barWidth = 16.0; // 更宽的柱子，因为现在只有12个分组
                    final groupWidth = constraints.maxWidth / 12;
                    final maxTwoHourSeconds = maxPossibleSeconds * 2; // 两小时的最大秒数

                    return Stack(
                      children: [
                        // 背景网格
                        Positioned.fill(
                          child: CustomPaint(
                            painter: GridPainter(horizontalLinesCount: 5), // 增加水平线数量
                          ),
                        ),

                        // 所有分组的背景柱子（表示120分钟上限）
                        ...List.generate(12, (groupIndex) {
                          return Positioned(
                            left: groupIndex * groupWidth + (groupWidth - barWidth) / 2,
                            bottom: 0,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                // 60分钟标记线
                                Container(
                                  width: barWidth + 4,
                                  height: 1,
                                  color: Colors.grey.shade300,
                                  margin: EdgeInsets.only(bottom: constraints.maxHeight * 0.95 / 2),
                                ),
                                // 背景柱子
                                Container(
                                  width: barWidth,
                                  height: constraints.maxHeight * 0.95,
                                  decoration: BoxDecoration(
                                    color: Colors.grey.shade100,
                                    borderRadius: const BorderRadius.only(
                                      topLeft: Radius.circular(4),
                                      topRight: Radius.circular(4),
                                    ),
                                    border: Border.all(color: Colors.grey.shade200, width: 1),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }),

                        // 实际专注时长的柱子
                        ...List.generate(12, (groupIndex) {
                          final duration = twoHourlyData[groupIndex] ?? 0;
                          final minutes = duration / 60;
                          final heightPercentage = duration / maxTwoHourSeconds;
                          final height = constraints.maxHeight * 0.95 * heightPercentage;

                          // 计算颜色 - 使用分组的第一个小时的颜色
                          final Color barColor = _getBarColor(groupIndex * 2);

                          // 根据时间段调整颜色饱和度
                          final Color adjustedColor = HSLColor.fromColor(barColor)
                              .withSaturation(math.min(1.0, HSLColor.fromColor(barColor).saturation + 0.2))
                              .withLightness(math.max(0.3, HSLColor.fromColor(barColor).lightness - 0.1))
                              .toColor();

                          final startHour = groupIndex * 2;
                          final endHour = startHour + 2;

                          // 判断是否是当天最高的专注时长
                          final isHighest = duration == twoHourlyData.values.reduce((a, b) => a > b ? a : b) && duration > 0;

                          return Positioned(
                            left: groupIndex * groupWidth + (groupWidth - barWidth) / 2,
                            bottom: 0,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // 如果是最高值，显示标签
                                if (isHighest && minutes > 0)
                                  Transform.translate(
                                    offset: const Offset(0, -24),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: adjustedColor,
                                        borderRadius: BorderRadius.circular(10),
                                        boxShadow: [
                                          BoxShadow(
                                            color: adjustedColor.withAlpha(100),
                                            blurRadius: 3,
                                            offset: const Offset(0, 1),
                                          ),
                                        ],
                                      ),
                                      child: Text(
                                        '${minutes.round()}分钟',
                                        style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                  ),

                                // 专注时长柱子
                                Tooltip(
                                  message: '$startHour:00 - $endHour:00\n${minutes.round()}分钟',
                                  child: Container(
                                    width: barWidth,
                                    height: height > 0 ? math.max(height, 2) : 0,
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.bottomCenter,
                                        end: Alignment.topCenter,
                                        colors: [
                                          adjustedColor,
                                          adjustedColor.withAlpha(200),
                                        ],
                                      ),
                                      borderRadius: const BorderRadius.only(
                                        topLeft: Radius.circular(4),
                                        topRight: Radius.circular(4),
                                      ),
                                      boxShadow: height > 10 ? [
                                        BoxShadow(
                                          color: adjustedColor.withAlpha(100),
                                          blurRadius: 3,
                                          offset: const Offset(0, 1),
                                        ),
                                      ] : null,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }),

                        // 添加分组标记线
                        ...List.generate(13, (i) {
                          return Positioned(
                            left: i * groupWidth,
                            bottom: 0,
                            top: 0,
                            child: Container(
                              width: 1,
                              color: i % 3 == 0 ? Colors.grey.shade300 : Colors.transparent,
                            ),
                          );
                        }),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),

        // X轴刻度
        const SizedBox(height: 8),
        SizedBox(
          height: 20,
          child: Row(
            children: [
              // 留出与Y轴对齐的空间
              const SizedBox(width: 40),

              // X轴刻度
              Expanded(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: List.generate(7, (i) {
                        final hour = i * 4;
                        return Text(
                          '$hour:00',
                          style: TextStyle(fontSize: 11, color: Colors.grey.shade700),
                        );
                      }),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 获取柱状图颜色
  Color _getBarColor(int hour) {
    // 根据不同时段返回不同颜色
    if (hour >= 0 && hour < 6) {
      // 凌晨（深蓝色）
      return Colors.indigo;
    } else if (hour >= 6 && hour < 12) {
      // 上午（绿色）
      return AppColors.primary;
    } else if (hour >= 12 && hour < 18) {
      // 下午（橙色）
      return Colors.orange;
    } else {
      // 晚上（紫色）
      return Colors.purple;
    }
  }
}
