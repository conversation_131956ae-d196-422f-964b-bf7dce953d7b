import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/models/focus_record.dart';
import '../../../../shared/theme/constants.dart';
import '../../utils/time_period_utils.dart';

/// 周视图专注时长趋势图
/// 完整显示周一到周日七天的数据，无论当天是否有专注记录
class WeeklyFocusChart extends StatefulWidget {
  final List<FocusRecord> records;
  final DateTime selectedDate;

  const WeeklyFocusChart({
    super.key,
    required this.records,
    required this.selectedDate,
  });

  @override
  State<WeeklyFocusChart> createState() => _WeeklyFocusChartState();
}

class _WeeklyFocusChartState extends State<WeeklyFocusChart> {
  // 图表数据
  late List<DailyFocusData> _chartData;
  
  // 最大值
  late double _maxValue;
  
  // 本周和上周的总专注时长
  late double _thisWeekTotalHours;
  late double _lastWeekTotalHours;
  
  // 本周和上周的专注次数
  late int _thisWeekSessionCount;
  late int _lastWeekSessionCount;
  
  @override
  void initState() {
    super.initState();
    _prepareChartData();
  }
  
  @override
  void didUpdateWidget(WeeklyFocusChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.records != oldWidget.records || 
        widget.selectedDate != oldWidget.selectedDate) {
      _prepareChartData();
    }
  }
  
  // 准备图表数据
  void _prepareChartData() {
    // 获取本周的开始日期（周一）
    final weekStart = TimePeriodUtils.getWeekStart(widget.selectedDate);
    
    // 创建本周七天的日期列表（周一到周日）
    final List<DateTime> weekDays = List.generate(
      7, 
      (index) => weekStart.add(Duration(days: index))
    );
    
    // 按日期分组记录
    final Map<String, List<FocusRecord>> recordsByDate = {};
    
    // 初始化本周七天的记录列表
    for (final day in weekDays) {
      final dateStr = DateFormat('yyyy-MM-dd').format(day);
      recordsByDate[dateStr] = [];
    }
    
    // 将记录按日期分组
    for (final record in widget.records) {
      final dateStr = DateFormat('yyyy-MM-dd').format(record.startTime);
      
      if (recordsByDate.containsKey(dateStr)) {
        recordsByDate[dateStr]!.add(record);
      }
    }
    
    // 计算每天的总专注时长
    final List<DailyFocusData> data = [];
    
    for (final day in weekDays) {
      final dateStr = DateFormat('yyyy-MM-dd').format(day);
      final dayRecords = recordsByDate[dateStr] ?? [];
      
      final totalSeconds = dayRecords.fold<int>(
        0, (sum, record) => sum + record.durationSeconds);
      
      final hours = totalSeconds / 3600.0;
      
      data.add(DailyFocusData(
        date: day,
        hours: hours,
        dayOfWeek: _getDayOfWeekName(day.weekday),
      ));
    }
    
    _chartData = data;
    
    // 计算最大值
    _maxValue = _chartData.isEmpty 
        ? 1.0 
        : _chartData.map((e) => e.hours).reduce((a, b) => a > b ? a : b);
    
    // 确保最大值至少为1
    _maxValue = _maxValue < 1.0 ? 1.0 : _maxValue;
    
    // 为了美观，将最大值向上取整
    _maxValue = (_maxValue * 1.2).ceilToDouble();
    
    // 计算本周总专注时长
    _thisWeekTotalHours = _chartData.fold<double>(
      0, (sum, data) => sum + data.hours);
    
    // 计算本周专注次数
    _thisWeekSessionCount = widget.records.length;
    
    // 计算上周的数据
    _calculateLastWeekData();
  }
  
  // 计算上周的数据
  void _calculateLastWeekData() {
    // 获取上周的开始和结束日期
    final thisWeekStart = TimePeriodUtils.getWeekStart(widget.selectedDate);
    final lastWeekStart = thisWeekStart.subtract(const Duration(days: 7));
    final lastWeekEnd = thisWeekStart.subtract(const Duration(days: 1));
    
    // 过滤出上周的记录
    final lastWeekRecords = widget.records.where((record) {
      final recordDate = record.startTime;
      return recordDate.isAfter(lastWeekStart.subtract(const Duration(seconds: 1))) && 
             recordDate.isBefore(lastWeekEnd.add(const Duration(days: 1)));
    }).toList();
    
    // 计算上周总专注时长
    _lastWeekTotalHours = lastWeekRecords.fold<int>(
      0, (sum, record) => sum + record.durationSeconds) / 3600.0;
    
    // 计算上周专注次数
    _lastWeekSessionCount = lastWeekRecords.length;
  }
  
  // 获取星期几的名称
  String _getDayOfWeekName(int weekday) {
    switch (weekday) {
      case 1: return '周一';
      case 2: return '周二';
      case 3: return '周三';
      case 4: return '周四';
      case 5: return '周五';
      case 6: return '周六';
      case 7: return '周日';
      default: return '';
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 专注时长趋势图
        Text(
          '专注时长趋势',
          style: AppTextStyles.headline3,
        ),
        const SizedBox(height: 16),
        Container(
          height: 250,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [AppShadows.low],
          ),
          child: _chartData.isEmpty
              ? const Center(child: Text('暂无数据'))
              : Column(
                  children: [
                    // Y轴标签和图表主体
                    Expanded(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Y轴标签 - 竖向排列并只保留数字
                          Padding(
                            padding: const EdgeInsets.only(right: 4.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                RotatedBox(
                                  quarterTurns: 3,
                                  child: Text('小时', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                                ),
                                const SizedBox(height: 4),
                                Text('${_maxValue.toInt()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                                Text('${(_maxValue * 0.75).toInt()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                                Text('${(_maxValue * 0.5).toInt()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                                Text('${(_maxValue * 0.25).toInt()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                                Text('0', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                              ],
                            ),
                          ),
                          
                          // 图表主体
                          Expanded(
                            child: CustomPaint(
                              painter: WeeklyChartPainter(
                                data: _chartData,
                                maxValue: _maxValue,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // X轴标签
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: List.generate(
                        _chartData.length,
                        (index) => Text(
                          _chartData[index].dayOfWeek,
                          style: TextStyle(fontSize: 10, color: Colors.grey.shade700),
                        ),
                      ),
                    ),
                  ],
                ),
        ),
        
        const SizedBox(height: 24),
        
        // 本周与上周对比
        Text(
          '本周与上周对比',
          style: AppTextStyles.headline3,
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [AppShadows.low],
          ),
          child: Column(
            children: [
              // 专注时长对比
              Row(
                children: [
                  Expanded(
                    child: _buildComparisonItem(
                      title: '专注时长',
                      thisWeekValue: '${_thisWeekTotalHours.toStringAsFixed(1)}小时',
                      lastWeekValue: '${_lastWeekTotalHours.toStringAsFixed(1)}小时',
                      change: _calculateChange(_thisWeekTotalHours, _lastWeekTotalHours),
                      icon: Icons.access_time,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildComparisonItem(
                      title: '专注次数',
                      thisWeekValue: '$_thisWeekSessionCount次',
                      lastWeekValue: '$_lastWeekSessionCount次',
                      change: _calculateChange(_thisWeekSessionCount.toDouble(), _lastWeekSessionCount.toDouble()),
                      icon: Icons.repeat,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              
              // 提示信息
              Row(
                children: [
                  Icon(Icons.info_outline, size: 16, color: Colors.grey.shade600),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '对比上周，您的专注情况${_getComparisonTip()}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  // 构建对比项
  Widget _buildComparisonItem({
    required String title,
    required String thisWeekValue,
    required String lastWeekValue,
    required double change,
    required IconData icon,
  }) {
    final isPositive = change > 0;
    final isZero = change == 0;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.primary.withAlpha(26),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: AppColors.primary, size: 16),
            ),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  thisWeekValue,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Row(
                  children: [
                    Text(
                      '上周: $lastWeekValue',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(width: 4),
                    if (!isZero)
                      Icon(
                        isPositive ? Icons.arrow_upward : Icons.arrow_downward,
                        size: 12,
                        color: isPositive ? Colors.green : Colors.red,
                      ),
                    if (!isZero)
                      Text(
                        '${change.abs().toStringAsFixed(1)}%',
                        style: TextStyle(
                          fontSize: 12,
                          color: isPositive ? Colors.green : Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    if (isZero)
                      Text(
                        '持平',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }
  
  // 计算变化百分比
  double _calculateChange(double current, double previous) {
    if (previous == 0) {
      return current > 0 ? 100 : 0;
    }
    
    return ((current - previous) / previous) * 100;
  }
  
  // 获取对比提示信息
  String _getComparisonTip() {
    final timeChange = _calculateChange(_thisWeekTotalHours, _lastWeekTotalHours);
    final countChange = _calculateChange(_thisWeekSessionCount.toDouble(), _lastWeekSessionCount.toDouble());
    
    if (timeChange > 10 && countChange > 10) {
      return '有显著提升，继续保持！';
    } else if (timeChange > 0 && countChange > 0) {
      return '有所提升，继续加油！';
    } else if (timeChange < -10 && countChange < -10) {
      return '有明显下降，建议增加专注时间';
    } else if (timeChange < 0 && countChange < 0) {
      return '略有下降，可以适当增加专注';
    } else if (timeChange.abs() < 5 && countChange.abs() < 5) {
      return '基本持平，保持稳定';
    } else {
      return '变化不一，建议保持规律专注';
    }
  }
}

/// 每日专注数据
class DailyFocusData {
  final DateTime date;
  final double hours;
  final String dayOfWeek;
  
  DailyFocusData({
    required this.date,
    required this.hours,
    required this.dayOfWeek,
  });
}

/// 周视图图表绘制器
class WeeklyChartPainter extends CustomPainter {
  final List<DailyFocusData> data;
  final double maxValue;
  
  WeeklyChartPainter({
    required this.data,
    required this.maxValue,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // 绘制网格线
    _drawGrid(canvas, size);
    
    // 绘制柱状图
    _drawBars(canvas, size);
    
    // 绘制折线
    _drawLine(canvas, size);
    
    // 绘制数据点
    _drawPoints(canvas, size);
  }
  
  // 绘制网格线
  void _drawGrid(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withAlpha(40)
      ..strokeWidth = 1;
    
    // 横线（Y轴刻度线）
    for (int i = 0; i <= 4; i++) {
      final y = size.height - (i * size.height / 4);
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
    
    // 竖线（分隔每天）
    for (int i = 0; i <= 7; i++) {
      final x = i * size.width / 7;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }
  }
  
  // 绘制柱状图
  void _drawBars(Canvas canvas, Size size) {
    if (data.isEmpty) return;
    
    final barWidth = size.width / 7 * 0.6; // 柱宽为每天宽度的60%
    
    for (int i = 0; i < data.length; i++) {
      final x = i * size.width / 7 + (size.width / 7 - barWidth) / 2;
      final height = data[i].hours / maxValue * size.height;
      final y = size.height - height;
      
      // 根据星期几选择不同的颜色
      final color = _getBarColor(data[i].date.weekday);
      
      // 柱状图画笔
      final paint = Paint()
        ..color = color.withAlpha(100)
        ..style = PaintingStyle.fill;
      
      // 绘制柱状图
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(x, y, barWidth, height),
          const Radius.circular(4),
        ),
        paint,
      );
      
      // 绘制柱状图边框
      final borderPaint = Paint()
        ..color = color
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1;
      
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(x, y, barWidth, height),
          const Radius.circular(4),
        ),
        borderPaint,
      );
    }
  }
  
  // 绘制折线
  void _drawLine(Canvas canvas, Size size) {
    if (data.isEmpty) return;
    
    final paint = Paint()
      ..color = AppColors.primary
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    
    final path = Path();
    
    for (int i = 0; i < data.length; i++) {
      final x = i * size.width / 7 + size.width / 14; // 每天的中心点
      final y = size.height - (data[i].hours / maxValue * size.height);
      
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        // 使用贝塞尔曲线使折线更平滑
        final prevX = (i - 1) * size.width / 7 + size.width / 14;
        final prevY = size.height - (data[i - 1].hours / maxValue * size.height);
        
        final controlX1 = prevX + (x - prevX) / 3;
        final controlY1 = prevY;
        final controlX2 = prevX + (x - prevX) * 2 / 3;
        final controlY2 = y;
        
        path.cubicTo(controlX1, controlY1, controlX2, controlY2, x, y);
      }
    }
    
    canvas.drawPath(path, paint);
  }
  
  // 绘制数据点
  void _drawPoints(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.primary
      ..style = PaintingStyle.fill;
    
    final whitePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    
    for (int i = 0; i < data.length; i++) {
      final x = i * size.width / 7 + size.width / 14; // 每天的中心点
      final y = size.height - (data[i].hours / maxValue * size.height);
      
      // 外圆
      canvas.drawCircle(Offset(x, y), 4, paint);
      
      // 内圆（白色）
      canvas.drawCircle(Offset(x, y), 2, whitePaint);
    }
  }
  
  // 获取柱状图颜色
  Color _getBarColor(int weekday) {
    switch (weekday) {
      case 1: // 周一
      case 2: // 周二
      case 3: // 周三
      case 4: // 周四
      case 5: // 周五
        return AppColors.primary; // 工作日使用主色
      case 6: // 周六
      case 7: // 周日
        return Colors.orange; // 周末使用橙色
      default:
        return AppColors.primary;
    }
  }
  
  @override
  bool shouldRepaint(WeeklyChartPainter oldDelegate) => 
    data != oldDelegate.data || maxValue != oldDelegate.maxValue;
}
