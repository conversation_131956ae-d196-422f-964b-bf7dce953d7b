import 'dart:math';
import 'package:flutter/material.dart';
import '../../../../core/models/focus_record.dart';
import '../../../../core/models/subject_project.dart';

/// 科目专注时间占比圆环图
class SubjectDonutChart extends StatelessWidget {
  final List<FocusRecord> records;
  final List<Subject> subjects;
  final Function(Subject) onSubjectTap;

  const SubjectDonutChart({
    super.key,
    required this.records,
    required this.subjects,
    required this.onSubjectTap,
  });

  @override
  Widget build(BuildContext context) {
    // 计算每个科目的专注时长
    final Map<String, double> subjectHours = {};
    double totalHours = 0;

    for (final record in records) {
      if (!subjectHours.containsKey(record.subjectId)) {
        subjectHours[record.subjectId] = 0;
      }
      final hours = record.durationSeconds / 3600.0;
      subjectHours[record.subjectId] = (subjectHours[record.subjectId] ?? 0) + hours;
      totalHours += hours;
    }

    // 按专注时长排序科目
    final sortedSubjects = List<Subject>.from(subjects);
    sortedSubjects.sort((a, b) {
      final aHours = subjectHours[a.id] ?? 0;
      final bHours = subjectHours[b.id] ?? 0;
      return bHours.compareTo(aHours); // 降序排列
    });

    // 如果没有数据，显示空状态
    if (totalHours == 0) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('暂无专注数据'),
        ),
      );
    }

    // 使用科目自身的颜色
    final Map<String, Color> subjectColors = {};

    // 为每个科目使用其自身的颜色
    for (final subject in sortedSubjects) {
      // 使用科目自身的颜色属性
      subjectColors[subject.id] = Color(subject.color);
    }

    return Column(
      children: [
        // 圆环图
        SizedBox(
          height: 180,
          child: CustomPaint(
            painter: DonutChartPainter(
              subjects: sortedSubjects,
              subjectHours: subjectHours,
              subjectColors: subjectColors,
              totalHours: totalHours,
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Text(
                  //   '总专注时长',
                  //   style: TextStyle(
                  //     fontSize: 12,
                  //     color: Colors.grey.shade600,
                  //   ),
                  // ),
                  // const SizedBox(height: 4),
                  Text(
                    '${totalHours.toStringAsFixed(1)}h',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF424242),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        const SizedBox(height: 16),

        // 科目图例
        Wrap(
          spacing: 16,
          runSpacing: 12,
          children: sortedSubjects.map((subject) {
            final hours = subjectHours[subject.id] ?? 0;
            final percentage = totalHours > 0 ? (hours / totalHours * 100) : 0;

            return InkWell(
              onTap: () => onSubjectTap(subject),
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: const Color(0xFFEEEEEE)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: Color(subject.color),
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      subject.name,
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${percentage.toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),

        // 提示文本
        const SizedBox(height: 12),
        Center(
          child: Text(
            '点击标签查看详细科目数据',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade500,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      ],
    );
  }
}

/// 圆环图绘制器
class DonutChartPainter extends CustomPainter {
  final List<Subject> subjects;
  final Map<String, double> subjectHours;
  final Map<String, Color> subjectColors;
  final double totalHours;

  DonutChartPainter({
    required this.subjects,
    required this.subjectHours,
    required this.subjectColors,
    required this.totalHours,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 2 * 0.8;
    final innerRadius = radius * 0.6; // 内圆半径

    // 绘制背景圆环
    final bgPaint = Paint()
      ..color = Colors.grey.shade200
      ..style = PaintingStyle.stroke
      ..strokeWidth = radius - innerRadius;

    canvas.drawCircle(center, (radius + innerRadius) / 2, bgPaint);

    // 如果没有数据，直接返回
    if (totalHours == 0) return;

    // 绘制各科目扇形
    double startAngle = -pi / 2; // 从12点钟方向开始

    for (final subject in subjects) {
      final hours = subjectHours[subject.id] ?? 0;
      if (hours <= 0) continue;

      final sweepAngle = 2 * pi * (hours / totalHours);
      // 直接使用科目自身的颜色，确保一致性
      final color = Color(subject.color);

      final paint = Paint()
        ..color = color
        ..style = PaintingStyle.stroke
        ..strokeWidth = radius - innerRadius
        ..strokeCap = StrokeCap.butt;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: (radius + innerRadius) / 2),
        startAngle,
        sweepAngle,
        false,
        paint,
      );

      startAngle += sweepAngle;
    }
  }

  @override
  bool shouldRepaint(DonutChartPainter oldDelegate) =>
      subjects != oldDelegate.subjects ||
      subjectHours != oldDelegate.subjectHours ||
      subjectColors != oldDelegate.subjectColors ||
      totalHours != oldDelegate.totalHours;
}
