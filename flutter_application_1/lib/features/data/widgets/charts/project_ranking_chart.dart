import 'package:flutter/material.dart';
import '../../../../core/models/focus_record.dart';
import '../../../../core/models/subject_project.dart';

/// 项目专注时间排行图表
/// 使用横向柱状图展示项目专注时间排行
class ProjectRankingChart extends StatefulWidget {
  final List<FocusRecord> records;
  final List<Project> projects;
  final List<Subject> subjects;

  const ProjectRankingChart({
    super.key,
    required this.records,
    required this.projects,
    required this.subjects,
  });

  @override
  State<ProjectRankingChart> createState() => _ProjectRankingChartState();
}

class _ProjectRankingChartState extends State<ProjectRankingChart> {
  // 是否显示所有项目（包括非活跃项目）
  bool _showAllProjects = false;

  // 控制过滤选项浮窗的显示
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  @override
  void dispose() {
    // 确保在组件销毁时关闭浮窗
    _hideFilterOptions();
    super.dispose();
  }

  // 显示过滤选项浮窗
  void _showFilterOptions(BuildContext context) {
    // 先关闭已存在的浮窗
    _hideFilterOptions();

    // 获取按钮的位置信息
    final RenderBox? buttonBox = context.findRenderObject() as RenderBox?;
    if (buttonBox == null) return;

    // 获取按钮在屏幕上的位置
    final buttonPosition = buttonBox.localToGlobal(Offset.zero);

    // 创建浮窗
    _overlayEntry = OverlayEntry(
      builder: (context) {
        // 使用全屏覆盖以便捕获点击事件
        return Stack(
          children: [
            // 透明层，用于捕获点击事件关闭浮窗
            Positioned.fill(
              child: GestureDetector(
                onTap: _hideFilterOptions,
                behavior: HitTestBehavior.translucent,
                child: Container(
                  color: Colors.transparent,
                ),
              ),
            ),
            // 过滤选项菜单 - 位置对齐卡片右侧，底部刚好遮挡按钮
            Positioned(
              right: 16, // 与卡片右侧对齐
              top: buttonPosition.dy + 20, // 顶部位置调整，使底部刚好遮挡住按钮
              width: 200,
              child: GestureDetector(
                // 防止点击菜单时关闭浮窗
                onTap: () {},
                child: Material(
                  elevation: 4,
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 标题
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          child: Row(
                            children: [
                              const Icon(Icons.filter_list, size: 16),
                              const SizedBox(width: 8),
                              const Text(
                                '过滤项目',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const Divider(height: 1),
                        // 显示所有项目选项
                        ListTile(
                          dense: true,
                          title: const Text('显示所有项目'),
                          leading: Icon(
                            _showAllProjects ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                            color: _showAllProjects ? const Color(0xFF4CAF50) : Colors.grey,
                            size: 18,
                          ),
                          onTap: () {
                            setState(() {
                              _showAllProjects = true;
                            });
                            _hideFilterOptions();
                          },
                        ),
                        // 仅显示活跃项目选项
                        ListTile(
                          dense: true,
                          title: const Text('仅显示活跃项目'),
                          subtitle: const Text('隐藏已归档/过期项目', style: TextStyle(fontSize: 12)),
                          leading: Icon(
                            !_showAllProjects ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                            color: !_showAllProjects ? const Color(0xFF4CAF50) : Colors.grey,
                            size: 18,
                          ),
                          onTap: () {
                            setState(() {
                              _showAllProjects = false;
                            });
                            _hideFilterOptions();
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );

    // 显示浮窗
    Overlay.of(context).insert(_overlayEntry!);
  }

  // 隐藏过滤选项浮窗
  void _hideFilterOptions() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  Widget build(BuildContext context) {
    // 计算每个项目的专注时长
    final Map<String, double> projectHours = {};
    double totalHours = 0;

    // 项目ID到科目ID的映射
    final Map<String, String> projectToSubject = {};
    for (final project in widget.projects) {
      projectToSubject[project.id] = project.subjectId;
    }

    // 计算每个项目的专注时长
    for (final record in widget.records) {
      if (!projectHours.containsKey(record.projectId)) {
        projectHours[record.projectId] = 0;
      }
      final hours = record.durationSeconds / 3600.0;
      projectHours[record.projectId] = (projectHours[record.projectId] ?? 0) + hours;
      totalHours += hours;
    }

    // 按专注时长排序项目
    final sortedProjects = List<Project>.from(widget.projects);
    sortedProjects.sort((a, b) {
      final aHours = projectHours[a.id] ?? 0;
      final bHours = projectHours[b.id] ?? 0;
      return bHours.compareTo(aHours); // 降序排列
    });

    // 只保留有专注记录的项目
    final projectsWithFocus = sortedProjects.where((p) => (projectHours[p.id] ?? 0) > 0).toList();

    // 如果没有数据，显示空状态
    if (projectsWithFocus.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('暂无专注数据'),
        ),
      );
    }

    // 获取科目颜色映射
    final Map<String, Color> subjectColors = {};
    for (final subject in widget.subjects) {
      subjectColors[subject.id] = Color(subject.color);
    }

    // 判断项目是否活跃
    bool isProjectActive(Project project) {
      // 已归档的项目不活跃
      if (project.isArchived) {
        return false;
      }

      final now = DateTime.now();
      // 未开启进度追踪的项目视为活跃
      if (!project.isTrackingEnabled) {
        return true;
      }

      // 在时间范围内且未完成的项目视为活跃
      return now.isAfter(project.startDate) &&
             now.isBefore(project.endDate) &&
             project.progress < 1.0;
    }

    // 获取项目状态文本
    String getProjectStatusText(Project project) {
      if (project.isArchived) {
        return '已归档';
      }

      final now = DateTime.now();
      if (project.progress >= 1.0) {
        return '已完成';
      } else if (now.isBefore(project.startDate)) {
        return '未开始';
      } else if (now.isAfter(project.endDate)) {
        return '已过期';
      } else {
        return '';
      }
    }

    // 过滤项目
    final filteredProjects = projectsWithFocus.where((project) {
      return _showAllProjects || isProjectActive(project);
    }).toList();

    // 计算最大专注时长，用于确定柱状图的最大宽度
    final maxHours = filteredProjects.fold<double>(
      0, (max, project) => (projectHours[project.id] ?? 0) > max ? (projectHours[project.id] ?? 0) : max);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题和切换按钮
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '项目专注时间排行',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            // 过滤按钮 - 只使用图标，更加低调
            CompositedTransformTarget(
              link: _layerLink,
              child: IconButton(
                onPressed: () {
                  _showFilterOptions(context);
                },
                icon: Icon(
                  Icons.filter_list,
                  size: 20,
                  color: _showAllProjects ? const Color(0xFF4CAF50) : Colors.grey.shade600,
                ),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                splashRadius: 20,
                tooltip: '过滤项目',
              ),
            ),
          ],
        ),

        // 总专注时长
        Padding(
          padding: const EdgeInsets.only(top: 4, bottom: 16),
          child: Text(
            '总专注时长: ${totalHours.toStringAsFixed(1)}小时',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ),

        // 项目列表滚动区域
        Expanded(
          child: filteredProjects.isEmpty
              ? Center(
                  child: Text(
                    '暂无项目数据',
                    style: TextStyle(color: Colors.grey.shade600),
                  ),
                )
              : SingleChildScrollView(
                  child: Column(
                    children: [
                      // 柱状图
                      ...filteredProjects.map((project) {
          final hours = projectHours[project.id] ?? 0;
          // 移除百分比计算，不再需要
          final subjectId = projectToSubject[project.id] ?? '';
          final isActive = isProjectActive(project);
          final statusText = getProjectStatusText(project);

          // 使用灰色显示非活跃项目
          final color = isActive ? subjectColors[subjectId] ?? Colors.grey : Colors.grey.shade400;

          // 查找项目对应的科目
          final subject = widget.subjects.firstWhere(
            (s) => s.id == subjectId,
            orElse: () => Subject(id: '', name: '未知科目', color: 0xFF9E9E9E),
          );

          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 第一排：项目名称和专注时长
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              project.name,
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                                color: isActive ? Colors.black87 : Colors.grey.shade600,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (statusText.isNotEmpty)
                            Container(
                              margin: const EdgeInsets.only(left: 8),
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade200,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                statusText,
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    // 专注时长
                    // Text(
                    //   '${hours.toStringAsFixed(1)}小时',
                    //   style: TextStyle(
                    //     fontSize: 15,
                    //     fontWeight: FontWeight.bold,
                    //     color: isActive ? Colors.black87 : Colors.grey.shade600,
                    //   ),
                    // ),
                  ],
                ),

                const SizedBox(height: 2),

                // 第二排：科目颜色标记和科目名称
                Row(
                  children: [
                    // 科目颜色标记
                    Container(
                      width: 10,
                      height: 10,
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 6),
                    // 科目名称
                    Expanded(
                      child: Text(
                        subject.name,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Text(
                      '${hours.toStringAsFixed(1)}小时',
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: isActive ? Colors.black87 : Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 4),

                // 对比柱状图 - 调整颜色使其更弱化
                Container(
                  height: 10,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100, // 更灰的背景色
                    borderRadius: BorderRadius.circular(5),
                  ),
                  child: Stack(
                    children: [
                      // 对比条 - 使用弱化的颜色
                      FractionallySizedBox(
                        widthFactor: maxHours > 0 ? hours / maxHours : 0,
                        child: Container(
                          height: 10,
                          decoration: BoxDecoration(
                            // 使用弱化的科目颜色
                            color: color.withAlpha((0.6 * 255).toInt()), // 降低透明度
                            borderRadius: BorderRadius.circular(5),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }),
                    ],
                  ),
                ),
        ),
      ],
    );
  }
}
