import 'package:flutter/material.dart';
import '../../../../core/models/focus_record.dart';
import '../../../../core/models/subject_project.dart';
import '../charts/optimized_project_donut_chart.dart';
import '../time_period_selector.dart';

/// 项目专注数据类
class ProjectFocusData {
  final double focusHours;
  final int sessionCount;
  final double avgSessionMinutes;

  ProjectFocusData({
    required this.focusHours,
    required this.sessionCount,
    required this.avgSessionMinutes,
  });
}

/// 科目项目详情底部弹窗
/// 显示科目的详细数据和项目占比情况
class SubjectProjectsSheet extends StatelessWidget {
  final Subject subject;
  final List<Project> projects;
  final List<FocusRecord> records;
  final TimePeriodType periodType;
  final String periodTitle;
  final double focusHours;
  final int sessionCount;
  final double avgSessionMinutes;

  const SubjectProjectsSheet({
    super.key,
    required this.subject,
    required this.projects,
    required this.records,
    required this.periodType,
    required this.periodTitle,
    required this.focusHours,
    required this.sessionCount,
    required this.avgSessionMinutes,
  });

  @override
  Widget build(BuildContext context) {
    // 获取屏幕高度，用于计算最大高度
    final screenHeight = MediaQuery.of(context).size.height;
    final maxHeight = screenHeight * 0.85; // 最大高度为屏幕高度的85%

    return Container(
      constraints: BoxConstraints(maxHeight: maxHeight),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部拖动条
          Padding(
            padding: const EdgeInsets.only(top: 12, bottom: 4),
            child: Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
          ),

          // 内容区域 - 使用Expanded和SingleChildScrollView确保可滚动
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题和时间段
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              color: Color(subject.color),
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            subject.name,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      // 更醒目的时间周期标签 - 使用科目颜色但添加透明度
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 4),
                        decoration: BoxDecoration(
                          // 使用科目颜色但添加透明度，使其更柔和
                          color: Color(subject.color).withAlpha(166), // 约65%透明度 (0.65 * 255 ≈ 166)
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(20),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Text(
                          periodTitle,
                          style: const TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.bold,
                            color: Colors.white, // 白色文字
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // 科目详细数据
                  _buildSubjectDataSection(),

                  const SizedBox(height: 24),

                  // 项目分布卡片 - 使用卡片包裹，增加高度
                  Container(
                    padding: const EdgeInsets.all(16),
                    // 使用约束确保卡片高度足够大，但保留底部安全距离
                    constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context).size.height * 0.4, // 至少占屏幕高度的60%
                      maxHeight: MediaQuery.of(context).size.height * 0.6, // 最多占屏幕高度的75%，保留安全距离
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey.shade200),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(5),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 标题 - 放在卡片内部左上角
                        const Text(
                          '项目分布',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF212121),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // 项目分布圆环图和列表 - 使用Expanded确保填充剩余空间
                        Expanded(
                          child: OptimizedProjectDonutChart(
                            records: records,
                            projects: projects,
                            subject: subject,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建科目详细数据部分 - 参考外部数据分析中的3个一排设计
  Widget _buildSubjectDataSection() {
    // 使用固定的颜色，与外部数据分析保持一致
    const Color focusTimeColor = Color(0xFF2196F3); // AppColors.info - 蓝色
    const Color sessionCountColor = Color(0xFF4CAF50); // AppColors.success - 绿色
    const Color avgTimeColor = Color(0xFFAB47BC); // AppColors.purple - 紫色

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题 - 使用正常文本颜色
          const Text(
            '科目专注数据',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Color(0xFF212121), // 正常文本颜色
            ),
          ),

          const SizedBox(height: 16),

          // 3个一排的数据项 - 专注时长、专注次数、平均时长
          Row(
            children: [
              // 专注时长
              Expanded(
                child: _buildMetricItem(
                  icon: Icons.access_time,
                  value: '${focusHours.toStringAsFixed(1)}h',
                  label: '专注时长',
                  color: focusTimeColor,
                ),
              ),

              // 专注次数
              Expanded(
                child: _buildMetricItem(
                  icon: Icons.repeat,
                  value: '$sessionCount次',
                  label: '专注次数',
                  color: sessionCountColor,
                ),
              ),

              // 平均时长
              Expanded(
                child: _buildMetricItem(
                  icon: Icons.timer,
                  value: '${avgSessionMinutes.toStringAsFixed(0)}分钟',
                  label: '平均时长',
                  color: avgTimeColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建指标项 - 参考外部数据分析中的样式
  Widget _buildMetricItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: color.withAlpha(26), // 10% opacity
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  // 已删除未使用的方法
}
