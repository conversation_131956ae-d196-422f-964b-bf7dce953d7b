import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import 'time_period_selector.dart';

/// 项目标签页的日期选择器
/// 用于在特定时间周期内选择日期，参考专注tab的设计
class ProjectDateSelector extends StatelessWidget {
  final DateTime selectedDate;
  final TimePeriodType periodType;
  final Function() onPrevious;
  final Function() onNext;
  final Function() onToday;

  const ProjectDateSelector({
    super.key,
    required this.selectedDate,
    required this.periodType,
    required this.onPrevious,
    required this.onNext,
    required this.onToday,
  });

  @override
  Widget build(BuildContext context) {
    // 获取日期显示文本
    final dateText = _formatDateRange(selectedDate, periodType);

    // 判断是否是当前日期
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    bool isCurrentPeriod;

    switch (periodType) {
      case TimePeriodType.day:
        final selectedDay = DateTime(selectedDate.year, selectedDate.month, selectedDate.day);
        isCurrentPeriod = selectedDay.isAtSameMomentAs(today);
        break;
      case TimePeriodType.week:
        // 获取当前周的周一
        final currentWeekStart = _getWeekStart(today);
        // 获取所选周的周一
        final selectedWeekStart = _getWeekStart(selectedDate);
        isCurrentPeriod = currentWeekStart.isAtSameMomentAs(selectedWeekStart);
        break;
      case TimePeriodType.month:
        isCurrentPeriod = selectedDate.year == today.year && selectedDate.month == today.month;
        break;
    }

    return Container(
      height: 48, // 固定高度
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppShadows.low],
      ),
      child: Row(
        children: [
          // 上一个按钮
          IconButton(
            onPressed: onPrevious,
            icon: const Icon(Icons.arrow_back_ios, size: 18),
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(),
            iconSize: 18,
            splashRadius: 18,
            tooltip: '上一${_getPeriodName(periodType)}',
          ),

          // 中间部分：日期文本和返回当前按钮
          Expanded(
            child: Stack(
              alignment: Alignment.center,
              children: [
                // 日期文本 - 居中显示
                Align(
                  alignment: Alignment.center,
                  child: Text(
                    dateText,
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                // 返回当前按钮 - 仅在不是当前日期时显示
                if (!isCurrentPeriod)
                  Positioned(
                    right: 0,
                    child: IconButton(
                      onPressed: onToday,
                      icon: const Icon(Icons.refresh, size: 16),
                      padding: const EdgeInsets.all(4),
                      constraints: const BoxConstraints(),
                      iconSize: 16,
                      splashRadius: 16,
                      color: AppColors.primary,
                      tooltip: '返回当前${_getPeriodName(periodType)}',
                    ),
                  ),
              ],
            ),
          ),

          // 下一个按钮（如果允许）- 当前日期时禁用
          IconButton(
            onPressed: isCurrentPeriod ? null : onNext,
            icon: Icon(
              Icons.arrow_forward_ios,
              size: 18,
              color: isCurrentPeriod ? Colors.grey.shade300 : null,
            ),
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(),
            iconSize: 18,
            splashRadius: 18,
            tooltip: '下一${_getPeriodName(periodType)}',
          ),
        ],
      ),
    );
  }

  // 获取周期名称
  String _getPeriodName(TimePeriodType period) {
    switch (period) {
      case TimePeriodType.day:
        return '天';
      case TimePeriodType.week:
        return '周';
      case TimePeriodType.month:
        return '月';
    }
  }

  // 获取周的开始日期（周一）
  DateTime _getWeekStart(DateTime date) {
    final weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }

  // 格式化日期范围文本
  String _formatDateRange(DateTime date, TimePeriodType period) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final isToday = date.year == today.year && date.month == today.month && date.day == today.day;

    switch (period) {
      case TimePeriodType.day:
        if (isToday) {
          return '今日';
        } else {
          return '${date.month}月${date.day}日';
        }
      case TimePeriodType.week:
        // 获取所选周的周一
        final weekStart = _getWeekStart(date);
        // 获取所选周的周日
        final weekEnd = weekStart.add(const Duration(days: 6));

        if (weekStart.month == weekEnd.month) {
          // 同一个月内
          return '${weekStart.month}月${weekStart.day}-${weekEnd.day}日';
        } else {
          // 跨月
          return '${weekStart.month}月${weekStart.day}日-${weekEnd.month}月${weekEnd.day}日';
        }
      case TimePeriodType.month:
        if (date.year == today.year && date.month == today.month) {
          return '本月';
        } else {
          return '${date.year}年${date.month}月';
        }
    }
  }
}
