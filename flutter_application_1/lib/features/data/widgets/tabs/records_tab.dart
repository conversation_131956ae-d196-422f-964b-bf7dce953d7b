import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../shared/theme/constants.dart';
import '../../../../core/models/focus_record.dart';
import '../../../../core/services/hive_service.dart';

/// 记录标签页
/// 显示专注记录列表
class RecordsTab extends StatelessWidget {
  final List<FocusRecord> records;
  final Function(FocusRecord) onRecordTap;
  final HiveService hiveService;

  const RecordsTab({
    super.key,
    required this.records,
    required this.onRecordTap,
    required this.hiveService,
  });

  @override
  Widget build(BuildContext context) {

    return records.isEmpty
        ? const Center(child: Text('暂无专注记录'))
        : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: records.length,
            itemBuilder: (context, index) {
              final record = records[index];
              final durationMinutes = record.durationSeconds ~/ 60;

              // 获取项目和科目名称
              String projectName = '未知项目';
              String subjectName = '未知科目';

              final project = hiveService.subjectRepository.getProjectById(record.projectId);
              if (project != null) {
                projectName = project.name;
              }

              final subject = hiveService.subjectRepository.getSubjectById(record.subjectId);
              if (subject != null) {
                subjectName = subject.name;
              }

              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                child: ListTile(
                  title: Text(
                    DateFormat('yyyy-MM-dd HH:mm').format(record.startTime),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('$subjectName - $projectName'),
                      Text(
                        '状态: ${record.status == FocusRecordStatus.completed ? '完成' : '中断'}',
                        style: TextStyle(
                          color: record.status == FocusRecordStatus.completed
                              ? AppColors.success
                              : AppColors.warning,
                        ),
                      ),
                    ],
                  ),
                  trailing: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withAlpha(26),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      '$durationMinutes分钟',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                  onTap: () => onRecordTap(record),
                ),
              );
            },
          );
  }
}
