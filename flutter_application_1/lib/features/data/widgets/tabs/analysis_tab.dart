// import 'package:flutter/material.dart';
// import '../../../../shared/theme/constants.dart';
// import '../../../../core/models/focus_record.dart';
// import '../../utils/focus_data_calculator.dart';
// import '../cards/efficiency_item.dart';
// import '../cards/suggestion_item.dart';


//先取消分析tab，只提供数据不提供分析


// /// 分析标签页
// /// 显示专注行为分析和建议
// class AnalysisTab extends StatelessWidget {
//   final List<FocusRecord> records;

//   const AnalysisTab({
//     super.key,
//     required this.records,
//   });

//   @override
//   Widget build(BuildContext context) {
//     final avgMinutes = FocusDataCalculator.calculateAverageMinutes(records);
//     final completionRate = FocusDataCalculator.calculateCompletionRate(records);
//     final bestHour = FocusDataCalculator.getBestFocusHour(records);

//     return SingleChildScrollView(
//       padding: const EdgeInsets.all(16),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             '专注行为分析',
//             style: AppTextStyles.headline3,
//           ),
//           const SizedBox(height: 16),

//           // 专注时长分布
//           Container(
//             padding: const EdgeInsets.all(16),
//             decoration: BoxDecoration(
//               color: Colors.white,
//               borderRadius: BorderRadius.circular(16),
//               boxShadow: [AppShadows.low],
//             ),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 const Text(
//                   '专注时长分布',
//                   style: AppTextStyles.headline3,
//                 ),
//                 const SizedBox(height: 16),
//                 records.isEmpty
//                     ? const Center(child: Text('暂无数据'))
//                     : const Center(child: Text('专注时长分布图 - 开发中')),
//               ],
//             ),
//           ),

//           const SizedBox(height: 24),

//           // 专注效率分析
//           Container(
//             padding: const EdgeInsets.all(16),
//             decoration: BoxDecoration(
//               color: Colors.white,
//               borderRadius: BorderRadius.circular(16),
//               boxShadow: [AppShadows.low],
//             ),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 const Text(
//                   '专注效率分析',
//                   style: AppTextStyles.headline3,
//                 ),
//                 const SizedBox(height: 16),
//                 records.isEmpty
//                     ? const Center(child: Text('暂无数据'))
//                     : Column(
//                         children: [
//                           EfficiencyItem(
//                             title: '平均每次专注时长',
//                             value: '${avgMinutes.toStringAsFixed(1)}分钟',
//                           ),
//                           EfficiencyItem(
//                             title: '完成率',
//                             value: '${(completionRate * 100).toStringAsFixed(1)}%',
//                           ),
//                           EfficiencyItem(
//                             title: '最佳专注时段',
//                             value: '$bestHour:00 - ${(bestHour + 1) % 24}:00',
//                           ),
//                         ],
//                       ),
//               ],
//             ),
//           ),

//           const SizedBox(height: 24),

//           // 专注习惯建议
//           Container(
//             padding: const EdgeInsets.all(16),
//             decoration: BoxDecoration(
//               color: Colors.white,
//               borderRadius: BorderRadius.circular(16),
//               boxShadow: [AppShadows.low],
//             ),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 const Text(
//                   '专注习惯建议',
//                   style: AppTextStyles.headline3,
//                 ),
//                 const SizedBox(height: 16),
//                 SuggestionItem(
//                   icon: Icons.access_time,
//                   title: '选择最佳时间',
//                   content: '根据数据分析，您在$bestHour:00 - ${(bestHour + 1) % 24}:00时段专注效果最好，建议安排重要任务在此时段。',
//                 ),
//                 const Divider(),
//                 SuggestionItem(
//                   icon: Icons.timer,
//                   title: '合理安排时长',
//                   content: '您的平均专注时长为${avgMinutes.toStringAsFixed(1)}分钟，建议将任务分解为25-30分钟的小块，提高完成率。',
//                 ),
//                 if (completionRate < 0.8) ...[
//                   const Divider(),
//                   SuggestionItem(
//                     icon: Icons.warning_amber_rounded,
//                     title: '减少中断',
//                     content: '您的专注完成率为${(completionRate * 100).toStringAsFixed(1)}%，建议减少干扰，提高专注质量。',
//                   ),
//                 ],
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
