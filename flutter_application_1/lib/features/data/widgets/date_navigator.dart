import 'package:flutter/material.dart';
import 'time_period_selector.dart';

/// 日期导航组件
/// 用于在日/周/月视图中切换日期
class DateNavigator extends StatelessWidget {
  final DateTime selectedDate;
  final TimePeriodType periodType;
  final Function() onPrevious;
  final Function() onNext;
  final Function() onToday;
  final bool showNextButton;

  const DateNavigator({
    super.key,
    required this.selectedDate,
    required this.periodType,
    required this.onPrevious,
    required this.onNext,
    required this.onToday,
    this.showNextButton = true,
  });

  @override
  Widget build(BuildContext context) {
    // 根据时间段类型获取日期显示文本
    String dateText;
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final isToday = selectedDate.year == today.year &&
                    selectedDate.month == today.month &&
                    selectedDate.day == today.day;

    switch (periodType) {
      case TimePeriodType.day:
        if (isToday) {
          dateText = '今日';
        } else {
          dateText = '${selectedDate.month}月${selectedDate.day}日';
        }
        break;

      case TimePeriodType.week:
        // 获取所选周的周一
        final weekStart = selectedDate.subtract(Duration(days: selectedDate.weekday - 1));
        // 获取所选周的周日
        final weekEnd = weekStart.add(const Duration(days: 6));

        if (weekStart.month == weekEnd.month) {
          // 同一个月内
          dateText = '${weekStart.month}月${weekStart.day}-${weekEnd.day}日';
        } else {
          // 跨月
          dateText = '${weekStart.month}月${weekStart.day}日-${weekEnd.month}月${weekEnd.day}日';
        }
        break;

      case TimePeriodType.month:
        if (selectedDate.year == today.year && selectedDate.month == today.month) {
          dateText = '本月';
        } else {
          dateText = '${selectedDate.year}年${selectedDate.month}月';
        }
        break;
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 日期导航
        Row(
          children: [
            // 上一个按钮
            IconButton(
              onPressed: onPrevious,
              icon: const Icon(Icons.chevron_left),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              iconSize: 20,
              splashRadius: 20,
              tooltip: '上一${periodType == TimePeriodType.day ? '天' : periodType == TimePeriodType.week ? '周' : '月'}',
            ),

            // 日期文本
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                dateText,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // 返回当前日期按钮 - 仅在不是当前日期时显示
            if (!isToday)
              IconButton(
                onPressed: onToday,
                icon: const Icon(Icons.refresh, size: 16),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                iconSize: 16,
                splashRadius: 16,
                color: const Color(0xFF4CAF50), // 使用主色调
                tooltip: '返回当前${periodType == TimePeriodType.day ? '天' : periodType == TimePeriodType.week ? '周' : '月'}',
              ),

            // 下一个按钮（如果允许）- 当前日期时禁用
            if (showNextButton)
              IconButton(
                onPressed: isToday ? null : onNext, // 当前日期时禁用
                icon: Icon(
                  Icons.chevron_right,
                  color: isToday ? Colors.grey.shade300 : null, // 当前日期时显示灰色
                ),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                iconSize: 20,
                splashRadius: 20,
                tooltip: '下一${periodType == TimePeriodType.day ? '天' : periodType == TimePeriodType.week ? '周' : '月'}',
              ),
          ],
        ),
      ],
    );
  }
}
