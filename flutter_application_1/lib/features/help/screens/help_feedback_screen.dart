import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../utils/url_launcher_helper.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// 帮助与反馈页面
class HelpFeedbackScreen extends ConsumerStatefulWidget {
  const HelpFeedbackScreen({super.key});

  @override
  ConsumerState<HelpFeedbackScreen> createState() => _HelpFeedbackScreenState();
}

class _HelpFeedbackScreenState extends ConsumerState<HelpFeedbackScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('帮助与反馈'),
        backgroundColor: AppColors.background,
        elevation: 0,
        foregroundColor: AppColors.text,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSizes.paddingLarge),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
                // 删除常见问题功能项，简化界面结构

                // 问题反馈
                _buildSectionCard(
                  icon: Icons.bug_report_outlined,
                  title: '问题反馈',
                  description: '遇到Bug或功能异常？告诉我们',
                  onTap: () => _showFeedbackForm(context, FeedbackType.bug),
                ),

                const SizedBox(height: AppSizes.paddingMedium),

                // 功能建议
                _buildSectionCard(
                  icon: Icons.lightbulb_outline,
                  title: '功能建议',
                  description: '有好的想法？我们很乐意听取您的建议',
                  onTap: () => _showFeedbackForm(context, FeedbackType.suggestion),
                ),

                const SizedBox(height: AppSizes.paddingMedium),

                // 联系我们
                _buildSectionCard(
                  icon: Icons.email_outlined,
                  title: '联系我们',
                  description: '直接发送邮件与我们取得联系',
                  onTap: () => _contactUs(context),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required IconData icon,
    required String title,
    required String description,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
        child: Padding(
          padding: const EdgeInsets.all(AppSizes.paddingLarge),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.primary.withAlpha(AppColors.alpha10),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Icon(
                  icon,
                  color: AppColors.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: AppSizes.paddingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTextStyles.headline3,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.arrow_forward_ios,
                color: AppColors.textHint,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // FAQ功能已删除，简化界面结构

  void _showFeedbackForm(BuildContext context, FeedbackType type) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FeedbackFormScreen(type: type),
      ),
    );
  }

  void _contactUs(BuildContext context) {
    UrlLauncherHelper.launchEmailWithErrorHandling(context);
  }
}

/// 反馈类型枚举
enum FeedbackType {
  bug,
  suggestion,
}

// FAQ功能已删除，简化界面结构

/// 反馈表单页面
class FeedbackFormScreen extends ConsumerStatefulWidget {
  final FeedbackType type;

  const FeedbackFormScreen({
    super.key,
    required this.type,
  });

  @override
  ConsumerState<FeedbackFormScreen> createState() => _FeedbackFormScreenState();
}

class _FeedbackFormScreenState extends ConsumerState<FeedbackFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _contactController = TextEditingController();
  String? _selectedCategory;

  @override
  void dispose() {
    _descriptionController.dispose();
    _contactController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final title = widget.type == FeedbackType.bug ? '问题反馈' : '功能建议';
    final categories = widget.type == FeedbackType.bug
        ? ['功能异常', '性能问题', '界面问题', '数据问题', '其他问题']
        : ['新功能建议', '现有功能改进', '界面优化', '其他建议'];

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(title),
        backgroundColor: AppColors.background,
        elevation: 0,
        foregroundColor: AppColors.text,
      ),
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(AppSizes.paddingLarge),
            children: [
                // 分类选择
                Text(
                  '${widget.type == FeedbackType.bug ? '问题' : '建议'}类型',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: '请选择类型',
                  ),
                  items: categories.map((category) {
                    return DropdownMenuItem(
                      value: category,
                      child: Text(category),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value;
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请选择类型';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppSizes.paddingLarge),

                // 描述输入
                Text(
                  '详细描述',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _descriptionController,
                  maxLines: 5,
                  decoration: InputDecoration(
                    border: const OutlineInputBorder(),
                    hintText: widget.type == FeedbackType.bug
                        ? '请详细描述遇到的问题，包括操作步骤和期望结果'
                        : '请详细描述您的建议，包括期望的功能和使用场景',
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请输入详细描述';
                    }
                    if (value.trim().length < 10) {
                      return '描述至少需要10个字符';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppSizes.paddingLarge),

                // 联系方式（可选）
                Text(
                  '联系方式（可选）',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _contactController,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: '邮箱或其他联系方式，便于我们联系您',
                  ),
                ),

                const SizedBox(height: AppSizes.paddingXLarge),

                // 提交按钮
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _submitFeedback,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                      ),
                    ),
                    child: const Text(
                      '提交反馈',
                      style: AppTextStyles.buttonLarge,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _submitFeedback() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!mounted) return;

    try {
      // 收集设备信息
      final deviceInfo = await _getDeviceInfo();
      final appInfo = await _getAppInfo();

      // 构建邮件内容
      final subject = '[LimeFocus反馈] ${widget.type == FeedbackType.bug ? '问题反馈' : '功能建议'} - $_selectedCategory';
      final body = '''
--- 请在下方描述您的${widget.type == FeedbackType.bug ? '问题' : '建议'} ---

类型: $_selectedCategory

描述:
${_descriptionController.text}

${_contactController.text.isNotEmpty ? '联系方式: ${_contactController.text}' : ''}

--- 系统信息（请勿删除）---
应用版本: ${appInfo['version']}
设备型号: ${deviceInfo['model']}
系统版本: ${deviceInfo['systemVersion']}
反馈时间: ${DateTime.now().toString()}
''';

      // 调用邮件客户端
      final emailUri = Uri(
        scheme: 'mailto',
        path: '<EMAIL>',
        query: 'subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(body)}',
      );

      if (!mounted) return;

      await UrlLauncherHelper.launchUrlWithErrorHandling(
        context,
        emailUri.toString(),
        errorMessage: '无法打开邮件客户端，请检查是否已安装邮件应用',
      );

      // 显示成功提示并返回
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('感谢您的反馈！邮件客户端已打开，请发送邮件完成提交'),
            backgroundColor: AppColors.success,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('提交失败: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<Map<String, String>> _getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      final iosInfo = await deviceInfo.iosInfo;
      return {
        'model': iosInfo.model,
        'systemVersion': iosInfo.systemVersion,
      };
    } catch (e) {
      return {
        'model': '未知设备',
        'systemVersion': '未知版本',
      };
    }
  }

  Future<Map<String, String>> _getAppInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      return {
        'version': '${packageInfo.version}+${packageInfo.buildNumber}',
      };
    } catch (e) {
      return {
        'version': '1.0.0',
      };
    }
  }
}
