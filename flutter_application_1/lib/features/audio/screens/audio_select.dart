import 'package:flutter/material.dart';
import '../widgets/scene_card.dart';
import '../widgets/carousel_slider.dart';
import '/features/audio/screens/scene_screen.dart';
import '../../../core/api/scene_api.dart';

class AudioSelectScreen extends StatelessWidget {
  const AudioSelectScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text(
          '首页',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: Color.fromARGB(255, 25, 25, 25),
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color.fromARGB(255, 204, 255, 229),
              Color.fromARGB(255, 255, 250, 240),
            ],
          ),
        ),
        child: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.only(top: 100),
              child: ImageCarousel(
                items: [
                  const CarouselItem(
                    imageUrl: 'assets/images/1.jpg',
                    destination: '/scene/1',
                  ),
                  const CarouselItem(
                    imageUrl: 'assets/images/2.jpg',
                    destination: '/scene/2',
                  ),
                  const CarouselItem(
                    imageUrl: 'assets/images/3.jpg',
                    destination: '/scene/3',
                  ),
                ],
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: const [
                  Text(
                    '热门推荐',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color.fromARGB(255, 23, 22, 22),
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '精选优质冥想场景',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color.fromARGB(179, 64, 61, 61),
                    ),
                  ),
                ],
              ),
            ),
          ),
          SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: FutureBuilder<List<dynamic>>(
              future: SceneApi().getScenes(),
              builder: (context, AsyncSnapshot<List<dynamic>> snapshot) {
                if (snapshot.hasData) {
                  return SliverGrid(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final scene = snapshot.data![index];
                        return SceneCard(
                          scene: scene,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => SceneScreen(scene: scene),
                              ),
                            );
                          },
                        );
                      },
                      childCount: snapshot.data!.length,
                    ),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 0.8,
                    ),
                  );
                }
                if (snapshot.hasError) {
                  return SliverToBoxAdapter(
                    child: Center(
                      child: Text('错误: ${snapshot.error}'),
                    ),
                  );
                }
                return const SliverToBoxAdapter(
                  child: Center(child: CircularProgressIndicator()),
                );
              },
            ),
          ),
        ],
      ),
    )
    );
  }
}




