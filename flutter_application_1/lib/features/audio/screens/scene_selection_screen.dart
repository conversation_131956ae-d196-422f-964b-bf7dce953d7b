import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../widgets/scene_card.dart';
import 'scene_screen.dart';
import '/core/models/scene.dart';

class SceneSelectionScreen extends StatelessWidget {
  const SceneSelectionScreen({super.key});

  Future<List<Scene>> fetchScenes() async {
    final response = await http.get(Uri.parse('http://localhost:3000/scenes'));
    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      print('Response data: $data');  // 检查错误：添加这行来查看服务器返回的数据
      return data.map((json) => Scene.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load scenes');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '场景选择',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: Color.fromARGB(255, 25, 25, 25),
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color.fromARGB(255, 204, 255, 229),
              Color.fromARGB(255, 255, 250, 240),
            ],
          ),
        ),
        child: FutureBuilder<List<Scene>>(
          future: fetchScenes(),
          builder: (context, snapshot) {
            if (snapshot.hasData) {
              final scenes = snapshot.data!;
              return Padding(
                padding: const EdgeInsets.only(top: 100),
                child: GridView.builder(
                  padding: const EdgeInsets.all(16),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 0.8,
                  ),
                  itemCount: scenes.length,
                  itemBuilder: (context, index) {
                    final scene = scenes[index];
                    return SceneCard(
                      scene: scene,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => SceneScreen(scene: scene),
                          ),
                        );
                      },
                    );
                  },
                ),
              );
            }
            if (snapshot.hasError) {
              return Center(child: Text('错误: ${snapshot.error}', style: const TextStyle(color: Color.fromARGB(255, 25, 25, 25))));
            }
            return const Center(child: CircularProgressIndicator());
          },
        ),
      ),
    );
  }
}





