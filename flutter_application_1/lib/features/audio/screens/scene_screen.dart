// 该文件定义了一个 SceneScreen 组件，用于显示场景的详细信息页面。
// 它包含一个顶部应用栏、一个图片、一个描述和一个音频播放器。

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '/core/models/scene.dart';
import '../../../core/services/audio_service.dart';
import '../../../core/services/timer_service.dart';
import '../widgets/meditation_player.dart';
import '../widgets/white_noise_player.dart';
// import 'package:flutter_dominant_color_container/flutter_dominant_color_container.dart'; // 未使用的包
import 'package:palette_generator/palette_generator.dart';


// SceneScreen 是一个有状态组件，用于显示场景的详细信息页面。
// 继承自StatefulWidget以管理可变状态
class SceneScreen extends StatefulWidget {
  final Scene scene; // 存储当前显示的场景数据

  const SceneScreen({super.key, required this.scene});

  @override
  State<SceneScreen> createState() => _SceneScreenState();
}

// 音频播放器类型枚举
enum AudioPlayerMode {
  meditation,  // 冥想音频
  whiteNoise   // 白噪音
}

// SceneScreen的状态管理类
// 使用SingleTickerProviderStateMixin来支持动画功能
class _SceneScreenState extends State<SceneScreen> with SingleTickerProviderStateMixin {
  AudioPlayerMode _currentAudioMode = AudioPlayerMode.meditation; // 当前音频模式
  late AudioService _audioService;     // 处理音频播放的服务
  // TimerService暂未使用，但保留以备后续功能扩展
  late TimerService _timerService;     // 处理计时功能的服务
  bool _isFavorite = false;           // 追踪场景是否被收藏
  
  // 动画相关变量
  late AnimationController _animationController;  // 控制动画的播放
  late Animation<double> _fadeAnimation;          // 控制淡入效果
  late Animation<Offset> _slideAnimation;         // 控制滑动效果
  
  // 颜色相关变量
  PaletteGenerator? _paletteGenerator;           // 调色板生成器
  Color _dominantColor = const Color.fromARGB(255, 66, 65, 63);  // 主色调
  Color _lightColor = const Color.fromARGB(255, 182, 198, 208);  // 浅色调

  @override
  void initState() {
    super.initState();
    // 初始化服务和UI样式
    _audioService = AudioService();
    _timerService = TimerService();
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light);
    
    // 配置动画控制器，设置300毫秒的动画持续时间
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    // 设置淡入动画，使用easeOutExpo曲线使动画更自然
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutExpo,
    );
    
    // 设置滑动动画，从略微向下偏移的位置滑动到原位
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutExpo,
    ));
    
    _animationController.forward();  // 启动动画
    
    // 从图片中提取主色调
    _updatePaletteGenerator();
  }

  // 构建音频类型选择器
  Widget _buildAudioTypeSelector() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black26,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 冥想音频选项
          GestureDetector(
            onTap: () {
              setState(() {
                if (_currentAudioMode != AudioPlayerMode.meditation) {
                  _currentAudioMode = AudioPlayerMode.meditation;
                  _audioService.stop(); // 停止当前播放
                }
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: _currentAudioMode == AudioPlayerMode.meditation
                    ? Colors.white24
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                '冥想音频',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: _currentAudioMode == AudioPlayerMode.meditation
                      ? FontWeight.bold
                      : FontWeight.normal,
                ),
              ),
            ),
          ),
          
          // 白噪音选项
          GestureDetector(
            onTap: () {
              setState(() {
                if (_currentAudioMode != AudioPlayerMode.whiteNoise) {
                  _currentAudioMode = AudioPlayerMode.whiteNoise;
                  _audioService.stop(); // 停止当前播放
                }
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: _currentAudioMode == AudioPlayerMode.whiteNoise
                    ? Colors.white24
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                '白噪音',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: _currentAudioMode == AudioPlayerMode.whiteNoise
                      ? FontWeight.bold
                      : FontWeight.normal,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 根据当前选择的音频模式构建对应的播放器
  Widget _buildCurrentAudioPlayer() {
    switch (_currentAudioMode) {
      case AudioPlayerMode.meditation:
        return MeditationPlayer(
          audioService: _audioService,
          meditations: widget.scene.meditations,
          onPlayStateChanged: (isPlaying) {
            // 可以在这里处理播放状态变化
          },
          onVolumeChanged: (volume) {
            // 可以在这里处理音量变化
          },
          onAudioSelected: (audio) {
            // 可以在这里处理音频选择
          },
        );
      case AudioPlayerMode.whiteNoise:
        return WhiteNoisePlayer(
          audioService: _audioService,
          onPlayStateChanged: (isPlaying) {
            // 可以在这里处理播放状态变化
          },
          onVolumeChanged: (volume) {
            // 可以在这里处理音量变化
          },
        );
      // 添加默认返回，避免编译错误
      default:
        return const SizedBox.shrink(); // 返回一个空组件作为默认值
    }
  }

  @override
  void dispose() {
    _animationController.dispose();  // 释放动画控制器资源
    _audioService.dispose();         // 释放音频播放器资源
    super.dispose();
  }
  
  // 从图片中提取主色调
  Future<void> _updatePaletteGenerator() async {
    try {
      // 从网络图片创建ImageProvider
      final ImageProvider imageProvider = NetworkImage(widget.scene.imageUrl);
      
      // 生成调色板
      final PaletteGenerator paletteGenerator = await PaletteGenerator.fromImageProvider(
        imageProvider,
        maximumColorCount: 8, // 提取的颜色数量
      );
      
      // 如果组件已经被销毁，不更新状态
      if (!mounted) return;
      
      setState(() {
        _paletteGenerator = paletteGenerator;
        
        // 提取主色调，如果没有则使用默认颜色
        _dominantColor = paletteGenerator.dominantColor?.color ?? 
                         paletteGenerator.darkVibrantColor?.color ?? 
                         paletteGenerator.darkMutedColor?.color ?? 
                         const Color.fromARGB(255, 66, 65, 63);
        
        // 提取浅色调，如果没有则使用默认颜色
        _lightColor = paletteGenerator.lightVibrantColor?.color ?? 
                      paletteGenerator.lightMutedColor?.color ?? 
                      const Color.fromARGB(255, 182, 198, 208);
      });
    } catch (e) {
      // 出错时使用默认颜色
      print('提取图片颜色时出错: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,  // 允许内容延伸到AppBar后面
      appBar: AppBar(
        backgroundColor: const Color.fromARGB(0, 248, 248, 248),  // 透明背景
        elevation: 0,
        leading: IconButton(  // 返回按钮
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(  // 收藏按钮
            icon: Icon(_isFavorite ? Icons.favorite : Icons.favorite_border, color: Colors.white),
            onPressed: () {
              setState(() {
                _isFavorite = !_isFavorite;
              });
            },
          ),
        ],
      ),
      body: FadeTransition(  // 应用淡入动画
        opacity: _fadeAnimation,
        child: SlideTransition(  // 应用滑动动画
          position: _slideAnimation,
          child: Container(
            decoration: BoxDecoration(  // 设置渐变背景，使用从图片提取的颜色
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                stops: const [0.0, 1.0],
                colors: [_dominantColor, _lightColor],
                transform: const GradientRotation(2.53073),
              ),
            ),
            child: Column(
              children: [
                // 顶部安全区域间距
                const SizedBox(height: 100),
                // 音频类型选择器
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildAudioTypeSelector(),
                  ],
                ),
                // 图片容器，调整了上边距
                Container(
                  margin: const EdgeInsets.only(top: 24, left: 48, right: 48),
              decoration: BoxDecoration(
                color: const Color(0xFF1A1A1A),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: const Color.fromARGB(155, 137, 137, 137),
                  width: 0.5,
                ), // 边框设计
                boxShadow: [
                  BoxShadow(
                    color: const Color.fromARGB(26, 0, 0, 0), // 0.1 透明度
                    offset: const Offset(0, 2),
                    blurRadius: 4,
                  ),
                  BoxShadow(
                    color: const Color.fromARGB(13, 0, 0, 0), // 0.05 透明度
                    offset: const Offset(0, 3),
                    blurRadius: 6,
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: Stack(
                  children: [
                    Image.network(
                      widget.scene.imageUrl,
                      height: 400,
                      width: double.infinity,
                      fit: BoxFit.cover,
                    ),
                    Container(
                      height: 400,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            const Color.fromARGB(128, 0, 0, 0), // 0.5 透明度
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 8),
            
 // 场景描述与播放器组件
            Expanded(
              child: SingleChildScrollView(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.scene.name,
                        style: const TextStyle(
                          fontSize: 24,
                          height: 1.333,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          letterSpacing: 0.5,
                        ),
                      ),
                      const SizedBox(height: 8),
                      
                      // 场景描述（如果有的话）
                      if (widget.scene.description.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Text(
                            widget.scene.description,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Color.fromARGB(204, 255, 255, 255), // 使用0.8透明度的白色
                              height: 1.5,
                            ),
                          ),
                        ),
                      
                      // 音频播放控制组件
                      _buildCurrentAudioPlayer(),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      ),
      )
    );
  }
}
