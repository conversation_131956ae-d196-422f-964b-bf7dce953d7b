import 'dart:async';
import 'dart:ui';
import 'package:flutter/material.dart';
import '../../../core/models/scene.dart';
import '../../../core/services/audio_service.dart';

/// 冥想音频播放器组件
class MeditationPlayer extends StatefulWidget {
  final AudioService audioService;
  final List<MeditationAudio>? meditations;
  final Function(bool)? onPlayStateChanged;
  final Function(double)? onVolumeChanged;
  final Function(MeditationAudio)? onAudioSelected;

  const MeditationPlayer({
    super.key,
    required this.audioService,
    this.meditations,
    this.onPlayStateChanged,
    this.onVolumeChanged,
    this.onAudioSelected,
  });

  @override
  State<MeditationPlayer> createState() => _MeditationPlayerState();
}

class _MeditationPlayerState extends State<MeditationPlayer> {
  // 播放状态
  AudioPlaybackState _playbackState = AudioPlaybackState.stopped;
  
  // 当前播放位置和总时长
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  
  // 当前选中的音频
  MeditationAudio? _currentAudio;
  
  // 音量
  double _volume = 1.0;
  
  // 播放速度
  double _playbackSpeed = 1.0;
  
  // 状态订阅
  StreamSubscription? _stateSubscription;
  StreamSubscription? _positionSubscription;
  StreamSubscription? _errorSubscription;
  
  @override
  void initState() {
    super.initState();
    
    // 初始化默认选中的音频
    if (widget.meditations != null && widget.meditations!.isNotEmpty) {
      _currentAudio = widget.meditations![0];
    }
    
    // 订阅播放状态变化
    _stateSubscription = widget.audioService.stateStream.listen((state) {
      setState(() {
        _playbackState = state;
      });
      
      // 通知父组件播放状态变化
      if (state == AudioPlaybackState.playing) {
        widget.onPlayStateChanged?.call(true);
      } else if (state == AudioPlaybackState.paused || 
                state == AudioPlaybackState.stopped || 
                state == AudioPlaybackState.completed) {
        widget.onPlayStateChanged?.call(false);
      }
    });
    
    // 订阅播放位置变化
    _positionSubscription = widget.audioService.positionStream.listen((position) {
      setState(() {
        _currentPosition = position;
        _totalDuration = widget.audioService.totalDuration;
      });
    });
    
    // 订阅错误信息
    _errorSubscription = widget.audioService.errorStream.listen((errorMsg) {
      // 显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('播放错误: $errorMsg')),
      );
    });
  }
  
  @override
  void dispose() {
    // 取消订阅
    _stateSubscription?.cancel();
    _positionSubscription?.cancel();
    _errorSubscription?.cancel();
    super.dispose();
  }
  
  // 格式化时间
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    
    return hours == '00' ? '$minutes:$seconds' : '$hours:$minutes:$seconds';
  }
  
  // 处理播放/暂停
  void _handlePlayPause() {
    if (_currentAudio == null) return;
    
    setState(() {
      if (_playbackState == AudioPlaybackState.playing) {
        widget.audioService.pause();
      } else if (_playbackState == AudioPlaybackState.paused || 
                _playbackState == AudioPlaybackState.stopped || 
                _playbackState == AudioPlaybackState.completed) {
        if (_playbackState == AudioPlaybackState.completed) {
          // 如果已播放完成，从头开始播放
          widget.audioService.seekTo(Duration.zero);
        }
        widget.audioService.play(_currentAudio!.url);
      } else if (_playbackState == AudioPlaybackState.error) {
        // 错误状态下尝试重新播放
        widget.audioService.play(_currentAudio!.url);
      }
    });
  }
  
  // 处理进度拖动
  void _handleSeek(double value) {
    final newPosition = Duration(milliseconds: (value * _totalDuration.inMilliseconds).round());
    widget.audioService.seekTo(newPosition);
  }
  
  // 处理音量变化
  void _handleVolumeChange(double value) {
    setState(() {
      _volume = value;
      widget.audioService.changeVolume(_volume);
      widget.onVolumeChanged?.call(_volume);
    });
  }
  
  // 处理播放速度变化
  void _handleSpeedChange(double value) {
    setState(() {
      _playbackSpeed = value;
      widget.audioService.setPlaybackSpeed(_playbackSpeed);
    });
  }
  
  // 处理音频选择
  void _handleAudioSelection(MeditationAudio audio) {
    setState(() {
      _currentAudio = audio;
      widget.audioService.play(audio.url);
      widget.onAudioSelected?.call(audio);
    });
  }
  
  // 构建播放控制按钮
  Widget _buildPlayButton() {
    IconData icon;
    String tooltip;
    
    switch (_playbackState) {
      case AudioPlaybackState.playing:
        icon = Icons.pause_rounded;
        tooltip = '暂停';
        break;
      case AudioPlaybackState.loading:
      case AudioPlaybackState.buffering:
        return SizedBox(
          width: 48,
          height: 48,
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(const Color.fromARGB(229, 255, 255, 255)),
            strokeWidth: 3,
          ),
        );
      case AudioPlaybackState.error:
        icon = Icons.refresh_rounded;
        tooltip = '重试';
        break;
      default:
        icon = Icons.play_arrow_rounded;
        tooltip = '播放';
    }
    
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        icon: Icon(
          icon,
          size: 36,
          color: Colors.white.withOpacity(0.9),
        ),
        tooltip: tooltip,
        onPressed: _handlePlayPause,
      ),
    );
  }
  
  // 构建进度条
  Widget _buildProgressBar() {
    return Column(
      children: [
        SliderTheme(
          data: SliderThemeData(
            trackHeight: 2.0,
            activeTrackColor: Colors.white.withOpacity(0.9),
            inactiveTrackColor: Colors.white.withOpacity(0.3),
            thumbColor: Colors.white,
            overlayColor: Colors.white.withOpacity(0.1),
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6.0),
            overlayShape: const RoundSliderOverlayShape(overlayRadius: 16.0),
          ),
          child: Slider(
            value: _totalDuration.inMilliseconds > 0
                ? _currentPosition.inMilliseconds / _totalDuration.inMilliseconds
                : 0.0,
            onChanged: _handleSeek,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatDuration(_currentPosition),
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 12,
                ),
              ),
              Text(
                _formatDuration(_totalDuration),
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  // 构建音频选择列表
  Widget _buildAudioSelector() {
    if (widget.meditations == null || widget.meditations!.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return ClipRRect(
      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.6),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 顶部把手
              Container(
                margin: const EdgeInsets.symmetric(vertical: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  '选择冥想音频',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ),
              ConstrainedBox(
                constraints: const BoxConstraints(maxHeight: 300),
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const BouncingScrollPhysics(),
                  itemCount: widget.meditations!.length,
                  itemBuilder: (context, index) {
                    final audio = widget.meditations![index];
                    final isSelected = _currentAudio == widget.meditations![index];
                    
                    return Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                      decoration: BoxDecoration(
                        color: isSelected ? Colors.white.withOpacity(0.1) : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected ? Colors.white.withOpacity(0.2) : Colors.transparent,
                          width: 1,
                        ),
                      ),
                      child: ListTile(
                        title: Text(
                          audio.voice,
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.9),
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                        subtitle: Text(
                          '${audio.duration}秒 | ${audio.language}',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.6),
                          ),
                        ),
                        leading: Icon(
                          isSelected ? Icons.music_note : Icons.music_note_outlined,
                          color: isSelected ? Colors.white : Colors.white.withOpacity(0.6),
                        ),
                        onTap: () {
                          _handleAudioSelection(audio);
                          Navigator.pop(context);
                        },
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }
  
  // 显示音量控制弹窗
  void _showVolumeControl() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.6),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 顶部把手
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    '音量调节',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
                  child: Row(
                    children: [
                      Icon(Icons.volume_down, color: Colors.white.withOpacity(0.7)),
                      const SizedBox(width: 12),
                      Expanded(
                        child: SliderTheme(
                          data: SliderThemeData(
                            trackHeight: 2.0,
                            activeTrackColor: Colors.white.withOpacity(0.9),
                            inactiveTrackColor: Colors.white.withOpacity(0.3),
                            thumbColor: Colors.white,
                            overlayColor: Colors.white.withOpacity(0.1),
                            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6.0),
                            overlayShape: const RoundSliderOverlayShape(overlayRadius: 16.0),
                          ),
                          child: Slider(
                            value: _volume,
                            onChanged: _handleVolumeChange,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Icon(Icons.volume_up, color: Colors.white.withOpacity(0.7)),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  // 显示播放速度控制弹窗
  void _showSpeedControl() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('播放速度', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            Row(
              children: [
                const Text('0.5x'),
                Expanded(
                  child: Slider(
                    value: _playbackSpeed,
                    min: 0.5,
                    max: 2.0,
                    divisions: 3,
                    label: '${_playbackSpeed}x',
                    onChanged: _handleSpeedChange,
                  ),
                ),
                const Text('2.0x'),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 播放控制区域
        ClipRRect(
          borderRadius: BorderRadius.circular(16.0),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
            child: Container(
              margin: const EdgeInsets.all(16.0),
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
              decoration: BoxDecoration(
                color: const Color.fromARGB(26, 255, 255, 255),
                borderRadius: BorderRadius.circular(16.0),
                border: Border.all(
                  color: const Color.fromARGB(51, 255, 255, 255),
                  width: 1.0,
                ),
              ),
              child: Column(
                children: [
                  // 第一行：Voice名称
                  if (_currentAudio != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 12.0),
                      child: Text(
                        _currentAudio!.voice,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.white.withOpacity(0.9),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  
                  // 第二行：播放进度
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: _buildProgressBar(),
                  ),
                  
                  // 第三行：控制按钮
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // 左侧：音量调节按钮
                        IconButton(
                          icon: const Icon(Icons.volume_up, color: Colors.white70),
                          onPressed: _showVolumeControl,
                          tooltip: '音量',
                        ),
                        
                        // 中间：播放/暂停按钮
                        _buildPlayButton(),
                        
                        // 右侧：音频选择按钮
                        IconButton(
                          icon: const Icon(Icons.queue_music, color: Colors.white70),
                          onPressed: () => showModalBottomSheet(
                            context: context,
                            backgroundColor: Colors.transparent,
                            builder: (context) => _buildAudioSelector(),
                          ),
                          tooltip: '选择音频',
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}