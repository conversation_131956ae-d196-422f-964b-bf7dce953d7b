import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';

class CarouselItem {
  final String imageUrl;
  final String? destination;

  const CarouselItem({
    required this.imageUrl,
    this.destination,
  });
}

class ImageCarousel extends StatelessWidget {
  final List<CarouselItem> items;

  const ImageCarousel({
    super.key,
    required this.items,
  });

  @override
  Widget build(BuildContext context) {
    return CarouselSlider(
      options: CarouselOptions(
        height: 200.0,
        enlargeCenterPage: true,
        autoPlay: true,
        aspectRatio: 16 / 9,
        autoPlayCurve: Curves.fastOutSlowIn,
        enableInfiniteScroll: true,
        autoPlayAnimationDuration: const Duration(milliseconds: 800),
        viewportFraction: 0.8,
      ),
      items: items.asMap().entries.map((entry) {
        return Builder(
          builder: (BuildContext context) {
            return GestureDetector(
              onTap: entry.value.destination != null
                  ? () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:((context) => const TestRoute()),
                        ),
                      );
                    }
                  : null,
              child: Container(
                width: MediaQuery.of(context).size.width,
                margin: const EdgeInsets.symmetric(horizontal: 5.0),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20.0),
                  boxShadow: const [
                    BoxShadow(
                      color: Colors.black26,
                      blurRadius: 10.0,
                      offset: Offset(0, 5),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20.0),
                  child: Image.asset(
                  entry.value.imageUrl,
                  fit: BoxFit.cover,
                ),
              ),
              )
            );
          },
        );
      }).toList(),
    );
  }
}

class TestRoute extends StatelessWidget {
  const TestRoute({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Second Route'),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text('Go back!'),
        ),
      ),
    );
  }
}



// import 'package:flutter/material.dart';
// import 'package:carousel_slider/carousel_slider.dart';

// class ImageCarousel extends StatelessWidget {
//   final List<String> imageUrls;
//   final List<Widget> destinations;

//   const ImageCarousel({
//     super.key,
//     required this.imageUrls,
//     required this.destinations,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return CarouselSlider(
//       options: CarouselOptions(
//         height: 200.0,
//         enlargeCenterPage: true,
//         autoPlay: true,
//         aspectRatio: 16 / 9,
//         autoPlayCurve: Curves.fastOutSlowIn,
//         enableInfiniteScroll: true,
//         autoPlayAnimationDuration: const Duration(milliseconds: 800),
//         viewportFraction: 0.8,
//       ),
//       items: imageUrls.asMap().entries.map((entry) {
//         return Builder(
//           builder: (BuildContext context) {
//             return GestureDetector(
//               onTap: () {
//                 Navigator.push(
//                   context,
//                   MaterialPageRoute(builder: (context) => destinations[entry.key]),
//                 );
//               },
//               child: Container(
//                 width: MediaQuery.of(context).size.width,
//                 margin: const EdgeInsets.symmetric(horizontal: 5.0),
//                 decoration: BoxDecoration(
//                   color: Colors.amber,
//                   borderRadius: BorderRadius.circular(10.0),
//                 ),
//                 child: Image.asset(
//                   entry.value,
//                   fit: BoxFit.cover,
//                 ),
//               ),
//             );
//           },
//         );
//       }).toList(),
//     );
//   }
// }
