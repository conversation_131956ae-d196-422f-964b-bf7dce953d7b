// 该文件定义了一个 AudioPlayerControls 组件，用于封装音频播放相关的控制按钮。
// 它提供了音量调节、播放/暂停和音频列表选择功能，并支持不同类型的音频播放器。

import 'dart:async';
import 'package:flutter/material.dart';
import '../../../core/models/scene.dart';
import '../../../core/services/audio_service.dart';

/// 音频播放器类型枚举
enum AudioPlayerType {
  meditation, // 冥想音频
  whiteNoise, // 白噪音
  music       // 音乐
}

/// AudioPlayerControls 是一个有状态组件，用于显示音频播放控制按钮。
class AudioPlayerControls extends StatefulWidget {
  final AudioService audioService;
  final List<MeditationAudio>? meditations; // 可选的冥想音频列表
  final AudioPlayerType playerType; // 播放器类型
  final Function(bool)? onPlayStateChanged; // 播放状态变化回调
  final Function(double)? onVolumeChanged; // 音量变化回调
  final Function(MeditationAudio)? onAudioSelected; // 音频选择回调

  const AudioPlayerControls({
    super.key,
    required this.audioService,
    this.meditations,
    this.playerType = AudioPlayerType.meditation,
    this.onPlayStateChanged,
    this.onVolumeChanged,
    this.onAudioSelected,
  });

  @override
  State<AudioPlayerControls> createState() => _AudioPlayerControlsState();
}

class _AudioPlayerControlsState extends State<AudioPlayerControls> {
  // 播放状态
  AudioPlaybackState _playbackState = AudioPlaybackState.stopped;
  bool _isPlaying = false;
  double _volume = 1.0;
  MeditationAudio? _currentAudio;
  
  // 状态订阅
  StreamSubscription? _stateSubscription;
  StreamSubscription? _errorSubscription;

  @override
  void initState() {
    super.initState();
    // 如果有冥想音频，默认选择第一个
    if (widget.meditations != null && widget.meditations!.isNotEmpty) {
      _currentAudio = widget.meditations![0];
    }
    
    // 订阅播放状态变化
    _stateSubscription = widget.audioService.stateStream.listen((state) {
      setState(() {
        _playbackState = state;
        _isPlaying = state == AudioPlaybackState.playing;
      });
      
      // 通知父组件播放状态变化
      widget.onPlayStateChanged?.call(_isPlaying);
    });
    
    // 订阅错误信息
    _errorSubscription = widget.audioService.errorStream.listen((errorMsg) {
      // 显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('播放错误: $errorMsg')),
      );
    });
  }
  
  @override
  void dispose() {
    // 取消订阅
    _stateSubscription?.cancel();
    _errorSubscription?.cancel();
    super.dispose();
  }

  // 处理播放/暂停按钮点击
  void _handlePlayPause() {
    if (_currentAudio == null) return;
    
    if (_playbackState == AudioPlaybackState.playing) {
      widget.audioService.pause();
    } else if (_playbackState == AudioPlaybackState.paused || 
              _playbackState == AudioPlaybackState.stopped || 
              _playbackState == AudioPlaybackState.completed) {
      if (_playbackState == AudioPlaybackState.completed) {
        // 如果已播放完成，从头开始播放
        widget.audioService.seekTo(Duration.zero);
      }
      widget.audioService.play(_currentAudio!.url);
    } else if (_playbackState == AudioPlaybackState.error) {
      // 错误状态下尝试重新播放
      widget.audioService.play(_currentAudio!.url);
    }
    
    // 状态会通过订阅更新，不需要在这里设置
  }

  // 处理音量变化
  void _handleVolumeChange(double value) {
    setState(() {
      _volume = value;
      widget.audioService.changeVolume(_volume);
      
      // 通知父组件音量变化
      widget.onVolumeChanged?.call(_volume);
    });
  }

  // 处理音频选择
  void _handleAudioSelection(MeditationAudio audio) {
    setState(() {
      _currentAudio = audio;
      _isPlaying = true;
      widget.audioService.play(audio.url);
      
      // 通知父组件音频选择变化
      widget.onAudioSelected?.call(audio);
      widget.onPlayStateChanged?.call(true);
    });
  }

  // 显示音量调节弹窗
  void _showVolumeSlider() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: const Color(0xFF1A1A1A),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 24),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const Text(
              '音量调节',
              style: TextStyle(
                fontSize: 18, 
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 32),
            Row(
              children: [
                const Icon(Icons.volume_down, color: Colors.white),
                Expanded(
                  child: SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: Colors.white,
                      inactiveTrackColor: Colors.white.withOpacity(0.3),
                      thumbColor: Colors.white,
                      trackHeight: 4.0,
                      thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8.0),
                      overlayShape: const RoundSliderOverlayShape(overlayRadius: 16.0),
                    ),
                    child: Slider(
                      value: _volume,
                      onChanged: _handleVolumeChange,
                      min: 0.0,
                      max: 1.0,
                    ),
                  ),
                ),
                const Icon(Icons.volume_up, color: Colors.white),
              ],
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  // 显示音频列表选择弹窗
  void _showAudioList() {
    if (widget.meditations == null || widget.meditations!.isEmpty) return;
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: const Color(0xFF1A1A1A),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 24),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const Text(
              '选择冥想音频',
              style: TextStyle(
                fontSize: 18, 
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.4,
              ),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: widget.meditations!.length,
                itemBuilder: (context, index) {
                  final meditation = widget.meditations![index];
                  final isSelected = _currentAudio?.url == meditation.url;
                  
                  return ListTile(
                    title: Text(
                      meditation.voice,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.white.withOpacity(0.8),
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                    subtitle: Text(
                      '${meditation.duration ~/ 60}分${meditation.duration % 60}秒',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.6),
                      ),
                    ),
                    leading: isSelected 
                      ? const Icon(Icons.play_circle_filled, color: Colors.white)
                      : const Icon(Icons.play_circle_outline, color: Colors.white70),
                    onTap: () {
                      _handleAudioSelection(meditation);
                      Navigator.pop(context);
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // 音量按钮
        ControlButton(
          icon: Icons.volume_up,
          onPressed: _showVolumeSlider,
        ),
        
        // 播放/暂停按钮
        ControlButton(
          icon: _isPlaying ? Icons.pause : Icons.play_arrow,
          onPressed: _handlePlayPause,
        ),
        
        // 音频列表按钮
        ControlButton(
          icon: Icons.list,
          onPressed: widget.meditations != null && widget.meditations!.isNotEmpty 
            ? _showAudioList 
            : null,
        ),
      ],
    );
  }
}

/// ControlButton: 自定义控制按钮组件
/// 用于创建统一风格的交互按钮（播放、音量、列表等）
class ControlButton extends StatelessWidget {
  final IconData icon;           // 按钮图标 
  final VoidCallback? onPressed; // 点击回调函数
  final Color? backgroundColor;  // 背景颜色
  final Color? iconColor;        // 图标颜色

  const ControlButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.backgroundColor,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(  // 添加水波纹效果
      onTap: onPressed,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(  // 设置按钮样式
          shape: BoxShape.circle,
          color: backgroundColor ?? const Color.fromARGB(100, 26, 26, 26),
          boxShadow: [
            BoxShadow(  // 添加阴影效果
              color: const Color.fromARGB(26, 0, 0, 0),
              offset: const Offset(0, 2),
              blurRadius: 4,
            ),
          ],
        ),
        child: Icon(
          icon,
          color: iconColor ?? Colors.white,
          size: 32,
        ),
      ),
    );
  }
}