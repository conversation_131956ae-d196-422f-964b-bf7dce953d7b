// 该文件定义了一个 WhiteNoisePlayer 组件，用于播放白噪音音频。
// 它提供了音量调节和播放/暂停功能，可以与其他音频播放器组件一起使用。

import 'package:flutter/material.dart';
import '../../../core/services/audio_service.dart';

/// 白噪音类型枚举
enum WhiteNoiseType {
  rain,      // 雨声
  ocean,     // 海浪声
  forest,    // 森林声
  fire,      // 火焰声
  fan,       // 风扇声
  thunder,   // 雷声
  stream,    // 溪流声
  birds      // 鸟鸣声
}

/// 白噪音数据模型
class WhiteNoiseData {
  final String name;
  final String url;
  final WhiteNoiseType type;
  final String iconPath; // 图标路径

  const WhiteNoiseData({
    required this.name,
    required this.url,
    required this.type,
    required this.iconPath,
  });
}

/// 预定义的白噪音列表
final List<WhiteNoiseData> predefinedWhiteNoises = [
  WhiteNoiseData(
    name: '雨声',
    url: 'assets/audio/white_noise/rain.mp3',
    type: WhiteNoiseType.rain,
    iconPath: 'assets/icons/rain.png',
  ),
  WhiteNoiseData(
    name: '海浪声',
    url: 'assets/audio/white_noise/ocean.mp3',
    type: WhiteNoiseType.ocean,
    iconPath: 'assets/icons/ocean.png',
  ),
  WhiteNoiseData(
    name: '森林声',
    url: 'assets/audio/white_noise/forest.mp3',
    type: WhiteNoiseType.forest,
    iconPath: 'assets/icons/forest.png',
  ),
  WhiteNoiseData(
    name: '火焰声',
    url: 'assets/audio/white_noise/fire.mp3',
    type: WhiteNoiseType.fire,
    iconPath: 'assets/icons/fire.png',
  ),
];

/// WhiteNoisePlayer 是一个有状态组件，用于播放白噪音。
class WhiteNoisePlayer extends StatefulWidget {
  final AudioService audioService;
  final List<WhiteNoiseData>? whiteNoises; // 可选的白噪音列表，如果为空则使用预定义列表
  final Function(bool)? onPlayStateChanged; // 播放状态变化回调
  final Function(double)? onVolumeChanged; // 音量变化回调
  final Function(WhiteNoiseData)? onNoiseSelected; // 白噪音选择回调

  const WhiteNoisePlayer({
    super.key,
    required this.audioService,
    this.whiteNoises,
    this.onPlayStateChanged,
    this.onVolumeChanged,
    this.onNoiseSelected,
  });

  @override
  State<WhiteNoisePlayer> createState() => _WhiteNoisePlayerState();
}

class _WhiteNoisePlayerState extends State<WhiteNoisePlayer> with AutomaticKeepAliveClientMixin {
  bool _isPlaying = false;
  double _volume = 1.0;
  WhiteNoiseData? _currentNoise;
  late List<WhiteNoiseData> _noiseList;

  @override
  bool get wantKeepAlive => true; // 保持状态，避免切换时重置

  @override
  void initState() {
    super.initState();
    // 使用提供的白噪音列表或预定义列表
    _noiseList = widget.whiteNoises ?? predefinedWhiteNoises;
    
    // 如果有白噪音，默认选择第一个
    if (_noiseList.isNotEmpty) {
      _currentNoise = _noiseList[0];
    }
  }

  // 处理播放/暂停按钮点击
  void _handlePlayPause() {
    setState(() {
      _isPlaying = !_isPlaying;
      if (_isPlaying) {
        if (_currentNoise != null) {
          widget.audioService.play(_currentNoise!.url);
        }
      } else {
        widget.audioService.pause();
      }
      
      // 通知父组件播放状态变化
      widget.onPlayStateChanged?.call(_isPlaying);
    });
  }

  // 处理音量变化
  void _handleVolumeChange(double value) {
    setState(() {
      _volume = value;
      widget.audioService.changeVolume(_volume);
      
      // 通知父组件音量变化
      widget.onVolumeChanged?.call(_volume);
    });
  }

  // 处理白噪音选择
  void _handleNoiseSelection(WhiteNoiseData noise) {
    setState(() {
      _currentNoise = noise;
      _isPlaying = true;
      widget.audioService.play(noise.url);
      
      // 通知父组件白噪音选择变化
      widget.onNoiseSelected?.call(noise);
      widget.onPlayStateChanged?.call(true);
    });
  }

  // 显示音量调节弹窗
  void _showVolumeSlider() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: const Color(0xFF1A1A1A),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 24),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const Text(
              '音量调节',
              style: TextStyle(
                fontSize: 18, 
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 32),
            Row(
              children: [
                const Icon(Icons.volume_down, color: Colors.white),
                Expanded(
                  child: SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: Colors.white,
                      inactiveTrackColor: Colors.white.withOpacity(0.3),
                      thumbColor: Colors.white,
                      trackHeight: 4.0,
                      thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8.0),
                      overlayShape: const RoundSliderOverlayShape(overlayRadius: 16.0),
                    ),
                    child: Slider(
                      value: _volume,
                      onChanged: _handleVolumeChange,
                      min: 0.0,
                      max: 1.0,
                    ),
                  ),
                ),
                const Icon(Icons.volume_up, color: Colors.white),
              ],
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  // 显示白噪音选择网格
  void _showNoiseGrid() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: const Color(0xFF1A1A1A),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 24),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const Text(
              '选择白噪音',
              style: TextStyle(
                fontSize: 18, 
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 24),
            ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.4,
              ),
              child: GridView.builder(
                shrinkWrap: true,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 1.5,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                ),
                itemCount: _noiseList.length,
                itemBuilder: (context, index) {
                  final noise = _noiseList[index];
                  final isSelected = _currentNoise?.url == noise.url;
                  
                  return GestureDetector(
                    onTap: () {
                      _handleNoiseSelection(noise);
                      Navigator.pop(context);
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected 
                            ? const Color(0xFF3A3A3A) 
                            : const Color(0xFF2A2A2A),
                        borderRadius: BorderRadius.circular(12),
                        border: isSelected
                            ? Border.all(color: Colors.white, width: 2)
                            : null,
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _getIconForNoiseType(noise.type),
                            color: Colors.white,
                            size: 32,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            noise.name,
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 根据白噪音类型获取对应的图标
  IconData _getIconForNoiseType(WhiteNoiseType type) {
    switch (type) {
      case WhiteNoiseType.rain:
        return Icons.water_drop;
      case WhiteNoiseType.ocean:
        return Icons.waves;
      case WhiteNoiseType.forest:
        return Icons.forest;
      case WhiteNoiseType.fire:
        return Icons.local_fire_department;
      case WhiteNoiseType.fan:
        return Icons.air;
      case WhiteNoiseType.thunder:
        return Icons.flash_on;
      case WhiteNoiseType.stream:
        return Icons.water;
      case WhiteNoiseType.birds:
        return Icons.flutter_dash;
      default:
        return Icons.music_note;
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用，因为使用了AutomaticKeepAliveClientMixin
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // 音量按钮
        ControlButton(
          icon: Icons.volume_up,
          onPressed: _showVolumeSlider,
        ),
        
        // 播放/暂停按钮
        ControlButton(
          icon: _isPlaying ? Icons.pause : Icons.play_arrow,
          onPressed: _handlePlayPause,
        ),
        
        // 白噪音选择按钮
        ControlButton(
          icon: Icons.grid_view,
          onPressed: _noiseList.isNotEmpty ? _showNoiseGrid : null,
        ),
      ],
    );
  }
}

/// ControlButton: 自定义控制按钮组件
/// 用于创建统一风格的交互按钮（播放、音量、列表等）
class ControlButton extends StatelessWidget {
  final IconData icon;           // 按钮图标 
  final VoidCallback? onPressed; // 点击回调函数
  final Color? backgroundColor;  // 背景颜色
  final Color? iconColor;        // 图标颜色

  const ControlButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.backgroundColor,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(  // 添加水波纹效果
      onTap: onPressed,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(  // 设置按钮样式
          shape: BoxShape.circle,
          color: backgroundColor ?? const Color.fromARGB(100, 26, 26, 26),
          boxShadow: [
            BoxShadow(  // 添加阴影效果
              color: const Color.fromARGB(26, 0, 0, 0),
              offset: const Offset(0, 2),
              blurRadius: 4,
            ),
          ],
        ),
        child: Icon(
          icon,
          color: iconColor ?? Colors.white,
          size: 32,
        ),
      ),
    );
  }
}