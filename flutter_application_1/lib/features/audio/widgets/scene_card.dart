// 该文件定义了一个 SceneCard 组件，用于显示场景的卡片视图。
// 它接受一个 Scene 对象和一个点击事件处理函数作为参数，并根据这些参数显示场景的图片和名称。

import 'package:flutter/material.dart';
import '../../../core/models/scene.dart';

// SceneCard 是一个无状态组件，用于显示场景的卡片视图。
class SceneCard extends StatelessWidget {
  // 要显示的场景对象。
  final Scene scene;
  // 点击事件处理函数。
  final VoidCallback onTap;

  // 构造函数，接收一个 Scene 对象和一个点击事件处理函数作为参数。
  const SceneCard({
    super.key,
    required this.scene,
    required this.onTap,
  });

  // 构建方法，返回一个 Card 组件。
  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 0.8,
      child: Container(
        decoration: BoxDecoration(
          color: const Color.fromARGB(38, 255, 255, 255), // 0.15 透明度
          borderRadius: BorderRadius.circular(16.0),
          boxShadow: [BoxShadow(
            color: const Color.fromARGB(26, 0, 0, 0), // 0.1 透明度
            blurRadius: 8.0,
            offset: const Offset(0, 4),
          )],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16.0),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onTap,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AspectRatio(
                    aspectRatio: 16/9,
                    child: Stack(
                      children: [
                        Image.network(
                          scene.imageUrl,
                          width: double.infinity,
                          height: double.infinity,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Container(
                            color: Colors.grey[300],
                            child: const Icon(Icons.image_not_supported, size: 40),
                          ),
                        ),
                        if (scene.isPremium)
                          Positioned(
                            top: 8,
                            right: 8,
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.amber,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                '高级',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            scene.name,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Expanded(
                            child: Text(
                              scene.description,
                              style: const TextStyle(
                                color: Color.fromARGB(204, 255, 255, 255), // 0.8 透明度
                                fontSize: 14,
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
