import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../../../core/models/user.dart';
import '../../../core/services/user_service.dart';

/// 用户数据提供者
/// 用于获取和管理用户数据
final userProvider = FutureProvider<User>((ref) async {
  try {
    // 创建UserService实例
    final userService = UserService();

    // 获取用户信息
    final user = await userService.getUserProfile();
    return user;
  } catch (e) {
    debugPrint('获取用户数据失败: $e');

    // 如果API调用失败，创建一个模拟用户数据用于开发测试
    // 在实际生产环境中，应该抛出异常或返回错误状态
    return User(
      id: 'mock-user-id',
      username: '测试用户',
      nickname: '专注达人',
      email: '<EMAIL>',
      phone: '13800138000',
      avatar: null,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      lastLoginAt: DateTime.now(),
      preferences: {},
      settings: {
        'theme': 'light',
        'notifications': true,
      },
      usageStats: {
        'totalFocusTime': 3600, // 秒
        'sessionsCompleted': 10,
        'lastUsedDate': DateTime.now().toIso8601String(),
      },
    );
  }
});

/// 用户设置提供者
/// 用于管理用户设置
final userSettingsProvider = StateNotifierProvider<UserSettingsNotifier, Map<String, dynamic>>((ref) {
  final userAsync = ref.watch(userProvider);
  return UserSettingsNotifier(
    ref,
    initialSettings: userAsync.value?.settings ?? {},
  );
});

/// 用户设置状态管理
class UserSettingsNotifier extends StateNotifier<Map<String, dynamic>> {
  final Ref _ref;

  UserSettingsNotifier(this._ref, {required Map<String, dynamic> initialSettings}) : super(initialSettings);

  /// 更新设置
  Future<void> updateSetting(String key, dynamic value) async {
    state = {...state, key: value};

    try {
      // 获取当前用户
      final user = _ref.read(userProvider).value;
      if (user != null) {
        // 更新用户设置
        final updatedSettings = {...user.settings, key: value};

        // 调用更新API
        await _ref.read(updateUserProvider(
          {'settings': updatedSettings}
        ).future);
      }
    } catch (e) {
      // 错误处理
      debugPrint('更新设置失败: $e');
    }
  }

  /// 获取设置值
  T? getSetting<T>(String key, {T? defaultValue}) {
    return state[key] as T? ?? defaultValue;
  }

  /// 重置设置
  Future<void> resetSettings() async {
    state = {};

    try {
      // 获取当前用户
      final user = _ref.read(userProvider).value;
      if (user != null) {
        // 调用更新API
        await _ref.read(updateUserProvider(
          {'settings': {}}
        ).future);
      }
    } catch (e) {
      // 错误处理
      debugPrint('重置设置失败: $e');
    }
  }
}

/// 更新用户信息
/// 用于更新用户的个人资料
final updateUserProvider = FutureProvider.family<User, Map<String, dynamic>>((ref, userData) async {
  try {
    final userService = UserService();

    // 更新用户信息
    final updatedUser = await userService.updateUserProfile(
      nickname: userData['nickname'],
      email: userData['email'],
      preferences: userData['preferences'],
      settings: userData['settings'],
    );

    // 刷新用户数据
    ref.invalidate(userProvider);

    return updatedUser;
  } catch (e) {
    debugPrint('更新用户信息失败: $e');
    throw Exception('更新用户信息失败: $e');
  }
});
