import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../../../core/services/user_service.dart';
import '../../../core/utils/storage_utils.dart';
import 'user_provider.dart';

// 头像功能已移除

/// 个人资料更新状态提供者
final profileUpdateProvider = StateNotifierProvider<ProfileUpdateNotifier, AsyncValue<void>>((ref) {
  return ProfileUpdateNotifier(ref);
});

/// 个人资料更新状态管理
class ProfileUpdateNotifier extends StateNotifier<AsyncValue<void>> {
  final Ref _ref;
  final UserService _userService = UserService();

  ProfileUpdateNotifier(this._ref) : super(const AsyncValue.data(null));

  /// 更新个人资料
  Future<bool> updateProfile({
    required String nickname,
    String? email,
    String? avatarUrl,
  }) async {
    state = const AsyncValue.loading();

    try {
      debugPrint('开始更新个人资料: nickname=$nickname, email=$email');

      // 检查用户是否已登录
      final token = await StorageUtils.getToken();
      if (token == null) {
        debugPrint('更新个人资料失败: 用户未登录');
        state = AsyncValue.error(Exception('用户未登录，请先登录'), StackTrace.current);
        return false;
      }

      debugPrint('用户已登录，令牌: ${token.substring(0, 10)}...');

      // 准备更新数据
      final Map<String, dynamic> preferences = {};
      final Map<String, dynamic> userData = {
        'nickname': nickname,
      };

      if (email != null && email.isNotEmpty) {
        userData['email'] = email;
      }

      if (avatarUrl != null) {
        userData['avatar'] = avatarUrl;
      }

      // 调用更新API
      await _userService.updateUserProfile(
        nickname: nickname,
        email: email,
        preferences: preferences,
      );

      // 刷新用户数据
      final _ = await _ref.refresh(userProvider.future);

      debugPrint('个人资料更新成功');
      state = const AsyncValue.data(null);
      return true;
    } catch (e) {
      debugPrint('个人资料更新失败: $e');
      state = AsyncValue.error(e, StackTrace.current);
      return false;
    }
  }

  /// 重置状态
  void reset() {
    state = const AsyncValue.data(null);
  }
}
