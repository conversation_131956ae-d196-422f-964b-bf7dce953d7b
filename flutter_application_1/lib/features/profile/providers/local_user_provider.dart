import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../../../core/services/local_user_service.dart';

/// 本地用户服务提供者
final localUserServiceProvider = Provider<LocalUserService>((ref) {
  return LocalUserService();
});

/// 本地用户昵称提供者
final localNicknameProvider = StateNotifierProvider<LocalNicknameNotifier, AsyncValue<String>>((ref) {
  return LocalNicknameNotifier(ref.watch(localUserServiceProvider));
});

/// 本地用户昵称状态管理
class LocalNicknameNotifier extends StateNotifier<AsyncValue<String>> {
  final LocalUserService _localUserService;

  LocalNicknameNotifier(this._localUserService) : super(const AsyncValue.loading()) {
    _loadNickname();
  }

  /// 加载本地昵称
  Future<void> _loadNickname() async {
    try {
      final nickname = await _localUserService.getLocalNickname();
      state = AsyncValue.data(nickname);
    } catch (e) {
      debugPrint('加载本地昵称失败: $e');
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// 更新本地昵称
  Future<bool> updateNickname(String nickname) async {
    try {
      final success = await _localUserService.saveLocalNickname(nickname);
      if (success) {
        state = AsyncValue.data(nickname);
        debugPrint('本地昵称更新成功: $nickname');
      }
      return success;
    } catch (e) {
      debugPrint('更新本地昵称失败: $e');
      state = AsyncValue.error(e, StackTrace.current);
      return false;
    }
  }

  /// 刷新昵称
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    await _loadNickname();
  }

  /// 获取当前昵称（同步方法）
  String? getCurrentNickname() {
    return state.value;
  }
}
