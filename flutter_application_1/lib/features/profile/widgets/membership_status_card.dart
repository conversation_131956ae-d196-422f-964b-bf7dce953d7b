import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';

/// 会员状态卡片
/// 显示用户当前的会员状态
class MembershipStatusCard extends StatelessWidget {
  final AsyncValue<bool> isPremiumUser;

  const MembershipStatusCard({
    super.key,
    required this.isPremiumUser,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const Icon(
              Icons.star,
              size: 48,
              color: Colors.amber,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '会员状态',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  isPremiumUser.when(
                    data: (isPremium) => Text(
                      isPremium ? '您已是会员用户' : '您当前是免费用户',
                      style: TextStyle(
                        fontSize: 16,
                        color: isPremium ? AppColors.success : Colors.grey,
                      ),
                    ),
                    loading: () => const Text('正在检查会员状态...'),
                    error: (_, __) => const Text('会员状态检查失败'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
