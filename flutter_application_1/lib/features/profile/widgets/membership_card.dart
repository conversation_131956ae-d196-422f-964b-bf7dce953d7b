import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../shared/theme/constants.dart';

/// 会员卡片
/// 显示用户的会员状态、权益和到期时间
class MembershipCard extends StatelessWidget {
  final String membershipType;
  final DateTime? expiryDate;

  const MembershipCard({
    super.key,
    required this.membershipType,
    this.expiryDate,
  });

  @override
  Widget build(BuildContext context) {
    // 根据会员类型设置卡片样式
    Color cardColor;
    Color textColor;
    Color iconColor;
    String membershipTitle;
    List<String> benefits;

    switch (membershipType) {
      case 'premium':
        cardColor = AppColors.primaryLight100;
        textColor = AppColors.primary;
        iconColor = AppColors.primary;
        membershipTitle = '高级会员';
        benefits = ['无限专注时长', '高级音频解锁', '数据分析', '云端备份'];
        break;
      case 'pro':
        cardColor = AppColors.infoLight100;
        textColor = AppColors.info;
        iconColor = AppColors.info;
        membershipTitle = '专业版';
        benefits = ['无限专注时长', '全部音频解锁', '高级数据分析', '云端备份', '自定义主题'];
        break;
      default:
        cardColor = Colors.white;
        textColor = AppColors.textSecondary;
        iconColor = AppColors.textSecondary;
        membershipTitle = '免费用户';
        benefits = ['基础专注功能', '每日30分钟专注', '基础音频'];
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        decoration: BoxDecoration(
          color: cardColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white, width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13), // 约等于0.05透明度
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            // 会员信息
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // 会员图标
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(26), // 约等于0.1透明度
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Icon(
                      _getMembershipIcon(),
                      color: iconColor,
                      size: 24,
                    ),
                  ),

                  const SizedBox(width: 16),

                  // 会员标题和到期时间
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          membershipTitle,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: textColor,
                          ),
                        ),
                        if (expiryDate != null && membershipType != 'free')
                          Text(
                            '有效期至: ${DateFormat('yyyy年MM月dd日').format(expiryDate!)}',
                            style: TextStyle(
                              fontSize: 12,
                              color: textColor.withAlpha(179), // 约等于0.7透明度
                            ),
                          ),
                      ],
                    ),
                  ),

                  // 升级按钮
                  if (membershipType == 'free')
                    ElevatedButton(
                      onPressed: () {
                        // 导航到会员购买页面
                        Navigator.of(context).pushNamed('/profile/membership');
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                      child: const Text('立即升级'),
                    ),
                ],
              ),
            ),

            // 会员权益
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(128), // 约等于0.5透明度
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '会员权益',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: benefits.map((benefit) {
                      return Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: textColor.withAlpha(77), // 约等于0.3透明度
                            width: 1,
                          ),
                        ),
                        child: Text(
                          benefit,
                          style: TextStyle(
                            fontSize: 12,
                            color: textColor,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取会员图标
  IconData _getMembershipIcon() {
    switch (membershipType) {
      case 'premium':
        return Icons.star;
      case 'pro':
        return Icons.workspace_premium;
      default:
        return Icons.person;
    }
  }
}
