import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/providers/subscription_provider.dart';

/// Apple会员状态卡片
/// 显示用户当前的Apple订阅状态
class AppleMembershipStatusCard extends ConsumerWidget {
  const AppleMembershipStatusCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final subscriptionStatus = ref.watch(appleSubscriptionStatusProvider);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: subscriptionStatus.when(
          data: (isPremium) => Row(
            children: [
              Icon(
                isPremium ? Icons.star : Icons.star_border,
                size: 48,
                color: isPremium ? AppColors.primary : Colors.grey,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '会员状态',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      isPremium ? '您已是高级用户' : '您当前是免费用户',
                      style: TextStyle(
                        fontSize: 16,
                        color: isPremium ? AppColors.primary : Colors.grey,
                      ),
                    ),
                    if (isPremium) ...[
                      const SizedBox(height: 4),
                      const Text(
                        '感谢您的支持！享受所有高级功能',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (!isPremium)
                ElevatedButton(
                  onPressed: () {
                    Navigator.pushNamed(context, '/premiumSubscription');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                  child: const Text('升级'),
                ),
            ],
          ),
          loading: () => const Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在检查会员状态...'),
            ],
          ),
          error: (error, _) => Row(
            children: [
              const Icon(Icons.error, color: Colors.red),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '会员状态检查失败: $error',
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
