import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import '../models/settings_item.dart';

/// 设置选项分组
/// 用于显示一组相关的设置选项
class SettingsSection extends StatelessWidget {
  /// 设置组标题
  final String title;

  /// 设置项列表
  final List<SettingItem> items;

  /// 是否显示分割线
  final bool showDividers;

  /// 构造函数
  const SettingsSection({
    super.key,
    required this.title,
    required this.items,
    this.showDividers = true,
  });

  /// 从旧格式的Map列表创建（向后兼容）
  factory SettingsSection.fromMaps({
    required String title,
    required List<Map<String, dynamic>> items,
    bool showDividers = true,
  }) {
    return SettingsSection(
      title: title,
      showDividers: showDividers,
      items: items.map((item) => SettingItem(
        icon: item['icon'] as IconData,
        title: item['title'] as String,
        subtitle: item['subtitle'] as String?,
        route: item['route'] as String?,
        onTap: item['onTap'] as Function()?,
        isEnabled: item['isEnabled'] as bool? ?? true,
        trailing: item['trailing'] as Widget?,
      )).toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 分组标题
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 8),
          child: Text(
            title,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),

        // 设置选项列表
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.white, width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13), // 约等于0.05透明度
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ListView.separated(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: items.length,
            separatorBuilder: (context, index) => showDividers
              ? const Divider(
                  height: 1,
                  indent: 56,
                  endIndent: 16,
                )
              : const SizedBox.shrink(),
            itemBuilder: (context, index) {
              final item = items[index];
              return _buildSettingItem(context, item);
            },
          ),
        ),
      ],
    );
  }

  /// 构建单个设置选项
  Widget _buildSettingItem(BuildContext context, SettingItem item) {
    return InkWell(
      onTap: !item.isEnabled
        ? null
        : (item.onTap ?? (item.route != null
            ? () => Navigator.of(context).pushNamed(item.route!)
            : null)),
      borderRadius: BorderRadius.circular(16),
      child: Opacity(
        opacity: item.isEnabled ? 1.0 : 0.5,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              // 图标
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: AppColors.primary.withAlpha(26), // 约等于0.1透明度
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  item.icon,
                  size: 20,
                  color: AppColors.primary,
                ),
              ),

              const SizedBox(width: 16),

              // 标题和副标题
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.title,
                      style: AppTextStyles.bodyMedium,
                    ),
                    if (item.subtitle != null) ...[
                      const SizedBox(height: 2),
                      Text(
                        item.subtitle!,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              // 尾部组件或箭头图标
              item.trailing ?? const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppColors.textHint,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
