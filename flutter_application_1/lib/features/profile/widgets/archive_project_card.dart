import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/models/subject_project.dart';

/// 归档项目卡片
/// 用于在归档页面中显示已归档的项目
class ArchiveProjectCard extends StatelessWidget {
  final Project project;
  final Subject? subject;
  final VoidCallback onUnarchive;
  final VoidCallback onDelete;

  const ArchiveProjectCard({
    super.key,
    required this.project,
    this.subject,
    required this.onUnarchive,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
      decoration: AppDecorations.cardNoBorder(
        borderRadius: AppSizes.radiusLarge,
        boxShadow: [AppShadows.low],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 项目头部
          Container(
            padding: const EdgeInsets.all(AppSizes.paddingMedium),
            decoration: BoxDecoration(
              color: AppColors.cardBackground.withAlpha(AppColors.alpha10),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppSizes.radiusLarge),
                topRight: Radius.circular(AppSizes.radiusLarge),
              ),
            ),
            child: Row(
              children: [
                // 科目颜色指示器
                if (subject != null)
                  Container(
                    width: 4,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Color(subject!.color),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                if (subject != null) const SizedBox(width: AppSizes.paddingSmall),

                // 项目标题
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        project.name,
                        style: AppTextStyles.headline3,
                      ),
                      if (subject != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(
                            subject!.name,
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                // 归档标签
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSizes.paddingSmall,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.textTertiary.withAlpha(AppColors.alpha20),
                    borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                  ),
                  child: Text(
                    '已归档',
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textTertiary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 项目详情
          Padding(
            padding: const EdgeInsets.all(AppSizes.paddingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 项目日期
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today_outlined,
                      size: 16,
                      color: AppColors.textTertiary,
                    ),
                    const SizedBox(width: AppSizes.paddingSmall),
                    Text(
                      '${_formatDate(project.startDate)} - ${_formatDate(project.endDate)}',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),

                // 项目描述
                if (project.description != null && project.description!.isNotEmpty) ...[
                  const SizedBox(height: AppSizes.paddingSmall),
                  Text(
                    project.description!,
                    style: AppTextStyles.bodyMedium,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],

                // 项目进度
                if (project.isTrackingEnabled) ...[
                  const SizedBox(height: AppSizes.paddingSmall),
                  Row(
                    children: [
                      Icon(
                        Icons.trending_up_outlined,
                        size: 16,
                        color: AppColors.textTertiary,
                      ),
                      const SizedBox(width: AppSizes.paddingSmall),
                      Text(
                        '进度 ${(project.progress * 100).toStringAsFixed(1)}%',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                    child: LinearProgressIndicator(
                      value: project.progress,
                      backgroundColor: AppColors.textTertiary.withAlpha(AppColors.alpha20),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        subject != null
                            ? Color(subject!.color).withAlpha(200)
                            : AppColors.primary.withAlpha(200),
                      ),
                      minHeight: 6,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // 操作按钮
          Container(
            padding: const EdgeInsets.fromLTRB(
              AppSizes.paddingMedium,
              0,
              AppSizes.paddingMedium,
              AppSizes.paddingMedium,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // 取消归档按钮
                _buildActionButton(
                  onPressed: onUnarchive,
                  icon: Icons.unarchive_outlined,
                  label: '取消归档',
                  isPrimary: true,
                ),

                const SizedBox(width: AppSizes.paddingSmall),

                // 删除按钮
                _buildActionButton(
                  onPressed: onDelete,
                  icon: Icons.delete_outline,
                  label: '删除',
                  isPrimary: false,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required VoidCallback onPressed,
    required IconData icon,
    required String label,
    required bool isPrimary,
  }) {
    return Container(
      height: 36,
      decoration: BoxDecoration(
        color: isPrimary
            ? AppColors.primary.withAlpha(AppColors.alpha10)
            : AppColors.textTertiary.withAlpha(AppColors.alpha10),
        borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
        border: Border.all(
          color: isPrimary
              ? AppColors.primary.withAlpha(AppColors.alpha30)
              : AppColors.textTertiary.withAlpha(AppColors.alpha30),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppSizes.paddingMedium,
              vertical: AppSizes.paddingSmall,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: 16,
                  color: isPrimary
                      ? AppColors.primary
                      : AppColors.textTertiary,
                ),
                const SizedBox(width: 6),
                Text(
                  label,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: isPrimary
                        ? AppColors.primary
                        : AppColors.textTertiary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
