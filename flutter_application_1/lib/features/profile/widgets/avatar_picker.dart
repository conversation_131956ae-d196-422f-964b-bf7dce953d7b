import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:io';
import '../../../shared/theme/constants.dart';

/// 头像选择器组件
class AvatarPicker extends ConsumerWidget {
  final String? currentAvatarUrl;
  final File? avatarFile;
  final VoidCallback onPickAvatar;
  final bool isUploading;

  const AvatarPicker({
    super.key,
    this.currentAvatarUrl,
    this.avatarFile,
    required this.onPickAvatar,
    this.isUploading = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 获取预设头像URL
    // final presetAvatarUrl = ref.watch(presetAvatarProvider);
    // 构建头像选择器内容，暂时移除预设头像逻辑

    return Column(
      children: [
        const Text(
          '点击更换头像',
          style: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 12),
        GestureDetector(
          onTap: onPickAvatar,
          child: Stack(
            children: [
              // 头像
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: AppColors.primary.withAlpha(AppColors.alpha10),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppColors.white,
                    width: 3,
                  ),
                  boxShadow: [AppShadows.medium],
                ),
                // child: _buildAvatarContent(presetAvatarUrl),
                child: const Icon(  // 直接显示占位图标
                Icons.person,
                size: 50,
                color: AppColors.primary,
              ),
              ),

              // 编辑图标
              Positioned(
                right: 0,
                bottom: 0,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    shape: BoxShape.circle,
                    boxShadow: [AppShadows.low],
                  ),
                  child: const Icon(
                    Icons.camera_alt,
                    size: 20,
                    color: Colors.white,
                  ),
                ),
              ),

              // 上传中指示器
              if (isUploading)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withAlpha(200),
                      shape: BoxShape.circle,
                    ),
                    child: const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextButton.icon(
              onPressed: onPickAvatar,
              icon: const Icon(Icons.photo_library, size: 18),
              label: const Text('从相册选择'),
            ),
            const SizedBox(width: 8),
            TextButton.icon(
              onPressed: () {
                // 显示对话框，提示用户可以手动上传头像
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('提示'),
                    content: const Text('如果无法访问相册，您可以尝试重启应用或在设置中允许应用访问相册权限。'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('知道了'),
                      ),
                    ],
                  ),
                );
              },
              icon: const Icon(Icons.help_outline, size: 18),
              label: const Text('帮助'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAvatarContent(String? presetAvatarUrl) {
    // 如果有选择的新头像文件，显示文件
    if (avatarFile != null) {
      return ClipOval(
        child: Image.file(
          avatarFile!,
          width: 120,
          height: 120,
          fit: BoxFit.cover,
        ),
      );
    }

    // 如果有预设头像URL，优先显示预设头像
    if (presetAvatarUrl != null) {
      return ClipOval(
        child: Image.network(
          presetAvatarUrl,
          width: 120,
          height: 120,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return const Icon(
              Icons.person,
              size: 50,
              color: AppColors.primary,
            );
          },
        ),
      );
    }

    // 如果有现有头像URL，显示网络图片
    if (currentAvatarUrl != null && currentAvatarUrl!.isNotEmpty) {
      return ClipOval(
        child: Image.network(
          currentAvatarUrl!,
          width: 120,
          height: 120,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return const Icon(
              Icons.person,
              size: 50,
              color: AppColors.primary,
            );
          },
        ),
      );
    }

    // 默认显示占位图标
    return const Icon(
      Icons.person,
      size: 50,
      color: AppColors.primary,
    );
  }
}
