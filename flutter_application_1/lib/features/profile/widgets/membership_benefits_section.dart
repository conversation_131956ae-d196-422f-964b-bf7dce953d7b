import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';

/// 会员权益部分
/// 显示会员可享受的各种权益
class MembershipBenefitsSection extends StatelessWidget {
  const MembershipBenefitsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final benefits = [
      {'icon': Icons.cloud_upload, 'title': '云端备份', 'description': '自动备份您的数据到云端'},
      {'icon': Icons.devices, 'title': '多端同步', 'description': '在多个设备上同步您的数据'},
      {'icon': Icons.analytics, 'title': '高级统计', 'description': '获取更详细的学习数据分析'},
      {'icon': Icons.support_agent, 'title': '优先支持', 'description': '获得优先的客户支持服务'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '会员权益',
          style: AppTextStyles.headline2,
        ),
        const SizedBox(height: 16),
        ...benefits.map((benefit) => _buildBenefitItem(benefit)),
      ],
    );
  }

  Widget _buildBenefitItem(Map<String, dynamic> benefit) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            benefit['icon'] as IconData,
            color: AppColors.primary,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  benefit['title'] as String,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  benefit['description'] as String,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
