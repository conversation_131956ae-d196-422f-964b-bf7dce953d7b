import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/user.dart';
import '../../../core/providers/subscription_provider.dart';
import '../../../core/providers/auth_provider.dart';
import '../../../shared/theme/constants.dart';

/// 个人中心页面头部
/// 显示用户头像、用户名等基本信息
class ProfileHeader extends ConsumerWidget {
  final User user;
  final VoidCallback? onEditProfile;

  const ProfileHeader({
    super.key,
    required this.user,
    this.onEditProfile,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听订阅状态
    final subscriptionStatusAsync = ref.watch(appleSubscriptionStatusProvider);

    // 监听最新的用户数据，如果获取失败则使用传入的user参数作为fallback
    final currentUserAsync = ref.watch(currentUserProvider);
    final displayUser = currentUserAsync.value ?? user;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      decoration: BoxDecoration(
        color: AppColors.white.withAlpha(AppColors.alpha90),
        borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
        boxShadow: [AppShadows.medium],
      ),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 昵称、订阅状态和编辑按钮
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 昵称
                    Text(
                      displayUser.nickname,
                      style: AppTextStyles.headline3.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    // 订阅状态
                    subscriptionStatusAsync.when(
                      data: (isPremium) {
                        if (isPremium) {
                          return Container(
                            margin: const EdgeInsets.only(top: 4),
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: AppColors.primary.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: AppColors.primary.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              'PRO用户',
                              style: AppTextStyles.bodySmall.copyWith(
                                color: AppColors.primary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          );
                        }
                        return const SizedBox.shrink(); // 未订阅时不显示
                      },
                      loading: () => const SizedBox.shrink(), // 加载时不显示
                      error: (_, __) => const SizedBox.shrink(), // 错误时不显示
                    ),
                  ],
                ),
              ),
              if (onEditProfile != null)
                GestureDetector(
                  onTap: onEditProfile,
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Icon(
                      Icons.edit,
                      size: 16,
                      color: Colors.grey,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 4),
          // 登录方式显示
          Row(
            children: [
              Icon(
                _isAppleLogin(displayUser) ? Icons.apple : Icons.email,
                size: 14,
                color: AppColors.textSecondary,
              ),
              const SizedBox(width: 4),
              Text(
                _getLoginMethodDisplay(displayUser),
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 判断是否是Apple登录
  bool _isAppleLogin(User user) {
    return user.authMethods.contains('apple');
  }

  /// 获取登录方式显示文本
  String _getLoginMethodDisplay(User user) {
    if (_isAppleLogin(user)) {
      return 'Apple登录';
    } else {
      return user.email;
    }
  }
}
