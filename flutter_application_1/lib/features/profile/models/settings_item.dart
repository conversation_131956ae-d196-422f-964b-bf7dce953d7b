import 'package:flutter/material.dart';

/// 设置组
/// 包含一组相关的设置项
class SettingsGroup {
  /// 设置组标题
  final String title;
  
  /// 设置项列表
  final List<SettingItem> items;
  
  /// 构造函数
  SettingsGroup({
    required this.title,
    required this.items,
  });
}

/// 设置项
/// 表示一个设置选项
class SettingItem {
  /// 设置项图标
  final IconData icon;
  
  /// 设置项标题
  final String title;
  
  /// 设置项描述（可选）
  final String? subtitle;
  
  /// 导航路由（可选，与onTap互斥）
  final String? route;
  
  /// 点击回调（可选，与route互斥）
  final Function()? onTap;
  
  /// 是否启用
  final bool isEnabled;
  
  /// 尾部组件（可选）
  final Widget? trailing;
  
  /// 构造函数
  SettingItem({
    required this.icon,
    required this.title,
    this.subtitle,
    this.route,
    this.onTap,
    this.isEnabled = true,
    this.trailing,
  }) : assert(route != null || onTap != null, '必须提供route或onTap之一');
}
