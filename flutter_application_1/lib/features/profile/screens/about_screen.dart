import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../../../shared/theme/constants.dart';

/// 关于我们页面
class AboutScreen extends StatefulWidget {
  const AboutScreen({super.key});

  @override
  State<AboutScreen> createState() => _AboutScreenState();
}

class _AboutScreenState extends State<AboutScreen> {
  String _appVersion = '1.0.0';
  String _appName = '专注';
  
  @override
  void initState() {
    super.initState();
    _loadAppInfo();
  }
  
  Future<void> _loadAppInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _appName = packageInfo.appName;
        _appVersion = '${packageInfo.version}+${packageInfo.buildNumber}';
      });
    } catch (e) {
      // 如果无法获取版本信息，保持默认值
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('关于我们'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.pageBackground,
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppSizes.paddingLarge),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 应用图标
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: AppColors.primary.withAlpha(AppColors.alpha10),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.timer,
                    size: 60,
                    color: AppColors.primary,
                  ),
                ),
                
                const SizedBox(height: AppSizes.paddingMedium),
                
                // 应用名称
                Text(
                  _appName,
                  style: AppTextStyles.headline1,
                ),
                
                const SizedBox(height: AppSizes.paddingSmall),
                
                // 版本号
                Text(
                  '版本 $_appVersion',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                
                const SizedBox(height: AppSizes.paddingLarge),
                
                // 应用描述
                const Text(
                  '专注是一款帮助您提高工作和学习效率的应用。通过科学的时间管理方法，让您更专注于当下的任务，提高工作效率和生活质量。',
                  style: AppTextStyles.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: AppSizes.paddingXLarge),
                
                // 联系我们
                const Text(
                  '联系我们',
                  style: AppTextStyles.headline3,
                ),
                
                const SizedBox(height: AppSizes.paddingMedium),
                
                // 联系方式
                _buildContactItem(Icons.email, '邮箱', '<EMAIL>'),
                _buildContactItem(Icons.web, '网站', 'www.example.com'),
                
                const SizedBox(height: AppSizes.paddingXLarge),
                
                // 版权信息
                Text(
                  '© ${DateTime.now().year} 专注团队 保留所有权利',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textHint,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildContactItem(IconData icon, String title, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 20,
            color: AppColors.primary,
          ),
          const SizedBox(width: 8),
          Text(
            '$title: $value',
            style: AppTextStyles.bodyMedium,
          ),
        ],
      ),
    );
  }
}
