import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/models/user.dart';
import '../../../core/providers/auth_provider.dart';
// import '../../../core/services/user_service.dart'; // 暂时隐藏用户服务

// import '../widgets/profile_header.dart'; // 暂时隐藏个人信息头部
import '../widgets/settings_section.dart';
// import '../widgets/nickname_edit_dialog.dart'; // 暂时隐藏昵称编辑功能
// import '../providers/local_user_provider.dart'; // 暂时隐藏本地用户数据
import '../models/settings_item.dart';
import '../../../utils/url_launcher_helper.dart';
import '../../../core/config/release_config.dart';
// import '../../../core/providers/subscription_provider.dart'; // 暂时隐藏订阅状态
import './archive_screen.dart';
import '../../subscription/screens/premium_subscription_screen.dart';

/// 获取应用版本信息
Future<String> _getAppVersion() async {
  try {
    final packageInfo = await PackageInfo.fromPlatform();
    return '${packageInfo.version}+${packageInfo.buildNumber}';
  } catch (e) {
    return '1.0.0';
  }
}

/// 个人中心页面
class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听用户数据 - 使用正确的用户提供者
    final userAsyncValue = ref.watch(currentUserProvider);
    // 监听认证状态
    final authStatus = ref.watch(authStatusProvider);

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.pageBackground,
        ),
        child: SafeArea(
          child: authStatus == AuthStatus.unauthenticated
              ? _buildUnauthenticatedContent(context, ref)
              : userAsyncValue.when(
                  data: (user) => user != null
                      ? _buildContent(context, ref, user)
                      : _buildUnauthenticatedContent(context, ref),
                  loading: () => const Center(child: CircularProgressIndicator()),
                  error: (error, stackTrace) => Center(
                    child: Text(
                      '加载失败: $error',
                      style: AppTextStyles.bodyMedium,
                    ),
                  ),
                ),
        ),
      ),
    );
  }

  // 昵称编辑功能暂时隐藏 - v1.0.1版本
  // /// 显示昵称编辑弹窗
  // Future<void> _showNicknameEditDialog(BuildContext context, WidgetRef ref) async {
  //   final localNicknameNotifier = ref.read(localNicknameProvider.notifier);
  //   final currentNickname = localNicknameNotifier.getCurrentNickname() ?? 'User1';

  //   await showDialog<bool>(
  //     context: context,
  //     builder: (context) => NicknameEditDialog(
  //       currentNickname: currentNickname,
  //       onSave: (nickname) async {
  //         await localNicknameNotifier.updateNickname(nickname);
  //       },
  //     ),
  //   );

  //   // 昵称更新成功后界面会自动刷新显示，无需额外提示
  // }

  // /// 显示登录后的昵称编辑弹窗
  // Future<void> _showUserNicknameEditDialog(BuildContext context, WidgetRef ref, User user) async {
  //   await showDialog<bool>(
  //     context: context,
  //     builder: (context) => NicknameEditDialog(
  //       currentNickname: user.nickname,
  //       onSave: (nickname) async {
  //         try {
  //           debugPrint('🔄 开始更新用户昵称');
  //           debugPrint('  - 新昵称: $nickname');

  //           final userService = UserService();
  //           await userService.updateUserProfile(
  //             nickname: nickname,
  //           );

  //           debugPrint('✅ 用户昵称更新成功');

  //           // 刷新用户数据 - UserService已经更新了本地缓存
  //           ref.invalidate(currentUserProvider);

  //           // 显示成功提示
  //           if (context.mounted) {
  //             ScaffoldMessenger.of(context).showSnackBar(
  //               const SnackBar(
  //                 content: Text('昵称更新成功'),
  //                 backgroundColor: Colors.green,
  //                 duration: Duration(seconds: 2),
  //               ),
  //             );
  //           }
  //         } catch (e) {
  //           debugPrint('❌ 用户昵称更新失败: $e');

  //           // 显示错误提示
  //           if (context.mounted) {
  //             ScaffoldMessenger.of(context).showSnackBar(
  //               SnackBar(
  //                 content: Text('昵称更新失败: $e'),
  //                 backgroundColor: Colors.red,
  //                 duration: const Duration(seconds: 3),
  //               ),
  //             );
  //           }
  //         }
  //       },
  //     ),
  //   );
  // }

  // 登录注册相关功能暂时隐藏 - v1.0.1版本
  // /// 构建内容区域
  // // 显示登出确认对话框
  // void _showLogoutConfirmDialog(BuildContext context, WidgetRef ref) {
  //   showDialog(
  //     context: context,
  //     builder: (context) => AlertDialog(
  //       title: const Text('退出登录'),
  //       content: const Text('确定要退出登录吗？'),
  //       actions: [
  //         TextButton(
  //           onPressed: () => Navigator.pop(context),
  //           child: const Text('取消'),
  //         ),
  //         TextButton(
  //           onPressed: () {
  //             Navigator.pop(context);
  //             ref.read(authStatusProvider.notifier).logout();
  //           },
  //           child: const Text('确定'),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  // // 显示注销账号确认对话框
  // void _showDeleteAccountConfirmDialog(BuildContext context, WidgetRef ref) {
  //   showDialog(
  //     context: context,
  //     builder: (context) => _DeleteAccountDialog(
  //       onConfirm: (password, confirmText) async {
  //         await _handleDeleteAccount(context, ref, password, confirmText);
  //       },
  //     ),
  //   );
  // }

  // 测试Apple登录API连接
  Future<void> _testAppleLoginAPI(BuildContext context, WidgetRef ref) async {
    // 保存Navigator状态
    final navigator = Navigator.of(context);

    try {
      // 显示加载指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在测试API连接...'),
            ],
          ),
        ),
      );

      // 调用测试方法
      final authService = ref.read(authServiceProvider);
      final result = await authService.testAppleLoginAPI();

      // 关闭加载指示器
      navigator.pop();

      // 显示测试结果
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('API连接测试结果'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildTestResultItem('基础连接', result['baseConnection'] ?? false),
                  if (result['healthCheck'] != null)
                    Text('健康检查状态码: ${result['healthCheck']}'),
                  const SizedBox(height: 8),
                  _buildTestResultItem('Apple登录端点', result['appleLoginEndpoint'] ?? false),
                  if (result['appleLoginNote'] != null)
                    Text('注意: ${result['appleLoginNote']}'),
                  if (result['networkLatency'] != null && result['networkLatency'] != -1)
                    Text('网络延迟: ${result['networkLatency']}ms'),
                  if (result['error'] != null)
                    Text('错误: ${result['error']}', style: const TextStyle(color: Colors.red)),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('确定'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // 关闭加载指示器
      navigator.pop();

      // 显示错误信息
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('测试失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // 清理验证码缓存
  Future<void> _clearVerificationCodeCache(BuildContext context, WidgetRef ref) async {
    // 保存Navigator状态
    final navigator = Navigator.of(context);

    try {
      // 显示确认对话框
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('清理验证码缓存'),
          content: const Text('这将清理所有认证相关的缓存数据，可能需要重新登录。确定要继续吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('确定'),
            ),
          ],
        ),
      );

      if (confirmed != true) return;

      // 显示加载指示器
      if (context.mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('正在清理缓存...'),
              ],
            ),
          ),
        );
      }

      // 执行清理
      final authService = ref.read(authServiceProvider);
      await authService.clearVerificationCodeCache();

      // 关闭加载指示器
      navigator.pop();

      // 显示成功提示
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('验证码缓存清理完成'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // 关闭可能的加载指示器
      navigator.pop();

      // 显示错误提示
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('清理缓存失败: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // 模拟Apple登录测试
  Future<void> _simulateAppleLogin(BuildContext context, WidgetRef ref) async {
    // 保存Navigator状态
    final navigator = Navigator.of(context);

    try {
      // 显示加载指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在模拟Apple登录...'),
            ],
          ),
        ),
      );

      // 调用模拟测试方法
      final authService = ref.read(authServiceProvider);
      await authService.simulateAppleLoginTest();

      // 关闭加载指示器
      navigator.pop();

      // 显示成功信息
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('模拟Apple登录测试完成，请查看控制台日志'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      // 关闭加载指示器
      navigator.pop();

      // 显示错误信息
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('模拟测试失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // 构建测试结果项
  Widget _buildTestResultItem(String title, bool success) {
    return Row(
      children: [
        Icon(
          success ? Icons.check_circle : Icons.error,
          color: success ? Colors.green : Colors.red,
          size: 16,
        ),
        const SizedBox(width: 8),
        Text('$title: ${success ? '成功' : '失败'}'),
      ],
    );
  }

  // 处理注销账号
  Future<void> _handleDeleteAccount(
    BuildContext context,
    WidgetRef ref,
    String password,
    String confirmText,
  ) async {
    // 保存当前的Navigator状态，用于确保能正确关闭弹窗
    final navigator = Navigator.of(context);

    try {
      // 显示加载指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => PopScope(
          canPop: false, // 防止用户手动关闭
          child: const AlertDialog(
            content: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('正在注销账号...'),
              ],
            ),
          ),
        ),
      );

      // 执行注销
      final success = await ref.read(authStatusProvider.notifier).deleteAccount(
        password: password,
        confirmText: confirmText,
      );

      // 立即关闭加载指示器，使用保存的navigator
      try {
        navigator.pop();
      } catch (e) {
        debugPrint('关闭加载弹窗失败: $e');
      }

      if (success) {
        // 注销成功，显示成功消息
        debugPrint('账号注销成功，显示成功提示');

        // 使用短暂延迟确保UI状态更新
        await Future.delayed(const Duration(milliseconds: 100));

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('账号注销成功'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }
      } else {
        // 注销失败
        debugPrint('账号注销失败');
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('账号注销失败，请稍后重试'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // 关闭加载指示器，使用保存的navigator
      try {
        navigator.pop();
      } catch (popError) {
        debugPrint('关闭加载弹窗失败: $popError');
      }

      // 分析错误类型并提供相应的用户反馈
      String errorMessage = '注销账号失败，请稍后重试';
      final errorString = e.toString();

      if (errorString.contains('connection timeout') || errorString.contains('连接超时')) {
        errorMessage = '请求超时，注销账号需要较长时间处理，请稍后重试';
      } else if (errorString.contains('receive timeout') || errorString.contains('接收超时')) {
        errorMessage = '服务器响应超时，请检查网络连接后重试';
      } else if (errorString.contains('password') && errorString.contains('required')) {
        errorMessage = '密码字段缺失，请重新输入密码';
      } else if (errorString.contains('密码验证失败') || errorString.contains('密码不正确')) {
        errorMessage = '密码验证失败，请检查密码是否正确';
      } else if (errorString.contains('确认文本') && errorString.contains('不正确')) {
        errorMessage = '确认文本错误，请输入"确认注销"';
      } else if (errorString.contains('验证错误')) {
        errorMessage = '输入信息验证失败，请检查密码和确认文本';
      } else if (errorString.contains('401')) {
        errorMessage = '身份验证失败，请重新登录后再试';
      } else if (errorString.contains('403')) {
        errorMessage = '权限不足，无法执行此操作';
      } else if (errorString.contains('400')) {
        errorMessage = '请求参数错误，请检查输入信息';
      } else if (errorString.contains('网络')) {
        errorMessage = '网络连接失败，请检查网络后重试';
      }

      // 显示错误信息
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  // 构建未登录状态的内容
  Widget _buildUnauthenticatedContent(BuildContext context, WidgetRef ref) {
    return FutureBuilder<String>(
      future: _getAppVersion(),
      initialData: '1.0.0',
      builder: (context, snapshot) {
        final appVersion = snapshot.data ?? '1.0.0';
        // final localNicknameAsync = ref.watch(localNicknameProvider); // 暂时隐藏本地昵称功能

        return LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: constraints.maxHeight,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(AppSizes.paddingMedium),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                // 标题 - 参考其他页面的标题样式
                const Padding(
                  padding: EdgeInsets.fromLTRB(0, 16, 16, 8),
                  child: Text(
                    '个人中心',
                    style: AppTextStyles.headline2,
                  ),
                ),

                // 个人信息板块和账户管理板块已在v1.0.1版本中暂时隐藏
                // 本地用户信息卡片 - 暂时隐藏
                // Container(
                //   padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                //   decoration: BoxDecoration(
                //     color: AppColors.white.withAlpha(AppColors.alpha90),
                //     borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
                //     boxShadow: [AppShadows.medium],
                //   ),
                //   margin: const EdgeInsets.symmetric(horizontal: 0),
                //   child: Row(
                //     children: [
                //       Expanded(
                //         child: Column(
                //           crossAxisAlignment: CrossAxisAlignment.start,
                //           children: [
                //             // 昵称
                //             localNicknameAsync.when(
                //               data: (nickname) => Text(
                //                 nickname,
                //                 style: AppTextStyles.headline3.copyWith(
                //                   fontWeight: FontWeight.bold,
                //                 ),
                //                 maxLines: 1,
                //                 overflow: TextOverflow.ellipsis,
                //               ),
                //               loading: () => const Text(
                //                 '加载中...',
                //                 style: AppTextStyles.headline3,
                //               ),
                //               error: (_, __) => const Text(
                //                 'User',
                //                 style: AppTextStyles.headline3,
                //               ),
                //             ),
                //             // 订阅状态（未登录状态下也显示）
                //             Consumer(
                //               builder: (context, ref, child) {
                //                 final subscriptionStatusAsync = ref.watch(appleSubscriptionStatusProvider);
                //                 return subscriptionStatusAsync.when(
                //                   data: (isPremium) {
                //                     if (isPremium) {
                //                       return Container(
                //                         margin: const EdgeInsets.only(top: 4),
                //                         padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                //                         decoration: BoxDecoration(
                //                           color: AppColors.primary.withValues(alpha: 0.1),
                //                           borderRadius: BorderRadius.circular(12),
                //                           border: Border.all(
                //                             color: AppColors.primary.withValues(alpha: 0.3),
                //                             width: 1,
                //                           ),
                //                         ),
                //                         child: Text(
                //                           'PRO用户',
                //                           style: AppTextStyles.bodySmall.copyWith(
                //                             color: AppColors.primary,
                //                             fontWeight: FontWeight.w600,
                //                           ),
                //                         ),
                //                       );
                //                     }
                //                     return const SizedBox.shrink(); // 未订阅时不显示
                //                   },
                //                   loading: () => const SizedBox.shrink(), // 加载时不显示
                //                   error: (_, __) => const SizedBox.shrink(), // 错误时不显示
                //                 );
                //               },
                //             ),
                //           ],
                //         ),
                //       ),
                //       GestureDetector(
                //         onTap: () => _showNicknameEditDialog(context, ref),
                //         child: Container(
                //           padding: const EdgeInsets.all(6),
                //           decoration: BoxDecoration(
                //             color: Colors.grey.shade200,
                //             borderRadius: BorderRadius.circular(4),
                //           ),
                //           child: const Icon(
                //             Icons.edit,
                //             size: 16,
                //             color: Colors.grey,
                //           ),
                //         ),
                //       ),
                //     ],
                //   ),
                // ),

                // const SizedBox(height: AppSizes.paddingMedium),

                // 账户管理 - 暂时隐藏
                // SettingsSection(
                //   title: '账户管理',
                //   items: [
                //     SettingItem(
                //       icon: Icons.login,
                //       title: '登录注册',
                //       onTap: () {
                //         Navigator.pushNamed(context, '/auth/login');
                //       },
                //     ),
                //   ],
                // ),

                // const SizedBox(height: AppSizes.paddingMedium),

                // 设置选项
                SettingsSection(
                  title: '会员订阅',
                  items: [
                    SettingItem(
                      icon: Icons.star,
                      title: '解锁PRO',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const PremiumSubscriptionScreen(),
                          ),
                        );
                      },
                    ),
                  ],
                ),

                const SizedBox(height: AppSizes.paddingMedium),

                // 数据管理
                SettingsSection(
                  title: '应用设置',
                  items: [
                    SettingItem(
                      icon: Icons.archive,
                      title: '归档管理',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ArchiveScreen(),
                          ),
                        );
                      },
                    ),
                  ],
                ),

                const SizedBox(height: AppSizes.paddingMedium),

                // 其他
                SettingsSection(
                  title: '其他',
                  items: [
                    // 暂时隐藏"关于我们"界面 - MVP阶段
                    // SettingItem(
                    //   icon: Icons.info,
                    //   title: '关于我们',
                    //   route: '/about',
                    // ),
                    SettingItem(
                      icon: Icons.help,
                      title: '帮助与反馈',
                      route: '/help',
                    ),
                    SettingItem(
                      icon: Icons.description,
                      title: '用户协议',
                      onTap: () {
                        UrlLauncherHelper.launchTermsWithErrorHandling(context);
                      },
                    ),
                    SettingItem(
                      icon: Icons.privacy_tip,
                      title: '隐私政策',
                      onTap: () {
                        UrlLauncherHelper.launchPrivacyWithErrorHandling(context);
                      },
                    ),
                    if (ReleaseConfig.showDeveloperTools)
                      SettingItem(
                        icon: Icons.developer_mode,
                        title: '开发者工具',
                        route: '/dev/tools',
                      ),
                  ],
                ),

                const SizedBox(height: AppSizes.paddingLarge),

                // 版本信息
                Center(
                  child: Text(
                    '版本 $appVersion',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textHint,
                    ),
                  ),
                ),

                const SizedBox(height: AppSizes.paddingXLarge),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildContent(BuildContext context, WidgetRef ref, User user) {
    return FutureBuilder<String>(
      future: _getAppVersion(),
      initialData: '1.0.0',
      builder: (context, snapshot) {
        final appVersion = snapshot.data ?? '1.0.0';

        return RefreshIndicator(
          onRefresh: () async {
            // 实现下拉刷新逻辑
            return ref.refresh(currentUserProvider.future);
          },
          child: LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: constraints.maxHeight,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                // 页面标题
                const Padding(
                  padding: EdgeInsets.fromLTRB(16, 16, 16, 8),
                  child: Text(
                    '个人中心',
                    style: AppTextStyles.headline2,
                  ),
                ),

                // 用户信息头部 - 暂时隐藏
                // ProfileHeader(
                //   user: user,
                //   onEditProfile: () {
                //     // 显示昵称编辑弹窗
                //     _showUserNicknameEditDialog(context, ref, user);
                //   },
                // ),

                // const SizedBox(height: AppSizes.paddingMedium),

                // 设置选项
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 应用设置
                      SettingsSection(
                        title: '应用设置',
                        items: [
                          SettingItem(
                            icon: Icons.star,
                            title: '会员订阅',
                            route: '/profile/subscription',
                          ),
                        ],
                      ),

                      const SizedBox(height: AppSizes.paddingMedium),

                      // 数据管理
                      SettingsSection(
                        title: '数据管理',
                        items: [
                          SettingItem(
                            icon: Icons.archive,
                            title: '归档管理',
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const ArchiveScreen(),
                                ),
                              );
                            },
                          ),
                        ],
                      ),

                      const SizedBox(height: AppSizes.paddingMedium),

                      // 其他
                      SettingsSection(
                        title: '其他',
                        items: [
                          // 暂时隐藏"关于我们"界面 - MVP阶段
                          // SettingItem(
                          //   icon: Icons.info,
                          //   title: '关于我们',
                          //   route: '/about',
                          // ),
                          SettingItem(
                            icon: Icons.help,
                            title: '帮助与反馈',
                            route: '/help',
                          ),
                          SettingItem(
                            icon: Icons.description,
                            title: '用户协议',
                            onTap: () {
                              UrlLauncherHelper.launchTermsWithErrorHandling(context);
                            },
                          ),
                          SettingItem(
                            icon: Icons.privacy_tip,
                            title: '隐私政策',
                            onTap: () {
                              UrlLauncherHelper.launchPrivacyWithErrorHandling(context);
                            },
                          ),
                          if (ReleaseConfig.showDeveloperTools) ...[
                            SettingItem(
                              icon: Icons.developer_mode,
                              title: '开发者工具',
                              route: '/dev/tools',
                            ),
                            SettingItem(
                              icon: Icons.bug_report,
                              title: '测试Apple登录API',
                              onTap: () => _testAppleLoginAPI(context, ref),
                            ),
                            SettingItem(
                              icon: Icons.science,
                              title: '模拟Apple登录',
                              onTap: () => _simulateAppleLogin(context, ref),
                            ),
                            SettingItem(
                              icon: Icons.cleaning_services,
                              title: '清理验证码缓存',
                              onTap: () => _clearVerificationCodeCache(context, ref),
                            ),
                          ],
                          // 退出登录功能 - 暂时隐藏
                          // SettingItem(
                          //   icon: Icons.logout,
                          //   title: '退出登录',
                          //   onTap: () => _showLogoutConfirmDialog(context, ref),
                          // ),
                        ],
                      ),

                      const SizedBox(height: AppSizes.paddingLarge),

                      // 注销账号按钮 - 暂时隐藏
                      // Center(
                      //   child: TextButton(
                      //     onPressed: () => _showDeleteAccountConfirmDialog(context, ref),
                      //     style: TextButton.styleFrom(
                      //       foregroundColor: AppColors.textHint,
                      //       padding: const EdgeInsets.symmetric(
                      //         horizontal: AppSizes.paddingMedium,
                      //         vertical: AppSizes.paddingSmall,
                      //       ),
                      //     ),
                      //     child: const Text(
                      //       '注销账号',
                      //       style: TextStyle(
                      //         fontSize: 12,
                      //         fontWeight: FontWeight.w400,
                      //       ),
                      //     ),
                      //   ),
                      // ),

                      // const SizedBox(height: AppSizes.paddingSmall),

                      // 版本信息
                      Center(
                        child: Text(
                          '版本 $appVersion',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.textHint,
                          ),
                        ),
                      ),

                      const SizedBox(height: AppSizes.paddingXLarge),
                    ],
                  ),
                ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}


/// 注销账号确认弹窗
class _DeleteAccountDialog extends StatefulWidget {
  final Future<void> Function(String password, String confirmText) onConfirm;

  const _DeleteAccountDialog({
    required this.onConfirm,
  });

  @override
  State<_DeleteAccountDialog> createState() => _DeleteAccountDialogState();
}

class _DeleteAccountDialogState extends State<_DeleteAccountDialog> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _confirmTextController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmTextController.dispose();
    super.dispose();
  }

  Future<void> _handleConfirm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final password = _passwordController.text;
      final confirmText = _confirmTextController.text;

      Navigator.pop(context); // 先关闭弹窗

      await widget.onConfirm(password, confirmText);
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        // 显示错误信息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('注销失败: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('注销账号'),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('注销账号将会：'),
              const SizedBox(height: 8),
              const Text('• 永久删除您的账号和所有数据'),
              const Text('• 无法恢复任何信息'),
              const Text('• 立即退出登录'),
              const SizedBox(height: 16),
              const Text(
                '此操作不可逆，请谨慎操作！',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 20),

              // 密码输入框
              TextFormField(
                controller: _passwordController,
                obscureText: true,
                enabled: !_isLoading,
                decoration: const InputDecoration(
                  labelText: '请输入密码',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.lock),
                  isDense: true,
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入密码';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // 确认文本输入框
              TextFormField(
                controller: _confirmTextController,
                enabled: !_isLoading,
                decoration: const InputDecoration(
                  labelText: '请输入"确认注销"',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.warning),
                  isDense: true,
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入确认文本';
                  }
                  if (value != '确认注销') {
                    return '请输入"确认注销"';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: const Text('取消'),
        ),
        TextButton(
          onPressed: _isLoading ? null : _handleConfirm,
          style: TextButton.styleFrom(
            foregroundColor: Colors.red,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('确认注销'),
        ),
      ],
    );
  }
}
