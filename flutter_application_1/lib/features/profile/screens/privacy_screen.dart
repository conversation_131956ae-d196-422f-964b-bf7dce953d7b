import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';

/// 隐私政策页面
class PrivacyScreen extends StatelessWidget {
  const PrivacyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('隐私政策'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.pageBackground,
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppSizes.paddingLarge),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '隐私政策',
                  style: AppTextStyles.headline2,
                ),
                
                const SizedBox(height: AppSizes.paddingMedium),
                
                const Text(
                  '最后更新日期：2025年5月1日',
                  style: AppTextStyles.bodySmall,
                ),
                
                const SizedBox(height: AppSizes.paddingLarge),
                
                _buildSection(
                  '1. 引言',
                  '本隐私政策描述了"专注"应用（以下简称"本应用"）如何收集、使用、存储和保护您的个人信息。我们重视您的隐私，并致力于保护您的个人信息安全。请您仔细阅读本隐私政策，以了解我们的数据处理实践。',
                ),
                
                _buildSection(
                  '2. 我们收集的信息',
                  '2.1 您提供的信息：当您创建账户、使用本应用的功能或与我们联系时，我们可能会收集您提供的信息，如姓名、电子邮件地址、手机号码等。\n\n'
                  '2.2 自动收集的信息：当您使用本应用时，我们可能会自动收集某些信息，如设备信息、IP地址、使用记录等。\n\n'
                  '2.3 使用数据：我们收集您如何使用本应用的数据，如专注时间、目标完成情况等。',
                ),
                
                _buildSection(
                  '3. 信息使用',
                  '我们使用收集的信息用于：\n\n'
                  '3.1 提供、维护和改进本应用的服务。\n\n'
                  '3.2 处理您的请求和响应您的询问。\n\n'
                  '3.3 发送服务通知和更新。\n\n'
                  '3.4 分析使用模式以改进用户体验。\n\n'
                  '3.5 防止欺诈和滥用。',
                ),
                
                _buildSection(
                  '4. 信息共享',
                  '我们不会出售、出租或交易您的个人信息。我们可能在以下情况下共享您的信息：\n\n'
                  '4.1 经您同意。\n\n'
                  '4.2 与提供服务的合作伙伴共享（如云服务提供商）。\n\n'
                  '4.3 遵守法律要求。\n\n'
                  '4.4 保护我们的权利和财产。',
                ),
                
                _buildSection(
                  '5. 数据安全',
                  '我们采取合理的技术和组织措施来保护您的个人信息不被未经授权的访问、使用或披露。然而，没有任何互联网传输或电子存储方法是100%安全的。',
                ),
                
                _buildSection(
                  '6. 数据存储',
                  '6.1 本地存储：您的大部分数据存储在您的设备上。\n\n'
                  '6.2 云存储：如果您启用了云同步功能，您的数据将存储在我们的服务器上。\n\n'
                  '6.3 数据保留：我们会在必要的时间内保留您的数据，或根据法律要求保留。',
                ),
                
                _buildSection(
                  '7. 您的权利',
                  '根据适用的数据保护法律，您可能拥有以下权利：\n\n'
                  '7.1 访问您的个人信息。\n\n'
                  '7.2 更正不准确的个人信息。\n\n'
                  '7.3 删除您的个人信息。\n\n'
                  '7.4 限制或反对处理您的个人信息。\n\n'
                  '7.5 数据可携带性。',
                ),
                
                _buildSection(
                  '8. 儿童隐私',
                  '本应用不面向13岁以下的儿童。我们不会故意收集13岁以下儿童的个人信息。如果您发现我们可能收集了儿童的个人信息，请联系我们，我们将采取措施删除这些信息。',
                ),
                
                _buildSection(
                  '9. 隐私政策更新',
                  '我们可能会不时更新本隐私政策。更新后的隐私政策将在本应用内公布，继续使用本应用即表示您接受更新后的隐私政策。',
                ),
                
                _buildSection(
                  '10. 联系我们',
                  '如果您对本隐私政策有任何疑问或顾虑，请通过以下方式联系我们：\<EMAIL>',
                ),
                
                const SizedBox(height: AppSizes.paddingLarge),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildSection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSizes.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTextStyles.headline3,
          ),
          const SizedBox(height: AppSizes.paddingSmall),
          Text(
            content,
            style: AppTextStyles.bodyMedium,
          ),
        ],
      ),
    );
  }
}
