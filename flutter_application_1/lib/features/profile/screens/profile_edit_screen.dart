import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/models/user.dart';
import '../../../core/utils/storage_utils.dart';
import '../providers/user_provider.dart';
import '../providers/profile_edit_provider.dart';

/// 个人资料编辑页面
class ProfileEditScreen extends ConsumerStatefulWidget {
  const ProfileEditScreen({super.key});

  @override
  ConsumerState<ProfileEditScreen> createState() => _ProfileEditScreenState();
}

class _ProfileEditScreenState extends ConsumerState<ProfileEditScreen> {
  final _formKey = GlobalKey<FormState>();

  // 表单控制器
  late TextEditingController _nicknameController;
  late TextEditingController _emailController;

  // 保存状态
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();

    // 获取当前用户数据
    final user = ref.read(userProvider).value;
    if (user != null) {
      _nicknameController = TextEditingController(text: user.nickname);
      _emailController = TextEditingController(text: user.email.isEmpty ? '' : user.email);
    } else {
      _nicknameController = TextEditingController();
      _emailController = TextEditingController();
    }

    // 检查用户是否已登录
    _checkLoginStatus();
  }

  // 检查登录状态
  Future<void> _checkLoginStatus() async {
    final token = await StorageUtils.getToken();
    debugPrint('当前令牌: ${token != null ? '已存在' : '不存在'}');
    if (token != null) {
      debugPrint('令牌值: ${token.substring(0, 10)}...');
    }
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  // 头像功能已移除

  // 保存个人资料
  Future<void> _saveProfile() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isSaving = true;
      });

      try {
        // 检查用户是否已登录
        final token = await StorageUtils.getToken();
        if (token == null) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('请先登录后再保存个人资料')),
            );
            setState(() {
              _isSaving = false;
            });
          }
          return;
        }

        debugPrint('用户已登录，令牌: ${token.substring(0, 10)}...');

        // 打印调试信息
        debugPrint('开始保存个人资料');
        debugPrint('昵称: ${_nicknameController.text.trim()}');
        debugPrint('邮箱: ${_emailController.text.trim()}');

        // 准备用户数据
        final userData = <String, dynamic>{
          'nickname': _nicknameController.text.trim(),
        };

        // 只有当邮箱不为空时才添加
        final email = _emailController.text.trim();
        if (email.isNotEmpty) {
          userData['email'] = email;
        }

        debugPrint('准备更新用户数据: $userData');

        // 更新个人资料
        final profileUpdater = ref.read(profileUpdateProvider.notifier);
        final success = await profileUpdater.updateProfile(
          nickname: _nicknameController.text.trim(),
          email: email.isNotEmpty ? email : null,
          avatarUrl: null, // 不再上传头像
        );

        debugPrint('更新结果: $success');

        if (mounted) {
          if (success) {
            // 刷新用户数据
            try {
              final _ = await ref.refresh(userProvider.future);

              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('个人资料已更新')),
                );
                Navigator.pop(context);
              }
            } catch (e) {
              debugPrint('刷新用户数据失败: $e');
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('个人资料已更新，但刷新数据失败')),
                );
                Navigator.pop(context);
              }
            }
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('更新失败，请重试')),
            );
          }
        }
      } catch (e) {
        debugPrint('更新过程中出现异常: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('更新失败: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isSaving = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取当前用户数据
    final userAsync = ref.watch(userProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('编辑个人资料'),
        actions: [
          TextButton(
            onPressed: _isSaving ? null : _saveProfile,
            child: _isSaving
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('保存'),
          ),
        ],
      ),
      body: userAsync.when(
        data: (user) => _buildForm(user),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('加载失败: $error'),
        ),
      ),
    );
  }

  Widget _buildForm(User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 头像功能已移除
            const SizedBox(height: 16),

            // 昵称
            TextFormField(
              controller: _nicknameController,
              decoration: const InputDecoration(
                labelText: '昵称',
                hintText: '请输入您的昵称',
                prefixIcon: Icon(Icons.person),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return '请输入昵称';
                }
                if (value.length > 20) {
                  return '昵称不能超过20个字符';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // 邮箱
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: '邮箱',
                hintText: '请输入您的邮箱',
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  // 简单的邮箱格式验证
                  final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                  if (!emailRegex.hasMatch(value)) {
                    return '请输入有效的邮箱地址';
                  }
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // 手机号（只读）
            TextFormField(
              initialValue: user.phone,
              decoration: const InputDecoration(
                labelText: '手机号',
                prefixIcon: Icon(Icons.phone),
              ),
              readOnly: true,
              enabled: false,
            ),

            const SizedBox(height: 16),

            // 用户名（只读）
            TextFormField(
              initialValue: user.username,
              decoration: const InputDecoration(
                labelText: '用户名',
                prefixIcon: Icon(Icons.account_circle),
              ),
              readOnly: true,
              enabled: false,
            ),

            const SizedBox(height: 32),

            // 保存按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isSaving ? null : _saveProfile,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isSaving
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text('保存'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
