import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';

/// 会员购买页面
/// 显示会员套餐和购买选项
class MembershipScreen extends ConsumerStatefulWidget {
  const MembershipScreen({super.key});

  @override
  ConsumerState<MembershipScreen> createState() => _MembershipScreenState();
}

class _MembershipScreenState extends ConsumerState<MembershipScreen> {
  // 选中的会员类型
  String _selectedType = 'premium';

  // 选中的时长
  String _selectedDuration = 'monthly';

  // 会员套餐信息
  final Map<String, Map<String, dynamic>> _membershipPlans = {
    'premium': {
      'title': '高级会员',
      'color': const Color(0xFFE8F5E9),
      'textColor': AppColors.primary,
      'icon': Icons.star,
      'features': [
        '无限专注时长',
        '高级音频解锁',
        '数据分析',
        '云端备份',
      ],
      'prices': {
        'monthly': 18,
        'quarterly': 48,
        'yearly': 168,
      },
      'originalPrices': {
        'monthly': 30,
        'quarterly': 90,
        'yearly': 360,
      },
    },
    'pro': {
      'title': '专业版',
      'color': const Color(0xFFE0F2F1),
      'textColor': const Color(0xFF00897B),
      'icon': Icons.workspace_premium,
      'features': [
        '无限专注时长',
        '全部音频解锁',
        '高级数据分析',
        '云端备份',
        '自定义主题',
      ],
      'prices': {
        'monthly': 28,
        'quarterly': 78,
        'yearly': 268,
      },
      'originalPrices': {
        'monthly': 50,
        'quarterly': 150,
        'yearly': 600,
      },
    },
  };

  // 时长选项
  final Map<String, String> _durationOptions = {
    'monthly': '月付',
    'quarterly': '季付',
    'yearly': '年付',
  };

  // 时长折扣信息
  final Map<String, String> _durationDiscounts = {
    'monthly': '无折扣',
    'quarterly': '8.0折',
    'yearly': '7.0折',
  };

  @override
  Widget build(BuildContext context) {
    final selectedPlan = _membershipPlans[_selectedType]!;
    final price = selectedPlan['prices'][_selectedDuration];
    final originalPrice = selectedPlan['originalPrices'][_selectedDuration];

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('会员升级'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 会员类型选择
              Text(
                '选择会员类型',
                style: AppTextStyles.headline3,
              ),
              const SizedBox(height: 16),

              // 会员类型卡片
              Row(
                children: _membershipPlans.entries.map((entry) {
                  final type = entry.key;
                  final plan = entry.value;

                  return Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedType = type;
                        });
                      },
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: _selectedType == type
                              ? plan['color']
                              : Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: _selectedType == type
                                ? plan['textColor']
                                : AppColors.border,
                            width: _selectedType == type ? 2 : 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            Icon(
                              plan['icon'],
                              color: plan['textColor'],
                              size: 32,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              plan['title'],
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: plan['textColor'],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),

              const SizedBox(height: 24),

              // 会员权益
              Text(
                '会员权益',
                style: AppTextStyles.headline3,
              ),
              const SizedBox(height: 16),

              // 权益列表
              ...selectedPlan['features'].map((feature) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: selectedPlan['textColor'],
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        feature,
                        style: AppTextStyles.bodyMedium,
                      ),
                    ],
                  ),
                );
              }).toList(),

              const SizedBox(height: 24),

              // 时长选择
              Text(
                '选择时长',
                style: AppTextStyles.headline3,
              ),
              const SizedBox(height: 16),

              // 时长选项
              Row(
                children: _durationOptions.entries.map((entry) {
                  final duration = entry.key;
                  final label = entry.value;
                  final discount = _durationDiscounts[duration];

                  return Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedDuration = duration;
                        });
                      },
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: _selectedDuration == duration
                              ? selectedPlan['color']
                              : Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: _selectedDuration == duration
                                ? selectedPlan['textColor']
                                : AppColors.border,
                            width: _selectedDuration == duration ? 2 : 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            Text(
                              label,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: _selectedDuration == duration
                                    ? selectedPlan['textColor']
                                    : AppColors.text,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              discount!,
                              style: TextStyle(
                                fontSize: 12,
                                color: _selectedDuration == duration
                                    ? selectedPlan['textColor']
                                    : AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),

              const SizedBox(height: 32),

              // 价格和购买按钮
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [AppShadows.low],
                ),
                child: Column(
                  children: [
                    // 价格信息
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '¥',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: selectedPlan['textColor'],
                          ),
                        ),
                        Text(
                          '$price',
                          style: TextStyle(
                            fontSize: 36,
                            fontWeight: FontWeight.bold,
                            color: selectedPlan['textColor'],
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '原价: ¥$originalPrice',
                          style: const TextStyle(
                            fontSize: 14,
                            decoration: TextDecoration.lineThrough,
                            color: AppColors.textHint,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // 购买按钮
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          // 实现购买逻辑
                          _showPaymentDialog(context);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: selectedPlan['textColor'],
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                        child: const Text(
                          '立即购买',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // 协议提示
                    const Text(
                      '点击立即购买，表示您同意《会员服务协议》和《自动续费协议》',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textHint,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 显示支付对话框
  void _showPaymentDialog(BuildContext context) {
    final selectedPlan = _membershipPlans[_selectedType]!;
    final price = selectedPlan['prices'][_selectedDuration];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择支付方式'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('您将支付: ¥$price'),
            const SizedBox(height: 16),
            _buildPaymentOption(context, '微信支付', Icons.wechat, Colors.green),
            const SizedBox(height: 8),
            _buildPaymentOption(context, '支付宝', Icons.account_balance_wallet, Colors.blue),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  // 构建支付选项
  Widget _buildPaymentOption(BuildContext context, String title, IconData icon, Color color) {
    return InkWell(
      onTap: () {
        Navigator.of(context).pop();
        // 模拟支付成功
        _showPaymentSuccess(context);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.border),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(icon, color: color),
            const SizedBox(width: 12),
            Text(
              title,
              style: AppTextStyles.bodyMedium,
            ),
            const Spacer(),
            const Icon(Icons.arrow_forward_ios, size: 16),
          ],
        ),
      ),
    );
  }

  // 显示支付成功对话框
  void _showPaymentSuccess(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('支付成功'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.check_circle,
              color: AppColors.success,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              '恭喜您成为${_membershipPlans[_selectedType]!['title']}',
              style: AppTextStyles.bodyLarge,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // 返回个人页面
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
