import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';

/// 用户协议页面
class TermsScreen extends StatelessWidget {
  const TermsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('用户协议'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.pageBackground,
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppSizes.paddingLarge),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '用户协议',
                  style: AppTextStyles.headline2,
                ),
                
                const SizedBox(height: AppSizes.paddingMedium),
                
                const Text(
                  '最后更新日期：2025年5月1日',
                  style: AppTextStyles.bodySmall,
                ),
                
                const SizedBox(height: AppSizes.paddingLarge),
                
                _buildSection(
                  '1. 协议的接受',
                  '欢迎使用"专注"应用（以下简称"本应用"）。本用户协议（以下简称"本协议"）是您与本应用开发者之间关于您使用本应用服务所订立的协议。请您仔细阅读本协议，如果您不同意本协议的任何条款，请勿使用本应用。当您安装、使用或访问本应用时，即表示您已经阅读并同意本协议的所有条款。',
                ),
                
                _buildSection(
                  '2. 服务内容',
                  '本应用是一款帮助用户提高工作和学习效率的工具，主要功能包括但不限于：专注计时、目标管理、数据分析等。本应用保留随时变更、中断或终止部分或全部服务的权利。',
                ),
                
                _buildSection(
                  '3. 用户账户',
                  '3.1 您可能需要创建账户才能使用本应用的某些功能。您有责任维护您的账户安全性。\n\n'
                  '3.2 您保证提供的所有注册信息是真实、准确、完整和最新的。\n\n'
                  '3.3 您不得将您的账户转让给他人使用。',
                ),
                
                _buildSection(
                  '4. 用户行为规范',
                  '4.1 您同意不会使用本应用从事任何违法或不当的活动。\n\n'
                  '4.2 您不得干扰或破坏本应用的正常运行。\n\n'
                  '4.3 您不得尝试未经授权访问本应用的系统或网络。',
                ),
                
                _buildSection(
                  '5. 知识产权',
                  '5.1 本应用及其原始内容、功能和设计受知识产权法律保护。\n\n'
                  '5.2 未经明确许可，您不得复制、修改、分发或销售本应用的任何部分。',
                ),
                
                _buildSection(
                  '6. 免责声明',
                  '6.1 本应用按"现状"提供，不提供任何明示或暗示的保证。\n\n'
                  '6.2 开发者不保证本应用将始终可用、安全、无错误或满足您的要求。\n\n'
                  '6.3 您使用本应用的风险由您自行承担。',
                ),
                
                _buildSection(
                  '7. 责任限制',
                  '在法律允许的最大范围内，开发者不对因使用或无法使用本应用而导致的任何直接、间接、附带、特殊、惩罚性或后果性损害负责。',
                ),
                
                _buildSection(
                  '8. 协议修改',
                  '开发者保留随时修改本协议的权利。修改后的协议将在本应用内公布，继续使用本应用即表示您接受修改后的协议。',
                ),
                
                _buildSection(
                  '9. 适用法律',
                  '本协议受中华人民共和国法律管辖并按其解释。',
                ),
                
                _buildSection(
                  '10. 联系我们',
                  '如果您对本协议有任何疑问，请通过以下方式联系我们：\<EMAIL>',
                ),
                
                const SizedBox(height: AppSizes.paddingLarge),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildSection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSizes.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTextStyles.headline3,
          ),
          const SizedBox(height: AppSizes.paddingSmall),
          Text(
            content,
            style: AppTextStyles.bodyMedium,
          ),
        ],
      ),
    );
  }
}
