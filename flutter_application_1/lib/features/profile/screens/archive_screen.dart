import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/models/subject_project.dart';
import '../../../core/models/archived_goal.dart';
import '../../../core/services/hive_service.dart';
import '../../task/providers/goal_state.dart';
import '../../task/providers/subject_state.dart';
import '../widgets/archive_project_card.dart';
import '../../../shared/widgets/top_message_overlay.dart';

/// 归档页面
/// 显示已归档的项目和目标
class ArchiveScreen extends ConsumerStatefulWidget {
  const ArchiveScreen({super.key});

  @override
  ConsumerState<ArchiveScreen> createState() => _ArchiveScreenState();
}

class _ArchiveScreenState extends ConsumerState<ArchiveScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final HiveService _hiveService = HiveService();
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');

  List<Project> _archivedProjects = [];
  List<ArchivedGoal> _archivedGoalDetails = [];
  bool _isLoading = true;

  /// 格式化日期
  String _formatDate(DateTime date) {
    return _dateFormat.format(date);
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _initializeAndLoadData();
  }

  /// 初始化并加载数据
  Future<void> _initializeAndLoadData() async {
    try {
      // 确保Hive服务已初始化
      await _hiveService.initHive();
      debugPrint('Hive服务初始化成功');

      // 加载归档数据
      await _loadArchivedData();
    } catch (e) {
      debugPrint('初始化或加载数据失败: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载数据失败: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 加载归档数据
  Future<void> _loadArchivedData() async {
    debugPrint('开始加载归档数据');

    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // 确保SubjectRepository已初始化
      if (!_hiveService.subjectRepository.isInitialized) {
        debugPrint('SubjectRepository未初始化，正在初始化...');
        await _hiveService.subjectRepository.init();
        debugPrint('SubjectRepository初始化成功');
      }

      // 加载归档项目
      debugPrint('正在加载归档项目...');
      final archivedProjects = _hiveService.subjectRepository.getArchivedProjects();
      debugPrint('获取到 ${archivedProjects.length} 个归档项目');

      // 打印归档项目信息
      for (final project in archivedProjects) {
        debugPrint('归档项目: ${project.id}, ${project.name}, isArchived: ${project.isArchived}');
      }

      // 确保GoalRepository已初始化
      try {
        debugPrint('确保GoalRepository已初始化...');
        await _hiveService.goalRepository.init();
        debugPrint('GoalRepository初始化成功');
      } catch (e) {
        // 可能已经初始化，忽略错误
        debugPrint('GoalRepository可能已经初始化: $e');
      }

      // 加载归档目标
      debugPrint('正在加载归档目标...');
      final archivedGoals = _hiveService.goalRepository.getArchivedGoals();
      debugPrint('获取到 ${archivedGoals.length} 个归档目标');

      // 打印归档目标信息
      for (final goal in archivedGoals) {
        debugPrint('归档目标: ${goal.id}, ${goal.name}, isArchived: ${goal.isArchived}');
      }

      // 加载归档目标详情
      debugPrint('正在加载归档目标详情...');
      try {
        // 确保ArchivedGoalRepository已初始化
        await _hiveService.archivedGoalRepository.init();

        final archivedGoalDetails = _hiveService.archivedGoalRepository.getAllArchivedGoals();
        debugPrint('获取到 ${archivedGoalDetails.length} 个归档目标详情');

        // 打印归档目标详情信息
        for (final goal in archivedGoalDetails) {
          debugPrint('归档目标详情: ${goal.id}, ${goal.name}, 科目数: ${goal.subjects.length}, 项目数: ${goal.projects.length}');
        }

        _archivedGoalDetails = archivedGoalDetails;
      } catch (e) {
        debugPrint('加载归档目标详情失败: $e');
      }

      if (mounted) {
        setState(() {
          _archivedProjects = archivedProjects;
          _isLoading = false;
        });
        debugPrint('归档数据加载完成，UI已更新');
      }
    } catch (e) {
      debugPrint('加载归档数据出错: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载归档数据失败: $e')),
        );
      }
    }
  }

  /// 取消归档项目
  Future<void> _unarchiveProject(Project project) async {
    // 显示加载对话框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text('正在取消归档项目 "${project.name}"...'),
          ],
        ),
      ),
    );

    try {
      debugPrint('开始取消归档项目: ${project.id}, ${project.name}');

      // 更新数据库
      await _hiveService.subjectRepository.unarchiveProject(project.id);
      debugPrint('数据库更新成功');

      // 更新状态管理器中的项目
      ref.read(subjectStateProvider.notifier).unarchiveProject(project);
      debugPrint('状态管理器更新成功');

      // 重新加载项目列表
      await ref.read(subjectStateProvider.notifier).reloadProjects();
      debugPrint('项目列表重新加载成功');

      // 刷新归档列表
      await _loadArchivedData();
      debugPrint('归档列表刷新成功');

      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 显示成功消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('项目 "${project.name}" 已取消归档'),
            backgroundColor: AppColors.success,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('取消归档项目出错: $e');

      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('取消归档失败: $e'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// 删除归档项目
  Future<void> _deleteProject(Project project) async {
    try {
      // 显示确认对话框
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('删除项目'),
          content: Text('确定要删除项目 "${project.name}" 吗？此操作不可恢复。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: Text(
                '删除',
                style: TextStyle(color: AppColors.textTertiary),
              ),
            ),
          ],
        ),
      );

      if (confirmed != true) return;

      // 检查组件是否仍然挂载
      if (!mounted) return;

      debugPrint('开始删除项目: ${project.id}, ${project.name}');

      // 删除项目
      await _hiveService.subjectRepository.deleteProject(project.id);
      debugPrint('项目删除成功');

      // 更新状态
      ref.read(subjectStateProvider.notifier).deleteProject(project.id);
      debugPrint('状态管理器更新成功');

      // 重新加载归档列表
      await _loadArchivedData();
      debugPrint('归档列表刷新成功');

      // 显示简洁的成功消息
      if (mounted) {
        TopMessageOverlayManager().showSuccess(
          context: context,
          message: '已删除',
          duration: const Duration(seconds: 1), // 缩短显示时间
        );
      }
    } catch (e) {
      debugPrint('删除项目出错: $e');

      if (mounted) {
        TopMessageOverlayManager().showError(
          context: context,
          message: '删除失败',
          duration: const Duration(seconds: 2),
        );
      }
    }
  }

  /// 删除归档目标
  Future<void> _deleteArchivedGoal(ArchivedGoal archivedGoal) async {
    try {
      // 显示确认对话框
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('删除归档目标'),
          content: Text('确定要删除归档目标 "${archivedGoal.name}" 吗？此操作不可恢复，相关的科目、项目和里程碑信息也会被删除。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: Text(
                '删除',
                style: TextStyle(color: AppColors.error),
              ),
            ),
          ],
        ),
      );

      if (confirmed != true) return;

      // 检查组件是否仍然挂载
      if (!mounted) return;

      // 显示加载指示器
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('正在删除归档目标...')),
      );

      // 删除归档目标
      await _hiveService.archivedGoalRepository.deleteArchivedGoal(archivedGoal.id);
      debugPrint('归档目标删除成功: ${archivedGoal.id}, ${archivedGoal.name}');

      // 重新加载归档列表
      await _loadArchivedData();

      // 显示成功消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('归档目标 "${archivedGoal.name}" 已删除')),
        );
      }
    } catch (e) {
      debugPrint('删除归档目标出错: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('删除失败: $e')),
        );
      }
    }
  }

  // 构建iOS风格的标签项
  Widget _buildTabItem(int index, String label) {
    final isSelected = _tabController.index == index;

    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _tabController.animateTo(index);
          });
        },
        child: Container(
          decoration: BoxDecoration(
            color: isSelected
                ? AppColors.primary.withAlpha(AppColors.alpha30)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8.0),
          ),
          margin: const EdgeInsets.all(3.0),
          child: Center(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 15,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected ? AppColors.primary : AppColors.text,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: const Text(
          '归档管理',
          style: AppTextStyles.headline2,
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: AppColors.text,
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.pageBackground,
        ),
        child: SafeArea(
          child: Column(
            children: [
              const SizedBox(height: AppSizes.paddingMedium),

              // iOS风格标签页栏
              Container(
                margin: const EdgeInsets.fromLTRB(AppSizes.paddingMedium, 16, AppSizes.paddingMedium, 16),
                height: 44, // 固定高度
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(15),
                      blurRadius: 2,
                      spreadRadius: 0,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    _buildTabItem(0, '项目归档'),
                    _buildTabItem(1, '目标归档'),
                  ],
                ),
              ),

              // 主要内容区域
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : TabBarView(
                        controller: _tabController,
                        children: [
                          // 项目归档标签页
                          _buildProjectsTab(),

                          // 目标归档标签页
                          _buildGoalsTab(),
                        ],
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建项目归档标签页
  Widget _buildProjectsTab() {
    return RefreshIndicator(
      onRefresh: _loadArchivedData,
      child: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _archivedProjects.isEmpty
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 32),
                    child: Text('暂无归档项目', style: AppTextStyles.bodyLarge),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _archivedProjects.length,
                  itemBuilder: (context, index) {
                    final project = _archivedProjects[index];
                    final subject = ref.read(subjectStateProvider.notifier).getSubjectById(project.subjectId);

                    return ArchiveProjectCard(
                      project: project,
                      subject: subject,
                      onUnarchive: () => _unarchiveProject(project),
                      onDelete: () => _deleteProject(project),
                    );
                  },
                ),
    );
  }

  /// 显示归档目标确认对话框
  Future<void> _showArchiveGoalConfirmation() async {
    // 获取当前目标
    final currentGoal = ref.read(goalStateProvider).currentGoal;
    if (currentGoal == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('没有可归档的目标')),
      );
      return;
    }

    // 显示确认对话框
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('归档目标'),
        content: Text(
          '确定要归档目标 "${currentGoal.name}" 吗？\n\n'
          '归档后，将仅保留专注历史记录与数据，并删除所有目标、科目、项目信息。\n\n'
          '此操作不可逆，请谨慎操作。'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('归档'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _archiveCurrentGoal();
    }
  }

  /// 归档当前目标
  Future<void> _archiveCurrentGoal() async {
    try {
      // 获取当前目标
      final currentGoal = ref.read(goalStateProvider).currentGoal;
      if (currentGoal == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('没有可归档的目标')),
        );
        return;
      }

      // 检查组件是否仍然挂载
      if (!mounted) return;

      // 显示加载指示器
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('正在归档目标...')),
      );

      debugPrint('开始归档目标: ${currentGoal.id}, ${currentGoal.name}');

      // 获取所有科目
      final subjects = _hiveService.subjectRepository.getAllSubjects();
      debugPrint('获取到 ${subjects.length} 个科目');

      // 获取所有项目
      final projects = _hiveService.subjectRepository.getAllProjects(includeArchived: true);
      debugPrint('获取到 ${projects.length} 个项目');

      // 创建归档目标
      debugPrint('创建归档目标...');
      final archivedGoal = await _hiveService.archivedGoalRepository.archiveGoal(
        currentGoal,
        subjects: subjects,
        projects: projects,
      );
      debugPrint('归档目标创建成功: ${archivedGoal.id}, ${archivedGoal.name}');

      // 删除原目标
      debugPrint('删除原目标...');
      await _hiveService.goalRepository.deleteGoal(currentGoal.id);
      debugPrint('原目标删除成功');

      // 删除所有科目和项目
      debugPrint('删除所有科目和项目...');
      for (final subject in subjects) {
        try {
          await _hiveService.subjectRepository.deleteSubject(subject.id);
          debugPrint('科目删除成功: ${subject.id}, ${subject.name}');
        } catch (e) {
          debugPrint('科目删除失败: ${subject.id}, ${subject.name}, 错误: $e');
        }
      }

      // 清空当前目标
      ref.read(goalStateProvider.notifier).clearGoalAndMilestones();
      debugPrint('清空当前目标成功');

      // 重新加载项目列表
      await ref.read(subjectStateProvider.notifier).reloadProjects();
      debugPrint('重新加载项目列表成功');

      // 重新加载归档数据
      await _loadArchivedData();
      debugPrint('重新加载归档数据成功');

      // 检查组件是否仍然挂载
      if (!mounted) return;

      // 显示成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('目标 "${currentGoal.name}" 已归档')),
      );
    } catch (e) {
      debugPrint('归档目标失败: $e');

      // 检查组件是否仍然挂载
      if (!mounted) return;

      // 显示错误消息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('归档失败: $e')),
      );
    }
  }

  /// 构建目标归档标签页
  Widget _buildGoalsTab() {
    return RefreshIndicator(
      onRefresh: _loadArchivedData,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // 归档当前目标按钮
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '归档当前目标',
                    style: AppTextStyles.headline3,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '目标归档：保留专注历史与数据，清除当前所有目标、科目、项目信息的方法。',
                    style: AppTextStyles.bodyMedium,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          icon: const Icon(Icons.archive_outlined),
                          label: const Text('归档当前目标'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          onPressed: _showArchiveGoalConfirmation,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // 归档目标列表
          if (_archivedGoalDetails.isEmpty)
            const Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 32),
                child: Text('暂无归档目标', style: AppTextStyles.bodyLarge),
              ),
            )
          else
            ...List.generate(_archivedGoalDetails.length, (index) {
              final archivedGoal = _archivedGoalDetails[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 16),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 目标头部
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withAlpha(25),
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                      ),
                      child: Row(
                        children: [
                          // 目标标题
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  archivedGoal.name,
                                  style: AppTextStyles.headline3,
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(top: 4),
                                  child: Text(
                                    '归档于 ${_formatDate(archivedGoal.archivedAt)}',
                                    style: AppTextStyles.bodySmall.copyWith(
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // 归档标签
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.grey.withAlpha(50),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const Text(
                              '已归档',
                              style: TextStyle(
                                fontSize: 12,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // 目标详情
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 目标日期
                          Row(
                            children: [
                              const Icon(
                                Icons.calendar_today,
                                size: 16,
                                color: AppColors.textSecondary,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '${_formatDate(archivedGoal.startDate)} - ${_formatDate(archivedGoal.endDate)}',
                                style: AppTextStyles.bodyMedium.copyWith(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),

                          // 科目信息
                          if (archivedGoal.subjects.isNotEmpty) ...[
                            Text(
                              '科目 (${archivedGoal.subjects.length})',
                              style: AppTextStyles.bodyLarge.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: archivedGoal.subjects.map((subject) => Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Color(subject.color).withAlpha(50),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  subject.name,
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Color(subject.color),
                                  ),
                                ),
                              )).toList(),
                            ),
                          ],

                          const SizedBox(height: 16),

                          // 项目信息
                          if (archivedGoal.projects.isNotEmpty) ...[
                            Text(
                              '项目 (${archivedGoal.projects.length})',
                              style: AppTextStyles.bodyLarge.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            ...archivedGoal.projects.take(3).map((project) => Padding(
                              padding: const EdgeInsets.only(bottom: 4),
                              child: Row(
                                children: [
                                  const Icon(
                                    Icons.assignment_outlined,
                                    size: 16,
                                    color: AppColors.textSecondary,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      project.name,
                                      style: AppTextStyles.bodyMedium,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            )),
                            if (archivedGoal.projects.length > 3)
                              Padding(
                                padding: const EdgeInsets.only(top: 4),
                                child: Text(
                                  '还有 ${archivedGoal.projects.length - 3} 个项目',
                                  style: AppTextStyles.bodySmall.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ),
                          ],

                          // 里程碑信息
                          if (archivedGoal.milestones.isNotEmpty) ...[
                            const SizedBox(height: 16),
                            Text(
                              '里程碑 (${archivedGoal.milestones.length})',
                              style: AppTextStyles.bodyLarge.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            ...archivedGoal.milestones.take(3).map((milestone) => Padding(
                              padding: const EdgeInsets.only(bottom: 4),
                              child: Row(
                                children: [
                                  const Icon(
                                    Icons.flag,
                                    size: 16,
                                    color: AppColors.textSecondary,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      milestone.name,
                                      style: AppTextStyles.bodyMedium,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  Text(
                                    _formatDate(milestone.date),
                                    style: AppTextStyles.bodySmall.copyWith(
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                ],
                              ),
                            )),
                            if (archivedGoal.milestones.length > 3)
                              Padding(
                                padding: const EdgeInsets.only(top: 4),
                                child: Text(
                                  '还有 ${archivedGoal.milestones.length - 3} 个里程碑',
                                  style: AppTextStyles.bodySmall.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ),
                          ],
                        ],
                      ),
                    ),

                    // 操作按钮
                    Padding(
                      padding: const EdgeInsets.fromLTRB(
                        AppSizes.paddingMedium,
                        0,
                        AppSizes.paddingMedium,
                        AppSizes.paddingMedium,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          // 删除按钮 - 参考项目归档卡片样式
                          Container(
                            height: 36,
                            decoration: BoxDecoration(
                              color: AppColors.textTertiary.withAlpha(AppColors.alpha10),
                              borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                              border: Border.all(
                                color: AppColors.textTertiary.withAlpha(AppColors.alpha30),
                                width: 1,
                              ),
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: () => _deleteArchivedGoal(archivedGoal),
                                borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: AppSizes.paddingMedium,
                                    vertical: AppSizes.paddingSmall,
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.delete_outline,
                                        size: 16,
                                        color: AppColors.textTertiary,
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        '删除',
                                        style: AppTextStyles.bodySmall.copyWith(
                                          color: AppColors.textTertiary,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }),
        ],
      ),
    );
  }
}