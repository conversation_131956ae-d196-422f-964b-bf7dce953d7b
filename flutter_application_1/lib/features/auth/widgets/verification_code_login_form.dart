import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/providers/auth_provider.dart';
import '../../../shared/theme/constants.dart';

/// 验证码登录表单
class VerificationCodeLoginForm extends ConsumerStatefulWidget {
  final Function(bool) onLoginResult;

  const VerificationCodeLoginForm({
    super.key,
    required this.onLoginResult,
  });

  @override
  ConsumerState<VerificationCodeLoginForm> createState() => _VerificationCodeLoginFormState();
}

class _VerificationCodeLoginFormState extends ConsumerState<VerificationCodeLoginForm> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _codeController = TextEditingController();
  bool _isLoading = false;
  final bool _rememberMe = true;
  String? _errorMessage;

  // 验证码倒计时
  int _countdown = 0;
  Timer? _timer;

  @override
  void dispose() {
    _emailController.dispose();
    _codeController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  // 发送验证码
  Future<void> _sendVerificationCode() async {
    // 清除之前的错误信息
    setState(() {
      _errorMessage = null;
    });

    // 验证邮箱输入
    final email = _emailController.text.trim();
    if (email.isEmpty) {
      setState(() {
        _errorMessage = '请输入邮箱地址';
      });
      // 聚焦到邮箱输入框
      FocusScope.of(context).requestFocus(FocusNode());
      return;
    }

    // 验证邮箱格式
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
      setState(() {
        _errorMessage = '请输入有效的邮箱地址格式';
      });
      // 聚焦到邮箱输入框
      FocusScope.of(context).requestFocus(FocusNode());
      return;
    }

    // 显示加载状态
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final result = await ref.read(authStatusProvider.notifier).sendVerificationCode(
        email,
        type: 'login',
      );

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      if (result) {
        // 显示成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(child: Text('验证码已发送至 $email')),
              ],
            ),
            backgroundColor: AppColors.success,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
          ),
        );

        // 开始倒计时
        setState(() {
          _countdown = 60;
        });

        _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
          if (mounted) {
            setState(() {
              if (_countdown > 0) {
                _countdown--;
              } else {
                _timer?.cancel();
              }
            });
          } else {
            timer.cancel();
          }
        });
      } else {
        setState(() {
          _errorMessage = '发送验证码失败，请检查邮箱是否已注册或稍后重试';
        });
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // 解析错误信息，提供更具体的提示
      String errorMessage = '网络错误，请检查网络连接后重试';

      final errorString = e.toString();
      if (errorString.contains('邮箱未注册') || errorString.contains('尚未注册')) {
        errorMessage = '该邮箱尚未注册，请先注册账号或检查邮箱是否正确';
      } else if (errorString.contains('验证码发送过于频繁')) {
        errorMessage = '验证码发送过于频繁，请稍后重试';
      } else if (errorString.contains('邮箱格式')) {
        errorMessage = '邮箱格式不正确，请检查后重试';
      } else if (errorString.contains('已被注册')) {
        errorMessage = '该邮箱已注册，请直接登录';
      }

      setState(() {
        _errorMessage = errorMessage;
      });

      debugPrint('发送验证码失败: $e');
    }
  }

  // 登录
  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 先验证验证码
      final isCodeValid = await ref.read(authStatusProvider.notifier).verifyCode(
        _emailController.text.trim(),
        _codeController.text.trim(),
      );

      if (!isCodeValid) {
        setState(() {
          _errorMessage = '验证码无效或已过期';
          _isLoading = false;
        });
        widget.onLoginResult(false);
        return;
      }

      // 验证码有效，进行登录
      final success = await ref.read(authStatusProvider.notifier).loginWithVerificationCode(
        email: _emailController.text.trim(),
        verificationCode: _codeController.text.trim(),
        rememberMe: _rememberMe,
      );

      if (success) {
        widget.onLoginResult(true);
      } else {
        setState(() {
          _errorMessage = '登录失败，请稍后重试';
          _isLoading = false;
        });
        widget.onLoginResult(false);
      }
    } catch (e) {
      setState(() {
        _errorMessage = '登录失败: $e';
        _isLoading = false;
      });
      widget.onLoginResult(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(height: 8),
          // 邮箱输入框
          TextFormField(
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            decoration: const InputDecoration(
              labelText: '邮箱',
              hintText: '请输入邮箱',
              prefixIcon: Icon(Icons.email),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请输入邮箱';
              }
              if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                return '请输入有效的邮箱';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // 验证码输入框
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: TextFormField(
                  controller: _codeController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: '验证码',
                    hintText: '请输入验证码',
                    prefixIcon: Icon(Icons.security),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入验证码';
                    }
                    if (value.length < 4 || value.length > 6) {
                      return '请输入有效的验证码';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 8),
              SizedBox(
                height: 48,
                child: ElevatedButton(
                  onPressed: _countdown > 0 || _isLoading ? null : _sendVerificationCode,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _countdown > 0 || _isLoading
                        ? Colors.grey[300]
                        : AppColors.primary,
                    foregroundColor: _countdown > 0 || _isLoading
                        ? Colors.grey[600]
                        : Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 0,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (_countdown > 0) ...[
                              const Icon(Icons.timer, size: 16),
                              const SizedBox(width: 4),
                              Text(
                                '${_countdown}s',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ] else ...[
                              const Icon(Icons.send, size: 16),
                              const SizedBox(width: 4),
                              const Text(
                                '获取验证码',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ],
                        ),
                ),
              ),
            ],
          ),
          // 错误信息
          if (_errorMessage != null)
            Container(
              margin: const EdgeInsets.only(top: 8),
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              decoration: BoxDecoration(
                color: Colors.red.withAlpha(25),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                _errorMessage!,
                style: const TextStyle(color: Colors.red),
                textAlign: TextAlign.center,
              ),
            ),

          const SizedBox(height: 64),

          // 登录按钮
          ElevatedButton(
            onPressed: _isLoading ? null : _login,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : const Text('登录'),
          ),
        ],
      ),
    );
  }
}
