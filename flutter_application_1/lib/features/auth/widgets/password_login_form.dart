import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/providers/auth_provider.dart';
import '../../../shared/theme/constants.dart';
import 'rate_limit_dialog.dart';

/// 密码登录表单
class PasswordLoginForm extends ConsumerStatefulWidget {
  final Function(bool) onLoginResult;

  const PasswordLoginForm({
    super.key,
    required this.onLoginResult,
  });

  @override
  ConsumerState<PasswordLoginForm> createState() => _PasswordLoginFormState();
}

class _PasswordLoginFormState extends ConsumerState<PasswordLoginForm> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  final bool _rememberMe = true;
  String? _errorMessage;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  // 登录
  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final success = await ref.read(authStatusProvider.notifier).loginWithPassword(
        email: _emailController.text.trim(),
        password: _passwordController.text.trim(),
        rememberMe: _rememberMe,
      );

      if (success) {
        widget.onLoginResult(true);
      } else {
        setState(() {
          _errorMessage = '邮箱或密码错误';
          _isLoading = false;
        });
        widget.onLoginResult(false);
      }
    } catch (e) {
      String errorMessage = '登录失败';
      final errorString = e.toString();

      if (errorString.contains('429')) {
        // 处理请求过于频繁的错误 - 显示专门的弹窗
        setState(() {
          _isLoading = false;
        });

        String message = '登录尝试次数过多，请稍后再试';
        if (errorString.contains('登录尝试次数过多')) {
          message = '登录尝试次数过多，为了账号安全，请稍后再试';
        }

        // 显示频率限制弹窗
        if (mounted) {
          RateLimitDialog.show(
            context,
            message: message,
            retryAfterSeconds: 60,
          );
        }

        widget.onLoginResult(false);
        return; // 直接返回，不设置错误消息
      } else if (errorString.contains('400')) {
        if (errorString.contains('邮箱或密码错误') || errorString.contains('Invalid credentials')) {
          errorMessage = '邮箱或密码错误，请检查后重试';
        } else if (errorString.contains('账号被锁定') || errorString.contains('locked')) {
          errorMessage = '账号已被锁定，请联系客服';
        } else {
          errorMessage = '登录失败，请检查邮箱和密码';
        }
      } else if (errorString.contains('401')) {
        errorMessage = '邮箱或密码错误，请重新输入';
      } else if (errorString.contains('网络错误') || errorString.contains('network')) {
        errorMessage = '网络连接失败，请检查网络';
      } else if (errorString.contains('timeout')) {
        errorMessage = '请求超时，请重试';
      } else {
        errorMessage = '登录失败，请稍后重试';
      }

      setState(() {
        _errorMessage = errorMessage;
        _isLoading = false;
      });
      widget.onLoginResult(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(height: 8),
          // 邮箱输入框
          TextFormField(
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            decoration: const InputDecoration(
              labelText: '邮箱',
              hintText: '请输入邮箱',
              prefixIcon: Icon(Icons.email),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请输入邮箱';
              }
              if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                return '请输入有效的邮箱';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // 密码输入框
          TextFormField(
            controller: _passwordController,
            obscureText: true,
            decoration: const InputDecoration(
              labelText: '密码',
              hintText: '请输入密码',
              prefixIcon: Icon(Icons.lock_outline),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请输入密码';
              }
              if (value.length < 6) {
                return '密码长度不能少于6位';
              }
              return null;
            },
          ),
          const SizedBox(height: 8),

          // 记住我选项
          Row(
            children: [
              // Checkbox(
              //   value: _rememberMe,
              //   onChanged: (value) {
              //     setState(() {
              //       _rememberMe = value ?? false;
              //     });
              //   },
              // ),
              // const Text('记住我'),
              const Spacer(),
              TextButton(
                onPressed: () {
                  // 跳转到忘记密码页面
                  Navigator.of(context).pushNamed('/auth/forgot-password');
                },
                child: const Text('忘记密码?'),
              ),
            ],
          ),

          // 错误信息
          if (_errorMessage != null)
            Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red.shade700,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: TextStyle(
                        color: Colors.red.shade700,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          const SizedBox(height: 8),

          // 登录按钮
          ElevatedButton(
            onPressed: _isLoading ? null : _login,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : const Text('登录'),
          ),




        ],
      ),
    );
  }
}
