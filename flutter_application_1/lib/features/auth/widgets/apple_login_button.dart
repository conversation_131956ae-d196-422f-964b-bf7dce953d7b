import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/providers/auth_provider.dart';

/// 苹果登录按钮
class AppleLoginButton extends ConsumerStatefulWidget {
  final Function(bool) onLoginResult;

  const AppleLoginButton({
    super.key,
    required this.onLoginResult,
  });

  @override
  ConsumerState<AppleLoginButton> createState() => _AppleLoginButtonState();
}

class _AppleLoginButtonState extends ConsumerState<AppleLoginButton> {
  bool _isLoading = false;

  // 苹果登录
  Future<void> _loginWithApple() async {
    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint('🍎 用户点击Apple登录按钮');

      final success = await ref.read(authStatusProvider.notifier).loginWithApple();

      debugPrint('🍎 Apple登录结果: ${success ? '成功' : '失败'}');

      if (success) {
        debugPrint('✅ Apple登录成功，通知父组件');
        widget.onLoginResult(true);
      } else {
        debugPrint('❌ Apple登录失败，显示错误信息');
        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Apple登录失败，请重试'),
              backgroundColor: Colors.red,
            ),
          );
        }

        widget.onLoginResult(false);
      }
    } catch (e) {
      debugPrint('💥 Apple登录按钮异常: $e');

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        String errorMessage = 'Apple登录失败';

        if (e.toString().contains('ASAuthorizationError')) {
          errorMessage = '用户取消了Apple登录';
        } else if (e.toString().contains('network') || e.toString().contains('DioException')) {
          errorMessage = '网络连接失败，请检查网络';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }

      widget.onLoginResult(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    // 只在iOS平台显示
    if (!Platform.isIOS) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 0),
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : _loginWithApple,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 12),
          backgroundColor: Colors.black,
          foregroundColor: Colors.white,
        ),
        icon: const Icon(Icons.apple),
        label: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : const Text('使用Apple登录'),
      ),
    );
  }
}
