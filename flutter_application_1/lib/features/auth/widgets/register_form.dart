import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/providers/auth_provider.dart';
import '../../../core/services/local_user_service.dart';
import '../../../shared/theme/constants.dart';
import '../../../utils/url_launcher_helper.dart';

/// 注册表单
class RegisterForm extends ConsumerStatefulWidget {
  final Function(bool) onRegisterResult;

  const RegisterForm({
    super.key,
    required this.onRegisterResult,
  });

  @override
  ConsumerState<RegisterForm> createState() => _RegisterFormState();
}

class _RegisterFormState extends ConsumerState<RegisterForm> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _codeController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _nicknameController = TextEditingController();

  bool _isLoading = false;
  bool _agreeToTerms = false;
  String? _errorMessage;

  // 验证码倒计时
  int _countdown = 0;
  Timer? _timer;
  DateTime? _codeRequestTime; // 记录验证码请求时间

  @override
  void initState() {
    super.initState();
    _loadLocalNickname();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _codeController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _nicknameController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  /// 加载本地昵称
  Future<void> _loadLocalNickname() async {
    try {
      final localUserService = LocalUserService();
      final nickname = await localUserService.getLocalNickname();
      if (mounted) {
        _nicknameController.text = nickname;
      }
    } catch (e) {
      // 忽略错误，使用空昵称
    }
  }

  // 发送验证码
  Future<void> _sendVerificationCode({bool forceRetry = false}) async {
    if (_emailController.text.isEmpty) {
      setState(() {
        _errorMessage = '请输入邮箱';
      });
      return;
    }

    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(_emailController.text)) {
      setState(() {
        _errorMessage = '请输入有效的邮箱';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 在发送验证码前，清理可能的缓存问题
      debugPrint('🧹 发送验证码前清理缓存');

      // 如果是强制重试，先清理所有缓存
      if (forceRetry) {
        debugPrint('🚀 强制重试模式，清理所有缓存');
        final authService = ref.read(authServiceProvider);
        await authService.clearVerificationCodeCache();

        // 重置倒计时
        setState(() {
          _countdown = 0;
        });
        _timer?.cancel();
      }

      final success = await ref.read(authStatusProvider.notifier).sendVerificationCode(
        _emailController.text.trim(),
        type: 'register',
      );

      setState(() {
        _isLoading = false;
      });

      if (success) {
        // 显示验证码发送成功提示
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('验证码已发送到您的邮箱，请查收'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }

        // 记录验证码请求时间并开始倒计时
        setState(() {
          _countdown = 60;
          _codeRequestTime = DateTime.now();
        });

        _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
          if (mounted) {
            setState(() {
              if (_countdown > 0) {
                _countdown--;
              } else {
                _timer?.cancel();
              }
            });
          } else {
            timer.cancel();
          }
        });
      } else {
        setState(() {
          _errorMessage = '发送验证码失败，请检查邮箱地址或稍后重试';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      // 解析错误信息，提供更具体的提示
      String errorMessage = '发送验证码失败，请稍后重试';

      final errorString = e.toString();
      if (errorString.contains('已被注册') || errorString.contains('邮箱已注册')) {
        errorMessage = '该邮箱已被注册，请直接登录或使用其他邮箱';
      } else if (errorString.contains('验证码发送过于频繁') || errorString.contains('请求过于频繁') || errorString.contains('429')) {
        errorMessage = '请求过于频繁，请稍后重试。如果问题持续，请重启应用。';

        // 显示特殊提示
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('检测到频率限制，建议重启应用后重试'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 5),
            ),
          );
        }
      } else if (errorString.contains('邮箱格式')) {
        errorMessage = '邮箱格式不正确，请检查后重试';
      } else if (errorString.contains('网络错误')) {
        errorMessage = '网络错误，请检查网络连接后重试';
      }

      setState(() {
        _errorMessage = errorMessage;
      });

      debugPrint('发送验证码失败: $e');
    }
  }

  // 注册
  Future<void> _register() async {
    if (!_formKey.currentState!.validate()) return;

    // 检查是否同意条款
    if (!_agreeToTerms) {
      setState(() {
        _errorMessage = '请先同意服务条款与隐私协议';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 检查验证码是否在有效期内（5分钟）
      if (_codeRequestTime != null) {
        final now = DateTime.now();
        final timeDiff = now.difference(_codeRequestTime!);
        if (timeDiff.inMinutes >= 5) {
          setState(() {
            _errorMessage = '验证码已过期，请重新获取验证码';
            _isLoading = false;
          });
          return;
        }
      }

      // 直接进行注册，让后端验证验证码
      // 生成合适的用户名：如果有昵称就用昵称，否则用邮箱前缀
      String? username;
      final nickname = _nicknameController.text.trim();
      if (nickname.isNotEmpty) {
        username = nickname;
      } else {
        // 使用邮箱前缀，但确保符合用户名规范
        final emailPrefix = _emailController.text.trim().split('@')[0];
        // 移除特殊字符，只保留字母数字
        username = emailPrefix.replaceAll(RegExp(r'[^a-zA-Z0-9]'), '');
        // 如果处理后为空或太短，使用默认格式
        if (username.isEmpty || username.length < 3) {
          username = 'user${DateTime.now().millisecondsSinceEpoch % 100000}';
        }
      }

      final success = await ref.read(authStatusProvider.notifier).register(
        email: _emailController.text.trim(),
        verificationCode: _codeController.text.trim(),
        password: _passwordController.text.trim(),
        nickname: nickname.isNotEmpty ? nickname : null,
        username: username,
        agreeToTerms: _agreeToTerms, // 使用实际的同意条款状态
      );

      if (success) {
        widget.onRegisterResult(true);
      } else {
        setState(() {
          _errorMessage = '注册失败，请稍后重试';
          _isLoading = false;
        });
        widget.onRegisterResult(false);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      // 解析错误信息，提供用户友好的提示
      String errorMessage = '注册失败，请稍后重试';

      final errorString = e.toString();
      if (errorString.contains('400')) {
        if (errorString.contains('验证码验证失败') || errorString.contains('验证码无效') || errorString.contains('验证码')) {
          errorMessage = '验证码无效或已过期，请重新获取验证码';
        } else if (errorString.contains('密码') || errorString.contains('输入验证失败') || errorString.contains('bad syntax')) {
          errorMessage = '密码格式不符合要求，需要6-30位且包含小写字母和大写字母或数字';
        } else if (errorString.contains('邮箱已注册') || errorString.contains('already exists')) {
          errorMessage = '该邮箱已被注册，请直接登录';
        } else if (errorString.contains('用户名') || errorString.contains('username')) {
          errorMessage = '用户名已被使用，请更换用户名';
        } else {
          errorMessage = '注册信息有误，请检查后重试';
        }
      } else if (errorString.contains('网络错误') || errorString.contains('network')) {
        errorMessage = '网络连接失败，请检查网络';
      } else if (errorString.contains('timeout')) {
        errorMessage = '请求超时，请重试';
      }

      setState(() {
        _errorMessage = errorMessage;
      });

      debugPrint('注册失败详细信息: $e');
      widget.onRegisterResult(false);
    }
  }



  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 邮箱输入框
          TextFormField(
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            decoration: const InputDecoration(
              labelText: '邮箱',
              hintText: '请输入邮箱',
              prefixIcon: Icon(Icons.email),
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请输入邮箱';
              }
              if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                return '请输入有效的邮箱';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // 验证码输入框
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: TextFormField(
                  controller: _codeController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: '验证码',
                    hintText: '请输入验证码',
                    prefixIcon: const Icon(Icons.security),
                    border: const OutlineInputBorder(),
                    helperText: _codeRequestTime != null
                        ? '验证码5分钟内有效，请及时使用'
                        : '请先获取验证码',
                    helperStyle: TextStyle(
                      fontSize: 12,
                      color: _codeRequestTime != null
                          ? Colors.orange.shade600
                          : Colors.grey.shade600,
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入验证码';
                    }
                    if (value.length < 4 || value.length > 6) {
                      return '请输入有效的验证码';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 8),
              SizedBox(
                height: 48,
                child: ElevatedButton(
                  onPressed: _countdown > 0 ? null : _sendVerificationCode,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    disabledBackgroundColor: Colors.grey,
                  ),
                  child: Text(_countdown > 0 ? '${_countdown}s' : '获取验证码'),
                ),
              ),
              // 如果出现429错误，显示强制重试按钮
              if (_errorMessage != null && _errorMessage!.contains('请求过于频繁')) ...[
                const SizedBox(width: 8),
                SizedBox(
                  height: 48,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : () => _sendVerificationCode(forceRetry: true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('强制重试'),
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 16),

          // 密码输入框
          TextFormField(
            controller: _passwordController,
            obscureText: true,
            decoration: InputDecoration(
              labelText: '密码',
              hintText: '6-30位，包含小写字母和大写字母或数字',
              prefixIcon: const Icon(Icons.lock_outline),
              border: const OutlineInputBorder(),
              helperText: '密码需包含至少一个小写字母和一个大写字母或数字',
              helperStyle: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请输入密码';
              }
              if (value.length < 6) {
                return '密码至少需要6位字符';
              }
              if (value.length > 30) {
                return '密码不能超过30位字符';
              }
              // 后端规则：必须包含至少一个小写字母和一个大写字母或数字
              if (!RegExp(r'^(?=.*[a-z])(?=.*[A-Z]|.*\d).*$').hasMatch(value)) {
                return '密码需包含至少一个小写字母和一个大写字母或数字';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // 确认密码输入框
          TextFormField(
            controller: _confirmPasswordController,
            obscureText: true,
            decoration: const InputDecoration(
              labelText: '确认密码',
              hintText: '请再次输入密码',
              prefixIcon: Icon(Icons.lock_outline),
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请确认密码';
              }
              if (value != _passwordController.text) {
                return '两次输入的密码不一致';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // 昵称输入框（可选）
          TextFormField(
            controller: _nicknameController,
            decoration: const InputDecoration(
              labelText: '昵称（可选）',
              hintText: '请输入昵称',
              prefixIcon: Icon(Icons.person),
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 8),

          // 同意条款选项
          Row(
            children: [
              Checkbox(
                value: _agreeToTerms,
                onChanged: (value) {
                  setState(() {
                    _agreeToTerms = value ?? false;
                  });
                },
              ),
              Expanded(
                child: Wrap(
                  children: [
                    const Text('我同意'),
                    GestureDetector(
                      onTap: () {
                        UrlLauncherHelper.launchTermsWithErrorHandling(context);
                      },
                      child: const Text(
                        '服务条款',
                        style: TextStyle(
                          color: AppColors.primary,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                    const Text('与'),
                    GestureDetector(
                      onTap: () {
                        UrlLauncherHelper.launchPrivacyWithErrorHandling(context);
                      },
                      child: const Text(
                        '隐私协议',
                        style: TextStyle(
                          color: AppColors.primary,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          // 错误信息
          if (_errorMessage != null)
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red.shade700,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: TextStyle(
                        color: Colors.red.shade700,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          const SizedBox(height: 16),

          // 注册按钮
          ElevatedButton(
            onPressed: _isLoading ? null : _register,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : const Text('注册'),
          ),
        ],
      ),
    );
  }
}
