// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import '../../../core/models/user.dart';
// import '../../../core/services/user_service.dart';
// import '../../../core/utils/storage_utils.dart';
// import '../../../core/utils/event_bus.dart';
// import '../../../core/constants/app_constants.dart';

// /// Apple登录后的用户信息完善页面
// class ProfileCompletionScreen extends ConsumerStatefulWidget {
//   final User user;
//   final VoidCallback onCompleted;

//   const ProfileCompletionScreen({
//     super.key,
//     required this.user,
//     required this.onCompleted,
//   });

//   @override
//   ConsumerState<ProfileCompletionScreen> createState() => _ProfileCompletionScreenState();
// }

// class _ProfileCompletionScreenState extends ConsumerState<ProfileCompletionScreen> {
//   final _formKey = GlobalKey<FormState>();
//   final _usernameController = TextEditingController();
//   final _nicknameController = TextEditingController();
//   bool _isLoading = false;

//   @override
//   void initState() {
//     super.initState();
//     _initializeFields();
//   }

//   void _initializeFields() {
//     // 初始化字段
//     _usernameController.text = widget.user.username;
//     _nicknameController.text = widget.user.nickname;
//   }

//   @override
//   void dispose() {
//     _usernameController.dispose();
//     _nicknameController.dispose();
//     super.dispose();
//   }

//   Future<void> _updateProfile() async {
//     if (!_formKey.currentState!.validate()) {
//       return;
//     }

//     setState(() {
//       _isLoading = true;
//     });

//     try {
//       debugPrint('🔄 开始更新用户信息');
      
//       final userService = UserService();
//       final updatedUser = await userService.updateUserProfile(
//         username: _usernameController.text.trim(),
//         nickname: _nicknameController.text.trim(),
//       );

//       if (updatedUser != null) {
//         debugPrint('✅ 用户信息更新成功');
        
//         // 更新本地存储
//         await StorageUtils.saveUserInfo(updatedUser.toJson().toString());
        
//         // 发送用户信息更新事件
//         eventBus.fire(EventType.userProfileUpdated);
        
//         if (mounted) {
//           ScaffoldMessenger.of(context).showSnackBar(
//             const SnackBar(
//               content: Text('用户信息更新成功'),
//               backgroundColor: Colors.green,
//             ),
//           );
          
//           // 完成设置
//           widget.onCompleted();
//         }
//       }
//     } catch (e) {
//       debugPrint('❌ 更新用户信息失败: $e');
      
//       if (mounted) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(
//             content: Text('更新失败: $e'),
//             backgroundColor: Colors.red,
//           ),
//         );
//       }
//     } finally {
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//         });
//       }
//     }
//   }

//   void _skipForNow() {
//     debugPrint('⏭️ 用户选择跳过信息完善');
//     widget.onCompleted();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: Colors.white,
//       appBar: AppBar(
//         title: const Text('完善个人信息'),
//         backgroundColor: Colors.white,
//         foregroundColor: Colors.black,
//         elevation: 0,
//         automaticallyImplyLeading: false, // 不显示返回按钮
//       ),
//       body: SafeArea(
//         child: Padding(
//           padding: const EdgeInsets.all(24.0),
//           child: Form(
//             key: _formKey,
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 // 说明文本
//                 Container(
//                   padding: const EdgeInsets.all(16),
//                   decoration: BoxDecoration(
//                     color: AppConstants.primaryColor.withOpacity(0.1),
//                     borderRadius: BorderRadius.circular(12),
//                   ),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Row(
//                         children: [
//                           Icon(
//                             Icons.info_outline,
//                             color: AppConstants.primaryColor,
//                             size: 20,
//                           ),
//                           const SizedBox(width: 8),
//                           const Text(
//                             'Apple隐私保护',
//                             style: TextStyle(
//                               fontWeight: FontWeight.bold,
//                               fontSize: 16,
//                             ),
//                           ),
//                         ],
//                       ),
//                       const SizedBox(height: 8),
//                       const Text(
//                         '您选择了Apple的隐私保护功能，我们为您生成了默认的用户名。您可以在这里自定义您的用户名和昵称。',
//                         style: TextStyle(
//                           fontSize: 14,
//                           color: Colors.grey,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
                
//                 const SizedBox(height: 32),
                
//                 // 用户名输入
//                 const Text(
//                   '用户名',
//                   style: TextStyle(
//                     fontSize: 16,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//                 const SizedBox(height: 8),
//                 TextFormField(
//                   controller: _usernameController,
//                   decoration: InputDecoration(
//                     hintText: '请输入用户名',
//                     border: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(12),
//                     ),
//                     prefixIcon: const Icon(Icons.person_outline),
//                   ),
//                   validator: (value) {
//                     if (value == null || value.trim().isEmpty) {
//                       return '请输入用户名';
//                     }
//                     if (value.trim().length < 2) {
//                       return '用户名至少需要2个字符';
//                     }
//                     if (value.trim().length > 20) {
//                       return '用户名不能超过20个字符';
//                     }
//                     return null;
//                   },
//                 ),
                
//                 const SizedBox(height: 24),
                
//                 // 昵称输入
//                 const Text(
//                   '昵称',
//                   style: TextStyle(
//                     fontSize: 16,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//                 const SizedBox(height: 8),
//                 TextFormField(
//                   controller: _nicknameController,
//                   decoration: InputDecoration(
//                     hintText: '请输入昵称（用于显示）',
//                     border: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(12),
//                     ),
//                     prefixIcon: const Icon(Icons.badge_outlined),
//                   ),
//                   validator: (value) {
//                     if (value == null || value.trim().isEmpty) {
//                       return '请输入昵称';
//                     }
//                     if (value.trim().length < 1) {
//                       return '昵称至少需要1个字符';
//                     }
//                     if (value.trim().length > 20) {
//                       return '昵称不能超过20个字符';
//                     }
//                     return null;
//                   },
//                 ),
                
//                 const SizedBox(height: 32),
                
//                 // 邮箱显示（只读）
//                 const Text(
//                   '邮箱',
//                   style: TextStyle(
//                     fontSize: 16,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//                 const SizedBox(height: 8),
//                 Container(
//                   width: double.infinity,
//                   padding: const EdgeInsets.all(16),
//                   decoration: BoxDecoration(
//                     color: Colors.grey[100],
//                     borderRadius: BorderRadius.circular(12),
//                     border: Border.all(color: Colors.grey[300]!),
//                   ),
//                   child: Row(
//                     children: [
//                       const Icon(Icons.email_outlined, color: Colors.grey),
//                       const SizedBox(width: 12),
//                       Expanded(
//                         child: Text(
//                           widget.user.email,
//                           style: const TextStyle(
//                             fontSize: 16,
//                             color: Colors.grey,
//                           ),
//                         ),
//                       ),
//                       if (widget.user.email.contains('privaterelay.appleid.com'))
//                         Container(
//                           padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
//                           decoration: BoxDecoration(
//                             color: AppConstants.primaryColor.withOpacity(0.1),
//                             borderRadius: BorderRadius.circular(8),
//                           ),
//                           child: Text(
//                             '隐私保护',
//                             style: TextStyle(
//                               fontSize: 12,
//                               color: AppConstants.primaryColor,
//                               fontWeight: FontWeight.bold,
//                             ),
//                           ),
//                         ),
//                     ],
//                   ),
//                 ),
                
//                 const Spacer(),
                
//                 // 按钮
//                 Column(
//                   children: [
//                     SizedBox(
//                       width: double.infinity,
//                       height: 50,
//                       child: ElevatedButton(
//                         onPressed: _isLoading ? null : _updateProfile,
//                         style: ElevatedButton.styleFrom(
//                           backgroundColor: AppConstants.primaryColor,
//                           foregroundColor: Colors.white,
//                           shape: RoundedRectangleBorder(
//                             borderRadius: BorderRadius.circular(12),
//                           ),
//                         ),
//                         child: _isLoading
//                             ? const SizedBox(
//                                 width: 20,
//                                 height: 20,
//                                 child: CircularProgressIndicator(
//                                   strokeWidth: 2,
//                                   valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
//                                 ),
//                               )
//                             : const Text(
//                                 '保存设置',
//                                 style: TextStyle(
//                                   fontSize: 16,
//                                   fontWeight: FontWeight.bold,
//                                 ),
//                               ),
//                       ),
//                     ),
                    
//                     const SizedBox(height: 12),
                    
//                     SizedBox(
//                       width: double.infinity,
//                       height: 50,
//                       child: TextButton(
//                         onPressed: _isLoading ? null : _skipForNow,
//                         style: TextButton.styleFrom(
//                           foregroundColor: Colors.grey,
//                           shape: RoundedRectangleBorder(
//                             borderRadius: BorderRadius.circular(12),
//                           ),
//                         ),
//                         child: const Text(
//                           '暂时跳过',
//                           style: TextStyle(
//                             fontSize: 16,
//                           ),
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }
