// 导入Cupertino库
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/models/goal_milestone.dart';
import '../widgets/subject_project_card_riverpod.dart';

import '../widgets/goal_edit_sheet.dart';
import '../widgets/milestone_edit_sheet.dart';
import '../widgets/timeline_item.dart';
import '../widgets/timeline_container.dart';
import '../providers/goal_state.dart';
import '../providers/task_tab_state_provider.dart';
import '../../../core/services/hive_service.dart';

/// 目标页面 - Riverpod版本
/// 显示用户的目标和里程碑信息，或科目和项目信息，使用Riverpod进行状态管理
class TaskGoalScreenRiverpod extends ConsumerStatefulWidget {
  const TaskGoalScreenRiverpod({super.key});

  @override
  ConsumerState<TaskGoalScreenRiverpod> createState() => _TaskGoalScreenRiverpodState();
}

class _TaskGoalScreenRiverpodState extends ConsumerState<TaskGoalScreenRiverpod> {
  // Hive服务
  final HiveService _hiveService = HiveService();
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    // 使用 Future.microtask 确保在构建完成后加载数据
    Future.microtask(() {
      if (mounted) {
        _loadData();
      }
    });
  }

  // 从Hive加载数据
  Future<void> _loadData() async {
    if (!mounted) return; // 确保组件仍然挂载

    try {
      // 使用 Future.microtask 确保不在构建生命周期中直接修改状态
      Future.microtask(() {
        if (mounted) {
          // 先设置加载状态
          ref.read(goalStateProvider.notifier).setLoading(true);
        }
      });

      // 确保Hive服务已初始化
      await _hiveService.initHive();

      // 获取所有非归档目标
      final goals = _hiveService.goalRepository.getAllGoals(includeArchived: false);

      if (!mounted) return; // 再次检查组件是否仍然挂载

      // 使用 Future.microtask 确保不在构建生命周期中直接修改状态
      Future.microtask(() {
        if (!mounted) return;

        // 如果有目标，设置第一个目标为当前目标
        if (goals.isNotEmpty) {
          final currentGoal = _hiveService.goalRepository.getGoalWithMilestones(goals.first.id);
          if (currentGoal != null && mounted) {
            ref.read(goalStateProvider.notifier).setCurrentGoal(currentGoal);
            ref.read(goalStateProvider.notifier).setMilestones(currentGoal.milestones);
          }
        }

        // 设置加载完成
        ref.read(goalStateProvider.notifier).setLoading(false);
        setState(() {
          _isInitialized = true;
        });
      });
    } catch (e) {
      debugPrint('加载数据出错: $e');

      // 使用 Future.microtask 确保不在构建生命周期中直接修改状态
      Future.microtask(() {
        if (mounted) {
          ref.read(goalStateProvider.notifier).setError('加载数据失败: $e');
          ref.read(goalStateProvider.notifier).setLoading(false);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // 监听目标状态和tab状态
    final goalState = ref.watch(goalStateProvider);
    final selectedTabIndex = ref.watch(taskTabStateProvider);

    // 如果正在加载，显示加载指示器
    if (goalState.isLoading && !_isInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    // 如果有错误，显示错误信息
    if (goalState.error != null) {
      // 不在build方法中修改状态，而是在显示错误后通过按钮让用户手动清除
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('错误: ${goalState.error}', textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // 使用 Future.microtask 确保不在构建生命周期中直接修改状态
                Future.microtask(() {
                  if (mounted) {
                    ref.read(goalStateProvider.notifier).setError(null);
                    // 重新加载数据
                    _loadData();
                  }
                });
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    return Scaffold(
      // 使用渐变背景，与应用其他页面保持一致
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color.fromARGB(255, 204, 255, 229), // 顶部颜色
              Color.fromARGB(255, 255, 250, 240), // 底部颜色
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0), // 调整为仅水平padding
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16), // 调整顶部间距
                // 标题
                const Text(
                  '目标管理',
                  style: AppTextStyles.headline2,
                ),
                const SizedBox(height: 16),

                // 简洁的滑块式Tab切换按钮 - 优化色彩搭配
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16.0),
                  height: 44, // 适当的高度
                  decoration: BoxDecoration(
                    color: Colors.white, // 使用白色背景，更加明亮
                    borderRadius: BorderRadius.circular(10.0),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(15), // 稍微增强阴影效果
                        blurRadius: 2,
                        spreadRadius: 0,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      // 目标Tab
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            ref.read(taskTabStateProvider.notifier).setTabIndex(0);
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: selectedTabIndex == 0
                                  ? AppColors.primary.withAlpha(AppColors.alpha30) // 进一步增加透明度，使颜色更明显
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            margin: const EdgeInsets.all(3.0),
                            child: Center(
                              child: Text(
                                '目标',
                                style: TextStyle(
                                  fontSize: 15, // 稍微增大字体
                                  fontWeight: selectedTabIndex == 0
                                      ? FontWeight.w600
                                      : FontWeight.w500,
                                  color: selectedTabIndex == 0
                                      ? AppColors.primary
                                      : AppColors.text, // 使用主文本色而非次要文本色
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),

                      // 项目Tab
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            ref.read(taskTabStateProvider.notifier).setTabIndex(1);
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: selectedTabIndex == 1
                                  ? AppColors.primary.withAlpha(AppColors.alpha30) // 进一步增加透明度，使颜色更明显
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            margin: const EdgeInsets.all(3.0),
                            child: Center(
                              child: Text(
                                '项目',
                                style: TextStyle(
                                  fontSize: 15, // 稍微增大字体
                                  fontWeight: selectedTabIndex == 1
                                      ? FontWeight.w600
                                      : FontWeight.w500,
                                  color: selectedTabIndex == 1
                                      ? AppColors.primary
                                      : AppColors.text, // 使用主文本色而非次要文本色
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // 根据选择显示内容 - 使用Expanded和SingleChildScrollView确保内容可滚动
                Expanded(
                  child: SingleChildScrollView(
                    child: _buildSelectedContent(goalState, selectedTabIndex),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 根据选择的分段构建内容
  Widget _buildSelectedContent(GoalState goalState, int selectedTabIndex) {
    if (selectedTabIndex == 0) {
      // 显示目标与里程碑
      return goalState.currentGoal == null
          ? _buildEmptyGoalCard()
          : _buildGoalCard(goalState.currentGoal!, goalState.milestones);
    } else {
      // 显示科目与项目
      return const SubjectProjectCardRiverpod();
    }
  }

  /// 构建空目标卡片
  /// 引导用户创建第一个目标
  Widget _buildEmptyGoalCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [AppShadows.low],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            "目标 & 里程碑",
            style: AppTextStyles.headline3,
          ),
          const SizedBox(height: 24),
          Icon(
            Icons.flag_outlined,
            size: 64,
            color: AppColors.primary.withAlpha(AppColors.alpha50),
          ),
          const SizedBox(height: 16),
          const Text(
            "暂无目标",
            style: AppTextStyles.headline3,
          ),
          const SizedBox(height: 8),
          const Text(
            "设置一个目标来追踪您的进度",
            style: AppTextStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: const Text("创建目标"),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () => _showCreateGoalDialog(),
          ),
        ],
      ),
    );
  }

  /// 显示创建目标对话框
  void _showCreateGoalDialog() {
    if (!mounted) return; // 确保组件仍然挂载

    // 创建一个空目标
    final newGoal = Goal(
      id: const Uuid().v4(),
      name: '',
      startDate: DateTime.now(),
      endDate: DateTime.now().add(const Duration(days: 365)),
    );

    // 保存当前上下文
    final BuildContext currentContext = context;

    // 显示编辑目标弹窗
    showModalBottomSheet(
      context: currentContext,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => GoalEditSheet(
        goal: newGoal,
        onGoalEdited: (editedGoal) async {
          try {
            // 保存到Hive
            await _hiveService.goalRepository.saveGoal(editedGoal);

            // 确保组件仍然挂载
            if (!mounted) return;

            // 更新Riverpod状态
            ref.read(goalStateProvider.notifier).setCurrentGoal(editedGoal);
            ref.read(goalStateProvider.notifier).setMilestones([]);
          } catch (e) {
            debugPrint('保存目标出错: $e');
          }
        },
      ),
    );
  }

  /// 构建目标卡片
  /// 显示用户设定的目标与进度，以及里程碑
  Widget _buildGoalCard(Goal currentGoal, List<Milestone> milestones) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [AppShadows.low],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏 - 优化边距和间距
          Padding(
            padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 16.0), // 增加底部边距为16，与下方元素保持更大间距
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "目标",
                  style: AppTextStyles.headline3.copyWith(
                    fontSize: 18,
                  ),
                ),
                Container(
                  height: 32, // 保持高度一致
                  width: 32, // 保持宽度一致
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8), // 保持圆角一致
                    color: AppColors.background, // 使用背景色，不使用边框
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.edit_outlined, size: 16),
                    tooltip: "编辑目标",
                    color: AppColors.textSecondary,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    onPressed: () {
                      if (!mounted) return; // 确保组件仍然挂载

                      // 保存当前上下文
                      final BuildContext currentContext = context;

                      // 直接显示编辑目标弹窗
                      showModalBottomSheet(
                        context: currentContext,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder: (context) => GoalEditSheet(
                          goal: currentGoal,
                          onGoalEdited: (editedGoal) async {
                            try {
                              // 保存到Hive
                              await _hiveService.goalRepository.saveGoal(editedGoal);

                              // 确保组件仍然挂载
                              if (!mounted) return;

                              // 更新Riverpod状态
                              ref.read(goalStateProvider.notifier).setCurrentGoal(editedGoal);
                            } catch (e) {
                              debugPrint('保存编辑的目标出错: $e');
                            }
                          },
                        ),
                      );
                    },
                ),
                )
              ],
            ),
          ),
          // 总目标进度
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: _buildMainProgressSection(
              currentGoal.name,
              currentGoal.progress,
              currentGoal.startDate.toString().substring(0, 10),
              currentGoal.endDate.toString().substring(0, 10),
              "",
              "",
              true,
            ),
          ),

          const SizedBox(height: 24),

          // 时间轴
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: _buildTimelineSection(currentGoal, milestones),
          ),
        ],
      ),
    );
  }

  /// 构建时间轴部分 - 重新设计的竖向时间轴
  Widget _buildTimelineSection(Goal goal, List<Milestone> milestones) {
    // 标题部分
    final timelineHeader = Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          "里程碑",
          style: AppTextStyles.headline3.copyWith(
            fontSize: 18,
          ),
        ),
        Container(
          height: 32, // 与目标编辑按钮保持一致
          width: 32, // 与目标编辑按钮保持一致
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8), // 与目标编辑按钮保持一致
            color: AppColors.background, // 与目标编辑按钮保持一致
          ),
          child: IconButton(
            icon: const Icon(Icons.settings_outlined, size: 16), // 增加图标大小
            tooltip: "管理里程碑",
            color: AppColors.textSecondary,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            onPressed: () {
              // 验证是否已创建总目标
              final currentGoal = ref.read(goalStateProvider).currentGoal;
              if (currentGoal == null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('请先创建总目标，然后再添加里程碑'),
                    duration: Duration(seconds: 2),
                  ),
                );
                return;
              }

              // 显示里程碑管理底部弹窗
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor: Colors.white,
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                ),
                builder: (context) => _buildMilestoneManagementSheet(goal, milestones),
              );
            },
          ),
        )
      ],
    );

    // 如果没有里程碑，显示空状态
    if (milestones.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          timelineHeader,
          const SizedBox(height: 24),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            margin: const EdgeInsets.only(bottom: 16), // 添加底部间距，避免与卡片底部重叠
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(AppColors.alpha10),
                  blurRadius: 2,
                  spreadRadius: 0,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Column(
              children: [
                Icon(
                  Icons.flag_outlined,
                  size: 48,
                  color: AppColors.textHint,
                ),
                const SizedBox(height: 16),
                const Text(
                  "暂无里程碑",
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  "添加里程碑来规划您的目标进度",
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textTertiary,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                // 添加引导式按钮
                ElevatedButton.icon(
                  icon: const Icon(Icons.add, size: 18),
                  label: const Text("添加第一个里程碑"),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 1, // 降低阴影
                  ),
                  onPressed: () {
                    // 检查里程碑数量是否已达到上限(7个)
                    if (milestones.length >= 7) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('里程碑数量已达到上限(7个)，请先删除部分里程碑再添加新的'),
                          duration: Duration(seconds: 3),
                        ),
                      );
                      return;
                    }

                    // 直接显示底部弹窗，不使用 Future.delayed
                    final BuildContext currentContext = context;
                    showModalBottomSheet(
                      context: currentContext,
                      isScrollControlled: true,
                      backgroundColor: Colors.transparent,
                      builder: (context) => MilestoneEditSheet(
                        goalId: goal.id,
                        goal: goal,
                        onMilestoneSaved: (newMilestone) async {
                          try {
                            // 保存到Hive
                            await _hiveService.goalRepository.saveMilestone(newMilestone);

                            // 使用 Future.microtask 确保不在构建生命周期中直接修改状态
                            Future.microtask(() {
                              if (mounted) {
                                // 更新Riverpod状态
                                ref.read(goalStateProvider.notifier).addMilestone(newMilestone);

                                // 更新当前目标的里程碑列表
                                final updatedGoal = _hiveService.goalRepository.getGoalWithMilestones(goal.id);
                                if (updatedGoal != null) {
                                  ref.read(goalStateProvider.notifier).setCurrentGoal(updatedGoal);
                                }
                              }
                            });
                          } catch (e) {
                            // 使用日志记录错误，而不是print
                            debugPrint('保存里程碑出错: $e');
                          }
                        },
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      );
    }

    // 对里程碑进行排序，按日期从早到晚
    final sortedMilestones = List<Milestone>.from(milestones)
      ..sort((a, b) => a.date.compareTo(b.date));

    // 计算时间范围
    final startDate = goal.startDate;
    final endDate = goal.endDate;
    final totalDays = endDate.difference(startDate).inDays;
    final now = DateTime.now();

    // 找出下一个里程碑
    final nextMilestone = sortedMilestones
        .where((m) => m.date.isAfter(now))
        .fold<Milestone?>(
          null,
          (prev, curr) => prev == null || curr.date.isBefore(prev.date) ? curr : prev,
        );

    // 创建时间轴项目列表
    List<Widget> timelineItems = [];

    // 添加开始日期节点
    timelineItems.add(
      _buildTimelineItem(
        date: startDate,
        title: "开始日期",
        subtitle: "",
        isStart: true,
        isEnd: false,
        isPast: true,
        statusColor: AppColors.primary,
      ),
    );

    // 添加里程碑节点
    for (int i = 0; i < sortedMilestones.length; i++) {
      final milestone = sortedMilestones[i];
      final isPast = milestone.date.isBefore(now);
      final isNext = milestone == nextMilestone;

      final statusColor = isPast
          ? AppColors.success
          : isNext
              ? AppColors.warning
              : AppColors.primary;

      final statusText = _getMilestoneStatus(milestone, sortedMilestones);

      timelineItems.add(
        _buildTimelineItem(
          date: milestone.date,
          title: milestone.name,
          subtitle: statusText,
          isStart: false,
          isEnd: false,
          isPast: isPast,
          isNext: isNext,
          statusColor: statusColor,
        ),
      );
    }

    // 添加结束日期节点
    timelineItems.add(
      _buildTimelineItem(
        date: endDate,
        title: "结束日期",
        subtitle: "",
        isStart: false,
        isEnd: true,
        isPast: false,
        statusColor: AppColors.textSecondary,
      ),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        timelineHeader,
        const SizedBox(height: 20),

        // 使用新的时间轴容器组件
        TimelineContainer(
          timelineItems: [
            // 开始日期节点
            TimelineItem(
              date: startDate,
              title: "开始日期",
              subtitle: "",
              isStart: true,
              isEnd: false,
              isPast: true,
              isNext: false,
              statusColor: AppColors.primary,
              isLastItem: sortedMilestones.isEmpty && endDate.difference(startDate).inDays < 7,
            ),

            // 里程碑节点
            ...List.generate(sortedMilestones.length, (i) {
              final milestone = sortedMilestones[i];
              final isPast = milestone.date.isBefore(now);
              final isNext = milestone == nextMilestone;

              // 判断是否为最后一个项目
              final bool isLastMilestone = i == sortedMilestones.length - 1;
              final bool noEndDateNode = isLastMilestone &&
                                        endDate.difference(milestone.date).inDays < 7;
              final isLastItem = isLastMilestone && noEndDateNode;

              final statusColor = isPast
                  ? AppColors.success
                  : isNext
                      ? AppColors.warning
                      : AppColors.primary;

              final statusText = _getMilestoneStatus(milestone, sortedMilestones);

              return TimelineItem(
                date: milestone.date,
                title: milestone.name,
                subtitle: statusText,
                isStart: false,
                isEnd: false,
                isPast: isPast,
                isNext: isNext,
                statusColor: statusColor,
                isLastItem: isLastItem,
              );
            }),

            // 结束日期节点 - 始终显示
            TimelineItem(
              date: endDate,
              title: "结束日期",
              subtitle: "",
              isStart: false,
              isEnd: true,
              isPast: false,
              isNext: false,
              statusColor: AppColors.textSecondary,
              isLastItem: true,
            ),
          ],
          totalDays: totalDays,
        ),

        // 底部间距
        const SizedBox(height: 16),
      ],
    );
  }

  /// 构建时间轴项目 - 简化版，更稳定的布局
  Widget _buildTimelineItem({
    required DateTime date,
    required String title,
    required String subtitle,
    required bool isStart,
    required bool isEnd,
    bool isPast = false,
    bool isNext = false,
    required Color statusColor,
  }) {
    // 确定节点样式
    final bool isFilledNode = isPast;

    // 节点颜色
    final Color nodeColor = isPast
        ? AppColors.success
        : isNext
            ? AppColors.warning
            : AppColors.primary;

    return Container(
      height: 80, // 增加高度，避免内容溢出
      margin: const EdgeInsets.only(bottom: 0), // 移除底部间距
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start, // 顶部对齐所有元素
        children: [
          // 左侧日期 - 分为两行显示
          Container(
            width: 50,
            padding: const EdgeInsets.only(left: 8, top: 8), // 添加顶部间距
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // 月-日
                Text(
                  "${date.month}-${date.day.toString().padLeft(2, '0')}",
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.right,
                ),
                // 年份
                Text(
                  "${date.year}",
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textTertiary,
                    fontSize: 10,
                  ),
                  textAlign: TextAlign.right,
                ),
              ],
            ),
          ),

          // 中间时间轴节点
          Container(
            width: 30,
            padding: const EdgeInsets.only(top: 8), // 添加顶部间距，与卡片顶部对齐
            alignment: Alignment.topCenter, // 顶部对齐
            child: Stack(
              alignment: Alignment.center,
              children: [
                // 白色背景圆圈，确保完全覆盖线条
                Container(
                  width: 24,
                  height: 24,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
                // 实际的圆点
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: isFilledNode ? nodeColor : Colors.white,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: nodeColor,
                      width: 2,
                    ),
                  ),
                  child: isPast && !isStart && !isEnd
                      ? Icon(Icons.check, size: 10, color: Colors.white)
                      : null,
                ),
              ],
            ),
          ),

          // 右侧内容卡片
          Expanded(
            child: Container(
              margin: const EdgeInsets.only(top: 8, right: 16), // 添加顶部间距
              padding: isStart || isEnd
                  ? EdgeInsets.zero
                  : const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: isStart || isEnd
                  ? null
                  : BoxDecoration(
                      color: statusColor.withAlpha(AppColors.alpha10),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: statusColor.withAlpha(AppColors.alpha20),
                        width: 1,
                      ),
                    ),
              // 使用SingleChildScrollView避免内容溢出
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: 40, // 最小高度
                  maxHeight: 60, // 最大高度，避免溢出
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min, // 尽可能小
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题
                    Text(
                      title,
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isStart || isEnd ? statusColor : AppColors.text,
                      ),
                      maxLines: 1, // 限制最大行数
                      overflow: TextOverflow.ellipsis, // 溢出时显示省略号
                    ),

                    // 副标题（状态）- 只对里程碑显示
                    if (subtitle.isNotEmpty && !isStart && !isEnd) ...[
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            color: statusColor.withAlpha(AppColors.alpha30),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          subtitle,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: statusColor,
                          ),
                          maxLines: 1, // 限制最大行数
                          overflow: TextOverflow.ellipsis, // 溢出时显示省略号
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainProgressSection(
    String title,
    double progress,
    String leftValue,
    String rightValue,
    String leftLabel,
    String rightLabel,
    bool isMainGoal,
    {bool isPast = false}
  ) {
    final Color progressColor = isPast ? AppColors.success : AppColors.primary;
    // 计算剩余天数
    final DateTime now = DateTime.now();
    final DateTime endDate = DateTime.parse(rightValue);
    final int remainingDays = endDate.difference(now).inDays;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题和进度百分比
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 标题
            Expanded(
              child: Text(
                title,
                style: isMainGoal
                  ? AppTextStyles.headline1.copyWith(
                      fontSize: 26,
                      fontWeight: FontWeight.bold,
                    )
                  : AppTextStyles.bodyLarge.copyWith(fontWeight: FontWeight.w600),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // 进度百分比
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: progressColor.withAlpha(AppColors.alpha10),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                "${(progress * 100).toInt()}%",
                style: TextStyle(
                  fontSize: isMainGoal ? 18 : 14,
                  fontWeight: FontWeight.bold,
                  color: progressColor,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // 进度条
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 进度条标签
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "总体进度",
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  "距离目标还有 $remainingDays 天",
                  style: AppTextStyles.bodySmall.copyWith(
                    color: progressColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // 进度条
            Stack(
              children: [
                // 背景进度条
                Container(
                  height: isMainGoal ? 10 : 8,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: const Color(0xFFEEEEEE),
                    borderRadius: BorderRadius.circular(5),
                  ),
                ),
                // 主目标进度条
                FractionallySizedBox(
                  widthFactor: progress,
                  child: Container(
                    height: isMainGoal ? 10 : 8,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          progressColor.withAlpha(AppColors.alpha70),
                          progressColor,
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                      borderRadius: BorderRadius.circular(5),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 20),

        // 日期信息
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.background,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.divider),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 左侧信息 - 开始日期
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "开始日期",
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    leftValue,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.text,
                    ),
                  ),
                ],
              ),

              // 中间分隔
              Container(
                height: 40,
                width: 1,
                color: AppColors.divider,
              ),

              // 右侧信息 - 结束日期
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    "结束日期",
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    rightValue,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.text,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建里程碑管理底部弹窗
  Widget _buildMilestoneManagementSheet(Goal goal, List<Milestone> milestones) {
    // 对里程碑进行排序，按日期从早到晚
    final sortedMilestones = List<Milestone>.from(milestones)
      ..sort((a, b) => a.date.compareTo(b.date));

    return StatefulBuilder(
      builder: (context, setState) {
        return Container(
          padding: const EdgeInsets.only(top: 8, bottom: 24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 顶部拖动条
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // 标题
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      "管理里程碑",
                      style: AppTextStyles.headline3,
                    ),
                    // 关闭按钮
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 8),
              const Divider(),

              // 里程碑列表
              if (sortedMilestones.isEmpty)
                Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      Icon(
                        Icons.flag_outlined,
                        size: 48,
                        color: AppColors.textHint,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        "暂无里程碑",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        "添加里程碑来规划您的目标进度",
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textTertiary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      // 添加引导式按钮
                      ElevatedButton.icon(
                        icon: const Icon(Icons.add),
                        label: const Text("添加第一个里程碑"),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onPressed: () {
                          // 检查里程碑数量是否已达到上限(7个)
                          if (sortedMilestones.length >= 7) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('里程碑数量已达到上限(7个)，请先删除部分里程碑再添加新的'),
                                duration: Duration(seconds: 3),
                              ),
                            );
                            return;
                          }

                          // 关闭当前弹窗
                          Navigator.pop(context);
                          // 显示添加里程碑弹窗
                          final BuildContext currentContext = context;
                          showModalBottomSheet(
                            context: currentContext,
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            builder: (context) => MilestoneEditSheet(
                              goalId: goal.id,
                              goal: goal,
                              onMilestoneSaved: (newMilestone) async {
                                try {
                                  // 保存到Hive
                                  await _hiveService.goalRepository.saveMilestone(newMilestone);

                                  // 使用 Future.microtask 确保不在构建生命周期中直接修改状态
                                  Future.microtask(() {
                                    if (mounted) {
                                      // 更新Riverpod状态
                                      ref.read(goalStateProvider.notifier).addMilestone(newMilestone);

                                      // 更新当前目标的里程碑列表
                                      final updatedGoal = _hiveService.goalRepository.getGoalWithMilestones(goal.id);
                                      if (updatedGoal != null) {
                                        ref.read(goalStateProvider.notifier).setCurrentGoal(updatedGoal);
                                      }
                                    }
                                  });
                                } catch (e) {
                                  // 使用日志记录错误，而不是print
                                  debugPrint('保存里程碑出错: $e');
                                }
                              },
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                )
              else
                Flexible(
                  child: ListView.separated(
                    shrinkWrap: true,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    itemCount: sortedMilestones.length,
                    separatorBuilder: (context, index) => const Divider(),
                    itemBuilder: (context, index) {
                      final milestone = sortedMilestones[index];
                      final statusText = _getMilestoneStatus(milestone, sortedMilestones);
                      final now = DateTime.now();
                      final isPast = milestone.date.isBefore(now);

                      return ListTile(
                        contentPadding: EdgeInsets.zero,
                        title: Text(
                          milestone.name,
                          style: AppTextStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        subtitle: Text(
                          "${milestone.date.year}-${milestone.date.month.toString().padLeft(2, '0')}-${milestone.date.day.toString().padLeft(2, '0')} · $statusText",
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                        leading: Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: isPast ? AppColors.success.withAlpha(AppColors.alpha10) : AppColors.primary.withAlpha(AppColors.alpha10),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            isPast ? Icons.check : Icons.flag_outlined,
                            size: 16,
                            color: isPast ? AppColors.success : AppColors.primary,
                          ),
                        ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // 编辑按钮
                            IconButton(
                              icon: const Icon(Icons.edit_outlined, size: 20),
                              onPressed: () {
                                // 关闭当前弹窗
                                Navigator.pop(context);
                                // 显示编辑里程碑弹窗
                                final BuildContext currentContext = context;
                                showModalBottomSheet(
                                  context: currentContext,
                                  isScrollControlled: true,
                                  backgroundColor: Colors.transparent,
                                  builder: (context) => MilestoneEditSheet(
                                    milestone: milestone,
                                    goalId: goal.id,
                                    goal: goal,
                                    onMilestoneSaved: (editedMilestone) async {
                                      try {
                                        // 保存到Hive
                                        await _hiveService.goalRepository.saveMilestone(editedMilestone);

                                        // 使用 Future.microtask 确保不在构建生命周期中直接修改状态
                                        Future.microtask(() {
                                          if (mounted) {
                                            // 更新Riverpod状态
                                            ref.read(goalStateProvider.notifier).updateMilestone(editedMilestone);

                                            // 更新当前目标的里程碑列表
                                            final updatedGoal = _hiveService.goalRepository.getGoalWithMilestones(goal.id);
                                            if (updatedGoal != null) {
                                              ref.read(goalStateProvider.notifier).setCurrentGoal(updatedGoal);
                                            }
                                          }
                                        });
                                      } catch (e) {
                                        // 使用日志记录错误，而不是print
                                        debugPrint('更新里程碑出错: $e');
                                      }
                                    },
                                  ),
                                );
                              },
                            ),
                            // 删除按钮
                            IconButton(
                              icon: const Icon(Icons.delete_outline, size: 20),
                              onPressed: () {
                                // 显示确认对话框
                                final BuildContext currentContext = context;
                                showDialog(
                                  context: currentContext,
                                  builder: (dialogContext) => AlertDialog(
                                    title: const Text("删除里程碑"),
                                    content: Text("确定要删除里程碑\"${milestone.name}\"吗？"),
                                    actions: [
                                      TextButton(
                                        child: const Text("取消"),
                                        onPressed: () => Navigator.pop(dialogContext),
                                      ),
                                      TextButton(
                                        child: const Text("删除", style: TextStyle(color: Colors.red)),
                                        onPressed: () async {
                                          try {
                                            // 关闭对话框
                                            Navigator.pop(dialogContext);

                                            // 从Hive删除
                                            await _hiveService.goalRepository.deleteMilestone(milestone.id);

                                            // 使用 Future.microtask 确保不在构建生命周期中直接修改状态
                                            Future.microtask(() {
                                              if (mounted) {
                                                // 更新Riverpod状态
                                                ref.read(goalStateProvider.notifier).deleteMilestone(milestone.id);

                                                // 更新当前目标的里程碑列表
                                                final updatedGoal = _hiveService.goalRepository.getGoalWithMilestones(goal.id);
                                                if (updatedGoal != null) {
                                                  ref.read(goalStateProvider.notifier).setCurrentGoal(updatedGoal);
                                                }

                                                // 更新底部弹窗状态
                                                setState(() {
                                                  sortedMilestones.remove(milestone);
                                                });
                                              }
                                            });
                                          } catch (e) {
                                            // 使用日志记录错误，而不是print
                                            debugPrint('删除里程碑出错: $e');
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),

              // 当有里程碑时，在底部添加一个添加按钮
              if (sortedMilestones.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.add),
                    label: const Text("添加新里程碑"),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onPressed: () {
                      // 检查里程碑数量是否已达到上限(7个)
                      if (sortedMilestones.length >= 7) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('里程碑数量已达到上限(7个)，请先删除部分里程碑再添加新的'),
                            duration: Duration(seconds: 3),
                          ),
                        );
                        return;
                      }

                      // 关闭当前弹窗
                      Navigator.pop(context);
                      // 显示添加里程碑弹窗
                      final BuildContext currentContext = context;
                      showModalBottomSheet(
                        context: currentContext,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder: (context) => MilestoneEditSheet(
                          goalId: goal.id,
                          goal: goal,
                          onMilestoneSaved: (newMilestone) async {
                            try {
                              // 保存到Hive
                              await _hiveService.goalRepository.saveMilestone(newMilestone);

                              // 使用 Future.microtask 确保不在构建生命周期中直接修改状态
                              Future.microtask(() {
                                if (mounted) {
                                  // 更新Riverpod状态
                                  ref.read(goalStateProvider.notifier).addMilestone(newMilestone);

                                  // 更新当前目标的里程碑列表
                                  final updatedGoal = _hiveService.goalRepository.getGoalWithMilestones(goal.id);
                                  if (updatedGoal != null) {
                                    ref.read(goalStateProvider.notifier).setCurrentGoal(updatedGoal);
                                  }
                                }
                              });
                            } catch (e) {
                              // 使用日志记录错误，而不是print
                              debugPrint('保存里程碑出错: $e');
                            }
                          },
                        ),
                      );
                    },
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  /// 获取里程碑状态显示文本
  String _getMilestoneStatus(Milestone milestone, List<Milestone> allMilestones) {
    final now = DateTime.now();
    final daysRemaining = milestone.date.difference(now).inDays;

    if (milestone.date.isBefore(now)) {
      return "已完成";
    } else {
      // 找到最近的未完成里程碑
      try {
        final futureMilestones = allMilestones.where((m) => m.date.isAfter(now)).toList();
        if (futureMilestones.isEmpty) return "未开始";

        final nextMilestone = futureMilestones.reduce((a, b) => a.date.isBefore(b.date) ? a : b);

        if (milestone.id == nextMilestone.id) {
          return "还剩$daysRemaining天";
        } else {
          return "未开始";
        }
      } catch (e) {
        return "未开始";
      }
    }
  }


}

