import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/models/subject_project.dart';
import '../../../core/models/project_progress_change.dart';

import '../../../core/services/enhanced_hive_service.dart';
import '../providers/subject_state.dart';
import '../widgets/project_edit_sheet.dart';
import '../../focus/screens/project_focus_records_screen.dart';
import '../../../shared/widgets/top_message_overlay.dart';

/// 项目详情页面
class ProjectDetailScreen extends ConsumerStatefulWidget {
  final Project project;

  const ProjectDetailScreen({
    super.key,
    required this.project,
  });

  @override
  ConsumerState<ProjectDetailScreen> createState() => _ProjectDetailScreenState();
}

class _ProjectDetailScreenState extends ConsumerState<ProjectDetailScreen> {
  late Project _project;
  // Hive服务
  final EnhancedHiveService _hiveService = EnhancedHiveService();

  @override
  void initState() {
    super.initState();
    _project = widget.project;
    _refreshProjectData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 当页面重新获得焦点时刷新数据
    _refreshProjectData();
  }

  // 刷新项目数据
  Future<void> _refreshProjectData() async {
    try {
      await _hiveService.initHive();
      final updatedProject = _hiveService.subjectRepository.getProjectById(_project.id);
      if (updatedProject != null && mounted) {
        setState(() {
          _project = updatedProject;
        });
        debugPrint('项目数据已刷新: ${_project.name}');
        debugPrint('  - focusedHours: ${_project.focusedHours}h');
        debugPrint('  - currentFocusHours: ${_project.currentFocusHours}h');
        debugPrint('  - 显示的专注时间: ${(_project.currentFocusHours ?? _project.focusedHours)}h');
      }
    } catch (e) {
      debugPrint('刷新项目数据失败: $e');
    }
  }

  // 显示编辑项目的底部弹窗
  void _showEditProjectSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ProjectEditSheet(
        project: _project,
        subject: ref.read(subjectStateProvider.notifier).getSubjectById(_project.subjectId),
        onSave: (updatedProject) async {
          try {
            // 保存到Hive数据库
            await _hiveService.subjectRepository.saveProject(updatedProject);
            debugPrint('项目编辑已保存到Hive: ${updatedProject.id}, ${updatedProject.name}');

            // 更新本地状态
            setState(() {
              _project = updatedProject;
            });

            // 更新Riverpod状态
            ref.read(subjectStateProvider.notifier).updateProject(updatedProject);

            // 项目编辑成功，界面会自动更新显示新的数据，无需额外提示
          } catch (e) {
            debugPrint('保存项目编辑到Hive失败: $e');
            // 开发环境：只记录错误日志，不显示用户错误提示
            // 生产环境：可根据需要决定是否显示错误提示
          }
        },
      ),
    );
  }

  // 显示修改进度对话框
  void _showEditProgressDialog(int currentValue, int targetValue, String unit) {
    // 使用一个简单的字符串来存储用户输入
    String inputStr = currentValue.toString();

    // 使用一个简单的对话框，不使用TextEditingController
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('修改进度'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('请输入当前完成的$unit数'),
              const SizedBox(height: 16),
              // 使用简单的文本字段，不使用控制器
              TextField(
                autofocus: true,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  border: const OutlineInputBorder(),
                  hintText: '例如：10',
                  suffixText: unit,
                ),
                onChanged: (value) {
                  // 直接更新字符串变量
                  inputStr = value;
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(dialogContext),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () async {
                // 先关闭对话框
                Navigator.pop(dialogContext);

                // 然后处理输入
                final inputValue = int.tryParse(inputStr) ?? currentValue;

                // 验证输入值是否在有效范围内（0到目标值之间）
                if (inputValue < 0) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('请输入大于等于0的有效值')),
                    );
                  }
                  return;
                }

                // 验证输入值是否超过目标值
                if (inputValue > targetValue) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('输入值不能超过目标值 $targetValue')),
                    );
                  }
                  return;
                }

                // 计算新进度
                final newProgress = targetValue > 0 ?
                    (inputValue / targetValue).clamp(0.0, 1.0) : 0.0;

                // 更新项目
                final updatedProject = _project.copyWith(
                  progress: newProgress,
                  currentCustomValue: inputValue,
                );

                setState(() {
                  _project = updatedProject;
                });

                // 更新项目数据到Riverpod状态
                ref.read(subjectStateProvider.notifier).updateProject(updatedProject);

                // 保存到Hive数据库
                try {
                  // 保存项目
                  await _hiveService.subjectRepository.saveProject(updatedProject);
                  debugPrint('项目进度已保存到Hive: ${updatedProject.id}, 进度: ${(updatedProject.progress * 100).toStringAsFixed(1)}%');

                  // 记录进度变化
                  final progressChange = ProjectProgressChange(
                    id: 'progress_${DateTime.now().millisecondsSinceEpoch}',
                    projectId: _project.id,
                    timestamp: DateTime.now(),
                    previousValue: _project.currentCustomValue?.toDouble() ?? 0,
                    newValue: inputValue.toDouble(),
                    previousProgress: _project.progress,
                    newProgress: newProgress,
                    source: ProgressChangeSource.manualAdjustment,
                  );

                  // 保存进度变化记录
                  await _hiveService.projectProgressRepository.saveProgressChange(progressChange);
                  debugPrint('项目进度变化已记录: ${progressChange.id}, 来源: 手动调整, 变化值: $inputValue $unit');

                  // 只有在保存成功后才显示成功提示
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('进度已更新为 $inputValue $unit')),
                    );
                  }
                } catch (e) {
                  debugPrint('保存项目进度到Hive失败: $e');
                  // 开发环境：只记录错误日志，不显示用户错误提示
                  // 生产环境：可根据需要决定是否显示错误提示
                  return;
                }
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }



  // 显示归档确认对话框
  void _showArchiveConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('归档项目'),
        content: Text('您确定要归档项目 "${_project.name}" 吗？\n\n归档后，项目将不会在项目列表和专注页面中显示，但您可以在归档管理中查看和恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              // 关闭对话框
              Navigator.pop(context);
              // 归档项目
              _archiveProject();
            },
            child: const Text('归档'),
          ),
        ],
      ),
    );
  }

  // 归档项目
  void _archiveProject() async {
    // 显示加载对话框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text('正在归档项目 "${_project.name}"...'),
          ],
        ),
      ),
    );

    try {
      debugPrint('开始归档项目: ${_project.id}, ${_project.name}');

      // 确保Hive服务已初始化
      await _hiveService.initHive();

      // 归档项目
      await _hiveService.subjectRepository.archiveProject(_project.id);
      debugPrint('数据库更新成功');

      // 更新状态管理器中的项目
      final updatedProject = _project.copyWith(isArchived: true);
      ref.read(subjectStateProvider.notifier).updateProject(updatedProject);
      debugPrint('状态管理器更新成功');

      // 重新加载项目列表，确保归档的项目不再显示
      await ref.read(subjectStateProvider.notifier).reloadProjects();
      debugPrint('项目列表重新加载成功');

      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 检查组件是否仍然挂载
      if (!mounted) return;

      // 项目归档成功，用户会看到项目从列表中消失，无需额外提示

      // 返回上一页
      Navigator.pop(context);
    } catch (e) {
      debugPrint('归档项目失败: $e');

      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 检查组件是否仍然挂载
      if (!mounted) return;

      // 显示错误消息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('归档失败: $e'),
          backgroundColor: AppColors.error,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // 显示删除确认对话框
  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除项目'),
        content: Text('您确定要删除项目 "${_project.name}" 吗？\n\n此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              // 关闭对话框
              Navigator.pop(context);
              // 删除项目
              _deleteProject();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  // 删除项目
  void _deleteProject() async {
    try {
      // 确保Hive服务已初始化
      await _hiveService.initHive();

      // 从Hive中删除项目
      await _hiveService.subjectRepository.deleteProject(_project.id);

      // 从状态管理器中删除项目
      ref.read(subjectStateProvider.notifier).deleteProject(_project.id);

      // 检查组件是否仍然挂载
      if (!mounted) return;

      // 使用TopMessageOverlayManager显示简洁的成功提示
      TopMessageOverlayManager().showSuccess(
        context: context,
        message: '已删除',
        duration: const Duration(seconds: 1), // 缩短显示时间
      );

      // 返回上一页
      Navigator.pop(context);
    } catch (e) {
      // 检查组件是否仍然挂载
      if (!mounted) return;

      // 使用TopMessageOverlayManager显示错误消息
      TopMessageOverlayManager().showError(
        context: context,
        message: '删除失败',
        duration: const Duration(seconds: 2),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取项目关联的科目
    final subject = ref.watch(subjectStateProvider.notifier).getSubjectById(_project.subjectId);

    return Scaffold(
      // 设置底色与渐变底部颜色一致，防止出现灰色区域
      backgroundColor: const Color.fromARGB(255, 255, 250, 240),
      // 使用渐变背景，与应用其他页面保持一致
      body: Container(
        // 确保容器填满整个屏幕高度
        height: MediaQuery.of(context).size.height,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color.fromARGB(255, 204, 255, 229), // 顶部颜色
              Color.fromARGB(255, 255, 250, 240), // 底部颜色
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 顶部栏：返回按钮和编辑按钮
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // 返回按钮
                      IconButton(
                        icon: const Icon(Icons.arrow_back),
                        onPressed: () => Navigator.pop(context),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),

                      // 更多操作按钮
                      PopupMenuButton<String>(
                        icon: const Icon(CupertinoIcons.ellipsis), // 使用 Cupertino 图标
                        tooltip: '更多操作',
                        onSelected: (String result) {
                          switch (result) {
                            case 'edit':
                              _showEditProjectSheet();
                              break;
                            case 'archive':
                              _showArchiveConfirmation();
                              break;
                            case 'delete':
                              _showDeleteConfirmation();
                              break;
                          }
                        },
                        // 自定义弹窗样式，更符合iOS风格
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(14.0),
                        ),
                        color: Colors.white,
                        elevation: 8,
                        // 调整内边距
                        padding: EdgeInsets.zero,
                        // 构建菜单项
                        itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
                          PopupMenuItem<String>(
                            value: 'edit',
                            height: 48, // 增加高度，更符合iOS风格
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Row(
                              children: [
                                Icon(
                                  CupertinoIcons.pencil, // 使用iOS风格图标
                                  color: AppColors.textSecondary,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  '编辑项目',
                                  style: TextStyle(
                                    color: AppColors.text,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // 添加分隔线
                          const PopupMenuDivider(height: 1),
                          PopupMenuItem<String>(
                            value: 'archive',
                            height: 48,
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Row(
                              children: [
                                Icon(
                                  CupertinoIcons.archivebox,
                                  color: AppColors.textSecondary,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  '归档项目',
                                  style: TextStyle(
                                    color: AppColors.textSecondary,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          PopupMenuItem<String>(
                            value: 'delete',
                            height: 48, // 增加高度，更符合iOS风格
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Row(
                              children: [
                                Icon(
                                  CupertinoIcons.delete, // 使用iOS风格图标
                                  color: AppColors.error,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  '删除项目',
                                  style: TextStyle(
                                    color: AppColors.error,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // 项目信息卡片
                  _buildProjectInfoCard(subject),

                  // 根据是否开启进度追踪，调整卡片间距和显示内容
                  if (_project.isTrackingEnabled) ...[
                    const SizedBox(height: 24),
                    _buildProgressStatusCard(),
                    const SizedBox(height: 24),
                  ] else
                    const SizedBox(height: 24),

                  // 项目详细信息卡片
                  _buildProjectDetailsCard(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 构建项目信息卡片
  Widget _buildProjectInfoCard(Subject? subject) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppShadows.low],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 项目名称
          Text(
            _project.name,
            style: AppTextStyles.headline2,
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),

          const SizedBox(height: 16),

          // 科目信息
          if (subject != null)
            Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Color(subject.color),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    subject.name,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),

          const SizedBox(height: 16),

          // 项目日期
          Row(
            children: [
              const Icon(
                Icons.calendar_today,
                size: 16,
                color: AppColors.textSecondary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '${_formatDate(_project.startDate)} — ${_formatDate(_project.endDate)}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 时间进度条
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '时间进度',
                    style: AppTextStyles.bodyMedium,
                  ),
                  Text(
                    '${_getTimeProgressPercent().toStringAsFixed(1)}%',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              // 已进行时间与剩余时间
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '已进行 ${_getElapsedDays()}天',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textTertiary,
                    ),
                  ),
                  Text(
                    '剩余 ${_getRemainingDays()}天',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textTertiary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: _getTimeProgress(),
                backgroundColor: AppColors.background,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                minHeight: 8,
                borderRadius: BorderRadius.circular(4),
              ),
            ],
          ),
        ],
      ),
    );
  }







  // 构建项目专注情况卡片
  Widget _buildProjectDetailsCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppShadows.low],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '项目专注情况',
            style: AppTextStyles.headline3,
          ),

          const SizedBox(height: 16),

          // 项目描述
          if (_project.description != null && _project.description!.isNotEmpty)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '描述',
                  style: AppTextStyles.labelMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  _project.description!,
                  style: AppTextStyles.bodyMedium,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 5,
                ),
                const SizedBox(height: 16),
              ],
            ),

          // 项目统计信息
          Column(
            children: [
              _buildStatisticItem(
                icon: Icons.timer,
                title: '总专注时间',
                value: '${(_project.currentFocusHours ?? _project.focusedHours).toStringAsFixed(1)}小时',
              ),
              const Divider(),
              FutureBuilder<int>(
                future: _getFocusSessionCount(),
                builder: (context, snapshot) {
                  return _buildStatisticItem(
                    icon: Icons.repeat,
                    title: '专注次数',
                    value: '${snapshot.data ?? 0}次',
                  );
                },
              ),
            ],
          ),

          const SizedBox(height: 24),

          // 专注记录按钮
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ProjectFocusRecordsScreen(
                      projectId: _project.id,
                      projectName: _project.name,
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.history),
              label: const Text('查看专注记录'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: AppColors.primary,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: AppColors.primary),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 获取专注次数
  Future<int> _getFocusSessionCount() async {
    try {
      await _hiveService.initHive();
      final records = _hiveService.focusRecordRepository.getFocusRecordsByProjectId(_project.id);
      return records.length;
    } catch (e) {
      debugPrint('获取专注次数失败: $e');
      return 0;
    }
  }

  // 构建进度情况卡片
  Widget _buildProgressStatusCard() {
    // 获取进度百分比
    final progressPercent = (_project.progress * 100).toStringAsFixed(1);

    // 获取进度相关数据
    String progressDetail = '';
    if (_project.trackingMode == ProgressTrackingMode.focusTime) {
      final currentHours = _project.currentFocusHours ?? _project.focusedHours;
      final totalHours = _project.totalFocusHours ?? _project.targetHours;
      progressDetail = '${currentHours.toStringAsFixed(1)}/${totalHours.toStringAsFixed(1)}小时';
    } else if (_project.trackingMode == ProgressTrackingMode.custom) {
      final currentValue = _project.currentCustomValue ?? 0;
      final targetValue = _project.targetValue ?? 100;
      final unit = _project.customUnit ?? '单位';
      progressDetail = '$currentValue/$targetValue $unit';
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppShadows.low],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题和提示按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '进度情况',
                style: AppTextStyles.headline3,
              ),
              // 提示按钮 - 只在专注时间模式下显示
              if (_project.trackingMode == ProgressTrackingMode.focusTime)
                Tooltip(
                  message: '专注时间模式下，进度会随着专注时间自动更新整。',
                  preferBelow: false,
                  showDuration: const Duration(seconds: 3),
                  decoration: BoxDecoration(
                    color: Colors.black87,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  textStyle: AppTextStyles.bodySmall.copyWith(
                    color: Colors.white,
                  ),
                  child: Icon(
                    Icons.info_outline,
                    size: 20,
                    color: AppColors.textTertiary,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 24),

          // 圆环进度条和进度数据
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 左侧圆环和进度详情
              SizedBox(
                width: 160, // 固定宽度确保对齐
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // 圆环
                    SizedBox(
                      width: 130,
                      height: 130,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // 背景圆环
                          SizedBox(
                            width: 130,
                            height: 130,
                            child: CircularProgressIndicator(
                              value: 1,
                              strokeWidth: 12,
                              backgroundColor: AppColors.background,
                              valueColor: AlwaysStoppedAnimation<Color>(AppColors.background),
                            ),
                          ),
                          // 进度圆环
                          SizedBox(
                            width: 130,
                            height: 130,
                            child: CircularProgressIndicator(
                              value: _project.progress,
                              strokeWidth: 12,
                              backgroundColor: Colors.transparent,
                              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                            ),
                          ),
                          // 进度文本
                          Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                '$progressPercent%',
                                style: AppTextStyles.headline2.copyWith(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                '完成度',
                                style: AppTextStyles.bodyMedium.copyWith(
                                  color: AppColors.textTertiary,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 12),

                    // 进度详情 - 当前/目标进度
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withAlpha(15),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        progressDetail,
                        style: AppTextStyles.headline3.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 16),

              // 右侧信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 信息卡片 - 与左侧圆环顶部对齐
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(10),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          // 单位专注时间的项目速度
                          _buildSimpleInfoCard(
                            icon: Icons.speed,
                            title: '单位时间速度',
                            value: '${_getProgressPerHour().toStringAsFixed(1)}%/小时',
                            color: Colors.blue,
                            showBorder: true,
                          ),

                          // 平均每日推进速度
                          _buildSimpleInfoCard(
                            icon: Icons.trending_up,
                            title: '日均推进速度',
                            value: '${_getAverageDailyProgress().toStringAsFixed(1)}%/天',
                            color: Colors.purple,
                            showBorder: true,
                          ),

                          // 预估完成时间
                          _buildSimpleCompletionInfoCard(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          // 自定义模式下的进度调整按钮
          if (_project.trackingMode == ProgressTrackingMode.custom) ...[
            const SizedBox(height: 24),
            const Divider(),
            const SizedBox(height: 16),

            Text(
              '快速调整进度',
              style: AppTextStyles.labelMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 16),

            // 按钮组
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // 加1按钮
                _buildProgressButton(
                  label: '+1',
                  width: 70,
                  onPressed: () => _updateProgress(
                    (_project.currentCustomValue ?? 0) + 1,
                    _project.targetValue ?? 100,
                    _project.customUnit ?? '单位',
                  ),
                ),

                // 加5按钮
                _buildProgressButton(
                  label: '+5',
                  width: 70,
                  onPressed: () => _updateProgress(
                    (_project.currentCustomValue ?? 0) + 5,
                    _project.targetValue ?? 100,
                    _project.customUnit ?? '单位',
                  ),
                ),

                // 加10按钮
                _buildProgressButton(
                  label: '+10',
                  width: 70,
                  onPressed: () => _updateProgress(
                    (_project.currentCustomValue ?? 0) + 10,
                    _project.targetValue ?? 100,
                    _project.customUnit ?? '单位',
                  ),
                ),

                // 修改按钮
                SizedBox(
                  width: 70,
                  child: ElevatedButton(
                    onPressed: () => _showEditProgressDialog(
                      _project.currentCustomValue ?? 0,
                      _project.targetValue ?? 100,
                      _project.customUnit ?? '单位',
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: AppColors.primary,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side: BorderSide(color: AppColors.primary),
                      ),
                    ),
                    child: const Text('修改'),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  // 构建进度调整按钮
  Widget _buildProgressButton({
    required String label,
    required double width,
    required VoidCallback onPressed,
  }) {
    return SizedBox(
      width: width,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.greenLight400, // 使用light300颜色
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(label),
      ),
    );
  }

  // 更新进度
  Future<void> _updateProgress(int newValue, int targetValue, String unit) async {
    // 确保新值不超过目标值
    final adjustedValue = newValue > targetValue ? targetValue : newValue;

    // 计算新进度
    final newProgress = targetValue > 0 ?
        (adjustedValue / targetValue).clamp(0.0, 1.0) : 0.0;

    // 计算变化值（用于调试日志）
    // final changeValue = adjustedValue - (_project.currentCustomValue ?? 0);

    // 更新项目
    final updatedProject = _project.copyWith(
      progress: newProgress,
      currentCustomValue: adjustedValue,
    );

    setState(() {
      _project = updatedProject;
    });

    // 更新项目数据到Riverpod状态
    ref.read(subjectStateProvider.notifier).updateProject(updatedProject);

    // 保存到Hive数据库
    try {
      // 保存项目
      await _hiveService.subjectRepository.saveProject(updatedProject);

      // 记录进度变化
      final progressChange = ProjectProgressChange(
        id: 'progress_${DateTime.now().millisecondsSinceEpoch}',
        projectId: _project.id,
        timestamp: DateTime.now(),
        previousValue: (_project.currentCustomValue ?? 0).toDouble(),
        newValue: adjustedValue.toDouble(),
        previousProgress: _project.progress,
        newProgress: newProgress,
        source: ProgressChangeSource.manualAdjustment,
      );

      // 保存进度变化记录
      await _hiveService.projectProgressRepository.saveProgressChange(progressChange);

      // 进度更新成功，进度条已更新显示，无需额外提示
      debugPrint('快速进度调整成功: $adjustedValue $unit');
    } catch (e) {
      debugPrint('保存项目进度到Hive失败: $e');
      // 开发环境：只记录错误日志，不显示用户错误提示
      // 生产环境：可根据需要决定是否显示错误提示
    }
  }

  // 构建统计项
  Widget _buildStatisticItem({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: AppColors.primary,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              title,
              style: AppTextStyles.bodyMedium,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Flexible(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // 构建简化版信息卡片
  Widget _buildSimpleInfoCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
    bool showBorder = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: showBorder
            ? Border(bottom: BorderSide(color: Colors.grey.withAlpha(40), width: 1))
            : null,
      ),
      child: Row(
        children: [
          // 图标容器
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: color.withAlpha(30),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),

          // 文本内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题
                Text(
                  title,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),

                // 数值
                Text(
                  value,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建简化版完成信息卡片
  Widget _buildSimpleCompletionInfoCard() {
    final estimatedDate = _getEstimatedCompletionDate();
    final status = _getCompletionStatus();

    if (estimatedDate == null || status == CompletionStatus.unknown) {
      return _buildSimpleInfoCard(
        icon: Icons.calendar_today,
        title: '预估完成时间',
        value: '暂无数据',
        color: Colors.grey,
      );
    }

    // 格式化日期
    final formattedDate = _formatDate(estimatedDate);

    // 根据状态设置颜色和文本
    Color statusColor;
    String statusText;

    switch (status) {
      case CompletionStatus.early:
        statusColor = Colors.green;
        statusText = '$formattedDate (提前)';
        break;
      case CompletionStatus.onTime:
        statusColor = AppColors.blue;
        statusText = '$formattedDate (准时)';
        break;
      case CompletionStatus.late:
        statusColor = Colors.orange;
        statusText = '$formattedDate (延期)';
        break;
      default:
        statusColor = Colors.grey;
        statusText = formattedDate;
    }

    return _buildSimpleInfoCard(
      icon: Icons.calendar_today,
      title: '预估完成时间',
      value: statusText,
      color: statusColor,
    );
  }

  // 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // 计算剩余天数
  int _getRemainingDays() {
    final now = DateTime.now();
    final difference = _project.endDate.difference(now).inDays;
    return difference > 0 ? difference : 0;
  }

  // 计算已经过去的天数
  int _getElapsedDays() {
    final now = DateTime.now();
    final difference = now.difference(_project.startDate).inDays;
    // 至少返回1天，避免除以0的情况
    return difference > 0 ? difference : 1;
  }

  // 计算时间进度（0-1之间的值）
  double _getTimeProgress() {
    final now = DateTime.now();
    final totalDays = _project.endDate.difference(_project.startDate).inDays;

    if (totalDays <= 0) return 1.0; // 避免除以零

    final passedDays = now.difference(_project.startDate).inDays;

    if (passedDays <= 0) return 0.0; // 项目还未开始
    if (passedDays >= totalDays) return 1.0; // 项目已结束

    return passedDays / totalDays;
  }

  // 计算时间进度百分比
  double _getTimeProgressPercent() {
    return _getTimeProgress() * 100;
  }

  // 计算单位专注时间的项目速度
  double _getProgressPerHour() {
    if (_project.trackingMode == ProgressTrackingMode.focusTime) {
      // 专注时间模式下，每小时的进度是固定的
      final totalHours = _project.totalFocusHours ?? _project.targetHours;
      return totalHours > 0 ? (1 / totalHours) * 100 : 0;
    } else {
      // 自定义模式下，基于已专注时间和当前进度计算
      final focusedHours = _project.currentFocusHours ?? _project.focusedHours;
      debugPrint('_getProgressPerHour: 项目: ${_project.name}, 已专注时间: $focusedHours, 当前进度: ${_project.progress}');
      return focusedHours > 0 ? (_project.progress * 100) / focusedHours : 0;
    }
  }

  // 计算平均每日推进速度
  double _getAverageDailyProgress() {
    final elapsedDays = _getElapsedDays();
    // elapsedDays 现在至少为1，所以不需要检查 <= 0
    return (_project.progress * 100) / elapsedDays;
  }

  // 计算预估完成时间
  DateTime? _getEstimatedCompletionDate() {
    final dailyProgress = _getAverageDailyProgress();
    if (dailyProgress <= 0) return null;

    // 计算剩余进度
    final remainingProgress = 100 - (_project.progress * 100);

    // 计算剩余天数
    final remainingDays = (remainingProgress / dailyProgress).ceil();

    // 计算预估完成日期
    final now = DateTime.now();
    return now.add(Duration(days: remainingDays));
  }

  // 获取预估完成状态
  CompletionStatus _getCompletionStatus() {
    final estimatedDate = _getEstimatedCompletionDate();
    if (estimatedDate == null) return CompletionStatus.unknown;

    final endDate = _project.endDate;

    // 计算日期差异（不考虑时间部分）
    final estimatedDay = DateTime(estimatedDate.year, estimatedDate.month, estimatedDate.day);
    final endDay = DateTime(endDate.year, endDate.month, endDate.day);

    final difference = estimatedDay.difference(endDay).inDays;

    if (difference < 0) {
      return CompletionStatus.early; // 提前完成
    } else if (difference == 0) {
      return CompletionStatus.onTime; // 按时完成
    } else {
      return CompletionStatus.late; // 延迟完成
    }
  }
}

// 预估完成状态枚举
enum CompletionStatus {
  early,   // 提前完成
  onTime,  // 按时完成
  late,    // 延迟完成
  unknown, // 未知状态
}
