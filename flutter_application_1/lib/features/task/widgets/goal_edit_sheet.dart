import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/models/goal_milestone.dart';
import '../../../shared/widgets/improved_date_picker.dart';
import '../../../shared/widgets/enhanced_keyboard_bottom_sheet.dart';
import '../../../shared/widgets/keyboard_input_overlay.dart';
import '../../../shared/widgets/top_message_overlay.dart';

/// 目标编辑底部弹窗
class GoalEditSheet extends StatefulWidget {
  final Goal goal;
  final Function(Goal) onGoalEdited;

  const GoalEditSheet({
    super.key,
    required this.goal,
    required this.onGoalEdited,
  });

  @override
  State<GoalEditSheet> createState() => _GoalEditSheetState();
}

class _GoalEditSheetState extends State<GoalEditSheet> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  DateTime? _startDate;
  DateTime? _endDate;
  static const _maxNameLength = 10;

  @override
  void initState() {
    super.initState();
    _nameController.text = widget.goal.name;
    _startDate = widget.goal.startDate;
    _endDate = widget.goal.endDate;
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  void _submitForm() {
    if (_formKey.currentState?.validate() ?? false) {
      if (_startDate == null || _endDate == null) {
        TopMessageOverlayManager().showError(
          context: context,
          message: '请选择开始和结束日期',
        );
        return;
      }

      if (_endDate!.isBefore(_startDate!)) {
        TopMessageOverlayManager().showError(
          context: context,
          message: '结束日期不能早于开始日期',
        );
        return;
      }

      final editedGoal = widget.goal.copyWith(
        name: _nameController.text.trim(),
        startDate: _startDate,
        endDate: _endDate,
      );

      widget.onGoalEdited(editedGoal);
      Navigator.pop(context);
    }
  }



  @override
  Widget build(BuildContext context) {
    return EnhancedKeyboardFormSheet(
      title: '编辑目标',
      submitText: '保存',
      onSubmit: _submitForm,
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 目标名称输入框
            KeyboardAwareTextField(
              controller: _nameController,
              labelText: '目标名称',
              hintText: '请输入目标名称',
              maxLength: _maxNameLength,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return '请输入目标名称';
                }
                if (value.length > _maxNameLength) {
                  return '目标名称不能超过$_maxNameLength个字';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // 日期选择
            const Text('日期', style: AppTextStyles.labelMedium),
            const SizedBox(height: 8),
            Row(
              children: [
                // 开始日期选择
                Expanded(
                  child: ImprovedDatePicker(
                    initialDate: _startDate,
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2100),
                    title: '选择开始日期',
                    onDateSelected: (date) {
                      if (date != null) {
                        setState(() {
                          _startDate = date;
                          // 确保结束日期不早于开始日期
                          if (_endDate != null && _endDate!.isBefore(_startDate!)) {
                            _endDate = _startDate;
                          }
                        });
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                const Icon(Icons.arrow_forward, color: AppColors.textHint),
                const SizedBox(width: 16),
                // 结束日期选择
                Expanded(
                  child: ImprovedDatePicker(
                    initialDate: _endDate,
                    firstDate: _startDate ?? DateTime(2000),
                    lastDate: DateTime(2100),
                    title: '选择结束日期',
                    onDateSelected: (date) {
                      if (date != null) {
                        setState(() {
                          _endDate = date;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}