import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/models/subject_project.dart';
import '../../../shared/widgets/color_selector.dart';
import '../../../shared/utils/color_manager.dart';
import '../providers/subject_state.dart';
import '../../../shared/widgets/enhanced_keyboard_bottom_sheet.dart';
import '../../../shared/widgets/keyboard_input_overlay.dart';

/// 添加科目底部弹窗
class SubjectAddSheet extends ConsumerStatefulWidget {
  final Function(Subject) onSubjectAdded;

  const SubjectAddSheet({
    super.key,
    required this.onSubjectAdded,
  });

  @override
  ConsumerState<SubjectAddSheet> createState() => _SubjectAddSheetState();
}

class _SubjectAddSheetState extends ConsumerState<SubjectAddSheet> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  static const _maxNameLength = 7;

  // 默认选择第一个可用颜色
  Color? _selectedColor;

  @override
  void initState() {
    super.initState();
    // 立即初始化颜色
    _initializeColor();
  }

  void _initializeColor() {
    final usedColors = _getUsedColors();
    final nextAvailableColor = ColorManager.getNextAvailableColor(usedColors);
    _selectedColor = nextAvailableColor ?? AppColors.subjectColors.first;
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  void _submitForm() {
    if (_formKey.currentState?.validate() ?? false) {
      final selectedColor = _selectedColor ?? AppColors.subjectColors.first;
      final newSubject = Subject(
        id: const Uuid().v4(),
        name: _nameController.text.trim(),
        color: selectedColor.value,
      );

      widget.onSubjectAdded(newSubject);
      Navigator.pop(context);
    }
  }

  // 获取已使用的颜色列表
  List<Color> _getUsedColors() {
    final subjects = ref.read(subjectStateProvider).subjects;
    return subjects.map((s) => Color(s.color)).toList();
  }

  @override
  Widget build(BuildContext context) {
    return EnhancedKeyboardFormSheet(
      title: '添加科目',
      submitText: '确认添加',
      onSubmit: _submitForm,
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            KeyboardAwareTextField(
              controller: _nameController,
              labelText: '科目名称',
              hintText: '请输入科目名称',
              maxLength: _maxNameLength,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return '请输入科目名称';
                }
                if (value.length > _maxNameLength) {
                  return '科目名称不能超过$_maxNameLength个字';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // 颜色选择标题
            Row(
              children: [
                const Text('选择科目颜色', style: AppTextStyles.labelMedium),

                // //颜色禁选机制原因提示语
                // const SizedBox(width: 6),
                // const TooltipButton(
                //   message: '为确保数据分析中科目的区分度，当前不可重复选择颜色',
                //   iconSize: 14,
                //   iconColor: AppColors.info,
                //   preferBelow: false,
                // ),
              ],
            ),
            const SizedBox(height: 8),

            // 颜色选择器
            if (_selectedColor != null)
              ColorSelector(
                selectedColor: _selectedColor!,
                usedColors: _getUsedColors(),
                onColorSelected: (color) {
                  setState(() {
                    _selectedColor = color;
                  });
                },
              ),
          ],
        ),
      ),
    );
  }
}