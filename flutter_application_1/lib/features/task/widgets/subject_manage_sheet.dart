import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/models/subject_project.dart';

/// 科目管理底部弹窗
class SubjectManageSheet extends ConsumerWidget {
  final List<Subject> subjects;
  final VoidCallback onAddSubject;
  final Function(Subject) onEditSubject;
  final Function(Subject) onDeleteSubject;

  const SubjectManageSheet({
    super.key,
    required this.subjects,
    required this.onAddSubject,
    required this.onEditSubject,
    required this.onDeleteSubject,
  });

  // 显示删除确认对话框
  Future<bool> _showDeleteConfirmDialog(BuildContext context, Subject subject) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('删除科目"${subject.name}"将同时删除该科目下的所有项目数据，确定要删除吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
            ),
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('删除'),
          ),
        ],
      ),
    ) ?? false;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      padding: const EdgeInsets.only(top: 8, bottom: 24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 顶部拖动条
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 标题栏
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('科目管理', style: AppTextStyles.headline3),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),

          const SizedBox(height: 8),
          const Divider(),

          // 科目列表
          if (subjects.isEmpty)
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  Icon(
                    Icons.local_offer_outlined, // 使用标签图标，与专注页面选择科目按钮保持一致
                    size: 48,
                    color: AppColors.textHint,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    "暂无科目",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    "添加科目来管理您的项目",
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textTertiary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  // 添加引导式按钮
                  ElevatedButton.icon(
                    icon: const Icon(Icons.add),
                    label: const Text("添加第一个科目"),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onPressed: onAddSubject,
                  ),
                ],
              ),
            )
          else
            Flexible(
              child: ListView.separated(
                shrinkWrap: true,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                itemCount: subjects.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final subject = subjects[index];
                  return ListTile(
                    contentPadding: EdgeInsets.zero,
                    title: Text(
                      subject.name,
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    leading: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: Color(subject.color).withAlpha(AppColors.alpha10),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.local_offer_outlined, // 使用标签图标，与专注页面选择科目按钮保持一致
                        size: 16,
                        color: Color(subject.color),
                      ),
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 编辑按钮
                        IconButton(
                          icon: const Icon(Icons.edit_outlined, size: 20),
                          onPressed: () => onEditSubject(subject),
                          tooltip: '编辑科目',
                        ),
                        // 删除按钮
                        IconButton(
                          icon: const Icon(Icons.delete_outline, size: 20),
                          onPressed: () async {
                            debugPrint('删除按钮点击: ${subject.id}, ${subject.name}');
                            if (await _showDeleteConfirmDialog(context, subject)) {
                              debugPrint('用户确认删除: ${subject.id}, ${subject.name}');
                              onDeleteSubject(subject);
                            } else {
                              debugPrint('用户取消删除: ${subject.id}, ${subject.name}');
                            }
                          },
                          tooltip: '删除科目',
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),

          // 添加科目按钮 - 仅当有科目时显示
          if (subjects.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(16),
              child: ElevatedButton.icon(
                icon: const Icon(Icons.add),
                label: const Text('添加新科目'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onPressed: onAddSubject,
              ),
            ),
        ],
      ),
    );
  }
}
