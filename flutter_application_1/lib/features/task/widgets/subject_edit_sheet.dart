import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/models/subject_project.dart';
import '../../../shared/widgets/color_selector.dart';
import '../../../shared/utils/keyboard_utils.dart';
import '../providers/subject_state.dart';

/// 编辑科目底部弹窗
class SubjectEditSheet extends ConsumerStatefulWidget {
  final Subject subject;
  final Function(Subject) onSubjectEdited;

  const SubjectEditSheet({
    super.key,
    required this.subject,
    required this.onSubjectEdited,
  });

  @override
  ConsumerState<SubjectEditSheet> createState() => _SubjectEditSheetState();
}

class _SubjectEditSheetState extends ConsumerState<SubjectEditSheet> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  static const _maxNameLength = 7;

  // 当前选中的颜色
  late Color _selectedColor;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.subject.name);
    _selectedColor = Color(widget.subject.color);
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  void _submitForm() {
    if (_formKey.currentState?.validate() ?? false) {
      final editedSubject = Subject(
        id: widget.subject.id, // 保持原有ID
        name: _nameController.text.trim(),
        color: _selectedColor.value,
      );

      widget.onSubjectEdited(editedSubject);
      Navigator.pop(context);
    }
  }

  // 获取已使用的颜色列表（排除当前编辑的科目）
  List<Color> _getUsedColors() {
    final subjects = ref.read(subjectStateProvider).subjects;
    return subjects
        .where((s) => s.id != widget.subject.id) // 排除当前编辑的科目
        .map((s) => Color(s.color))
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => KeyboardUtils.hideKeyboard(context),
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(bottom: BorderSide(color: AppColors.divider)),
            ),
            child: Row(
              children: [
                const Text('编辑科目', style: AppTextStyles.headline3),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
          // 表单
          Padding(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextFormField(
                    controller: _nameController,
                    decoration: const InputDecoration(
                      labelText: '科目名称',
                      hintText: '请输入科目名称',
                      border: OutlineInputBorder(),
                    ),
                    maxLength: _maxNameLength,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return '请输入科目名称';
                      }
                      if (value.length > _maxNameLength) {
                        return '科目名称不能超过$_maxNameLength个字';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // 颜色选择标题
                  Row(
                    children: [
                      const Text('选择科目颜色', style: AppTextStyles.labelMedium),

                      //提示语
                      // const SizedBox(width: 6),
                      // const TooltipButton(
                      //   message: '已使用的颜色会自动排到最后并禁用，确保数据分析页面中不同科目的颜色不会冲突，便于区分和对比',
                      //   iconSize: 14,
                      //   iconColor: AppColors.info,
                      //   preferBelow: false,
                      // ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // 颜色选择器
                  ColorSelector(
                    selectedColor: _selectedColor,
                    usedColors: _getUsedColors(),
                    onColorSelected: (color) {
                      setState(() {
                        _selectedColor = color;
                      });
                    },
                  ),

                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: _submitForm,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary, // 使用主色调
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text('保存修改'),
                  ),
                ],
              ),
            ),
          ),
          // 底部安全区域
          SizedBox(height: MediaQuery.of(context).padding.bottom),
          ],
        ),
      ),
    );
  }
}
