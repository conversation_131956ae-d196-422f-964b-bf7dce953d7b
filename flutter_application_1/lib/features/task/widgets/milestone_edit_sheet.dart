import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/models/goal_milestone.dart';
import '../../../shared/widgets/improved_date_picker.dart';
import '../../../shared/widgets/enhanced_keyboard_bottom_sheet.dart';
import '../../../shared/widgets/keyboard_input_overlay.dart';
import '../../../shared/widgets/top_message_overlay.dart';

/// 里程碑编辑底部弹窗
class MilestoneEditSheet extends StatefulWidget {
  final Milestone? milestone; // 可为null，表示新增里程碑
  final String goalId; // 关联的目标ID
  final Function(Milestone) onMilestoneSaved;
  final Goal? goal; // 可选参数，如果提供则用于日期验证

  const MilestoneEditSheet({
    super.key,
    this.milestone,
    required this.goalId,
    required this.onMilestoneSaved,
    this.goal,
  });

  @override
  State<MilestoneEditSheet> createState() => _MilestoneEditSheetState();
}

class _MilestoneEditSheetState extends State<MilestoneEditSheet> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  DateTime? _date;
  static const _maxNameLength = 10;

  @override
  void initState() {
    super.initState();
    if (widget.milestone != null) {
      _nameController.text = widget.milestone!.name;
      _date = widget.milestone!.date;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  void _submitForm() {
    if (_formKey.currentState?.validate() ?? false) {
      if (_date == null) {
        TopMessageOverlayManager().showError(
          context: context,
          message: '请选择里程碑日期',
        );
        return;
      }

      // 获取目标信息进行日期验证
      Goal? goal = widget.goal;

      // 验证日期是否在目标的开始和结束日期之间（仅当目标信息可用时）
      if (goal != null && (_date!.isBefore(goal.startDate) || _date!.isAfter(goal.endDate))) {
        TopMessageOverlayManager().showError(
          context: context,
          message: '里程碑日期必须在目标的开始日期(${goal.startDate.toString().substring(0, 10)})和结束日期(${goal.endDate.toString().substring(0, 10)})之间',
        );
        return;
      }

      final milestone = widget.milestone != null
          ? widget.milestone!.copyWith(
              name: _nameController.text.trim(),
              date: _date,
            )
          : Milestone(
              id: const Uuid().v4(),
              name: _nameController.text.trim(),
              goalId: widget.goalId,
              date: _date!,
            );

      widget.onMilestoneSaved(milestone);
      Navigator.pop(context);
    }
  }





  @override
  Widget build(BuildContext context) {
    final isNewMilestone = widget.milestone == null;
    final title = isNewMilestone ? '添加里程碑' : '编辑里程碑';

    return EnhancedKeyboardFormSheet(
      title: title,
      submitText: '保存',
      onSubmit: _submitForm,
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 里程碑名称输入框
            KeyboardAwareTextField(
              controller: _nameController,
              labelText: '里程碑名称',
              hintText: '请输入里程碑名称',
              maxLength: _maxNameLength,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return '请输入里程碑名称';
                }
                if (value.length > _maxNameLength) {
                  return '里程碑名称不能超过$_maxNameLength个字';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // 日期选择 - 使用自定义日期选择器
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('里程碑日期', style: AppTextStyles.labelMedium),
                const SizedBox(height: 8),
                ImprovedDatePicker(
                  initialDate: _date,
                  firstDate: widget.goal?.startDate ?? DateTime(2000),
                  lastDate: widget.goal?.endDate ?? DateTime(2100),
                  title: '选择里程碑日期',
                  onDateSelected: (date) {
                    if (date != null) {
                      setState(() {
                        _date = date;
                      });
                    }
                  },
                ),
                const SizedBox(height: 8),
                if (widget.goal != null)
                  Text(
                    '可选日期为目标设定范围: ${widget.goal!.startDate.toString().substring(0, 10)} 至 ${widget.goal!.endDate.toString().substring(0, 10)}',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textTertiary,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}