import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';

/// 时间轴容器组件
/// 用于显示一系列时间轴项目
class TimelineContainer extends StatelessWidget {
  final List<Widget> timelineItems;
  final int totalDays;

  const TimelineContainer({
    super.key,
    required this.timelineItems,
    required this.totalDays,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.only(top: 20, bottom: 12), // 减少底部内边距，避免溢出
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(AppColors.alpha10),
            blurRadius: 4,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min, // 确保列只占用所需的最小空间
        children: [
          // 时间轴标题
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "时间轴",
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.text,
                  ),
                ),
                Text(
                  "总计 $totalDays 天",
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textTertiary,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12), // 减少间距

          // 时间轴项目列表
          Flexible(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: timelineItems,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
