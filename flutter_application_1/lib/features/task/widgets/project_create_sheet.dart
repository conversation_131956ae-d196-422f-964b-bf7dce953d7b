import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '/shared/widgets/common_bottom_sheet.dart';
import '../../../core/models/subject_project.dart';
import '../../../core/providers/subscription_provider.dart';
import '../../../core/services/project_limit_service.dart';
import '../../subscription/widgets/project_limit_dialog.dart';
import '../../../shared/widgets/improved_date_picker.dart';
import '../../../shared/widgets/milestone_date_picker.dart';
import '../../../shared/widgets/keyboard_input_overlay.dart';
import '../../../shared/widgets/top_message_overlay.dart';


/// 项目创建底部弹窗
/// 用于创建新的项目
class ProjectCreateSheet extends ConsumerStatefulWidget {
  final List<Subject> subjects;
  final Function(Project) onProjectCreated;

  const ProjectCreateSheet({
    super.key,
    required this.subjects,
    required this.onProjectCreated,
  });

  @override
  ConsumerState<ProjectCreateSheet> createState() => _ProjectCreateSheetState();
}

class _ProjectCreateSheetState extends ConsumerState<ProjectCreateSheet> {
  // 表单控制器
  final TextEditingController _nameController = TextEditingController();
  //
  final GlobalKey _menuKey = GlobalKey();
  // 表单状态
  String? _selectedSubjectId;
  DateTime _startDate = DateTime.now();
  DateTime _endDate = DateTime.now().add(const Duration(days: 30));
  bool _isTrackingEnabled = false;
  ProgressTrackingMode _trackingMode = ProgressTrackingMode.custom;

  // 项目名称最大长度限制
  static const int _maxNameLength = 10;

  // 追踪模式相关控制器
  final TextEditingController _totalHoursController = TextEditingController(text: '10');
  final TextEditingController _customUnitController = TextEditingController(text: '个');
  final TextEditingController _targetValueController = TextEditingController(text: '100');

  @override
  void dispose() {
    _nameController.dispose();
    _totalHoursController.dispose();
    _customUnitController.dispose();
    _targetValueController.dispose();
    super.dispose();
  }

  // 显示追踪模式选择器
  void _showTrackingModeSelector(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
          ),
          backgroundColor: AppColors.cardBackground,
          elevation: 0,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('选择追踪模式', style: AppTextStyles.headline3),
                    IconButton(
                      icon: const Icon(Icons.close, size: 20),
                      onPressed: () => Navigator.pop(context),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      color: AppColors.textSecondary,
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // 专注时间选项
                _buildTrackingModeOption(
                  context,
                  title: '专注时间',
                  description: '根据专注时长追踪项目进度',
                  icon: Icons.timer,
                  isSelected: _trackingMode == ProgressTrackingMode.focusTime,
                  onTap: () {
                    setState(() {
                      _trackingMode = ProgressTrackingMode.focusTime;
                    });
                    Navigator.pop(context);
                  },
                ),

                const SizedBox(height: 12),

                // 自定义选项
                _buildTrackingModeOption(
                  context,
                  title: '自定义',
                  description: '使用自定义单位追踪项目进度',
                  icon: Icons.edit,
                  isSelected: _trackingMode == ProgressTrackingMode.custom,
                  onTap: () {
                    setState(() {
                      _trackingMode = ProgressTrackingMode.custom;
                    });
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 构建追踪模式选项
  Widget _buildTrackingModeOption(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppInputStyles.inputRadiusMedium),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary.withAlpha(20) : AppColors.white,
          borderRadius: BorderRadius.circular(AppInputStyles.inputRadiusMedium),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.border,
            width: isSelected ? 1.5 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: isSelected ? AppColors.primary.withAlpha(30) : AppColors.cardBackground,
                borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
              ),
              alignment: Alignment.center,
              child: Icon(
                icon,
                color: isSelected ? AppColors.primary : AppColors.textSecondary,
                size: 18,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: isSelected ? AppColors.primary : AppColors.text,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: AppColors.primary,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  // 创建项目
  Future<void> _createProject() async {
    final name = _nameController.text.trim();

    // 验证项目名称
    if (name.isEmpty) {
      TopMessageOverlayManager().showError(
        context: context,
        message: '请填写项目名称',
      );
      return;
    }

    if (name.length > _maxNameLength) {
      TopMessageOverlayManager().showError(
        context: context,
        message: '项目名称不能超过$_maxNameLength个字',
      );
      return;
    }

    // 验证科目选择
    if (_selectedSubjectId == null) {
      TopMessageOverlayManager().showError(
        context: context,
        message: '请选择科目',
      );
      return;
    }

    // 如果开启了进度追踪，检查项目数量限制
    if (_isTrackingEnabled) {
      final subscriptionStatus = ref.read(appleSubscriptionStatusProvider);
      final isPremium = subscriptionStatus.value ?? false;

      final projectLimitService = ProjectLimitService();
      final canCreate = await projectLimitService.canCreateTrackingProject(
        isPremiumUser: isPremium,
      );

      if (!canCreate) {
        final currentCount = await projectLimitService.getCurrentTrackingProjectCount();

        // 显示限制提示弹窗
        if (mounted) {
          showDialog(
            context: context,
            builder: (context) => ProjectLimitDialog(
              currentCount: currentCount,
              limit: ProjectLimitService.freeProjectLimit,
            ),
          );
        }
        return;
      }
    }

    // 创建项目对象
    final project = Project(
      id: DateTime.now().millisecondsSinceEpoch.toString(), // 临时ID生成方式
      name: _nameController.text,
      subjectId: _selectedSubjectId!,
      startDate: _startDate,
      endDate: _endDate,
      isTrackingEnabled: _isTrackingEnabled,
      trackingMode: _isTrackingEnabled ? _trackingMode : null,
      totalFocusHours: _isTrackingEnabled && _trackingMode == ProgressTrackingMode.focusTime ?
          double.tryParse(_totalHoursController.text) : null,
      customUnit: _isTrackingEnabled && _trackingMode == ProgressTrackingMode.custom ?
          _customUnitController.text : null,
      targetValue: _isTrackingEnabled && _trackingMode == ProgressTrackingMode.custom ?
          int.tryParse(_targetValueController.text) : null,
    );

    // 回调通知创建成功
    widget.onProjectCreated(project);

    // 关闭弹窗
    if (mounted) {
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return CommonBottomSheet(
      title: "创建项目",
      contentPadding: const EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 32.0), // 增加底部间距
      actions: [
        TextButton(
          onPressed: _createProject,
          child: const Text('创建', style: TextStyle(color: AppColors.primary)),
        ),
      ],
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 项目名称输入
            const Text('项目名称', style: AppTextStyles.labelMedium),
            const SizedBox(height: 8),
            KeyboardAwareTextField(
              controller: _nameController,
              hintText: '请输入项目名称',
              maxLength: _maxNameLength,
            ),

            const SizedBox(height: 24),

            // 科目选择
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('科目', style: AppTextStyles.labelMedium),
                if (widget.subjects.isNotEmpty)
                  Text(
                    '选择关联科目',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textTertiary,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),

            // 科目选择标签
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                border: Border.all(color: AppColors.border),
              ),
              child: widget.subjects.isEmpty
                ? const Center(
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 8.0),
                      child: Text(
                        '暂无科目，请先创建科目',
                        style: AppTextStyles.bodyMedium,
                      ),
                    ),
                  )
                : Wrap(
                    spacing: 8,
                    runSpacing: 10,
                    children: widget.subjects.map((subject) {
                      final isSelected = _selectedSubjectId == subject.id;
                      final subjectColor = Color(subject.color);

                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedSubjectId = isSelected ? null : subject.id;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: isSelected ? subjectColor.withAlpha(30) : Colors.white,
                            borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                            border: Border.all(
                              color: isSelected ? subjectColor : AppColors.border,
                              width: isSelected ? 1.5 : 1.0,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // 科目颜色标识
                              Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: subjectColor,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 6),
                              // 科目名称
                              Text(
                                subject.name,
                                style: AppTextStyles.bodyMedium.copyWith(
                                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                  color: isSelected ? subjectColor : AppColors.text,
                                ),
                              ),
                              // 选中标识
                              if (isSelected) ...[
                                const SizedBox(width: 4),
                                Icon(
                                  Icons.check,
                                  color: subjectColor,
                                  size: 16,
                                ),
                              ],
                            ],
                          ),
                        ),
                      );
                    }).toList(),
                  ),
            ),

            const SizedBox(height: 24),

            // 日期选择
            const Text('预期执行日期', style: AppTextStyles.labelMedium),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: AppInputStyles.selectorDecoration(),
                    child: ImprovedDatePicker(
                      initialDate: _startDate,
                      firstDate: DateTime.now().subtract(const Duration(days: 365)),
                      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
                      title: '选择开始日期',
                      onDateSelected: (date) {
                        if (date != null) {
                          setState(() {
                            _startDate = date;
                            // 确保结束日期不早于开始日期
                            if (_endDate.isBefore(_startDate)) {
                              _endDate = _startDate.add(const Duration(days: 30));
                            }
                          });
                        }
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                const Icon(Icons.arrow_forward, color: AppColors.textHint, size: 16),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(
                    decoration: AppInputStyles.selectorDecoration(),
                    child: MilestoneDatePicker(
                      initialDate: _endDate,
                      firstDate: _startDate,
                      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
                      title: '选择结束日期',
                      buttonText: '选择结束日期',
                      onDateSelected: (date) {
                        if (date != null) {
                          setState(() {
                            _endDate = date;
                          });
                        }
                      },
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // 进度追踪开关
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('进度追踪', style: AppTextStyles.labelMedium),
                Switch(
                  value: _isTrackingEnabled,
                  activeColor: AppColors.primary,
                  onChanged: (value) {
                    setState(() {
                      _isTrackingEnabled = value;
                    });
                  },
                ),
              ],
            ),

            // 进度追踪选项
            if (_isTrackingEnabled) ...[
              const SizedBox(height: 16),
              Container(
                decoration: BoxDecoration(
                  color: AppColors.cardBackground,
                  borderRadius: BorderRadius.circular(AppInputStyles.inputRadiusMedium),
                  border: Border.all(color: AppColors.border),
                  boxShadow: [AppShadows.low],
                ),
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 追踪模式选择器
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('追踪模式', style: AppTextStyles.labelMedium),
                        InkWell(
                          key: _menuKey,
                          onTap: () {
                            _showTrackingModeSelector(context);
                          },
                          borderRadius: BorderRadius.circular(AppInputStyles.inputRadiusMedium),
                          child: Container(
                            height: AppInputStyles.inputHeightSmall,
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            decoration: BoxDecoration(
                              color: AppColors.white,
                              borderRadius: BorderRadius.circular(AppInputStyles.inputRadiusMedium),
                              border: Border.all(color: AppColors.border),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                  _trackingMode == ProgressTrackingMode.focusTime ? '专注时间' : '自定义',
                                  style: AppInputStyles.inputTextStyle,
                                ),
                                const SizedBox(width: 8),
                                const Icon(Icons.keyboard_arrow_down, size: 16, color: AppColors.textSecondary),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const Divider(color: AppColors.divider, height: 1),
                    const SizedBox(height: 16),

                    // 专注时间模式配置
                    if (_trackingMode == ProgressTrackingMode.focusTime)...[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('目标总时间', style: AppTextStyles.labelMedium),
                          // 使用带单位的输入框
                          Container(
                            width: AppInputStyles.inputWidthSmall,
                            height: AppInputStyles.inputHeightSmall,
                            decoration: AppInputStyles.inputWithUnitDecoration(),
                            child: Row(
                              children: [
                                // 输入框部分
                                Expanded(
                                  flex: 2,
                                  child: Center(
                                    child: GestureDetector(
                                      onTap: () {
                                        KeyboardInputOverlayManager().showInputBar(
                                          context: context,
                                          controller: _totalHoursController,
                                          hint: '10',
                                          label: '目标总时间',
                                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                        );
                                      },
                                      child: AbsorbPointer(
                                        child: TextField(
                                          controller: _totalHoursController,
                                          keyboardType: TextInputType.number,
                                          textAlign: TextAlign.center,
                                          style: AppInputStyles.inputTextStyle,
                                          decoration: AppInputStyles.inputDecorationNoBorder(
                                            hintText: '10',
                                            contentPadding: EdgeInsets.zero,
                                            isDense: true,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                // 分隔线
                                Container(
                                  width: 1,
                                  height: 20,
                                  color: AppColors.divider,
                                ),
                                // 单位部分
                                Expanded(
                                  flex: 1,
                                  child: Container(
                                    alignment: Alignment.center,
                                    child: const Text(
                                      '小时',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: AppColors.textSecondary,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],

                    // 自定义模式配置
                    if (_trackingMode == ProgressTrackingMode.custom)...[
                      // 单位名称
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('单位名称', style: AppTextStyles.labelMedium),
                          Container(
                            width: AppInputStyles.inputWidthSmall,
                            height: AppInputStyles.inputHeightSmall,
                            decoration: AppInputStyles.inputWithUnitDecoration(),
                            child: Center(
                              child: GestureDetector(
                                onTap: () {
                                  KeyboardInputOverlayManager().showInputBar(
                                    context: context,
                                    controller: _customUnitController,
                                    hint: '个',
                                    label: '单位名称',
                                    maxLength: 5,
                                  );
                                },
                                child: AbsorbPointer(
                                  child: TextField(
                                    controller: _customUnitController,
                                    textAlign: TextAlign.center,
                                    style: AppInputStyles.inputTextStyle,
                                    decoration: AppInputStyles.inputDecorationNoBorder(
                                      hintText: '个',
                                      contentPadding: EdgeInsets.zero,
                                      isDense: true,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // 目标数值
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('目标数值', style: AppTextStyles.labelMedium),
                          Container(
                            width: AppInputStyles.inputWidthSmall,
                            height: AppInputStyles.inputHeightSmall,
                            decoration: AppInputStyles.inputWithUnitDecoration(),
                            child: Row(
                              children: [
                                // 输入框部分
                                Expanded(
                                  flex: 2,
                                  child: Center(
                                    child: GestureDetector(
                                      onTap: () {
                                        KeyboardInputOverlayManager().showInputBar(
                                          context: context,
                                          controller: _targetValueController,
                                          hint: '100',
                                          label: '目标数值',
                                          keyboardType: const TextInputType.numberWithOptions(decimal: false),
                                        );
                                      },
                                      child: AbsorbPointer(
                                        child: TextField(
                                          controller: _targetValueController,
                                          keyboardType: TextInputType.number,
                                          textAlign: TextAlign.center,
                                          style: AppInputStyles.inputTextStyle,
                                          decoration: AppInputStyles.inputDecorationNoBorder(
                                            hintText: '100',
                                            contentPadding: EdgeInsets.zero,
                                            isDense: true,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                // 分隔线
                                Container(
                                  width: 1,
                                  height: 20,
                                  color: AppColors.divider,
                                ),
                                // 单位部分
                                Expanded(
                                  flex: 1,
                                  child: Container(
                                    alignment: Alignment.center,
                                    child: Text(
                                      _customUnitController.text.isEmpty ? '单位' : _customUnitController.text,
                                      style: const TextStyle(
                                        fontSize: 12,
                                        color: AppColors.textSecondary,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],

                    const SizedBox(height: 8),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}