import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/models/subject_project.dart';
import 'project_card.dart';
import 'subject_manage_sheet.dart';
import 'project_create_sheet.dart';
import 'subject_add_sheet.dart';
import 'subject_edit_sheet.dart';
import '../providers/subject_state.dart';
import '../../../core/services/hive_service.dart';

/// 使用Riverpod状态管理的项目展示卡片
/// 解决了原有StatefulWidget实现中TabController更新不同步的问题
/// 使用外部滚动，取消内部滚动，整体卡片高度随项目增加而增加
class SubjectProjectCardRiverpod extends ConsumerStatefulWidget {
  const SubjectProjectCardRiverpod({super.key});

  @override
  ConsumerState<SubjectProjectCardRiverpod> createState() => _SubjectProjectCardRiverpodState();
}

class _SubjectProjectCardRiverpodState extends ConsumerState<SubjectProjectCardRiverpod> with TickerProviderStateMixin {
  // 标签控制器
  TabController? _tabController;

  // Hive服务
  final HiveService _hiveService = HiveService();

  @override
  void initState() {
    super.initState();
    // 检查是否已有数据，如果有则直接创建TabController，否则加载数据
    final subjectState = ref.read(subjectStateProvider);
    if (subjectState.subjects.isNotEmpty) {
      // 已有数据，直接创建TabController
      _updateTabController();
    } else {
      // 没有数据，从Hive加载
      _loadData();
    }
  }

  @override
  void didUpdateWidget(SubjectProjectCardRiverpod oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当组件更新时，检查是否需要更新TabController
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _updateTabController();
      }
    });
  }

  // 从Hive加载数据
  Future<void> _loadData() async {
    debugPrint('SubjectProjectCardRiverpod._loadData 开始');
    try {
      // 确保Hive服务已初始化
      debugPrint('初始化Hive...');
      await _hiveService.initHive();
      debugPrint('Hive初始化成功');

      // 确保SubjectRepository已初始化
      if (!_hiveService.subjectRepository.isInitialized) {
        debugPrint('SubjectRepository未初始化，正在初始化...');
        await _hiveService.subjectRepository.init();
        debugPrint('SubjectRepository初始化成功');
      }

      // 获取所有科目
      debugPrint('获取所有科目...');
      final subjects = _hiveService.subjectRepository.getAllSubjects();
      debugPrint('获取到${subjects.length}个科目');

      // 获取所有非归档项目
      debugPrint('获取所有非归档项目...');
      final projects = _hiveService.subjectRepository.getAllProjects(includeArchived: false);
      debugPrint('获取到${projects.length}个非归档项目');

      // 更新Riverpod状态
      if (mounted) {
        debugPrint('更新Riverpod状态...');
        ref.read(subjectStateProvider.notifier).setSubjects(subjects);
        ref.read(subjectStateProvider.notifier).setProjects(projects);
        debugPrint('Riverpod状态更新成功');

        // 创建TabController
        debugPrint('创建TabController...');
        _updateTabController();
        debugPrint('TabController创建成功');
      }

      debugPrint('SubjectProjectCardRiverpod._loadData 完成');
    } catch (e) {
      debugPrint('SubjectProjectCardRiverpod._loadData 失败: $e');
      if (mounted) {
        ref.read(subjectStateProvider.notifier).setError('加载数据失败: $e');
        // 显示错误信息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载数据失败: $e')),
        );
      }
    }
  }

  // 更新TabController
  void _updateTabController() {
    debugPrint('_updateTabController 开始');
    final subjectState = ref.read(subjectStateProvider);

    debugPrint('当前科目数量: ${subjectState.subjects.length}');
    for (var s in subjectState.subjects) {
      debugPrint('当前科目: ${s.id}, ${s.name}');
    }

    // 如果科目为空，重置TabController并返回
    if (subjectState.subjects.isEmpty) {
      debugPrint('科目为空，重置TabController');
      if (_tabController != null) {
        _tabController!.removeListener(_handleTabSelection);
        _tabController!.dispose();
        _tabController = null;
      }
      // 强制刷新UI
      if (mounted) {
        setState(() {});
      }
      return;
    }

    // 如果TabController已存在，先移除监听器并释放资源
    if (_tabController != null) {
      debugPrint('释放旧的TabController');
      _tabController!.removeListener(_handleTabSelection);
      _tabController!.dispose();
    }

    // 确保currentTabIndex在有效范围内
    int initialIndex = subjectState.currentTabIndex;
    if (initialIndex >= subjectState.subjects.length + 1) {
      debugPrint('currentTabIndex超出范围，重置为0');
      initialIndex = 0;
      // 更新状态
      ref.read(subjectStateProvider.notifier).setCurrentTabIndex(0);
    }

    // 创建新的TabController
    debugPrint('创建新的TabController，长度: ${subjectState.subjects.length + 1}, 初始索引: $initialIndex');
    _tabController = TabController(
      length: subjectState.subjects.length + 1,
      vsync: this,
      initialIndex: initialIndex,
    );
    _tabController!.addListener(_handleTabSelection);

    // 强制刷新UI
    if (mounted) {
      setState(() {});
    }

    debugPrint('_updateTabController 完成');
  }

  @override
  void dispose() {
    if (_tabController != null) {
      _tabController!.removeListener(_handleTabSelection);
      _tabController!.dispose();
    }
    super.dispose();
  }

  // 处理标签选择事件
  void _handleTabSelection() {
    if (_tabController != null && _tabController!.indexIsChanging) {
      ref.read(subjectStateProvider.notifier).setCurrentTabIndex(_tabController!.index);
    }
  }

  // 显示添加科目对话框
  void _showAddSubjectDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SubjectAddSheet(
        onSubjectAdded: (subject) async {
          debugPrint('添加科目: ${subject.id}, ${subject.name}');
          try {
            // 确保Hive已初始化
            if (!_hiveService.subjectRepository.isInitialized) {
              debugPrint('SubjectRepository未初始化，正在初始化...');
              await _hiveService.subjectRepository.init();
              debugPrint('SubjectRepository初始化成功');
            }

            // 保存到Hive
            debugPrint('保存科目到Hive...');
            await _hiveService.subjectRepository.saveSubject(subject);
            debugPrint('科目保存成功');

            // 更新Riverpod状态
            debugPrint('更新Riverpod状态...');
            ref.read(subjectStateProvider.notifier).addSubject(subject);
            debugPrint('Riverpod状态更新成功');

            // 使用WidgetsBinding.instance.addPostFrameCallback确保状态更新后再更新TabController
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                debugPrint('更新TabController...');
                _updateTabController();
                debugPrint('TabController更新成功');
              }
            });

            // 科目添加成功后界面会自动显示新科目，无需额外提示
          } catch (e) {
            debugPrint('添加科目失败: $e');
            // 显示错误消息
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('添加科目失败: $e')),
              );
            }
          }
        },
      ),
    );
  }

  // 构建项目列表 - 不使用滚动视图
  Widget _buildProjectList(List<Project> projects) {
    if (projects.isEmpty) {
      return Container(
        height: 200, // 空状态时的固定高度
        alignment: Alignment.center,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assignment_outlined,
              size: 40,
              color: AppColors.textHint,
            ),
            const SizedBox(height: 12),
            const Text(
              "暂无项目",
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              "点击右上角添加项目",
              style: TextStyle(
                fontSize: 13,
                color: AppColors.textTertiary,
              ),
            ),
          ],
        ),
      );
    }

    // 使用Column显示项目列表
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: projects.map((project) {
        final selectedProject = ref.watch(subjectStateProvider).currentProject;

        // 获取项目对应的科目
        Subject? subject;
        try {
          subject = ref.read(subjectStateProvider).subjects
              .firstWhere((s) => s.id == project.subjectId);
        } catch (e) {
          // 如果找不到对应的科目，subject 将保持为 null
        }

        return ProjectCard(
          project: project,
          isSelected: selectedProject?.id == project.id,
          subject: subject, // 传递科目参数
          onTap: () {
            ref.read(subjectStateProvider.notifier).setCurrentProject(project);
            // 同时设置当前项目分类
            if (subject != null) {
              ref.read(subjectStateProvider.notifier).setCurrentSubject(subject);
            }
          },
          onEdit: () {
            // 直接在ProjectCard中处理点击事件
          },
        );
      }).toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('SubjectProjectCardRiverpod.build 开始');
    // 监听科目状态
    final subjectState = ref.watch(subjectStateProvider);
    final subjects = subjectState.subjects;
    final projects = subjectState.projects;

    debugPrint('build 方法中的科目数量: ${subjects.length}');
    for (var s in subjects) {
      debugPrint('build 方法中的科目: ${s.id}, ${s.name}');
    }

    // 如果没有科目，显示空状态
    if (subjects.isEmpty) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.0),
          boxShadow: [AppShadows.low],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              "项目管理",
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: 24),
            Icon(
              Icons.subject_outlined,
              size: 64,
              color: AppColors.primary.withAlpha(128), // 0.5 opacity
            ),
            const SizedBox(height: 16),
            const Text(
              "暂无科目（标签）",
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: 8),
            const Text(
              "添加科目（标签）以开始管理您的项目",
              style: AppTextStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              icon: const Icon(Icons.add),
              label: const Text("添加科目"),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: _showAddSubjectDialog,
            ),
          ],
        ),
      );
    }

    // 使用简单的Card，不设置固定高度，让高度随内容自适应
    return Card(
      margin: EdgeInsets.zero,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min, // 确保Column只占用所需的空间
          children: [
            // 标题栏 - 优化版
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  "项目管理",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.text,
                  ),
                ),
                // 添加项目按钮 - 带文字的版本
                ElevatedButton.icon(
                  icon: const Icon(Icons.add, size: 16),
                  label: const Text("添加项目"),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary.withAlpha(15),
                    foregroundColor: AppColors.primary,
                    elevation: 0,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    textStyle: const TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  onPressed: () {
                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      backgroundColor: Colors.transparent,
                      builder: (context) => ProjectCreateSheet(
                        subjects: subjects,
                        onProjectCreated: (project) async {
                          // 保存到Hive
                          await _hiveService.subjectRepository.saveProject(project);

                          // 更新Riverpod状态
                          ref.read(subjectStateProvider.notifier).addProject(project);
                        },
                      ),
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 项目分类标签栏 - 重新优化版
            Row(
              children: [
                Expanded(
                  child: _tabController == null || _tabController!.length != subjects.length + 1
                  ? SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: [
                          // 默认全部标签
                          Container(
                            margin: const EdgeInsets.only(right: 6.0),
                            padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 6.0),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16.0),
                              color: AppColors.primary.withAlpha(25),
                              border: Border.all(
                                color: AppColors.primary,
                                width: 0.8,
                              ),
                            ),
                            child: const Text(
                              "全部",
                              style: TextStyle(
                                color: AppColors.primary,
                                fontWeight: FontWeight.w500,
                                fontSize: 13.0,
                              ),
                            ),
                          ),
                          // 科目标签
                          ...subjects.map((subject) {
                            return Container(
                              margin: const EdgeInsets.only(right: 6.0),
                              padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 6.0),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16.0),
                                color: Colors.transparent,
                                border: Border.all(
                                  color: AppColors.divider,
                                  width: 0.8,
                                ),
                              ),
                              child: Text(
                                subject.name,
                                style: const TextStyle(
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.normal,
                                  fontSize: 13.0,
                                ),
                              ),
                            );
                          }),
                        ],
                      ),
                    )
                  : TabBar(
                    controller: _tabController,
                    isScrollable: true,
                    labelColor: AppColors.primary,
                    unselectedLabelColor: AppColors.textSecondary,
                    // 移除默认indicator，我们将在Tab内部处理选中状态
                    indicator: const BoxDecoration(),
                    indicatorSize: TabBarIndicatorSize.label,
                    labelPadding: const EdgeInsets.symmetric(horizontal: 6.0),
                    padding: EdgeInsets.zero,
                    indicatorPadding: EdgeInsets.zero,
                    tabAlignment: TabAlignment.start,
                    labelStyle: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 13.0,
                    ),
                    unselectedLabelStyle: const TextStyle(
                      fontWeight: FontWeight.normal,
                      fontSize: 13.0,
                    ),
                    dividerColor: Colors.transparent, // 移除分隔线
                    tabs: [
                      // 全部标签 - 改进版
                      Tab(
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 6.0),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16.0),
                            color: _tabController!.index == 0
                                ? AppColors.primary.withAlpha(25)
                                : Colors.transparent,
                            border: Border.all(
                              color: _tabController!.index == 0
                                  ? AppColors.primary
                                  : AppColors.divider,
                              width: 0.8,
                            ),
                          ),
                          child: const Text("全部"),
                        ),
                      ),
                      // 项目分类标签 - 改进版
                      ...subjects.asMap().entries.map((entry) {
                        final int index = entry.key + 1; // +1 因为"全部"是第一个标签
                        final subject = entry.value;
                        return Tab(
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 6.0),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16.0),
                              color: _tabController!.index == index
                                  ? AppColors.primary.withAlpha(25)
                                  : Colors.transparent,
                              border: Border.all(
                                color: _tabController!.index == index
                                    ? AppColors.primary
                                    : AppColors.divider,
                                width: 0.8,
                              ),
                            ),
                            child: Text(subject.name),
                          ),
                        );
                      }),
                    ],
                  ),
                ),
                // 项目分类管理按钮 - 与目标编辑按钮保持一致
                Container(
                  height: 32,
                  width: 32,
                  margin: const EdgeInsets.only(left: 8.0),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.0),
                    color: AppColors.background, // 与目标编辑按钮保持一致
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.settings_outlined, size: 16),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    color: AppColors.textSecondary,
                    tooltip: "管理项目分类",
                    onPressed: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder: (context) => SubjectManageSheet(
                          subjects: subjects,
                          onAddSubject: () {
                            Navigator.pop(context);
                            _showAddSubjectDialog();
                          },
                          onEditSubject: (subject) {
                            Navigator.pop(context);
                            // 显示编辑科目底部弹窗
                            showModalBottomSheet(
                              context: context,
                              isScrollControlled: true,
                              backgroundColor: Colors.transparent,
                              builder: (sheetContext) => SubjectEditSheet(
                                subject: subject,
                                onSubjectEdited: (editedSubject) async {
                                  debugPrint('编辑科目: ${editedSubject.id}, ${editedSubject.name}');
                                  try {
                                    // 确保Hive已初始化
                                    if (!_hiveService.subjectRepository.isInitialized) {
                                      debugPrint('SubjectRepository未初始化，正在初始化...');
                                      await _hiveService.subjectRepository.init();
                                      debugPrint('SubjectRepository初始化成功');
                                    }

                                    // 保存到Hive
                                    debugPrint('保存科目到Hive...');
                                    await _hiveService.subjectRepository.saveSubject(editedSubject);
                                    debugPrint('科目保存成功');

                                    // 更新Riverpod状态
                                    debugPrint('更新Riverpod状态...');
                                    ref.read(subjectStateProvider.notifier).updateSubject(editedSubject);
                                    debugPrint('Riverpod状态更新成功');

                                    // 更新TabController
                                    debugPrint('更新TabController...');
                                    _updateTabController();
                                    debugPrint('TabController更新成功');

                                    // 显示成功消息
                                    if (mounted) {
                                      ScaffoldMessenger.of(sheetContext).showSnackBar(
                                        const SnackBar(content: Text('科目编辑成功')),
                                      );
                                    }
                                  } catch (e) {
                                    debugPrint('编辑科目出错: $e');
                                    // 显示错误消息
                                    if (mounted) {
                                      ScaffoldMessenger.of(sheetContext).showSnackBar(
                                        SnackBar(content: Text('编辑科目失败: $e')),
                                      );
                                    }
                                  }
                                },
                              ),
                            );
                          },
                          onDeleteSubject: (subject) async {
                            debugPrint('开始删除科目: ${subject.id}, ${subject.name}');
                            // 先关闭弹窗
                            Navigator.pop(context);

                            try {
                              // 确保Hive已初始化
                              if (!_hiveService.subjectRepository.isInitialized) {
                                debugPrint('SubjectRepository未初始化，正在初始化...');
                                await _hiveService.subjectRepository.init();
                                debugPrint('SubjectRepository初始化成功');
                              }

                              // 直接删除科目，不再显示确认弹窗（因为SubjectManageSheet中已经有确认弹窗）
                              debugPrint('调用 deleteSubject 方法前');
                              await _hiveService.subjectRepository.deleteSubject(subject.id);
                              debugPrint('调用 deleteSubject 方法后');

                              // 检查科目是否真的被删除
                              final remainingSubjects = _hiveService.subjectRepository.getAllSubjects();
                              debugPrint('删除后剩余科目数量: ${remainingSubjects.length}');
                              for (var s in remainingSubjects) {
                                debugPrint('剩余科目: ${s.id}, ${s.name}');
                              }

                              // 更新Riverpod状态
                              debugPrint('更新 Riverpod 状态前');
                              ref.read(subjectStateProvider.notifier).deleteSubject(subject.id);
                              debugPrint('更新 Riverpod 状态后');

                              // 检查 Riverpod 状态是否更新
                              final stateSubjects = ref.read(subjectStateProvider).subjects;
                              debugPrint('Riverpod 状态更新后科目数量: ${stateSubjects.length}');
                              for (var s in stateSubjects) {
                                debugPrint('Riverpod 中的科目: ${s.id}, ${s.name}');
                              }

                              // 更新TabController
                              debugPrint('更新 TabController 前');
                              _updateTabController();
                              debugPrint('更新 TabController 后');

                              // 强制刷新UI
                              if (mounted) {
                                setState(() {});
                                debugPrint('强制刷新UI完成');

                                // 显示成功消息
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(content: Text('科目删除成功')),
                                );
                              }
                            } catch (e) {
                              debugPrint('删除科目出错: $e');
                              // 显示错误消息
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(content: Text('删除科目失败: $e')),
                                );
                              }
                            }
                          },
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 项目列表区域 - 不使用TabBarView，而是直接根据当前选中的标签显示对应的项目列表
            Builder(
              builder: (context) {
                // 如果TabController尚未创建或长度不匹配，显示全部项目
                if (_tabController == null || _tabController!.length != subjects.length + 1) {
                  return _buildProjectList(projects);
                }

                final int currentIndex = _tabController!.index;

                // 根据当前选中的标签显示对应的项目列表
                if (currentIndex == 0) {
                  // 全部项目
                  return _buildProjectList(projects);
                } else if (currentIndex - 1 < subjects.length) {
                  // 特定分类的项目
                  final subject = subjects[currentIndex - 1];
                  final subjectProjects = projects
                      .where((p) => p.subjectId == subject.id && !p.isArchived)
                      .toList();
                  return _buildProjectList(subjectProjects);
                } else {
                  // 索引超出范围，显示全部项目
                  return _buildProjectList(projects);
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
