import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/models/subject_project.dart';
import '../screens/project_detail_screen.dart';

/// 项目卡片组件
/// 用于在任务目标页面展示项目信息
class ProjectCard extends StatelessWidget {
  final Project project;
  final bool isSelected;
  final VoidCallback onTap;
  final VoidCallback? onEdit;
  final Subject? subject; // 添加科目参数，用于获取科目颜色

  const ProjectCard({
    super.key,
    required this.project,
    this.isSelected = false,
    required this.onTap,
    this.onEdit,
    this.subject, // 可选参数，如果不提供，将使用默认颜色
  });

  @override
  Widget build(BuildContext context) {
    // 计算进度百分比用于显示
    final double progressPercentage = project.isTrackingEnabled && project.trackingMode != null
        ? project.trackingMode == ProgressTrackingMode.focusTime
            ? ((project.currentFocusHours ?? 0) / (project.totalFocusHours ?? 1) * 100).clamp(0, 100)
            : ((project.currentCustomValue ?? 0) / (project.targetValue ?? 1) * 100).clamp(0, 100)
        : 0.0;

    // 确定项目图标和颜色
    IconData projectIcon = Icons.assignment_outlined;
    Color projectColor;

    // 如果提供了科目，使用科目的颜色
    if (subject != null) {
      projectColor = Color(subject!.color);
    } else {
      // 默认使用蓝色
      projectColor = AppColors.info;

      // 根据项目名称设置不同的图标和颜色
      if (project.name.contains("单词")) {
        projectIcon = Icons.menu_book_outlined;
        projectColor = AppColors.purple; // 紫色
      } else if (project.name.contains("论")) {
        projectIcon = Icons.article_outlined;
        projectColor = AppColors.orange; // 橙色
      } else if (project.name.contains("复习")) {
        projectIcon = Icons.replay_outlined;
        projectColor = AppColors.green; // 绿色
      } else if (project.name.contains("练习") || project.name.contains("题")) {
        projectIcon = Icons.edit_note_outlined;
        projectColor = AppColors.amber; // 琥珀色
      }
    }

    return Card(
      elevation: 2, // 添加轻微阴影增加层次感
      shadowColor: AppColors.black.withAlpha(25), // 约10%透明度
      color: Colors.white, // 始终使用白色背景，不再有选中状态
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10), // 稍微减小圆角
        side: BorderSide(
          color: AppColors.divider, // 始终使用相同的边框颜色
          width: 0.8,
        ),
      ),
      margin: const EdgeInsets.only(bottom: 10.0),
      child: InkWell(
        onTap: () {
          // 首先执行原来的 onTap 事件（选中项目，但不再显示选中状态）
          onTap();

          // 然后导航到项目详情页面
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ProjectDetailScreen(project: project),
            ),
          );
        },
        borderRadius: BorderRadius.circular(10),
        child: Padding(
          padding: const EdgeInsets.all(14.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 项目名称和详情按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // 左侧：项目图标和名称
                  Row(
                    children: [
                      // 项目图标 - 使用不同颜色
                      Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: projectColor.withAlpha(30),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          projectIcon,
                          size: 18,
                          color: projectColor,
                        ),
                      ),
                      const SizedBox(width: 10),
                      // 项目名称
                      Text(
                        project.name,
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: AppColors.text,
                        ),
                      ),
                    ],
                  ),

                  // 右侧：详情按钮 - 使用更简洁的设计
                  Container(
                    height: 28,
                    width: 28,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                      color: AppColors.background,
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.arrow_forward_ios,
                        size: 14,
                        color: AppColors.textSecondary,
                      ),
                      constraints: const BoxConstraints(),
                      padding: EdgeInsets.zero,
                      tooltip: "查看详情",
                      onPressed: () {
                        // 打开项目详情页面
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ProjectDetailScreen(project: project),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),

              // 项目进度条和详情信息（仅在开启追踪时显示）
              if (project.isTrackingEnabled && project.trackingMode != null) ...[
                const SizedBox(height: 12),

                // 进度信息行 - 重新设计
                Row(
                  children: [
                    // 进度百分比 - 使用项目对应的颜色
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: projectColor.withAlpha(20),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        "${progressPercentage.toInt()}%",
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: projectColor,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),

                    // 进度详情
                    Expanded(
                      child: Text(
                        project.trackingMode == ProgressTrackingMode.focusTime
                            ? (project.totalFocusHours != null
                                ? "${(project.currentFocusHours ?? 0).toStringAsFixed(1)}/${project.totalFocusHours} 小时"
                                : "未设置目标时长")
                            : (project.targetValue != null && project.customUnit != null
                                ? "${(project.currentCustomValue ?? 0)}/${project.targetValue} ${project.customUnit}"
                                : "未设置目标数值"),
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // 进度条 - 使用项目对应的颜色
                ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: LinearProgressIndicator(
                    value: project.trackingMode == ProgressTrackingMode.focusTime
                        ? (project.currentFocusHours ?? 0) / (project.totalFocusHours ?? 1)
                        : (project.currentCustomValue ?? 0) / (project.targetValue ?? 1),
                    backgroundColor: AppColors.background,
                    valueColor: AlwaysStoppedAnimation<Color>(projectColor),
                    minHeight: 6,
                  ),
                ),

                // 日期信息 - 移到底部，单独一行，更清晰
                const SizedBox(height: 8),
                Align(
                  alignment: Alignment.centerRight,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: AppColors.background,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          project.startDate.toString().substring(5, 10),
                          style: const TextStyle(
                            fontSize: 11,
                            color: AppColors.textTertiary,
                          ),
                        ),
                        const SizedBox(width: 4),
                        const Icon(
                          Icons.arrow_forward,
                          size: 10,
                          color: AppColors.textTertiary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          project.endDate.toString().substring(5, 10),
                          style: const TextStyle(
                            fontSize: 11,
                            color: AppColors.textTertiary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],

              // 如果没有启用进度追踪，显示简单的项目信息
              if (!project.isTrackingEnabled || project.trackingMode == null) ...[
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // 左侧：项目状态标签
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: AppColors.background,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Text(
                        "未设置进度",
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),

                    // 右侧：日期信息 - 更清晰的设计
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: AppColors.background,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            project.startDate.toString().substring(5, 10),
                            style: const TextStyle(
                              fontSize: 11,
                              color: AppColors.textTertiary,
                            ),
                          ),
                          const SizedBox(width: 4),
                          const Icon(
                            Icons.arrow_forward,
                            size: 10,
                            color: AppColors.textTertiary,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            project.endDate.toString().substring(5, 10),
                            style: const TextStyle(
                              fontSize: 11,
                              color: AppColors.textTertiary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}