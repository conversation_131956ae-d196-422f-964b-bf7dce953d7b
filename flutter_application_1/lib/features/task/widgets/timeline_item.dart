import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';

/// 时间轴项目组件
/// 每个项目包含左侧日期、中间节点和右侧内容
class TimelineItem extends StatelessWidget {
  final DateTime date;
  final String title;
  final String subtitle;
  final bool isStart;
  final bool isEnd;
  final bool isPast;
  final bool isNext;
  final Color statusColor;
  final bool isLastItem; // 是否为最后一个项目，用于决定是否显示连接线

  const TimelineItem({
    super.key,
    required this.date,
    required this.title,
    required this.subtitle,
    required this.isStart,
    required this.isEnd,
    this.isPast = false,
    this.isNext = false,
    required this.statusColor,
    required this.isLastItem,
  });

  @override
  Widget build(BuildContext context) {
    // 确定节点样式
    final bool isFilledNode = isPast;

    // 节点颜色
    final Color nodeColor = isPast
        ? AppColors.success
        : isNext
            ? AppColors.warning
            : AppColors.primary;

    // 使用没有固定高度的布局，让内容自然扩展
    return Padding(
      padding: const EdgeInsets.only(bottom: 0.0), // 移除底部间距，由线段和节点控制间距
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 左侧日期 - 分为两行显示
          SizedBox(
            width: 50,
            child: Padding(
              padding: const EdgeInsets.only(left: 8, top: 8),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  // 月-日
                  Text(
                    "${date.month}-${date.day.toString().padLeft(2, '0')}",
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.right,
                  ),
                  // 年份
                  Text(
                    "${date.year}",
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textTertiary,
                      fontSize: 10,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ],
              ),
            ),
          ),

          // 中间时间轴节点和连接线
          _buildNodeWithLine(nodeColor, isFilledNode),

          // 右侧内容卡片
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(top: 8, right: 16),
              child: Container(
                padding: isStart || isEnd
                    ? EdgeInsets.zero
                    : const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: isStart || isEnd
                    ? null
                    : BoxDecoration(
                        color: statusColor.withAlpha(AppColors.alpha10),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: statusColor.withAlpha(AppColors.alpha20),
                          width: 1,
                        ),
                      ),
                // 移除高度约束，让内容自然扩展
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题
                    Text(
                      title,
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isStart || isEnd ? statusColor : AppColors.text,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    // 副标题（状态）- 只对里程碑显示
                    if (subtitle.isNotEmpty && !isStart && !isEnd) ...[
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            color: statusColor.withAlpha(AppColors.alpha30),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          subtitle,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: statusColor,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建节点和连接线
  Widget _buildNodeWithLine(Color nodeColor, bool isFilledNode) {
    // 使用更简单的布局，分离节点和线条
    return SizedBox(
      width: 30,
      child: Column(
        children: [
          // 节点部分
          Padding(
            padding: const EdgeInsets.only(top: 10),
            child: Stack(
              alignment: Alignment.center,
              children: [
                // 白色背景圆圈，确保完全覆盖线条
                Container(
                  width: 24,
                  height: 24,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
                // 实际的圆点
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: isFilledNode ? nodeColor : Colors.white,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: nodeColor,
                      width: 2,
                    ),
                  ),
                  child: isPast && !isStart && !isEnd
                      ? const Icon(Icons.check, size: 10, color: Colors.white)
                      : null,
                ),
              ],
            ),
          ),

          // 连接线部分 - 只对非最后一个项目显示
          if (!isLastItem)
            Container(
              width: 2,
              height: 50, // 调整连接线高度
              color: const Color(0xFFE0E0E0),
              margin: const EdgeInsets.only(top: 8), // 调整与节点的间距，使上下间距一致
            ),
        ],
      ),
    );
  }
}


