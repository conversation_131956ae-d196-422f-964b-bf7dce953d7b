import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../shared/widgets/common_bottom_sheet.dart';
import '../../../core/models/subject_project.dart';
import '../../../shared/widgets/improved_date_picker.dart';
import '../../../shared/widgets/milestone_date_picker.dart';
import '../../../shared/widgets/top_message_overlay.dart';

/// 项目编辑底部弹窗
class ProjectEditSheet extends ConsumerStatefulWidget {
  final Project project;
  final Subject? subject;
  final Function(Project) onSave;

  const ProjectEditSheet({
    super.key,
    required this.project,
    this.subject,
    required this.onSave,
  });

  @override
  ConsumerState<ProjectEditSheet> createState() => _ProjectEditSheetState();
}

class _ProjectEditSheetState extends ConsumerState<ProjectEditSheet> {
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late TextEditingController _customUnitController;
  late TextEditingController _targetValueController;
  late DateTime _startDate;
  late DateTime _endDate;
  late double _targetHours;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.project.name);
    _descriptionController = TextEditingController(text: widget.project.description ?? '');
    _customUnitController = TextEditingController(text: widget.project.customUnit ?? '单位');
    _targetValueController = TextEditingController(text: (widget.project.targetValue ?? 100).toString());
    _startDate = widget.project.startDate;
    _endDate = widget.project.endDate;
    _targetHours = widget.project.targetHours;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _customUnitController.dispose();
    _targetValueController.dispose();
    super.dispose();
  }

  // 保存项目
  void _saveProject() {
    // 验证输入
    if (_nameController.text.trim().isEmpty) {
      TopMessageOverlayManager().showError(
        context: context,
        message: '请输入项目名称',
      );
      return;
    }

    if (_endDate.isBefore(_startDate)) {
      TopMessageOverlayManager().showError(
        context: context,
        message: '结束日期不能早于开始日期',
      );
      return;
    }

    // 创建更新后的项目
    final updatedProject = widget.project.copyWith(
      name: _nameController.text.trim(),
      description: _descriptionController.text.trim(),
      startDate: _startDate,
      endDate: _endDate,
      targetHours: widget.project.trackingMode == ProgressTrackingMode.focusTime ? _targetHours : widget.project.targetHours,
      // 如果是自定义模式，更新自定义相关参数
      customUnit: widget.project.trackingMode == ProgressTrackingMode.custom ? _customUnitController.text.trim() : widget.project.customUnit,
      targetValue: widget.project.trackingMode == ProgressTrackingMode.custom ? int.tryParse(_targetValueController.text) ?? widget.project.targetValue : widget.project.targetValue,
    );

    // 调用保存回调
    widget.onSave(updatedProject);

    // 关闭底部弹窗
    Navigator.pop(context);
  }



  @override
  Widget build(BuildContext context) {
    return CommonBottomSheet(
      title: '编辑项目',
      actions: [
        TextButton(
          onPressed: _saveProject,
          child: const Text(
            '保存',
            style: TextStyle(color: AppColors.primary),
          ),
        ),
      ],
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 项目名称
            const Text('项目名称', style: AppTextStyles.labelMedium),
            const SizedBox(height: 8),
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                hintText: '请输入项目名称',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
            ),

            const SizedBox(height: 16),

            // 项目描述
            Row(
              children: [
                const Text('项目描述', style: AppTextStyles.labelMedium),
                const SizedBox(width: 8),
                Text('(可选)', style: AppTextStyles.caption.copyWith(color: AppColors.textHint)),
              ],
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                hintText: '请输入项目描述',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              maxLines: 3,
            ),

            const SizedBox(height: 16),

            // 科目信息（只读）
            if (widget.subject != null) ...[
              const Text('所属科目', style: AppTextStyles.labelMedium),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.background,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.border),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: Color(widget.subject!.color),
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      widget.subject!.name,
                      style: AppTextStyles.bodyMedium,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),
            ],

            // 日期选择
            const Text('日期', style: AppTextStyles.labelMedium),
            const SizedBox(height: 8),
            Row(
              children: [
                // 开始日期
                Expanded(
                  child: ImprovedDatePicker(
                    initialDate: _startDate,
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2100),
                    title: '选择开始日期',
                    onDateSelected: (date) {
                      if (date != null) {
                        setState(() {
                          _startDate = date;
                          // 确保结束日期不早于开始日期
                          if (_endDate.isBefore(_startDate)) {
                            _endDate = _startDate;
                          }
                        });
                      }
                    },
                  ),
                ),

                const SizedBox(width: 16),
                const Icon(Icons.arrow_forward, color: AppColors.textHint),
                const SizedBox(width: 16),

                // 结束日期
                Expanded(
                  child: MilestoneDatePicker(
                    initialDate: _endDate,
                    firstDate: _startDate,
                    lastDate: DateTime(2100),
                    title: '选择结束日期',
                    buttonText: '选择结束日期',
                    onDateSelected: (date) {
                      if (date != null) {
                        setState(() {
                          _endDate = date;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 只有开启进度追踪时才显示进度相关设置
            if (widget.project.isTrackingEnabled) ...[
              // 显示当前进度模式（只读）
              const Text('进度模式', style: AppTextStyles.labelMedium),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.background,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.border),
                ),
                child: Row(
                  children: [
                    Icon(
                      widget.project.trackingMode == ProgressTrackingMode.focusTime
                          ? Icons.timer
                          : Icons.edit,
                      color: AppColors.textSecondary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      widget.project.trackingMode == ProgressTrackingMode.focusTime
                          ? '专注时间模式'
                          : '自定义模式',
                      style: AppTextStyles.bodyMedium,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // 根据追踪模式显示不同的设置
              if (widget.project.trackingMode == ProgressTrackingMode.focusTime) ...[
              const Text('目标时间（小时）', style: AppTextStyles.labelMedium),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: Slider(
                      value: _targetHours,
                      min: 1,
                      max: 100,
                      divisions: 99,
                      label: _targetHours.toStringAsFixed(1),
                      onChanged: (value) {
                        setState(() {
                          _targetHours = value;
                        });
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Container(
                    width: 60,
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.background,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      _targetHours.toStringAsFixed(1),
                      style: AppTextStyles.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ] else if (widget.project.trackingMode == ProgressTrackingMode.custom) ...[
              // 自定义模式的设置
              Row(
                children: [
                  // 自定义单位
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('自定义单位', style: AppTextStyles.labelMedium),
                        const SizedBox(height: 8),
                        TextField(
                          controller: _customUnitController,
                          decoration: const InputDecoration(
                            hintText: '例如：页、章、题',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(width: 16),

                  // 目标数值
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('目标数值', style: AppTextStyles.labelMedium),
                        const SizedBox(height: 8),
                        TextField(
                          controller: _targetValueController,
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(
                            hintText: '例如：100',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
            ],

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }


}
