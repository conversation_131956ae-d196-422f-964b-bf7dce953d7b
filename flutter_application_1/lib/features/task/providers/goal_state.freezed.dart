// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'goal_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

GoalState _$GoalStateFromJson(Map<String, dynamic> json) {
  return _GoalState.fromJson(json);
}

/// @nodoc
mixin _$GoalState {
  @JsonKey(fromJson: _goalFromJson, toJson: _goalToJson)
  Goal? get currentGoal => throw _privateConstructorUsedError;
  @JsonKey(fromJson: _milestonesFromJson, toJson: _milestonesToJson)
  List<Milestone> get milestones => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  /// Serializes this GoalState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GoalState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GoalStateCopyWith<GoalState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GoalStateCopyWith<$Res> {
  factory $GoalStateCopyWith(GoalState value, $Res Function(GoalState) then) =
      _$GoalStateCopyWithImpl<$Res, GoalState>;
  @useResult
  $Res call(
      {@JsonKey(fromJson: _goalFromJson, toJson: _goalToJson) Goal? currentGoal,
      @JsonKey(fromJson: _milestonesFromJson, toJson: _milestonesToJson)
      List<Milestone> milestones,
      bool isLoading,
      String? error});
}

/// @nodoc
class _$GoalStateCopyWithImpl<$Res, $Val extends GoalState>
    implements $GoalStateCopyWith<$Res> {
  _$GoalStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GoalState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentGoal = freezed,
    Object? milestones = null,
    Object? isLoading = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      currentGoal: freezed == currentGoal
          ? _value.currentGoal
          : currentGoal // ignore: cast_nullable_to_non_nullable
              as Goal?,
      milestones: null == milestones
          ? _value.milestones
          : milestones // ignore: cast_nullable_to_non_nullable
              as List<Milestone>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GoalStateImplCopyWith<$Res>
    implements $GoalStateCopyWith<$Res> {
  factory _$$GoalStateImplCopyWith(
          _$GoalStateImpl value, $Res Function(_$GoalStateImpl) then) =
      __$$GoalStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(fromJson: _goalFromJson, toJson: _goalToJson) Goal? currentGoal,
      @JsonKey(fromJson: _milestonesFromJson, toJson: _milestonesToJson)
      List<Milestone> milestones,
      bool isLoading,
      String? error});
}

/// @nodoc
class __$$GoalStateImplCopyWithImpl<$Res>
    extends _$GoalStateCopyWithImpl<$Res, _$GoalStateImpl>
    implements _$$GoalStateImplCopyWith<$Res> {
  __$$GoalStateImplCopyWithImpl(
      _$GoalStateImpl _value, $Res Function(_$GoalStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of GoalState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentGoal = freezed,
    Object? milestones = null,
    Object? isLoading = null,
    Object? error = freezed,
  }) {
    return _then(_$GoalStateImpl(
      currentGoal: freezed == currentGoal
          ? _value.currentGoal
          : currentGoal // ignore: cast_nullable_to_non_nullable
              as Goal?,
      milestones: null == milestones
          ? _value._milestones
          : milestones // ignore: cast_nullable_to_non_nullable
              as List<Milestone>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GoalStateImpl implements _GoalState {
  const _$GoalStateImpl(
      {@JsonKey(fromJson: _goalFromJson, toJson: _goalToJson) this.currentGoal,
      @JsonKey(fromJson: _milestonesFromJson, toJson: _milestonesToJson)
      final List<Milestone> milestones = const [],
      this.isLoading = false,
      this.error})
      : _milestones = milestones;

  factory _$GoalStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$GoalStateImplFromJson(json);

  @override
  @JsonKey(fromJson: _goalFromJson, toJson: _goalToJson)
  final Goal? currentGoal;
  final List<Milestone> _milestones;
  @override
  @JsonKey(fromJson: _milestonesFromJson, toJson: _milestonesToJson)
  List<Milestone> get milestones {
    if (_milestones is EqualUnmodifiableListView) return _milestones;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_milestones);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? error;

  @override
  String toString() {
    return 'GoalState(currentGoal: $currentGoal, milestones: $milestones, isLoading: $isLoading, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GoalStateImpl &&
            (identical(other.currentGoal, currentGoal) ||
                other.currentGoal == currentGoal) &&
            const DeepCollectionEquality()
                .equals(other._milestones, _milestones) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, currentGoal,
      const DeepCollectionEquality().hash(_milestones), isLoading, error);

  /// Create a copy of GoalState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GoalStateImplCopyWith<_$GoalStateImpl> get copyWith =>
      __$$GoalStateImplCopyWithImpl<_$GoalStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GoalStateImplToJson(
      this,
    );
  }
}

abstract class _GoalState implements GoalState {
  const factory _GoalState(
      {@JsonKey(fromJson: _goalFromJson, toJson: _goalToJson)
      final Goal? currentGoal,
      @JsonKey(fromJson: _milestonesFromJson, toJson: _milestonesToJson)
      final List<Milestone> milestones,
      final bool isLoading,
      final String? error}) = _$GoalStateImpl;

  factory _GoalState.fromJson(Map<String, dynamic> json) =
      _$GoalStateImpl.fromJson;

  @override
  @JsonKey(fromJson: _goalFromJson, toJson: _goalToJson)
  Goal? get currentGoal;
  @override
  @JsonKey(fromJson: _milestonesFromJson, toJson: _milestonesToJson)
  List<Milestone> get milestones;
  @override
  bool get isLoading;
  @override
  String? get error;

  /// Create a copy of GoalState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GoalStateImplCopyWith<_$GoalStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
