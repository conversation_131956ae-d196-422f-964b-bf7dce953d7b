// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'subject_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SubjectState _$SubjectStateFromJson(Map<String, dynamic> json) {
  return _SubjectState.fromJson(json);
}

/// @nodoc
mixin _$SubjectState {
  List<Subject> get subjects => throw _privateConstructorUsedError;
  List<Project> get projects => throw _privateConstructorUsedError;
  Subject? get currentSubject => throw _privateConstructorUsedError;
  Project? get currentProject => throw _privateConstructorUsedError;
  int get currentTabIndex => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  /// Serializes this SubjectState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SubjectState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SubjectStateCopyWith<SubjectState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubjectStateCopyWith<$Res> {
  factory $SubjectStateCopyWith(
          SubjectState value, $Res Function(SubjectState) then) =
      _$SubjectStateCopyWithImpl<$Res, SubjectState>;
  @useResult
  $Res call(
      {List<Subject> subjects,
      List<Project> projects,
      Subject? currentSubject,
      Project? currentProject,
      int currentTabIndex,
      bool isLoading,
      String? error});
}

/// @nodoc
class _$SubjectStateCopyWithImpl<$Res, $Val extends SubjectState>
    implements $SubjectStateCopyWith<$Res> {
  _$SubjectStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SubjectState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjects = null,
    Object? projects = null,
    Object? currentSubject = freezed,
    Object? currentProject = freezed,
    Object? currentTabIndex = null,
    Object? isLoading = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      subjects: null == subjects
          ? _value.subjects
          : subjects // ignore: cast_nullable_to_non_nullable
              as List<Subject>,
      projects: null == projects
          ? _value.projects
          : projects // ignore: cast_nullable_to_non_nullable
              as List<Project>,
      currentSubject: freezed == currentSubject
          ? _value.currentSubject
          : currentSubject // ignore: cast_nullable_to_non_nullable
              as Subject?,
      currentProject: freezed == currentProject
          ? _value.currentProject
          : currentProject // ignore: cast_nullable_to_non_nullable
              as Project?,
      currentTabIndex: null == currentTabIndex
          ? _value.currentTabIndex
          : currentTabIndex // ignore: cast_nullable_to_non_nullable
              as int,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SubjectStateImplCopyWith<$Res>
    implements $SubjectStateCopyWith<$Res> {
  factory _$$SubjectStateImplCopyWith(
          _$SubjectStateImpl value, $Res Function(_$SubjectStateImpl) then) =
      __$$SubjectStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<Subject> subjects,
      List<Project> projects,
      Subject? currentSubject,
      Project? currentProject,
      int currentTabIndex,
      bool isLoading,
      String? error});
}

/// @nodoc
class __$$SubjectStateImplCopyWithImpl<$Res>
    extends _$SubjectStateCopyWithImpl<$Res, _$SubjectStateImpl>
    implements _$$SubjectStateImplCopyWith<$Res> {
  __$$SubjectStateImplCopyWithImpl(
      _$SubjectStateImpl _value, $Res Function(_$SubjectStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SubjectState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjects = null,
    Object? projects = null,
    Object? currentSubject = freezed,
    Object? currentProject = freezed,
    Object? currentTabIndex = null,
    Object? isLoading = null,
    Object? error = freezed,
  }) {
    return _then(_$SubjectStateImpl(
      subjects: null == subjects
          ? _value._subjects
          : subjects // ignore: cast_nullable_to_non_nullable
              as List<Subject>,
      projects: null == projects
          ? _value._projects
          : projects // ignore: cast_nullable_to_non_nullable
              as List<Project>,
      currentSubject: freezed == currentSubject
          ? _value.currentSubject
          : currentSubject // ignore: cast_nullable_to_non_nullable
              as Subject?,
      currentProject: freezed == currentProject
          ? _value.currentProject
          : currentProject // ignore: cast_nullable_to_non_nullable
              as Project?,
      currentTabIndex: null == currentTabIndex
          ? _value.currentTabIndex
          : currentTabIndex // ignore: cast_nullable_to_non_nullable
              as int,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SubjectStateImpl with DiagnosticableTreeMixin implements _SubjectState {
  const _$SubjectStateImpl(
      {final List<Subject> subjects = const [],
      final List<Project> projects = const [],
      this.currentSubject,
      this.currentProject,
      this.currentTabIndex = 0,
      this.isLoading = false,
      this.error})
      : _subjects = subjects,
        _projects = projects;

  factory _$SubjectStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$SubjectStateImplFromJson(json);

  final List<Subject> _subjects;
  @override
  @JsonKey()
  List<Subject> get subjects {
    if (_subjects is EqualUnmodifiableListView) return _subjects;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_subjects);
  }

  final List<Project> _projects;
  @override
  @JsonKey()
  List<Project> get projects {
    if (_projects is EqualUnmodifiableListView) return _projects;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_projects);
  }

  @override
  final Subject? currentSubject;
  @override
  final Project? currentProject;
  @override
  @JsonKey()
  final int currentTabIndex;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? error;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SubjectState(subjects: $subjects, projects: $projects, currentSubject: $currentSubject, currentProject: $currentProject, currentTabIndex: $currentTabIndex, isLoading: $isLoading, error: $error)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SubjectState'))
      ..add(DiagnosticsProperty('subjects', subjects))
      ..add(DiagnosticsProperty('projects', projects))
      ..add(DiagnosticsProperty('currentSubject', currentSubject))
      ..add(DiagnosticsProperty('currentProject', currentProject))
      ..add(DiagnosticsProperty('currentTabIndex', currentTabIndex))
      ..add(DiagnosticsProperty('isLoading', isLoading))
      ..add(DiagnosticsProperty('error', error));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SubjectStateImpl &&
            const DeepCollectionEquality().equals(other._subjects, _subjects) &&
            const DeepCollectionEquality().equals(other._projects, _projects) &&
            (identical(other.currentSubject, currentSubject) ||
                other.currentSubject == currentSubject) &&
            (identical(other.currentProject, currentProject) ||
                other.currentProject == currentProject) &&
            (identical(other.currentTabIndex, currentTabIndex) ||
                other.currentTabIndex == currentTabIndex) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_subjects),
      const DeepCollectionEquality().hash(_projects),
      currentSubject,
      currentProject,
      currentTabIndex,
      isLoading,
      error);

  /// Create a copy of SubjectState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SubjectStateImplCopyWith<_$SubjectStateImpl> get copyWith =>
      __$$SubjectStateImplCopyWithImpl<_$SubjectStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SubjectStateImplToJson(
      this,
    );
  }
}

abstract class _SubjectState implements SubjectState {
  const factory _SubjectState(
      {final List<Subject> subjects,
      final List<Project> projects,
      final Subject? currentSubject,
      final Project? currentProject,
      final int currentTabIndex,
      final bool isLoading,
      final String? error}) = _$SubjectStateImpl;

  factory _SubjectState.fromJson(Map<String, dynamic> json) =
      _$SubjectStateImpl.fromJson;

  @override
  List<Subject> get subjects;
  @override
  List<Project> get projects;
  @override
  Subject? get currentSubject;
  @override
  Project? get currentProject;
  @override
  int get currentTabIndex;
  @override
  bool get isLoading;
  @override
  String? get error;

  /// Create a copy of SubjectState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SubjectStateImplCopyWith<_$SubjectStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
