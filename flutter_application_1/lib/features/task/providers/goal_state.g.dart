// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'goal_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$GoalStateImpl _$$GoalStateImplFromJson(Map<String, dynamic> json) =>
    _$GoalStateImpl(
      currentGoal: _goalFromJson(json['currentGoal'] as Map<String, dynamic>?),
      milestones: json['milestones'] == null
          ? const []
          : _milestonesFromJson(json['milestones'] as List?),
      isLoading: json['isLoading'] as bool? ?? false,
      error: json['error'] as String?,
    );

Map<String, dynamic> _$$GoalStateImplToJson(_$GoalStateImpl instance) =>
    <String, dynamic>{
      'currentGoal': _goalToJson(instance.currentGoal),
      'milestones': _milestonesToJson(instance.milestones),
      'isLoading': instance.isLoading,
      'error': instance.error,
    };
