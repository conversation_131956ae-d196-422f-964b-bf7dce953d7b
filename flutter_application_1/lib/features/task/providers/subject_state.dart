// 该文件定义了科目相关的状态管理Provider

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../core/models/subject_project.dart';
import '../../../core/services/preferences_service.dart';
import '../../../core/services/hive_service.dart';

part 'subject_state.freezed.dart';
part 'subject_state.g.dart';

// 科目状态数据类
@freezed
class SubjectState with _$SubjectState {
  const factory SubjectState({
    @Default([]) List<Subject> subjects,
    @Default([]) List<Project> projects,
    Subject? currentSubject,
    Project? currentProject,
    @Default(0) int currentTabIndex,
    @Default(false) bool isLoading,
    String? error,
  }) = _SubjectState;

  factory SubjectState.fromJson(Map<String, dynamic> json) =>
      _$SubjectStateFromJson(json);
}

// 科目状态Provider
final subjectStateProvider = StateNotifierProvider<SubjectStateNotifier, SubjectState>(
  (ref) => SubjectStateNotifier(),
);

// 科目状态管理类
class SubjectStateNotifier extends StateNotifier<SubjectState> {
  final PreferencesService _preferencesService = PreferencesService();

  SubjectStateNotifier() : super(const SubjectState());

  // 设置加载状态
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  // 设置错误信息
  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  // 加载所有科目
  void setSubjects(List<Subject> subjects) {
    state = state.copyWith(subjects: subjects);
  }

  // 加载所有项目
  void setProjects(List<Project> projects) {
    state = state.copyWith(projects: projects);
  }

  // 设置当前选中的科目
  void setCurrentSubject(Subject? subject) {
    state = state.copyWith(currentSubject: subject);

    // 保存选择的科目
    if (subject != null) {
      _preferencesService.saveLastSubjectId(subject.id);

      // 如果切换了科目，自动选择该科目的第一个项目
      final subjectProjects = state.projects.where((p) => p.subjectId == subject.id).toList();
      if (subjectProjects.isNotEmpty) {
        setCurrentProject(subjectProjects.first);
      } else {
        // 如果没有项目，清除当前项目
        setCurrentProject(null);
      }
    }
  }

  // 设置当前选中的项目
  void setCurrentProject(Project? project) {
    state = state.copyWith(currentProject: project);

    // 保存选择的项目
    if (project != null) {
      _preferencesService.saveLastProjectId(project.id);
    }
  }

  // 设置当前标签索引
  void setCurrentTabIndex(int index) {
    state = state.copyWith(currentTabIndex: index);
  }

  // 添加新科目
  void addSubject(Subject subject) {
    final updatedSubjects = [...state.subjects, subject];
    state = state.copyWith(subjects: updatedSubjects);
  }

  // 更新科目
  void updateSubject(Subject updatedSubject) {
    final updatedSubjects = state.subjects.map((subject) {
      return subject.id == updatedSubject.id ? updatedSubject : subject;
    }).toList();
    state = state.copyWith(subjects: updatedSubjects);
  }

  // 删除科目
  void deleteSubject(String subjectId) {
    debugPrint('SubjectStateNotifier.deleteSubject 开始: $subjectId');

    // 删除前的状态
    debugPrint('删除前科目数量: ${state.subjects.length}');
    for (var s in state.subjects) {
      debugPrint('删除前科目: ${s.id}, ${s.name}');
    }

    final updatedSubjects = state.subjects.where((subject) => subject.id != subjectId).toList();
    final updatedProjects = state.projects.where((project) => project.subjectId != subjectId).toList();

    debugPrint('过滤后科目数量: ${updatedSubjects.length}');
    for (var s in updatedSubjects) {
      debugPrint('过滤后科目: ${s.id}, ${s.name}');
    }

    // 如果当前选中的科目被删除，则重置当前科目
    Subject? newCurrentSubject = state.currentSubject;
    Project? newCurrentProject = state.currentProject;

    if (state.currentSubject?.id == subjectId) {
      debugPrint('当前选中的科目被删除，需要重置');
      newCurrentSubject = updatedSubjects.isNotEmpty ? updatedSubjects.first : null;
      newCurrentProject = null;
    }

    // 如果当前选中的项目属于被删除的科目，则重置当前项目
    if (state.currentProject?.subjectId == subjectId) {
      debugPrint('当前选中的项目属于被删除的科目，需要重置');
      newCurrentProject = null;
    }

    debugPrint('更新状态前');
    state = state.copyWith(
      subjects: updatedSubjects,
      projects: updatedProjects,
      currentSubject: newCurrentSubject,
      currentProject: newCurrentProject,
    );
    debugPrint('更新状态后');

    // 验证状态是否更新
    debugPrint('验证状态更新后科目数量: ${state.subjects.length}');
    for (var s in state.subjects) {
      debugPrint('状态更新后科目: ${s.id}, ${s.name}');
    }

    // 检查被删除的科目是否还存在
    final stillExists = state.subjects.any((s) => s.id == subjectId);
    if (stillExists) {
      debugPrint('警告：科目在状态中仍然存在: $subjectId');
    } else {
      debugPrint('科目已从状态中成功删除: $subjectId');
    }

    debugPrint('SubjectStateNotifier.deleteSubject 完成: $subjectId');
  }

  // 添加新项目
  void addProject(Project project) {
    final updatedProjects = [...state.projects, project];
    state = state.copyWith(projects: updatedProjects);
  }

  // 更新项目
  void updateProject(Project updatedProject) {
    final updatedProjects = state.projects.map((project) {
      return project.id == updatedProject.id ? updatedProject : project;
    }).toList();

    // 如果更新的是当前选中的项目，同时更新当前项目
    Project? newCurrentProject = state.currentProject;
    if (state.currentProject?.id == updatedProject.id) {
      newCurrentProject = updatedProject;
    }

    state = state.copyWith(
      projects: updatedProjects,
      currentProject: newCurrentProject,
    );
  }

  // 删除项目
  void deleteProject(String projectId) {
    final updatedProjects = state.projects.where((project) => project.id != projectId).toList();

    // 如果当前选中的项目被删除，则重置当前项目
    Project? newCurrentProject = state.currentProject;
    if (state.currentProject?.id == projectId) {
      newCurrentProject = null;
    }

    state = state.copyWith(
      projects: updatedProjects,
      currentProject: newCurrentProject,
    );

    // 注意：数据保存已由其他机制处理
  }

  // 获取指定科目的所有项目
  List<Project> getProjectsBySubjectId(String subjectId, {bool includeArchived = false}) {
    if (includeArchived) {
      return state.projects.where((project) => project.subjectId == subjectId).toList();
    } else {
      return state.projects.where((project) => project.subjectId == subjectId && !project.isArchived).toList();
    }
  }

  // 获取所有归档项目
  List<Project> getArchivedProjects() {
    return state.projects.where((project) => project.isArchived).toList();
  }

  // 归档项目
  void archiveProject(Project project) {
    final updatedProject = project.copyWith(isArchived: true);
    updateProject(updatedProject);
  }

  // 取消归档项目
  void unarchiveProject(Project project) {
    final updatedProject = project.copyWith(isArchived: false);
    updateProject(updatedProject);
  }

  // 重新加载项目列表
  Future<void> reloadProjects() async {
    debugPrint('SubjectStateNotifier.reloadProjects 开始');
    try {
      // 创建HiveService实例
      final hiveService = HiveService();

      // 确保Hive服务已初始化
      await hiveService.initHive();

      // 获取所有非归档项目
      final projects = hiveService.subjectRepository.getAllProjects(includeArchived: false);
      debugPrint('获取到${projects.length}个非归档项目');

      // 更新状态
      setProjects(projects);

      debugPrint('SubjectStateNotifier.reloadProjects 完成');
      return;
    } catch (e) {
      debugPrint('重新加载项目列表失败: $e');
      setError('重新加载项目列表失败: $e');
      rethrow;
    }
  }

  // 根据ID获取科目
  Subject? getSubjectById(String subjectId) {
    try {
      return state.subjects.firstWhere((subject) => subject.id == subjectId);
    } catch (e) {
      return null;
    }
  }

  // 加载上次选择的科目和项目
  Future<void> loadLastSelection() async {
    try {
      // 加载上次选择的科目和项目ID
      final lastSubjectId = await _preferencesService.getLastSubjectId();
      final lastProjectId = await _preferencesService.getLastProjectId();

      // 如果有上次选择的科目，尝试找到并设置
      if (lastSubjectId != null && state.subjects.isNotEmpty) {
        final subject = state.subjects.firstWhere(
          (s) => s.id == lastSubjectId,
          orElse: () => state.subjects.first,
        );

        // 设置当前科目（不会自动设置项目，因为我们还要处理上次选择的项目）
        state = state.copyWith(currentSubject: subject);

        // 获取该科目的所有项目
        final subjectProjects = state.projects.where((p) => p.subjectId == subject.id).toList();

        // 如果有上次选择的项目，尝试找到并设置
        if (lastProjectId != null && subjectProjects.isNotEmpty) {
          final project = subjectProjects.firstWhere(
            (p) => p.id == lastProjectId,
            orElse: () => subjectProjects.first,
          );

          // 设置当前项目
          state = state.copyWith(currentProject: project);
        } else if (subjectProjects.isNotEmpty) {
          // 如果没有上次选择的项目，但有项目，选择第一个
          state = state.copyWith(currentProject: subjectProjects.first);
        }
      } else if (state.subjects.isNotEmpty) {
        // 如果没有上次选择的科目，但有科目，选择第一个
        final subject = state.subjects.first;
        state = state.copyWith(currentSubject: subject);

        // 获取该科目的所有项目
        final subjectProjects = state.projects.where((p) => p.subjectId == subject.id).toList();

        if (subjectProjects.isNotEmpty) {
          // 如果有项目，选择第一个
          state = state.copyWith(currentProject: subjectProjects.first);
        }
      }
    } catch (e) {
      debugPrint('加载上次选择出错: $e');
    }
  }
}