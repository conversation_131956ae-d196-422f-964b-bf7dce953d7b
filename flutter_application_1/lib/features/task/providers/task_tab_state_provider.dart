import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 目标管理页面tab状态管理
class TaskTabStateNotifier extends StateNotifier<int> {
  TaskTabStateNotifier() : super(0) {
    _loadTabState();
  }

  static const String _tabStateKey = 'task_goal_tab_state';

  /// 加载保存的tab状态
  Future<void> _loadTabState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedTabIndex = prefs.getInt(_tabStateKey) ?? 0;
      // 确保索引在有效范围内（0: 目标, 1: 项目）
      if (savedTabIndex >= 0 && savedTabIndex <= 1) {
        state = savedTabIndex;
      }
    } catch (e) {
      // 如果加载失败，保持默认值0
      state = 0;
    }
  }

  /// 设置tab状态并保存
  Future<void> setTabIndex(int index) async {
    if (index >= 0 && index <= 1) {
      state = index;
      await _saveTabState(index);
    }
  }

  /// 保存tab状态到SharedPreferences
  Future<void> _saveTabState(int index) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_tabStateKey, index);
    } catch (e) {
      // 保存失败时不影响功能，只是下次启动时会重置为默认值
    }
  }
}

/// 目标管理页面tab状态Provider
final taskTabStateProvider = StateNotifierProvider<TaskTabStateNotifier, int>((ref) {
  return TaskTabStateNotifier();
});
