// 该文件定义了目标相关的状态管理Provider

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../core/models/goal_milestone.dart';

part 'goal_state.freezed.dart';
part 'goal_state.g.dart';

// JSON序列化辅助函数
Goal? _goalFromJson(Map<String, dynamic>? json) {
  if (json == null) return null;
  return Goal(
    id: json['id'] as String,
    name: json['name'] as String,
    startDate: DateTime.parse(json['startDate'] as String),
    endDate: DateTime.parse(json['endDate'] as String),
    milestones: _milestonesFromJson(json['milestones'] as List?),
  );
}

Map<String, dynamic>? _goalToJson(Goal? goal) {
  if (goal == null) return null;
  return {
    'id': goal.id,
    'name': goal.name,
    'startDate': goal.startDate.toIso8601String(),
    'endDate': goal.endDate.toIso8601String(),
    'milestones': _milestonesToJson(goal.milestones),
  };
}

List<Milestone> _milestonesFromJson(List? json) {
  if (json == null) return [];
  return json
      .map((e) => Milestone(
            id: e['id'] as String,
            name: e['name'] as String,
            goalId: e['goalId'] as String,
            date: DateTime.parse(e['date'] as String),
          ))
      .toList();
}

List<Map<String, dynamic>> _milestonesToJson(List<Milestone> milestones) {
  return milestones
      .map((e) => {
            'id': e.id,
            'name': e.name,
            'goalId': e.goalId,
            'date': e.date.toIso8601String(),
          })
      .toList();
}

// 目标状态数据类
@freezed
class GoalState with _$GoalState {
  const factory GoalState({
    Goal? currentGoal,
    @Default([]) List<Milestone> milestones,
    @Default(false) bool isLoading,
    String? error,
  }) = _GoalState;

  factory GoalState.fromJson(Map<String, dynamic> json) =>
      _$GoalStateFromJson(json);
}

// 目标状态Provider
final goalStateProvider = StateNotifierProvider<GoalStateNotifier, GoalState>(
  (ref) => GoalStateNotifier(),
);

// 目标状态管理类
class GoalStateNotifier extends StateNotifier<GoalState> {
  GoalStateNotifier() : super(const GoalState());

  // 设置加载状态
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  // 设置错误信息
  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  // 设置当前目标
  void setCurrentGoal(Goal? goal) {
    state = state.copyWith(currentGoal: goal);
  }

  // 设置里程碑列表
  void setMilestones(List<Milestone> milestones) {
    state = state.copyWith(milestones: milestones);
  }

  // 添加里程碑
  void addMilestone(Milestone milestone) {
    final updatedMilestones = [...state.milestones, milestone];
    state = state.copyWith(milestones: updatedMilestones);
  }

  // 更新里程碑
  void updateMilestone(Milestone updatedMilestone) {
    final updatedMilestones = state.milestones.map((milestone) {
      return milestone.id == updatedMilestone.id ? updatedMilestone : milestone;
    }).toList();
    state = state.copyWith(milestones: updatedMilestones);
  }

  // 删除里程碑
  void deleteMilestone(String milestoneId) {
    final updatedMilestones = state.milestones.where((milestone) => milestone.id != milestoneId).toList();
    state = state.copyWith(milestones: updatedMilestones);
  }

  // 更新目标和里程碑
  void updateGoalWithMilestones(Goal goal, List<Milestone> milestones) {
    state = state.copyWith(
      currentGoal: goal,
      milestones: milestones,
    );
  }

  // 清空目标和里程碑
  void clearGoalAndMilestones() {
    state = const GoalState();
  }

  // 归档目标
  void archiveGoal(Goal goal) {
    final updatedGoal = goal.copyWith(isArchived: true);
    setCurrentGoal(updatedGoal);
  }

  // 取消归档目标
  void unarchiveGoal(Goal goal) {
    final updatedGoal = goal.copyWith(isArchived: false);
    setCurrentGoal(updatedGoal);
  }
}