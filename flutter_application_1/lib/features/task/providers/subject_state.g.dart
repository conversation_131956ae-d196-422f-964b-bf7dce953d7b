// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subject_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SubjectStateImpl _$$SubjectStateImplFromJson(Map<String, dynamic> json) =>
    _$SubjectStateImpl(
      subjects: (json['subjects'] as List<dynamic>?)
              ?.map((e) => Subject.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      projects: (json['projects'] as List<dynamic>?)
              ?.map((e) => Project.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      currentSubject: json['currentSubject'] == null
          ? null
          : Subject.fromJson(json['currentSubject'] as Map<String, dynamic>),
      currentProject: json['currentProject'] == null
          ? null
          : Project.fromJson(json['currentProject'] as Map<String, dynamic>),
      currentTabIndex: (json['currentTabIndex'] as num?)?.toInt() ?? 0,
      isLoading: json['isLoading'] as bool? ?? false,
      error: json['error'] as String?,
    );

Map<String, dynamic> _$$SubjectStateImplToJson(_$SubjectStateImpl instance) =>
    <String, dynamic>{
      'subjects': instance.subjects.map((e) => e.toJson()).toList(),
      'projects': instance.projects.map((e) => e.toJson()).toList(),
      'currentSubject': instance.currentSubject?.toJson(),
      'currentProject': instance.currentProject?.toJson(),
      'currentTabIndex': instance.currentTabIndex,
      'isLoading': instance.isLoading,
      'error': instance.error,
    };
