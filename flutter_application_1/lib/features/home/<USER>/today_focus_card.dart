import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/services/hive_service.dart';
import '../../../features/data/utils/focus_data_calculator.dart';

/// 今日专注情况卡片
/// 显示今日专注时长、专注次数和平均时长
class TodayFocusCard extends StatefulWidget {
  const TodayFocusCard({super.key});

  @override
  State<TodayFocusCard> createState() => _TodayFocusCardState();
}

class _TodayFocusCardState extends State<TodayFocusCard> {
  // Hive服务
  final HiveService _hiveService = HiveService();

  // 今日专注数据
  double _todayHours = 0;
  int _todaySessionCount = 0;
  double _todayAvgMinutes = 0;

  @override
  void initState() {
    super.initState();
    _loadTodayData();
  }

  // 加载今日专注数据
  Future<void> _loadTodayData() async {
    try {
      // 确保Hive服务已初始化
      await _hiveService.initHive();

      // 获取今日专注记录
      final todayRecords = _hiveService.focusRecordRepository.getTodayFocusRecords();

      // 计算今日数据
      final todayHours = FocusDataCalculator.calculateTotalHours(todayRecords);
      final todaySessionCount = FocusDataCalculator.calculateSessionCount(todayRecords);
      final todayAvgMinutes = FocusDataCalculator.calculateAverageMinutes(todayRecords);

      // 更新状态
      if (mounted) {
        setState(() {
          _todayHours = todayHours;
          _todaySessionCount = todaySessionCount;
          _todayAvgMinutes = todayAvgMinutes;
        });
      }
    } catch (e) {
      debugPrint('加载今日专注数据出错: $e');
    }
  }

  // 构建指标项
  Widget _buildMetricItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // 图标
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withAlpha(26), // 10% opacity
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(height: 8),

        // 数值
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.text,
          ),
        ),
        const SizedBox(height: 4),

        // 标签
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.zero,
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            const Text(
              "今日专注情况",
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: 16),

            // 今日数据指标行
            Row(
              children: [
                // 今日专注时长
                Expanded(
                  child: _buildMetricItem(
                    icon: Icons.access_time,
                    value: '${_todayHours.toStringAsFixed(1)}h',
                    label: '今日专注',
                    color: AppColors.info,
                  ),
                ),

                // 今日专注次数
                Expanded(
                  child: _buildMetricItem(
                    icon: Icons.repeat,
                    value: '$_todaySessionCount次',
                    label: '专注次数',
                    color: AppColors.success,
                  ),
                ),

                // 今日平均时长
                Expanded(
                  child: _buildMetricItem(
                    icon: Icons.timer,
                    value: '${_todayAvgMinutes.toStringAsFixed(1)}分钟',
                    label: '平均时长',
                    color: AppColors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
