import 'package:flutter/material.dart';
import 'feature_card.dart';

/// 功能卡片数据类
class FeatureCardData {
  final String title;
  final String? description;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;
  final bool showArrow;

  FeatureCardData({
    required this.title,
    this.description,
    required this.icon,
    required this.color,
    required this.onTap,
    this.showArrow = true,
  });
}

/// 功能卡片网格组件
/// 显示功能卡片网格
class FeatureCardGrid extends StatelessWidget {
  final List<FeatureCardData> cards;
  final int crossAxisCount;
  final double horizontalSpacing;
  final double verticalSpacing;

  const FeatureCardGrid({
    super.key,
    required this.cards,
    this.crossAxisCount = 2,
    this.horizontalSpacing = 16.0,
    this.verticalSpacing = 16.0,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 计算每个卡片的宽度
        final cardWidth = (constraints.maxWidth - (crossAxisCount - 1) * horizontalSpacing) / crossAxisCount;

        // 计算行数
        final rowCount = (cards.length / crossAxisCount).ceil();

        return Column(
          children: List.generate(rowCount, (rowIndex) {
            // 计算当前行的卡片数量
            final itemsInRow = (rowIndex == rowCount - 1 && cards.length % crossAxisCount != 0)
                ? cards.length % crossAxisCount
                : crossAxisCount;

            return Padding(
              padding: EdgeInsets.only(bottom: rowIndex < rowCount - 1 ? verticalSpacing : 0),
              child: Row(
                children: List.generate(itemsInRow, (colIndex) {
                  final cardIndex = rowIndex * crossAxisCount + colIndex;
                  final card = cards[cardIndex];

                  return Container(
                    width: cardWidth,
                    margin: EdgeInsets.only(
                      right: colIndex < itemsInRow - 1 ? horizontalSpacing : 0,
                    ),
                    child: FeatureCard(
                      title: card.title,
                      description: card.description,
                      icon: card.icon,
                      color: card.color,
                      onTap: card.onTap,
                      showArrow: card.showArrow,
                    ),
                  );
                }),
              ),
            );
          }),
        );
      },
    );
  }
}
