import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import '../../data/screens/data_detail_screen.dart';

/// 数据入口卡片组件
/// 显示数据分析入口
class DataEntryCard extends StatelessWidget {
  const DataEntryCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.zero,
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
      ),
      child: InkWell(
        onTap: () => _navigateToDataScreen(context),
        borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题行
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    "更多数据",
                    style: AppTextStyles.headline3,
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: AppColors.textTertiary,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // 内容区域
              Row(
                children: [
                  // 图标
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withAlpha(30),
                      borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                    ),
                    child: Icon(
                      Icons.bar_chart,
                      size: 32,
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(width: 16),
                  // 文本说明
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: const [
                        Text(
                          "查看您的学习数据",
                          style: AppTextStyles.bodyLarge,
                        ),
                        SizedBox(height: 4),
                        Text(
                          "分析专注时间、科目分布和学习趋势",
                          style: AppTextStyles.bodySmall,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 导航到数据页面
  void _navigateToDataScreen(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const DataDetailScreen(),
      ),
    );
  }
}
