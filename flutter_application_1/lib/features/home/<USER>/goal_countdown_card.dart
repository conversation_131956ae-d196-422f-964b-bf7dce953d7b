import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/models/goal_milestone.dart';
import '../../task/providers/goal_state.dart';

/// 目标倒计时卡片组件
/// 显示用户设定的目标与倒计时
class GoalCountdownCard extends ConsumerWidget {
  const GoalCountdownCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final goalState = ref.watch(goalStateProvider);

    // 卡片高度
    const double cardHeight = 150.0;

    // 如果没有目标，显示空状态
    if (goalState.currentGoal == null) {
      return _buildEmptyCard(cardHeight);
    }

    // 有目标，显示目标信息
    final currentGoal = goalState.currentGoal!;
    return _buildGoalCard(currentGoal, cardHeight);
  }

  /// 构建空状态卡片
  Widget _buildEmptyCard(double height) {
    return Card(
      margin: EdgeInsets.zero,
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
      ),
      child: SizedBox(
        width: double.infinity,
        height: height,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.flag_outlined,
              size: 40,
              color: AppColors.primary,
            ),
            const SizedBox(height: 12),
            const Text(
              "暂无目标",
              style: AppTextStyles.headline3,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              "请在目标页面设置目标",
              style: AppTextStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建目标卡片
  Widget _buildGoalCard(Goal goal, double height) {
    return Card(
      margin: EdgeInsets.zero,
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
      ),
      child: SizedBox(
        width: double.infinity,
        height: height,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 目标名称
              Text(
                goal.name,
                style: AppTextStyles.headline3,
                maxLines: 1,
                overflow: TextOverflow.ellipsis, // 防止长名称溢出
              ),
              const SizedBox(height: 24),
              // 目标时间信息
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        "${goal.passedDays}天",
                        style: AppTextStyles.headline3.copyWith(
                          color: AppColors.primary,
                        ),
                      ),
                      const Text(
                        "已坚持",
                        style: AppTextStyles.bodySmall,
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        "${goal.remainingDays}天",
                        style: AppTextStyles.headline3.copyWith(
                          color: AppColors.primary,
                        ),
                      ),
                      const Text(
                        "剩余天数",
                        style: AppTextStyles.bodySmall,
                      ),
                    ],
                  ),
                ],
              ),
              const Spacer(),
              // 进度条
              LinearProgressIndicator(
                value: goal.progress, // 使用目标的进度值
                backgroundColor: AppColors.divider,
                valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
                minHeight: 8,
                borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
