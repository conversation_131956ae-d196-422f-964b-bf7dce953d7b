import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import '../../../shared/widgets/app_card.dart';

import 'feature_card_grid.dart';

/// 自定义卡片区域组件
/// 显示底部功能卡片区域
class CustomCardsArea extends StatelessWidget {
  const CustomCardsArea({super.key});

  @override
  Widget build(BuildContext context) {
    // 创建单个"其他功能"卡片数据
    final List<FeatureCardData> cards = [
      FeatureCardData(
        title: '其他功能',
        description: '更多功能正在开发中...',
        icon: Icons.more_horiz,
        color: Colors.grey,
        showArrow: false,
        onTap: () {
          // 不可点击，无操作
        },
      ),
    ];

    return AppCard(
      padding: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题区域
          const Padding(
            padding: EdgeInsets.only(
              left: AppSizes.paddingMedium,
              right: AppSizes.paddingMedium,
              top: AppSizes.paddingMedium
            ),
            child: Text(
              "其他功能",
              style: AppTextStyles.headline3,
            ),
          ),
          const SizedBox(height: AppSizes.paddingMedium),
          // 功能卡片网格 - 使用方形卡片样式
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium),
            child: FeatureCardGrid(
              cards: cards,
              crossAxisCount: 2, // 2列布局，但只有一个卡片
              horizontalSpacing: AppSizes.paddingMedium,
              verticalSpacing: AppSizes.paddingMedium,
            ),
          ),
          // 底部留出一些空间
          const SizedBox(height: AppSizes.paddingMedium),
        ],
      ),
    );
  }
}
