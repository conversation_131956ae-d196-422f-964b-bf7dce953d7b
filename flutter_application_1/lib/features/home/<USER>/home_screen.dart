import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/services/hive_service.dart';
import '../../task/providers/goal_state.dart';
import '../../task/providers/subject_state.dart';
import '../widgets/goal_countdown_card.dart';
import '../widgets/today_focus_card.dart';
import '../widgets/data_entry_card.dart';
import '../widgets/custom_cards_area.dart';
import '../../../shared/utils/keyboard_cleanup_helper.dart';

/// 首页
/// 显示用户的目标倒计时、数据入口和其他功能卡片
class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  // Hive服务
  final HiveService _hiveService = HiveService();

  @override
  void initState() {
    super.initState();
    // 从Hive加载数据
    _loadData();
  }

  // 从Hive加载数据
  Future<void> _loadData() async {
    try {
      // 确保Hive服务已初始化
      await _hiveService.initHive();

      // 获取所有目标
      final goals = _hiveService.goalRepository.getAllGoals();

      // 获取所有科目和项目
      final subjects = _hiveService.subjectRepository.getAllSubjects();
      final projects = _hiveService.subjectRepository.getAllProjects();

      // 使用Future.microtask确保不在构建生命周期中直接修改状态
      Future.microtask(() async {
        // 设置加载状态
        ref.read(goalStateProvider.notifier).setLoading(true);
        ref.read(subjectStateProvider.notifier).setLoading(true);

        // 如果有目标，设置第一个目标为当前目标
        if (goals.isNotEmpty) {
          final currentGoal = _hiveService.goalRepository.getGoalWithMilestones(goals.first.id);
          if (currentGoal != null) {
            ref.read(goalStateProvider.notifier).setCurrentGoal(currentGoal);
            ref.read(goalStateProvider.notifier).setMilestones(currentGoal.milestones);
          }
        }

        // 更新科目和项目状态
        ref.read(subjectStateProvider.notifier).setSubjects(subjects);
        ref.read(subjectStateProvider.notifier).setProjects(projects);

        // 加载上次选择的科目和项目
        await ref.read(subjectStateProvider.notifier).loadLastSelection();

        // 设置加载完成
        ref.read(goalStateProvider.notifier).setLoading(false);
        ref.read(subjectStateProvider.notifier).setLoading(false);
      });
    } catch (e) {
      debugPrint('加载数据出错: $e');
      // 使用Future.microtask确保不在构建生命周期中直接修改状态
      Future.microtask(() {
        ref.read(goalStateProvider.notifier).setError('加载数据失败: $e');
        ref.read(goalStateProvider.notifier).setLoading(false);
        ref.read(subjectStateProvider.notifier).setError('加载数据失败: $e');
        ref.read(subjectStateProvider.notifier).setLoading(false);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // 使用渐变背景
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.pageBackground,
        ),
        // 使用SingleChildScrollView使整个页面可滚动
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium),
              // 使用ConstrainedBox确保内容至少占据屏幕高度
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: MediaQuery.of(context).size.height - MediaQuery.of(context).padding.top - MediaQuery.of(context).padding.bottom,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: const [
                    SizedBox(height: AppSizes.paddingMedium),
                    // 1. 顶部目标倒计时卡片
                    GoalCountdownCard(),
                    SizedBox(height: AppSizes.paddingLarge),
                    // 2. 今日专注情况卡片
                    TodayFocusCard(),
                    SizedBox(height: AppSizes.paddingLarge),
                    // 3. 数据入口卡片
                    DataEntryCard(),
                    SizedBox(height: AppSizes.paddingLarge),
                    // 4. 底部自定义卡片区域
                    CustomCardsArea(),
                    // 添加底部空间，确保有足够的安全距离
                    SizedBox(height: AppSizes.paddingLarge),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
      // 添加隐藏的键盘清理按钮（仅在开发模式下显示）
      floatingActionButton: KeyboardCleanupHelper.hasActiveInputBar()
          ? const KeyboardCleanupButton()
          : null,
    );
  }
}
