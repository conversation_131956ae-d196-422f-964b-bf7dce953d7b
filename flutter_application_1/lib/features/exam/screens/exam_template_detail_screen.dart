import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import '../models/exam_template_models.dart';
import '../services/exam_template_service.dart';
import '../screens/exam_template_edit_screen.dart';

class ExamTemplateDetailScreen extends StatefulWidget {
  final ExamTemplate? template;
  final bool isCreating;

  const ExamTemplateDetailScreen({
    super.key,
    this.template,
    this.isCreating = false,
  });

  @override
  State<ExamTemplateDetailScreen> createState() =>
      _ExamTemplateDetailScreenState();
}

class _ExamTemplateDetailScreenState extends State<ExamTemplateDetailScreen>
    with SingleTickerProviderStateMixin {
  // 模板服务
  final _templateService = ExamTemplateService();

  // 控制器
  late TextEditingController _nameController;
  late TextEditingController _totalScoreController;
  late TextEditingController _durationController;

  // 当前模板
  ExamTemplate? _currentTemplate;

  // 是否编辑模式
  bool _isEditing = false;

  // 标签控制器
  late TabController _tabController;

  // 考试范围选择
  bool _isFullExam = true; // 默认选择完整考试

  // 选中的题型模块ID列表
  final List<String> _selectedSectionIds = [];

  // 选中的考试实例ID
  String? _selectedInstanceId;

  @override
  void initState() {
    super.initState();

    // 初始化标签控制器
    _tabController = TabController(length: 2, vsync: this);

    // 添加标签控制器监听器
    _tabController.addListener(_handleTabIndexChange);

    // 初始化控制器
    _nameController = TextEditingController(
      text: widget.template?.name ?? '',
    );
    _totalScoreController = TextEditingController(
      text: widget.template?.totalScore.toString() ?? '',
    );
    _durationController = TextEditingController(
      text: widget.template?.duration.toString() ?? '',
    );

    // 设置当前模板
    _currentTemplate = widget.template;

    // 如果是创建模式，自动进入编辑模式
    _isEditing = widget.isCreating;
  }

  // 处理标签索引变化
  void _handleTabIndexChange() {
    // 当标签索引变化时，触发重建以更新分段控制器的选择状态
    if (_tabController.indexIsChanging) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    // 移除标签控制器监听器
    _tabController.removeListener(_handleTabIndexChange);
    _tabController.dispose();
    _nameController.dispose();
    _totalScoreController.dispose();
    _durationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final title = widget.isCreating
        ? '创建考试模板'
        : (_isEditing ? '编辑考试模板' : _currentTemplate?.name ?? '');

    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: AppColors.text,
        actions: [
          if (!widget.isCreating && !_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: _navigateToEditScreen,
            ),
          if (!widget.isCreating && !_isEditing)
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert),
              onSelected: (value) {
                if (value == 'delete') {
                  _showDeleteConfirmDialog();
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem<String>(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: AppColors.error, size: 20),
                      SizedBox(width: 8),
                      Text('删除', style: TextStyle(color: AppColors.error)),
                    ],
                  ),
                ),
              ],
            ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 分段控制器
            Center(
              child: Container(
                margin: const EdgeInsets.symmetric(
                  vertical: AppSizes.paddingMedium,
                ),
                child: SegmentedButton<int>(
                  segments: const [
                    ButtonSegment<int>(
                      value: 0,
                      label: Text('快速开始'),
                      icon: Icon(Icons.play_arrow),
                    ),
                    ButtonSegment<int>(
                      value: 1,
                      label: Text('详细信息'),
                      icon: Icon(Icons.info_outline),
                    ),
                  ],
                  selected: {_tabController.index},
                  onSelectionChanged: (Set<int> selection) {
                    setState(() {
                      _tabController.animateTo(selection.first);
                    });
                  },
                  showSelectedIcon: false,
                  emptySelectionAllowed: false,
                ),
              ),
            ),

            // 内容区域
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // 快速开始页面
                  _buildQuickStartTab(),

                  // 详细信息页面
                  _buildDetailsTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 导航到编辑页面
  void _navigateToEditScreen() async {
    if (_currentTemplate == null) return;

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ExamTemplateEditScreen(template: _currentTemplate!),
      ),
    );

    if (result != null && result is ExamTemplate && mounted) {
      setState(() {
        _currentTemplate = result;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('模板更新成功')),
      );
    }
  }





  // 构建快速开始标签页
  Widget _buildQuickStartTab() {
    if (_currentTemplate == null) {
      return const Center(
        child: Text('模板信息不可用'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 模拟考试信息卡片
          Card(
            margin: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
            ),
            child: Padding(
              padding: const EdgeInsets.all(AppSizes.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 模板名称
                  Text(
                    _currentTemplate!.name,
                    style: AppTextStyles.headline2,
                  ),
                  const SizedBox(height: AppSizes.paddingSmall),

                  // 基本信息行
                  Row(
                    children: [
                      // 总分
                      _buildInfoChip(
                        '总分: ${_currentTemplate!.totalScore}',
                        Icons.score,
                      ),
                      const SizedBox(width: AppSizes.paddingMedium),

                      // 时长
                      _buildInfoChip(
                        '时长: ${_currentTemplate!.duration}分钟',
                        Icons.timer,
                      ),
                    ],
                  ),

                  // 题型模块信息
                  if (_currentTemplate!.sections.isNotEmpty) ...[
                    const SizedBox(height: AppSizes.paddingMedium),
                    const Divider(),
                    const SizedBox(height: AppSizes.paddingSmall),

                    Text(
                      '题型模块: ${_currentTemplate!.sections.length}个',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],

                  // 考试实例信息
                  if (_currentTemplate!.instances.isNotEmpty) ...[
                    const SizedBox(height: AppSizes.paddingMedium),
                    const Divider(),
                    const SizedBox(height: AppSizes.paddingSmall),

                    Text(
                      '考试实例: ${_currentTemplate!.instances.length}个',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),

          // 考试实例选择
          Card(
            margin: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
            ),
            child: Padding(
              padding: const EdgeInsets.all(AppSizes.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('考试实例', style: AppTextStyles.headline3),
                  const SizedBox(height: AppSizes.paddingMedium),

                  // 实例选择下拉菜单
                  if (_currentTemplate!.instances.isEmpty)
                    const Text('暂无考试实例', style: AppTextStyles.bodyMedium)
                  else
                    DropdownButtonFormField<String?>(
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                      ),
                      hint: const Text('选择考试实例'),
                      value: _selectedInstanceId,
                      items: [
                        // 不选择实例选项
                        const DropdownMenuItem<String?>(
                          value: null,
                          child: Text('不选择实例'),
                        ),
                        // 实例选项
                        ..._currentTemplate!.instances.map((instance) {
                          return DropdownMenuItem<String?>(
                            value: instance.id,
                            child: Text(instance.number),
                          );
                        }),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedInstanceId = value;
                        });
                      },
                    ),

                  const SizedBox(height: AppSizes.paddingMedium),

                  // 不选择实例和新建实例按钮行
                  Row(
                    children: [
                      // 不选择实例按钮
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            setState(() {
                              _selectedInstanceId = null;
                            });
                          },
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppColors.textSecondary,
                            side: const BorderSide(color: AppColors.border),
                          ),
                          child: const Text('不选择实例'),
                        ),
                      ),
                      const SizedBox(width: AppSizes.paddingMedium),
                      // 新建实例按钮
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            // 新建实例
                          },
                          icon: const Icon(Icons.add),
                          label: const Text('新建实例'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppColors.primary,
                            side: const BorderSide(color: AppColors.primary),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // 考试范围选择
          Card(
            margin: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
            ),
            child: Padding(
              padding: const EdgeInsets.all(AppSizes.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('考试范围', style: AppTextStyles.headline3),
                  const SizedBox(height: AppSizes.paddingMedium),

                  // 考试范围选项
                  _buildSelectionOption(
                    title: '完整考试',
                    subtitle: '包含所有题型模块',
                    isSelected: _isFullExam,
                    onTap: () {
                      setState(() {
                        _isFullExam = true;
                        _selectedSectionIds.clear();
                      });
                    },
                  ),

                  const SizedBox(height: AppSizes.paddingSmall),

                  _buildSelectionOption(
                    title: '自定义模块',
                    subtitle: '选择特定题型模块',
                    isSelected: !_isFullExam,
                    onTap: () {
                      setState(() {
                        _isFullExam = false;
                      });
                    },
                  ),

                  // 自定义模块选择区域
                  if (!_isFullExam) ...[
                    const SizedBox(height: AppSizes.paddingMedium),
                    const Divider(),
                    const SizedBox(height: AppSizes.paddingSmall),

                    if (_currentTemplate!.sections.isEmpty)
                      const Center(
                        child: Padding(
                          padding: EdgeInsets.all(AppSizes.paddingMedium),
                          child: Text('暂无题型模块可选择', style: AppTextStyles.bodyMedium),
                        ),
                      )
                    else
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('选择题型模块:', style: AppTextStyles.bodyMedium),
                          const SizedBox(height: AppSizes.paddingSmall),
                          ..._currentTemplate!.sections.map((section) {
                            final isSelected = _selectedSectionIds.contains(section.id);
                            return CheckboxListTile(
                              title: Text(section.name),
                              subtitle: Text('分值: ${section.score} | 预计用时: ${section.estimatedTime}分钟'),
                              value: isSelected,
                              onChanged: (value) {
                                setState(() {
                                  if (value == true) {
                                    if (!_selectedSectionIds.contains(section.id)) {
                                      _selectedSectionIds.add(section.id);
                                    }
                                  } else {
                                    _selectedSectionIds.remove(section.id);
                                  }
                                });
                              },
                              controlAffinity: ListTileControlAffinity.leading,
                              contentPadding: EdgeInsets.zero,
                            );
                          }),
                        ],
                      ),
                  ],
                ],
              ),
            ),
          ),

          // 开始考试按钮
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                // 开始考试
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('考试计时功能开发中')),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingMedium),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                ),
              ),
              child: const Text('开始考试', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            ),
          ),
        ],
      ),
    );
  }

  // 构建详细信息标签页
  Widget _buildDetailsTab() {
    if (_currentTemplate == null) {
      return const Center(
        child: Text('模板信息不可用'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 题型模块部分
          const Text('题型模块', style: AppTextStyles.headline3),
          const SizedBox(height: AppSizes.paddingMedium),

          if (_currentTemplate!.sections.isEmpty)
            Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
              ),
              child: const Padding(
                padding: EdgeInsets.all(AppSizes.paddingMedium),
                child: Center(
                  child: Text('暂无题型模块', style: AppTextStyles.bodyMedium),
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _currentTemplate!.sections.length,
              itemBuilder: (context, index) {
                final section = _currentTemplate!.sections[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: AppSizes.paddingSmall),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                  ),
                  child: ListTile(
                    title: Text(section.name),
                    subtitle: Text('分值: ${section.score} | 预计用时: ${section.estimatedTime}分钟'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      // 查看题型详情
                    },
                  ),
                );
              },
            ),

          const SizedBox(height: AppSizes.paddingLarge),

          // 考试实例部分
          const Text('考试实例', style: AppTextStyles.headline3),
          const SizedBox(height: AppSizes.paddingMedium),

          if (_currentTemplate!.instances.isEmpty)
            Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
              ),
              child: const Padding(
                padding: EdgeInsets.all(AppSizes.paddingMedium),
                child: Center(
                  child: Text('暂无考试实例', style: AppTextStyles.bodyMedium),
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _currentTemplate!.instances.length,
              itemBuilder: (context, index) {
                final instance = _currentTemplate!.instances[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: AppSizes.paddingSmall),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                  ),
                  child: ListTile(
                    title: Text('编号: ${instance.number}'),
                    subtitle: Text(
                      '日期: ${instance.formattedDate} | 时间: ${instance.formattedTimeRange}',
                    ),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      // 查看实例详情
                    },
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  // 构建信息标签
  Widget _buildInfoChip(String label, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSizes.paddingSmall,
        vertical: AppSizes.paddingSmall / 2,
      ),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: AppColors.textSecondary),
          const SizedBox(width: 4),
          Text(label, style: AppTextStyles.bodySmall),
        ],
      ),
    );
  }

  // 构建选择选项
  Widget _buildSelectionOption({
    required String title,
    required String subtitle,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
      child: Container(
        padding: const EdgeInsets.all(AppSizes.paddingSmall),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.border,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
        ),
        child: Row(
          children: [
            Radio<bool>(
              value: true,
              groupValue: isSelected,
              onChanged: (_) => onTap(),
              activeColor: AppColors.primary,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title, style: AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.w500)),
                  Text(subtitle, style: AppTextStyles.bodySmall),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示删除确认对话框
  void _showDeleteConfirmDialog() {
    if (_currentTemplate == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除模拟考试"${_currentTemplate!.name}"吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteTemplate();
            },
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  // 删除模板
  Future<void> _deleteTemplate() async {
    if (_currentTemplate == null) return;

    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在删除...'),
            ],
          ),
        ),
      );

      // 删除模板
      await _templateService.deleteTemplate(_currentTemplate!.id);

      if (!mounted) return;

      // 关闭加载对话框
      Navigator.of(context).pop();

      // 显示成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('模拟考试已删除')),
      );

      // 返回上一页
      Navigator.of(context).pop();
    } catch (e) {
      if (!mounted) return;

      // 关闭加载对话框
      Navigator.of(context).pop();

      // 显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('删除失败: $e')),
      );
    }
  }
}
