import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import '../../../shared/widgets/app_card.dart';
import '../models/exam_template_models.dart';
import '../services/exam_template_service.dart';
import 'exam_instance_detail_screen.dart';

/// 考试实例列表页面
/// 显示某个考试模板下的所有实例
class ExamInstanceListScreen extends StatefulWidget {
  /// 考试模板
  final ExamTemplate template;

  const ExamInstanceListScreen({
    super.key,
    required this.template,
  });

  @override
  State<ExamInstanceListScreen> createState() => _ExamInstanceListScreenState();
}

class _ExamInstanceListScreenState extends State<ExamInstanceListScreen> {
  // 考试模板服务
  final ExamTemplateService _templateService = ExamTemplateService();

  // 当前模板
  late ExamTemplate _currentTemplate;

  // 获取所有实例
  List<ExamInstance> get _instances => _currentTemplate.instances;

  @override
  void initState() {
    super.initState();
    _currentTemplate = widget.template;
  }

  // 刷新模板数据
  Future<void> _refreshTemplate() async {
    try {
      final template = await _templateService.getTemplateById(_currentTemplate.id);
      if (template != null && mounted) {
        setState(() {
          _currentTemplate = template;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('刷新数据失败: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${_currentTemplate.name}的考试实例'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: AppColors.text,
      ),
      body: SafeArea(
        child: _instances.isEmpty
            ? _buildEmptyState()
            : _buildInstanceList(),
      ),
      // 添加实例按钮
      floatingActionButton: FloatingActionButton(
        onPressed: _createInstance,
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add),
      ),
    );
  }

  // 构建空状态提示
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.assignment_outlined,
            size: 64,
            color: AppColors.textTertiary,
          ),
          SizedBox(height: AppSizes.paddingMedium),
          Text(
            '暂无考试实例',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: AppSizes.paddingSmall),
          Text(
            '点击右下角的"+"按钮创建新的考试实例',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textTertiary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // 构建实例列表
  Widget _buildInstanceList() {
    // 按编号排序
    final sortedInstances = List<ExamInstance>.from(_instances);
    sortedInstances.sort((a, b) => b.number.compareTo(a.number));

    return ListView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      itemCount: sortedInstances.length,
      itemBuilder: (context, index) {
        final instance = sortedInstances[index];
        return _buildInstanceCard(instance);
      },
    );
  }

  // 构建实例卡片
  Widget _buildInstanceCard(ExamInstance instance) {
    final numberPrefix = instance.numberingMode == ExamNumberingMode.year
        ? '年份：'
        : '编号：';

    return AppCard(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
      padding: EdgeInsets.zero,
      onTap: () => _navigateToInstanceDetail(instance),
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingMedium),
        child: Row(
          children: [
            // 实例编号
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: AppColors.primary.withAlpha(30),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  instance.number,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ),
            ),
            const SizedBox(width: AppSizes.paddingMedium),

            // 实例信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '$numberPrefix${instance.number}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppColors.text,
                    ),
                  ),
                  const SizedBox(height: 4),
                  if (instance.examDate != null)
                    Text(
                      '考试日期：${instance.formattedDate}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  if (instance.startTime != null && instance.endTime != null)
                    Text(
                      '考试时间：${instance.formattedTimeRange}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                ],
              ),
            ),

            // 箭头图标
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColors.textTertiary,
            ),
          ],
        ),
      ),
    );
  }

  // 创建新实例
  Future<void> _createInstance() async {
    try {
      // 简化版创建实例逻辑
      await _templateService.createInstance(
        templateId: _currentTemplate.id,
        number: DateTime.now().year.toString(),
        numberingMode: ExamNumberingMode.year,
        examDate: DateTime.now(),
        startTime: const TimeOfDay(hour: 9, minute: 0),
        endTime: const TimeOfDay(hour: 12, minute: 0),
      );

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('实例创建成功')),
      );

      _refreshTemplate();
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('创建失败: $e')),
      );
    }
  }

  // 导航到实例详情页面
  void _navigateToInstanceDetail(ExamInstance instance) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ExamInstanceDetailScreen(
          template: _currentTemplate,
          instance: instance,
        ),
      ),
    ).then((_) {
      _refreshTemplate();
    });
  }
}
