// import 'package:flutter/material.dart';
// import '../../../shared/theme/constants.dart';
// import '../../../shared/widgets/app_card.dart';
// import '../models/exam_template_models.dart';
// import '../services/exam_template_service.dart';
// import 'exam_template_detail_screen.dart';
// import 'exam_template_create_new_screen.dart';

// /// 考试模板列表页面
// /// 显示所有可用的考试模板
// class ExamTemplateListScreen extends StatefulWidget {
//   const ExamTemplateListScreen({super.key});

//   @override
//   State<ExamTemplateListScreen> createState() => _ExamTemplateListScreenState();
// }

// class _ExamTemplateListScreenState extends State<ExamTemplateListScreen> {
//   // 考试模板服务
//   final ExamTemplateService _templateService = ExamTemplateService();

//   // 考试模板列表
//   List<ExamTemplate> _templates = [];
//   bool _isLoading = true;

//   @override
//   void initState() {
//     super.initState();
//     _loadTemplates();
//   }

//   // 加载模板列表
//   Future<void> _loadTemplates() async {
//     try {
//       setState(() {
//         _isLoading = true;
//       });

//       final templates = await _templateService.getAllTemplates();
//       if (mounted) {
//         setState(() {
//           _templates = templates;
//           _isLoading = false;
//         });
//       }
//     } catch (e) {
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//         });
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(content: Text('加载模板失败: $e')),
//         );
//       }
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('创建模拟考'),
//         backgroundColor: Colors.transparent,
//         elevation: 0,
//         foregroundColor: AppColors.text,
//       ),
//       body: SafeArea(
//         child: _isLoading
//             ? const Center(child: CircularProgressIndicator())
//             : RefreshIndicator(
//                 onRefresh: _loadTemplates,
//                 child: SingleChildScrollView(
//                   padding: const EdgeInsets.all(AppSizes.paddingMedium),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       // 标题和说明
//                       // const Text(
//                       //   '选择考试模板',
//                       //   style: AppTextStyles.headline2,
//                       // ),
//                       const SizedBox(height: AppSizes.paddingSmall),
//                       const Text(
//                         '浏览可用的考试模板，或创建新的模板',
//                         style: AppTextStyles.bodyMedium,
//                       ),
//                       const SizedBox(height: AppSizes.paddingLarge),

//                       // 考试模板列表
//                       if (_templates.isEmpty)
//                         Center(
//                           child: Padding(
//                             padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingLarge),
//                             child: Column(
//                               children: [
//                                 const Icon(
//                                   Icons.assignment_outlined,
//                                   size: 64,
//                                   color: AppColors.textTertiary,
//                                 ),
//                                 const SizedBox(height: AppSizes.paddingMedium),
//                                 const Text(
//                                   '暂无模板',
//                                   style: TextStyle(
//                                     fontSize: 16,
//                                     fontWeight: FontWeight.w500,
//                                     color: AppColors.textSecondary,
//                                   ),
//                                 ),
//                                 const SizedBox(height: AppSizes.paddingSmall),
//                                 const Text(
//                                   '点击下方的“创建新模板”卡片创建模板',
//                                   style: TextStyle(
//                                     fontSize: 14,
//                                     color: AppColors.textTertiary,
//                                   ),
//                                   textAlign: TextAlign.center,
//                                 ),
//                               ],
//                             ),
//                           ),
//                         )
//                       else
//                         ..._templates.map((template) => _buildTemplateCard(template)),

//                       // 创建新模板按钮
//                       _buildCreateTemplateCard(),
//                     ],
//                   ),
//                 ),
//               ),
//       ),
//     );
//   }

//   // 构建考试模板卡片
//   Widget _buildTemplateCard(ExamTemplate template) {
//     return AppCard(
//       margin: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
//       padding: EdgeInsets.zero,
//       onTap: () => _navigateToTemplateDetail(template),
//       child: Padding(
//         padding: const EdgeInsets.all(AppSizes.paddingMedium),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // 模板名称和实例数量
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Expanded(
//                   child: Text(
//                     template.name,
//                     style: AppTextStyles.headline3,
//                     maxLines: 1,
//                     overflow: TextOverflow.ellipsis,
//                   ),
//                 ),
//                 Container(
//                   padding: const EdgeInsets.symmetric(
//                     horizontal: 8,
//                     vertical: 4,
//                   ),
//                   decoration: BoxDecoration(
//                     color: AppColors.primary.withAlpha(30),
//                     borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
//                   ),
//                   child: Text(
//                     '${template.instances.length} 套试题',
//                     style: TextStyle(
//                       fontSize: 12,
//                       color: AppColors.primary,
//                       fontWeight: FontWeight.w500,
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//             const SizedBox(height: AppSizes.paddingSmall),

//             // 模板信息
//             Row(
//               children: [
//                 _buildInfoChip(
//                   '${template.totalScore}分',
//                   Icons.score,
//                 ),
//                 const SizedBox(width: 8),
//                 _buildInfoChip(
//                   '${template.duration}分钟',
//                   Icons.timer,
//                 ),
//                 const SizedBox(width: 8),
//                 _buildInfoChip(
//                   '${template.sections.length}个题型',
//                   Icons.format_list_numbered,
//                 ),
//               ],
//             ),
//             const SizedBox(height: AppSizes.paddingMedium),

//             // 题型列表
//             Wrap(
//               spacing: 8,
//               runSpacing: 8,
//               children: template.sections.map((section) {
//                 return Container(
//                   padding: const EdgeInsets.symmetric(
//                     horizontal: 8,
//                     vertical: 4,
//                   ),
//                   decoration: BoxDecoration(
//                     color: AppColors.background,
//                     borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
//                     border: Border.all(
//                       color: AppColors.border,
//                       width: 1,
//                     ),
//                   ),
//                   child: Text(
//                     '${section.name} (${section.score}分)',
//                     style: const TextStyle(
//                       fontSize: 12,
//                       color: AppColors.textSecondary,
//                     ),
//                   ),
//                 );
//               }).toList(),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   // 构建创建新模板卡片
//   Widget _buildCreateTemplateCard() {
//     return AppCard(
//       margin: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
//       padding: EdgeInsets.zero,
//       onTap: _navigateToCreateTemplate,
//       child: Container(
//         padding: const EdgeInsets.all(AppSizes.paddingMedium),
//         decoration: BoxDecoration(
//           border: Border.all(
//             color: AppColors.primary.withAlpha(50),
//             width: 1.5,
//           ),
//           borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
//         ),
//         child: Row(
//           children: [
//             Container(
//               width: 40,
//               height: 40,
//               decoration: BoxDecoration(
//                 color: AppColors.primary.withAlpha(30),
//                 shape: BoxShape.circle,
//               ),
//               child: const Icon(
//                 Icons.add,
//                 color: AppColors.primary,
//               ),
//             ),
//             const SizedBox(width: AppSizes.paddingMedium),
//             const Expanded(
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     '创建新模板',
//                     style: TextStyle(
//                       fontSize: 16,
//                       fontWeight: FontWeight.w600,
//                       color: AppColors.primary,
//                     ),
//                   ),
//                   SizedBox(height: 4),
//                   Text(
//                     '自定义考试题型、分值和时间',
//                     style: TextStyle(
//                       fontSize: 12,
//                       color: AppColors.textTertiary,
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//             const Icon(
//               Icons.arrow_forward_ios,
//               size: 16,
//               color: AppColors.primary,
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   // 构建信息标签
//   Widget _buildInfoChip(String label, IconData icon) {
//     return Container(
//       padding: const EdgeInsets.symmetric(
//         horizontal: 8,
//         vertical: 4,
//       ),
//       decoration: BoxDecoration(
//         color: AppColors.background,
//         borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
//       ),
//       child: Row(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Icon(
//             icon,
//             size: 12,
//             color: AppColors.textTertiary,
//           ),
//           const SizedBox(width: 4),
//           Text(
//             label,
//             style: const TextStyle(
//               fontSize: 12,
//               color: AppColors.textTertiary,
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   // 导航到模板详情页面
//   void _navigateToTemplateDetail(ExamTemplate template) {
//     Navigator.push(
//       context,
//       MaterialPageRoute(
//         builder: (context) => ExamTemplateDetailScreen(template: template),
//       ),
//     ).then((_) {
//       // 返回时刷新列表
//       setState(() {});
//     });
//   }

//   // 导航到创建模板页面
//   void _navigateToCreateTemplate() {
//     Navigator.push(
//       context,
//       MaterialPageRoute(
//         builder: (context) => const ExamTemplateCreateNewScreen(),
//       ),
//     ).then((_) {
//       // 返回时刷新列表
//       setState(() {});
//     });
//   }
// }
