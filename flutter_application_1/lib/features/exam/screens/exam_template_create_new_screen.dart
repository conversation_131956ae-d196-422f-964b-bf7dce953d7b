import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/models/subject_project.dart';
import '../../../core/services/hive_service.dart';
import '../models/exam_template_models.dart';
import '../services/exam_template_service.dart';
import '../providers/exam_state.dart';

/// 创建模式枚举
enum CreateMode {
  /// 自定义创建
  custom,

  /// 从模板创建
  template,
}

/// 创建新模拟考试模板页面
/// 这是一个独立的页面，用于创建新的模拟考试模板
class ExamTemplateCreateNewScreen extends ConsumerStatefulWidget {
  const ExamTemplateCreateNewScreen({super.key});

  @override
  ConsumerState<ExamTemplateCreateNewScreen> createState() => _ExamTemplateCreateNewScreenState();
}

class _ExamTemplateCreateNewScreenState extends ConsumerState<ExamTemplateCreateNewScreen> {
  // 当前创建模式
  CreateMode _currentMode = CreateMode.custom;

  @override
  Widget build(BuildContext context) {
    // 监听状态
    final examState = ref.watch(examStateProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('创建新模拟考试'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: AppColors.text,
      ),
      body: SafeArea(
        child: examState.isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  // 错误提示
                  if (examState.error != null)
                    Container(
                      margin: const EdgeInsets.all(AppSizes.paddingMedium),
                      padding: const EdgeInsets.all(AppSizes.paddingMedium),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                        border: Border.all(color: Colors.red.shade200),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.error_outline, color: Colors.red),
                          const SizedBox(width: AppSizes.paddingSmall),
                          Expanded(
                            child: Text(
                              examState.error!,
                              style: const TextStyle(color: Colors.red),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.close, color: Colors.red),
                            onPressed: () {
                              ref.read(examStateProvider.notifier).setError(null);
                            },
                          ),
                        ],
                      ),
                    ),

                  // 内容区域
                  Expanded(
                    child: _currentMode == CreateMode.custom
                        ? _CustomCreateView(currentMode: _currentMode, onModeChanged: (mode) {
                            setState(() {
                              _currentMode = mode;
                            });
                          })
                        : _TemplateCreateView(currentMode: _currentMode, onModeChanged: (mode) {
                            setState(() {
                              _currentMode = mode;
                            });
                          }),
                  ),
                ],
              ),
      ),
    );
  }


}

/// 自定义创建视图
class _CustomCreateView extends ConsumerStatefulWidget {
  final CreateMode currentMode;
  final Function(CreateMode) onModeChanged;

  const _CustomCreateView({
    required this.currentMode,
    required this.onModeChanged,
  });

  @override
  ConsumerState<_CustomCreateView> createState() => _CustomCreateViewState();
}

class _CustomCreateViewState extends ConsumerState<_CustomCreateView> {
  // 考试模板服务
  final ExamTemplateService _templateService = ExamTemplateService();

  // Hive服务
  final HiveService _hiveService = HiveService();

  // 表单控制器
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _totalScoreController = TextEditingController(text: '100');
  final TextEditingController _durationController = TextEditingController(text: '120');

  // 科目选择
  String? _selectedSubjectId;

  // 科目列表
  List<Subject> _subjects = [];

  // 题型模块列表
  final List<ExamSection> _sections = [];

  // 考试实例列表
  final List<ExamInstance> _instances = [];

  @override
  void initState() {
    super.initState();
    // 加载科目列表
    _loadSubjects();
  }

  // 加载科目列表
  Future<void> _loadSubjects() async {
    try {
      // 确保Hive服务已初始化
      await _hiveService.initHive();

      // 获取所有科目
      final subjects = _hiveService.subjectRepository.getAllSubjects();

      setState(() {
        _subjects = subjects;
      });
    } catch (e) {
      debugPrint('加载科目列表出错: $e');
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _totalScoreController.dispose();
    _durationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部分段控制器
          Center(
            child: Container(
              margin: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
              child: SegmentedButton<CreateMode>(
                segments: const [
                  ButtonSegment<CreateMode>(
                    value: CreateMode.custom,
                    label: Text('自定义'),
                    icon: Icon(Icons.create),
                  ),
                  ButtonSegment<CreateMode>(
                    value: CreateMode.template,
                    label: Text('模板'),
                    icon: Icon(Icons.content_copy),
                  ),
                ],
                selected: {widget.currentMode},
                onSelectionChanged: (Set<CreateMode> selection) {
                  widget.onModeChanged(selection.first);
                },
                showSelectedIcon: false,
                emptySelectionAllowed: false,
              ),
            ),
          ),

          // 标题
          const Text(
            '自定义创建模拟考试',
            style: AppTextStyles.headline2,
          ),
          const SizedBox(height: AppSizes.paddingSmall),
          const Text(
            '根据您的需求自定义创建模拟考试模板',
            style: AppTextStyles.bodyMedium,
          ),
          const SizedBox(height: AppSizes.paddingLarge),

          // 基本信息卡片
          _buildBasicInfoCard(),
          const SizedBox(height: AppSizes.paddingMedium),

          // 题型模块卡片
          _buildSectionsCard(),
          const SizedBox(height: AppSizes.paddingMedium),

          // 考试实例卡片
          _buildInstancesCard(),
          const SizedBox(height: AppSizes.paddingLarge),

          // 创建按钮
          Center(
            child: ElevatedButton(
              onPressed: _createTemplate,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSizes.paddingLarge,
                  vertical: AppSizes.paddingMedium,
                ),
                minimumSize: const Size(200, AppSizes.buttonHeightMedium),
              ),
              child: const Text('创建模拟考试'),
            ),
          ),
        ],
      ),
    );
  }

  // 构建基本信息卡片
  Widget _buildBasicInfoCard() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        side: const BorderSide(color: AppColors.border),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 卡片标题
            const Row(
              children: [
                Icon(Icons.info_outline, size: 20, color: AppColors.primary),
                SizedBox(width: AppSizes.paddingSmall),
                Text('基本信息', style: AppTextStyles.headline3),
              ],
            ),
            const Divider(height: 24),

            // 考试名称
            const Text('考试名称', style: AppTextStyles.labelMedium),
            const SizedBox(height: AppSizes.paddingSmall),
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                hintText: '输入考试名称，如“考研英语一”',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: AppSizes.paddingMedium,
                  vertical: AppSizes.paddingSmall,
                ),
              ),
            ),
            const SizedBox(height: AppSizes.paddingMedium),

            // 科目选择
            const Text('关联科目', style: AppTextStyles.labelMedium),
            const SizedBox(height: AppSizes.paddingSmall),
            DropdownButtonFormField<String>(
              value: _selectedSubjectId,
              decoration: const InputDecoration(
                hintText: '选择科目（可选）',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: AppSizes.paddingMedium,
                  vertical: AppSizes.paddingSmall,
                ),
              ),
              items: [
                // 添加“无”选项
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('无'),
                ),
                // 添加科目选项
                ..._subjects.map((subject) => DropdownMenuItem<String>(
                  value: subject.id,
                  child: Text(subject.name),
                )),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedSubjectId = value;
                });
              },
            ),
            const SizedBox(height: AppSizes.paddingMedium),

            // 总分值和考试时长
            Row(
              children: [
                // 总分值
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('总分值', style: AppTextStyles.labelMedium),
                      const SizedBox(height: AppSizes.paddingSmall),
                      TextField(
                        controller: _totalScoreController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          hintText: '输入总分值',
                          suffixText: '分',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingMedium,
                            vertical: AppSizes.paddingSmall,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: AppSizes.paddingMedium),

                // 考试时长
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('考试时长', style: AppTextStyles.labelMedium),
                      const SizedBox(height: AppSizes.paddingSmall),
                      TextField(
                        controller: _durationController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          hintText: '输入考试时长',
                          suffixText: '分钟',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingMedium,
                            vertical: AppSizes.paddingSmall,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 构建题型模块卡片
  Widget _buildSectionsCard() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        side: const BorderSide(color: AppColors.border),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 卡片标题和添加按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Row(
                  children: [
                    Icon(Icons.format_list_numbered, size: 20, color: AppColors.primary),
                    SizedBox(width: AppSizes.paddingSmall),
                    Text('题型模块', style: AppTextStyles.headline3),
                  ],
                ),
                TextButton.icon(
                  onPressed: _showAddSectionDialog,
                  icon: const Icon(Icons.add, size: 16),
                  label: const Text('添加'),
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.primary,
                  ),
                ),
              ],
            ),
            const Divider(height: 24),

            // 模块内容区域
            _sections.isEmpty
                ? const Center(
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: AppSizes.paddingLarge),
                      child: Text(
                        '暂无题型模块，点击“添加”按钮创建',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.textTertiary,
                        ),
                      ),
                    ),
                  )
                : Column(
                    children: _sections.map((section) => _buildSectionItem(section)).toList(),
                  ),
          ],
        ),
      ),
    );
  }

  // 构建考试实例卡片
  Widget _buildInstancesCard() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        side: const BorderSide(color: AppColors.border),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 卡片标题和添加按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Row(
                  children: [
                    Icon(Icons.assignment, size: 20, color: AppColors.primary),
                    SizedBox(width: AppSizes.paddingSmall),
                    Text('考试实例', style: AppTextStyles.headline3),
                  ],
                ),
                Row(
                  children: [
                    // 批量添加按钮
                    TextButton.icon(
                      onPressed: _showBatchAddInstanceDialog,
                      icon: const Icon(Icons.playlist_add, size: 16),
                      label: const Text('批量添加'),
                      style: TextButton.styleFrom(
                        foregroundColor: AppColors.primary,
                      ),
                    ),
                    const SizedBox(width: 8),
                    // 单个添加按钮
                    TextButton.icon(
                      onPressed: _showAddInstanceDialog,
                      icon: const Icon(Icons.add, size: 16),
                      label: const Text('添加'),
                      style: TextButton.styleFrom(
                        foregroundColor: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const Divider(height: 24),

            // 实例内容区域，设置最大高度并添加滚动功能
            Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.5, // 设置为屏幕高度的50%
              ),
              child: _instances.isEmpty
                  ? const Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: AppSizes.paddingLarge),
                        child: Text(
                          '暂无考试实例，点击“添加”按钮创建',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.textTertiary,
                          ),
                        ),
                      ),
                    )
                  : SingleChildScrollView(
                      child: Column(
                        children: _instances.map((instance) => _buildInstanceItem(instance)).toList(),
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示添加题型模块对话框
  void _showAddSectionDialog() {
    // 使用一个安全的方式来处理对话框
    // 先创建临时数据对象，而不直接使用控制器
    String name = '';
    String scoreText = '10';
    String timeText = '30';

    // 显示对话框
    showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false, // 防止点击外部关闭对话框
      builder: (dialogContext) {
        // 在对话框内部创建控制器，这样它们的生命周期与对话框绑定
        final nameController = TextEditingController();
        final scoreController = TextEditingController(text: scoreText);
        final timeController = TextEditingController(text: timeText);

        return StatefulBuilder(
          builder: (context, setDialogState) {
            // 验证并添加题型
            void validateAndAdd() {
              // 获取当前值
              name = nameController.text.trim();
              scoreText = scoreController.text.trim();
              timeText = timeController.text.trim();

              // 验证名称
              if (name.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入题型名称')),
                );
                return;
              }

              // 验证分值
              final score = int.tryParse(scoreText);
              if (score == null || score <= 0) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入有效的分值')),
                );
                return;
              }

              // 验证预计用时
              final estimatedTime = int.tryParse(timeText);
              if (estimatedTime == null || estimatedTime <= 0) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入有效的预计用时')),
                );
                return;
              }

              // 关闭对话框并返回数据
              Navigator.of(dialogContext).pop({
                'name': name,
                'score': score,
                'estimatedTime': estimatedTime,
              });
            }

            return AlertDialog(
              title: const Text('添加题型模块'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 题型名称
                    const Text('题型名称', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    TextField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        hintText: '输入题型名称，如“阅读理解”',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                      ),
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 题型分值和预计用时
                    Row(
                      children: [
                        // 题型分值
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('题型分值', style: AppTextStyles.labelMedium),
                              const SizedBox(height: AppSizes.paddingSmall),
                              TextField(
                                controller: scoreController,
                                keyboardType: TextInputType.number,
                                decoration: const InputDecoration(
                                  hintText: '输入分值',
                                  suffixText: '分',
                                  border: OutlineInputBorder(),
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: AppSizes.paddingMedium,
                                    vertical: AppSizes.paddingSmall,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: AppSizes.paddingMedium),

                        // 预计用时
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('预计用时', style: AppTextStyles.labelMedium),
                              const SizedBox(height: AppSizes.paddingSmall),
                              TextField(
                                controller: timeController,
                                keyboardType: TextInputType.number,
                                decoration: const InputDecoration(
                                  hintText: '输入用时',
                                  suffixText: '分钟',
                                  border: OutlineInputBorder(),
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: AppSizes.paddingMedium,
                                    vertical: AppSizes.paddingSmall,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                // 取消按钮
                TextButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                  child: const Text('取消'),
                ),
                // 确定按钮
                ElevatedButton(
                  onPressed: validateAndAdd,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('添加'),
                ),
              ],
            );
          },
        );
      },
    ).then((result) {
      // 如果用户点击了添加按钮并返回了数据
      if (result != null) {
        // 创建新的题型模块
        final newSection = ExamSection(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: result['name'],
          score: result['score'],
          estimatedTime: result['estimatedTime'],
          templateId: 'temp_id', // 暂时使用临时ID，创建模板时会更新
        );

        // 添加到列表
        setState(() {
          _sections.add(newSection);
        });
      }
    });
  }

  // 构建题型模块项
  Widget _buildSectionItem(ExamSection section) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingSmall),
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          // 题型图标
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.primary.withAlpha(30),
              borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
            ),
            child: const Center(
              child: Icon(
                Icons.format_list_numbered,
                color: AppColors.primary,
                size: 20,
              ),
            ),
          ),
          const SizedBox(width: AppSizes.paddingMedium),

          // 题型信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  section.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppColors.text,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      '${section.score}分',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      '${section.estimatedTime}分钟',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // 操作按钮
          Row(
            children: [
              // 编辑按钮
              IconButton(
                icon: const Icon(Icons.edit_outlined, color: AppColors.primary),
                onPressed: () {
                  _showEditSectionDialog(section);
                },
                tooltip: '编辑题型',
              ),
              // 删除按钮
              IconButton(
                icon: const Icon(Icons.delete_outline, color: AppColors.error),
                onPressed: () {
                  setState(() {
                    _sections.remove(section);
                  });
                },
                tooltip: '删除题型',
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 显示批量添加考试实例对话框
  void _showBatchAddInstanceDialog() {
    // 初始化数据
    String startNumber = '';
    String endNumber = '';

    // 显示对话框
    showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false, // 防止点击外部关闭对话框
      builder: (dialogContext) {
        // 在对话框内部创建控制器
        final startNumberController = TextEditingController();
        final endNumberController = TextEditingController();

        return StatefulBuilder(
          builder: (context, setDialogState) {
            // 验证并批量添加
            void validateAndBatchAdd() {
              // 获取当前值
              startNumber = startNumberController.text.trim();
              endNumber = endNumberController.text.trim();

              // 验证起始序号
              final startNum = int.tryParse(startNumber);
              if (startNum == null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入有效的起始序号')),
                );
                return;
              }

              // 验证结束序号
              final endNum = int.tryParse(endNumber);
              if (endNum == null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入有效的结束序号')),
                );
                return;
              }

              // 验证范围
              if (endNum <= startNum) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('结束序号必须大于起始序号')),
                );
                return;
              }

              // 验证范围大小
              if (endNum - startNum > 100) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('批量添加数量不能超过100个')),
                );
                return;
              }

              // 检查编号是否重复
              final existingNumbers = _instances
                  .where((instance) => instance.numberingMode == ExamNumberingMode.year)
                  .map((instance) => int.tryParse(instance.number))
                  .whereType<int>();

              final duplicateNumbers = <int>[];
              for (int i = startNum; i <= endNum; i++) {
                if (existingNumbers.contains(i)) {
                  duplicateNumbers.add(i);
                }
              }

              if (duplicateNumbers.isNotEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('序号 ${duplicateNumbers.join(', ')} 已存在')),
                );
                return;
              }

              // 关闭对话框并返回数据
              Navigator.of(dialogContext).pop({
                'startNumber': startNum,
                'endNumber': endNum,
              });
            }

            return AlertDialog(
              title: const Text('批量添加考试实例'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 说明文本
                    const Text(
                      '批量添加仅支持序号模式，将创建指定范围内的所有序号实例。',
                      style: TextStyle(fontSize: 14, color: AppColors.textSecondary),
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 起始序号
                    const Text('起始序号', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    TextField(
                      controller: startNumberController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        hintText: '输入起始序号，如“2020”',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                      ),
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 结束序号
                    const Text('结束序号', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    TextField(
                      controller: endNumberController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        hintText: '输入结束序号，如“2023”',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                // 取消按钮
                TextButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                  child: const Text('取消'),
                ),
                // 确定按钮
                ElevatedButton(
                  onPressed: validateAndBatchAdd,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('批量添加'),
                ),
              ],
            );
          },
        );
      },
    ).then((result) {
      // 如果用户点击了添加按钮并返回了数据
      if (result != null) {
        final startNum = result['startNumber'] as int;
        final endNum = result['endNumber'] as int;

        // 创建新的考试实例
        final newInstances = <ExamInstance>[];

        for (int i = startNum; i <= endNum; i++) {
          final newInstance = ExamInstance(
            id: '${DateTime.now().millisecondsSinceEpoch}_$i',
            templateId: 'temp_id', // 暂时使用临时ID，创建模板时会更新
            number: i.toString(),
            numberingMode: ExamNumberingMode.year,
            customSettings: {
              'difficulty': 0,
              'notes': '',
            },
          );

          newInstances.add(newInstance);
        }

        // 添加到列表
        setState(() {
          _instances.addAll(newInstances);
        });

        // 显示成功提示
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('成功批量添加 ${newInstances.length} 个考试实例')),
          );
        }
      }
    });
  }

  // 显示添加考试实例对话框
  void _showAddInstanceDialog() {
    // 使用一个安全的方式来处理对话框
    // 初始化数据
    String number = '';
    ExamNumberingMode numberingMode = ExamNumberingMode.year;
    int difficulty = 0;
    String notes = '';

    // 显示对话框
    showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false, // 防止点击外部关闭对话框
      builder: (dialogContext) {
        // 在对话框内部创建控制器
        final numberController = TextEditingController();
        final notesController = TextEditingController();

        return StatefulBuilder(
          builder: (context, setDialogState) {
            // 验证并添加实例
            void validateAndAdd() {
              // 获取当前值
              number = numberController.text.trim();
              notes = notesController.text.trim();

              // 验证编号
              if (number.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入编号')),
                );
                return;
              }

              // 如果是年份模式，验证是否为数字
              if (numberingMode == ExamNumberingMode.year) {
                if (int.tryParse(number) == null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('年份模式下编号必须为数字')),
                  );
                  return;
                }
              }

              // 检查编号是否重复
              final isNumberExists = _instances.any((instance) =>
                  instance.number == number && instance.numberingMode == numberingMode);

              if (isNumberExists) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('该编号已存在')),
                );
                return;
              }

              // 关闭对话框并返回数据
              Navigator.of(dialogContext).pop({
                'number': number,
                'numberingMode': numberingMode,
                'difficulty': difficulty,
                'notes': notes,
              });
            }

            // 切换编号模式
            void toggleNumberingMode() {
              setDialogState(() {
                numberingMode = numberingMode == ExamNumberingMode.year
                    ? ExamNumberingMode.custom
                    : ExamNumberingMode.year;

                // 如果切换到年份模式，清除非数字内容
                if (numberingMode == ExamNumberingMode.year && numberController.text.isNotEmpty) {
                  final numValue = int.tryParse(numberController.text);
                  if (numValue == null) {
                    numberController.clear();
                  }
                }
              });
            }

            // 设置难度级别
            void setDifficulty(int value) {
              setDialogState(() {
                difficulty = value;
              });
            }

            return AlertDialog(
              title: const Text('添加考试实例'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 编号模式选择
                    const Text('编号模式', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    // 自定义切换按钮
                    Container(
                      height: 36, // 固定高度
                      decoration: BoxDecoration(
                        color: AppColors.background,
                        borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                        border: Border.all(color: AppColors.border),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // 序号按钮
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                if (numberingMode != ExamNumberingMode.year) {
                                  setDialogState(() {
                                    toggleNumberingMode();
                                  });
                                }
                              },
                              borderRadius: const BorderRadius.horizontal(
                                left: Radius.circular(AppSizes.radiusSmall - 1),
                              ),
                              child: Container(
                                height: 36,
                                decoration: BoxDecoration(
                                  color: numberingMode == ExamNumberingMode.year
                                      ? AppColors.primary
                                      : Colors.transparent,
                                  borderRadius: const BorderRadius.horizontal(
                                    left: Radius.circular(AppSizes.radiusSmall - 1),
                                  ),
                                ),
                                child: Center(
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.format_list_numbered,
                                        size: 14,
                                        color: numberingMode == ExamNumberingMode.year
                                            ? Colors.white
                                            : AppColors.textSecondary,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '序号',
                                        style: TextStyle(
                                          fontSize: 13,
                                          fontWeight: FontWeight.w500,
                                          color: numberingMode == ExamNumberingMode.year
                                              ? Colors.white
                                              : AppColors.textSecondary,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                          // 分隔线
                          Container(
                            width: 1,
                            height: 36,
                            color: AppColors.border,
                          ),
                          // 自定义按钮
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                if (numberingMode != ExamNumberingMode.custom) {
                                  setDialogState(() {
                                    toggleNumberingMode();
                                  });
                                }
                              },
                              borderRadius: const BorderRadius.horizontal(
                                right: Radius.circular(AppSizes.radiusSmall - 1),
                              ),
                              child: Container(
                                height: 36,
                                decoration: BoxDecoration(
                                  color: numberingMode == ExamNumberingMode.custom
                                      ? AppColors.primary
                                      : Colors.transparent,
                                  borderRadius: const BorderRadius.horizontal(
                                    right: Radius.circular(AppSizes.radiusSmall - 1),
                                  ),
                                ),
                                child: Center(
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.edit,
                                        size: 14,
                                        color: numberingMode == ExamNumberingMode.custom
                                            ? Colors.white
                                            : AppColors.textSecondary,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '自定义',
                                        style: TextStyle(
                                          fontSize: 13,
                                          fontWeight: FontWeight.w500,
                                          color: numberingMode == ExamNumberingMode.custom
                                              ? Colors.white
                                              : AppColors.textSecondary,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 编号输入
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                numberingMode == ExamNumberingMode.year ? '序号' : '自定义',
                                style: AppTextStyles.labelMedium,
                              ),
                              const SizedBox(height: AppSizes.paddingSmall),
                              TextField(
                                controller: numberController,
                                keyboardType: numberingMode == ExamNumberingMode.year
                                    ? TextInputType.number
                                    : TextInputType.text,
                                decoration: InputDecoration(
                                  hintText: numberingMode == ExamNumberingMode.year
                                      ? '输入数字序号，如年份“2023”'
                                      : '输入自定义编号，如“A01”',
                                  border: const OutlineInputBorder(),
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: AppSizes.paddingMedium,
                                    vertical: AppSizes.paddingSmall,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 难度评级
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                const Text('难度评级', style: AppTextStyles.labelMedium),
                                const SizedBox(width: 8),
                                const Text('（可选）', style: TextStyle(fontSize: 12, color: AppColors.textTertiary)),
                              ],
                            ),
                            // 取消评级按钮
                            if (difficulty > 0)
                              IconButton(
                                onPressed: () => setDialogState(() {
                                  difficulty = 0;
                                }),
                                icon: const Icon(Icons.close, size: 16),
                                tooltip: '取消评级',
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
                                iconSize: 16,
                                color: AppColors.textTertiary,
                                splashRadius: 16,
                              ),
                          ],
                        ),
                        const SizedBox(height: AppSizes.paddingSmall),
                        Row(
                          children: List.generate(
                            5,
                            (index) => IconButton(
                              icon: Icon(
                                index < difficulty ? Icons.star : Icons.star_border,
                                color: index < difficulty ? Colors.amber : AppColors.textTertiary,
                              ),
                              onPressed: () => setDifficulty(index + 1),
                              tooltip: '评级 ${index + 1}',
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              constraints: const BoxConstraints(),
                              iconSize: 24,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 备注
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Text('备注', style: AppTextStyles.labelMedium),
                            const SizedBox(width: 8),
                            const Text('（可选）', style: TextStyle(fontSize: 12, color: AppColors.textTertiary)),
                          ],
                        ),
                        const SizedBox(height: AppSizes.paddingSmall),
                        TextField(
                          controller: notesController,
                          maxLines: 3,
                          decoration: const InputDecoration(
                            hintText: '输入备注信息',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: AppSizes.paddingMedium,
                              vertical: AppSizes.paddingSmall,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                // 取消按钮
                TextButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                  child: const Text('取消'),
                ),
                // 确定按钮
                ElevatedButton(
                  onPressed: validateAndAdd,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('添加'),
                ),
              ],
            );
          },
        );
      },
    ).then((result) {
      // 如果用户点击了添加按钮并返回了数据
      if (result != null) {
        // 创建新的考试实例
        final newInstance = ExamInstance(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          templateId: 'temp_id', // 暂时使用临时ID，创建模板时会更新
          number: result['number'],
          numberingMode: result['numberingMode'],
          customSettings: {
            'difficulty': result['difficulty'],
            'notes': result['notes'],
          },
        );

        // 添加到列表
        setState(() {
          _instances.add(newInstance);
        });
      }
    });
  }

  // 显示编辑题型模块对话框
  void _showEditSectionDialog(ExamSection section) {
    // 使用一个安全的方式来处理对话框
    // 先创建临时数据对象，而不直接使用控制器
    String name = section.name;
    String scoreText = section.score.toString();
    String timeText = section.estimatedTime.toString();

    // 显示对话框
    showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false, // 防止点击外部关闭对话框
      builder: (dialogContext) {
        // 在对话框内部创建控制器，这样它们的生命周期与对话框绑定
        final nameController = TextEditingController(text: name);
        final scoreController = TextEditingController(text: scoreText);
        final timeController = TextEditingController(text: timeText);

        return StatefulBuilder(
          builder: (context, setDialogState) {
            // 验证并保存题型
            void validateAndSave() {
              // 获取当前值
              name = nameController.text.trim();
              scoreText = scoreController.text.trim();
              timeText = timeController.text.trim();

              // 验证名称
              if (name.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入题型名称')),
                );
                return;
              }

              // 验证分值
              final score = int.tryParse(scoreText);
              if (score == null || score <= 0) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入有效的分值')),
                );
                return;
              }

              // 验证预计用时
              final estimatedTime = int.tryParse(timeText);
              if (estimatedTime == null || estimatedTime <= 0) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入有效的预计用时')),
                );
                return;
              }

              // 关闭对话框并返回数据
              Navigator.of(dialogContext).pop({
                'name': name,
                'score': score,
                'estimatedTime': estimatedTime,
              });
            }

            return AlertDialog(
              title: const Text('编辑题型模块'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 题型名称
                    const Text('题型名称', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    TextField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        hintText: '输入题型名称，如“阅读理解”',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                      ),
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 题型分值和预计用时
                    Row(
                      children: [
                        // 题型分值
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('题型分值', style: AppTextStyles.labelMedium),
                              const SizedBox(height: AppSizes.paddingSmall),
                              TextField(
                                controller: scoreController,
                                keyboardType: TextInputType.number,
                                decoration: const InputDecoration(
                                  hintText: '输入分值',
                                  suffixText: '分',
                                  border: OutlineInputBorder(),
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: AppSizes.paddingMedium,
                                    vertical: AppSizes.paddingSmall,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: AppSizes.paddingMedium),

                        // 预计用时
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('预计用时', style: AppTextStyles.labelMedium),
                              const SizedBox(height: AppSizes.paddingSmall),
                              TextField(
                                controller: timeController,
                                keyboardType: TextInputType.number,
                                decoration: const InputDecoration(
                                  hintText: '输入用时',
                                  suffixText: '分钟',
                                  border: OutlineInputBorder(),
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: AppSizes.paddingMedium,
                                    vertical: AppSizes.paddingSmall,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                // 取消按钮
                TextButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                  child: const Text('取消'),
                ),
                // 确定按钮
                ElevatedButton(
                  onPressed: validateAndSave,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('保存'),
                ),
              ],
            );
          },
        );
      },
    ).then((result) {
      // 如果用户点击了保存按钮并返回了数据
      if (result != null) {
        // 更新题型模块
        final updatedSection = section.copyWith(
          name: result['name'],
          score: result['score'],
          estimatedTime: result['estimatedTime'],
        );

        // 更新列表
        setState(() {
          final index = _sections.indexWhere((s) => s.id == section.id);
          if (index != -1) {
            _sections[index] = updatedSection;
          }
        });
      }
    });
  }

  // 构建考试实例项
  Widget _buildInstanceItem(ExamInstance instance) {
    // 获取自定义设置
    final difficulty = instance.customSettings?['difficulty'] as int? ?? 0;
    final notes = instance.customSettings?['notes'] as String? ?? '';

    // 根据编号模式设置标识颜色和形状
    final bool isYearMode = instance.numberingMode == ExamNumberingMode.year;

    // 使用固定颜色而非随机颜色，确保可读性
    final Color modeColor = isYearMode ? AppColors.info : AppColors.primary;
    final Color borderColor = isYearMode ? AppColors.infoLight300 : AppColors.primaryLight300;

    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingSmall),
      padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium, vertical: AppSizes.paddingSmall),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        border: Border.all(color: borderColor, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 第一排：实例编号和操作按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 编号信息
              Expanded(
                child: Row(
                  children: [
                    // 模式标识
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: modeColor.withAlpha(25), // 约等10%的透明度
                        shape: isYearMode ? BoxShape.circle : BoxShape.rectangle,
                        borderRadius: isYearMode ? null : BorderRadius.circular(4),
                        border: Border.all(
                          color: modeColor.withAlpha(128), // 约等50%的透明度
                          width: 1,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          isYearMode ? '#' : 'A',
                          style: TextStyle(
                            color: modeColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // 编号文本
                    Expanded(
                      child: Text(
                        instance.number,
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: AppColors.text,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              // 操作按钮
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 编辑按钮
                  IconButton(
                    icon: const Icon(Icons.edit_outlined, size: 18, color: AppColors.primary),
                    onPressed: () {
                      _showEditInstanceDialog(instance);
                    },
                    tooltip: '编辑实例',
                    constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                    padding: EdgeInsets.zero,
                    visualDensity: VisualDensity.compact,
                    splashRadius: 20,
                  ),
                  // 删除按钮
                  IconButton(
                    icon: const Icon(Icons.delete_outline, size: 18, color: AppColors.error),
                    onPressed: () {
                      setState(() {
                        _instances.remove(instance);
                      });
                    },
                    tooltip: '删除实例',
                    constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                    padding: EdgeInsets.zero,
                    visualDensity: VisualDensity.compact,
                    splashRadius: 20,
                  ),
                ],
              ),
            ],
          ),

          // 第二排：评级和备注
          if (difficulty > 0 || notes.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // 评级星星
                  if (difficulty > 0) ...[
                    Row(
                      children: List.generate(
                        5,
                        (index) => Icon(
                          index < difficulty ? Icons.star : Icons.star_border,
                          size: 14,
                          color: index < difficulty ? Colors.amber : AppColors.textTertiary,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                  ],

                  // 备注信息
                  if (notes.isNotEmpty)
                    Expanded(
                      child: Row(
                        children: [
                          const Icon(Icons.notes, size: 14, color: AppColors.textSecondary),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              notes,
                              style: const TextStyle(fontSize: 13, color: AppColors.textSecondary),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // 显示编辑考试实例对话框
  void _showEditInstanceDialog(ExamInstance instance) {
    // 使用一个安全的方式来处理对话框
    // 初始化数据
    String number = instance.number;
    ExamNumberingMode numberingMode = instance.numberingMode;
    int difficulty = instance.customSettings?['difficulty'] as int? ?? 0;
    String notes = instance.customSettings?['notes'] as String? ?? '';

    // 显示对话框
    showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false, // 防止点击外部关闭对话框
      builder: (dialogContext) {
        // 在对话框内部创建控制器
        final numberController = TextEditingController(text: number);
        final notesController = TextEditingController(text: notes);

        return StatefulBuilder(
          builder: (context, setDialogState) {
            // 验证并保存实例
            void validateAndSave() {
              // 获取当前值
              number = numberController.text.trim();
              notes = notesController.text.trim();

              // 验证编号
              if (number.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入编号')),
                );
                return;
              }

              // 如果是年份模式，验证是否为数字
              if (numberingMode == ExamNumberingMode.year) {
                if (int.tryParse(number) == null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('年份模式下编号必须为数字')),
                  );
                  return;
                }
              }

              // 检查编号是否与其他实例重复
              final isNumberConflict = _instances.any((i) =>
                  i.id != instance.id &&
                  i.number == number &&
                  i.numberingMode == numberingMode);

              if (isNumberConflict) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('该编号已被其他实例使用')),
                );
                return;
              }

              // 关闭对话框并返回数据
              Navigator.of(dialogContext).pop({
                'number': number,
                'numberingMode': numberingMode,
                'difficulty': difficulty,
                'notes': notes,
              });
            }



            // 设置难度级别
            void setDifficulty(int value) {
              setDialogState(() {
                difficulty = value;
              });
            }

            return AlertDialog(
              title: const Text('编辑考试实例'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 编号模式显示
                    const Text('编号模式', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    // 编号模式信息卡片（不可编辑）
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: AppColors.background,
                        borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                        border: Border.all(color: AppColors.border),
                      ),
                      child: Row(
                        children: [
                          // 模式图标
                          Icon(
                            numberingMode == ExamNumberingMode.year
                                ? Icons.format_list_numbered
                                : Icons.edit,
                            size: 16,
                            color: numberingMode == ExamNumberingMode.year
                                ? AppColors.info
                                : AppColors.primary,
                          ),
                          const SizedBox(width: 8),
                          // 模式文本
                          Text(
                            numberingMode == ExamNumberingMode.year ? '序号模式' : '自定义模式',
                            style: TextStyle(
                              fontSize: 14,
                              color: numberingMode == ExamNumberingMode.year
                                  ? AppColors.info
                                  : AppColors.primary,
                            ),
                          ),
                          const SizedBox(width: 8),
                          // 不可编辑提示
                          const Text(
                            '（创建后不可更改）',
                            style: TextStyle(fontSize: 12, color: AppColors.textTertiary),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 编号输入
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                numberingMode == ExamNumberingMode.year ? '年份编号' : '自定义编号',
                                style: AppTextStyles.labelMedium,
                              ),
                              const SizedBox(height: AppSizes.paddingSmall),
                              TextField(
                                controller: numberController,
                                keyboardType: numberingMode == ExamNumberingMode.year
                                    ? TextInputType.number
                                    : TextInputType.text,
                                decoration: InputDecoration(
                                  hintText: numberingMode == ExamNumberingMode.year
                                      ? '输入年份，如“2023”'
                                      : '输入编号，如“A01”',
                                  border: const OutlineInputBorder(),
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: AppSizes.paddingMedium,
                                    vertical: AppSizes.paddingSmall,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 难度评级
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                const Text('难度评级', style: AppTextStyles.labelMedium),
                                const SizedBox(width: 8),
                                const Text('（可选）', style: TextStyle(fontSize: 12, color: AppColors.textTertiary)),
                              ],
                            ),
                            // 取消评级按钮
                            if (difficulty > 0)
                              IconButton(
                                onPressed: () => setDialogState(() {
                                  difficulty = 0;
                                }),
                                icon: const Icon(Icons.close, size: 16),
                                tooltip: '取消评级',
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
                                iconSize: 16,
                                color: AppColors.textTertiary,
                                splashRadius: 16,
                              ),
                          ],
                        ),
                        const SizedBox(height: AppSizes.paddingSmall),
                        Row(
                          children: List.generate(
                            5,
                            (index) => IconButton(
                              icon: Icon(
                                index < difficulty ? Icons.star : Icons.star_border,
                                color: index < difficulty ? Colors.amber : AppColors.textTertiary,
                              ),
                              onPressed: () => setDifficulty(index + 1),
                              tooltip: '评级 ${index + 1}',
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              constraints: const BoxConstraints(),
                              iconSize: 24,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 备注
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Text('备注', style: AppTextStyles.labelMedium),
                            const SizedBox(width: 8),
                            const Text('（可选）', style: TextStyle(fontSize: 12, color: AppColors.textTertiary)),
                          ],
                        ),
                        const SizedBox(height: AppSizes.paddingSmall),
                        TextField(
                          controller: notesController,
                          maxLines: 3,
                          decoration: const InputDecoration(
                            hintText: '输入备注信息',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: AppSizes.paddingMedium,
                              vertical: AppSizes.paddingSmall,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                // 取消按钮
                TextButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                  child: const Text('取消'),
                ),
                // 确定按钮
                ElevatedButton(
                  onPressed: validateAndSave,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('保存'),
                ),
              ],
            );
          },
        );
      },
    ).then((result) {
      // 如果用户点击了保存按钮并返回了数据
      if (result != null) {
        // 更新考试实例
        final updatedInstance = instance.copyWith(
          number: result['number'],
          numberingMode: result['numberingMode'],
          customSettings: {
            'difficulty': result['difficulty'],
            'notes': result['notes'],
          },
        );

        // 更新列表
        setState(() {
          final index = _instances.indexWhere((i) => i.id == instance.id);
          if (index != -1) {
            _instances[index] = updatedInstance;
          }
        });
      }
    });
  }

  // 创建模板
  Future<void> _createTemplate() async {
    // 验证输入
    final name = _nameController.text.trim();
    if (name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入考试名称')),
      );
      return;
    }

    // 验证总分值
    final totalScoreText = _totalScoreController.text.trim();
    final totalScore = int.tryParse(totalScoreText);
    if (totalScore == null || totalScore <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入有效的总分值')),
      );
      return;
    }

    // 验证考试时长
    final durationText = _durationController.text.trim();
    final duration = int.tryParse(durationText);
    if (duration == null || duration <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入有效的考试时长')),
      );
      return;
    }

    // 如果有题型模块，验证总分值是否高于题型分值之和
    if (_sections.isNotEmpty) {
      final totalSectionScore = _sections.fold(0, (sum, section) => sum + section.score);
      if (totalSectionScore > totalScore) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('题型分值之和($totalSectionScore)超过了总分值($totalScore)')),
        );
        return;
      }
    }

    // 注意：这里我们直接使用原始参数，而不是通过 TemplateCreateParams 对象

    // 显示加载对话框，防止用户与界面交互
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在创建模拟考试...'),
          ],
        ),
      ),
    );

    try {
      // 使用单独的异步操作创建模板
      final newTemplate = await _templateService.createTemplate(
        name: name,
        subjectId: _selectedSubjectId,
        totalScore: totalScore,
        duration: duration,
      );

      // 添加题型模块
      for (final section in _sections) {
        final newSection = section.copyWith(
          templateId: newTemplate.id,
        );
        await _templateService.updateSection(newSection);
      }

      // 添加考试实例
      for (final instance in _instances) {
        final newInstance = instance.copyWith(
          templateId: newTemplate.id,
        );
        await _templateService.updateInstance(newInstance);
      }

      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 更新状态
      if (mounted) {
        // 更新状态
        ref.read(examStateProvider.notifier).addTemplate(newTemplate);

        // 模拟考试创建成功，界面会显示新创建的考试，无需额外提示

        // 返回上一页
        Navigator.pop(context);
      }
    } catch (e) {
      debugPrint('创建模板时发生错误: $e');

      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 显示错误提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('创建失败: $e')),
        );
      }
    }
  }
}

/// 模板创建视图
class _TemplateCreateView extends ConsumerStatefulWidget {
  final CreateMode currentMode;
  final Function(CreateMode) onModeChanged;

  const _TemplateCreateView({
    required this.currentMode,
    required this.onModeChanged,
  });

  @override
  ConsumerState<_TemplateCreateView> createState() => _TemplateCreateViewState();
}

class _TemplateCreateViewState extends ConsumerState<_TemplateCreateView> {
  // 考试模板服务
  // 注意：这里暂时保留服务实例，后续会使用
  // ignore: unused_field
  final ExamTemplateService _templateService = ExamTemplateService();

  @override
  Widget build(BuildContext context) {
    // 监听状态
    final examState = ref.watch(examStateProvider);
    final templates = examState.templates;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部分段控制器
          Center(
            child: Container(
              margin: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
              child: SegmentedButton<CreateMode>(
                segments: const [
                  ButtonSegment<CreateMode>(
                    value: CreateMode.custom,
                    label: Text('自定义'),
                    icon: Icon(Icons.create),
                  ),
                  ButtonSegment<CreateMode>(
                    value: CreateMode.template,
                    label: Text('模板'),
                    icon: Icon(Icons.content_copy),
                  ),
                ],
                selected: {widget.currentMode},
                onSelectionChanged: (Set<CreateMode> selection) {
                  widget.onModeChanged(selection.first);
                },
                showSelectedIcon: false,
                emptySelectionAllowed: false,
              ),
            ),
          ),

          // 标题
          const Text(
            '从模板创建模拟考试',
            style: AppTextStyles.headline2,
          ),
          const SizedBox(height: AppSizes.paddingSmall),
          const Text(
            '选择一个现有模板作为基础创建新的模拟考试',
            style: AppTextStyles.bodyMedium,
          ),
          const SizedBox(height: AppSizes.paddingLarge),

          // 模板列表
          if (templates.isEmpty)
            const Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: AppSizes.paddingLarge),
                child: Text(
                  '暂无模板，请先创建模板',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppColors.textTertiary,
                  ),
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: templates.length,
              itemBuilder: (context, index) {
                final template = templates[index];
                return Card(
                  elevation: 0,
                  margin: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                    side: const BorderSide(color: AppColors.border),
                  ),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                    onTap: () {
                      // 选择模板
                      ref.read(examStateProvider.notifier).setCurrentTemplate(template);

                      // 返回上一页
                      Navigator.pop(context);
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(AppSizes.paddingMedium),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 模板名称
                          Text(
                            template.name,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: AppSizes.paddingSmall),

                          // 模板信息
                          Row(
                            children: [
                              const Icon(
                                Icons.assignment,
                                size: 16,
                                color: AppColors.textSecondary,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '总分: ${template.totalScore}分',
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                              const SizedBox(width: 16),
                              const Icon(
                                Icons.timer,
                                size: 16,
                                color: AppColors.textSecondary,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '时长: ${template.duration}分钟',
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppSizes.paddingSmall),

                          // 题型模块信息
                          Text(
                            '题型: ${template.sections.map((s) => s.name).join(', ')}',
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppColors.textSecondary,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),

          // 刷新按钮
          const SizedBox(height: AppSizes.paddingMedium),
          Center(
            child: ElevatedButton.icon(
              onPressed: () {
                // 加载模板列表
                _loadTemplates();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('刷新模板列表'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 加载模板列表
  Future<void> _loadTemplates() async {
    // 显示加载对话框，防止用户与界面交互
    if (!mounted) return;

    // 清除错误状态
    ref.read(examStateProvider.notifier).setError(null);

    // 设置加载状态
    ref.read(examStateProvider.notifier).setLoading(true);

    try {
      // 获取模板列表
      List<ExamTemplate> templates = [];
      try {
        templates = await _templateService.getAllTemplates();
      } catch (loadError) {
        debugPrint('加载模板列表时发生错误: $loadError');
        // 如果加载失败，使用空列表
      }

      // 更新状态
      if (mounted) {
        ref.read(examStateProvider.notifier).setTemplates(templates);
        ref.read(examStateProvider.notifier).setLoading(false);
      }
    } catch (e) {
      debugPrint('在_loadTemplates中发生错误: $e');

      // 设置错误状态
      if (mounted) {
        ref.read(examStateProvider.notifier).setError('加载模板列表失败');
        ref.read(examStateProvider.notifier).setLoading(false);
      }
    }
  }

  bool _isInitialized = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isInitialized) {
      // 初始化时加载模板列表
      _loadTemplates();
      _isInitialized = true;
    }
  }
}
