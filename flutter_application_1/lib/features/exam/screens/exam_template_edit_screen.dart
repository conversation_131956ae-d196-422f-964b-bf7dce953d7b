import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import '../models/exam_template_models.dart';
import '../services/exam_template_service.dart';

/// 考试模板编辑页面
/// 用于编辑现有的考试模板
class ExamTemplateEditScreen extends StatefulWidget {
  /// 要编辑的考试模板
  final ExamTemplate template;

  const ExamTemplateEditScreen({
    super.key,
    required this.template,
  });

  @override
  State<ExamTemplateEditScreen> createState() => _ExamTemplateEditScreenState();
}

class _ExamTemplateEditScreenState extends State<ExamTemplateEditScreen> {
  // 模板服务
  final _templateService = ExamTemplateService();

  // 控制器
  late TextEditingController _nameController;
  late TextEditingController _totalScoreController;
  late TextEditingController _durationController;

  // 当前模板
  late ExamTemplate _currentTemplate;

  // 科目选择
  String? _selectedSubjectId;

  // 是否正在保存
  bool _isSaving = false;

  // 是否有未保存的更改
  bool _hasUnsavedChanges = false;

  @override
  void initState() {
    super.initState();

    // 初始化当前模板
    _currentTemplate = widget.template;

    // 初始化控制器
    _nameController = TextEditingController(text: _currentTemplate.name);
    _totalScoreController = TextEditingController(text: _currentTemplate.totalScore.toString());
    _durationController = TextEditingController(text: _currentTemplate.duration.toString());

    // 初始化科目选择
    _selectedSubjectId = _currentTemplate.subjectId;

    // 添加控制器监听器
    _nameController.addListener(_onFieldChanged);
    _totalScoreController.addListener(_onFieldChanged);
    _durationController.addListener(_onFieldChanged);
  }

  @override
  void dispose() {
    // 移除控制器监听器
    _nameController.removeListener(_onFieldChanged);
    _totalScoreController.removeListener(_onFieldChanged);
    _durationController.removeListener(_onFieldChanged);

    // 释放控制器
    _nameController.dispose();
    _totalScoreController.dispose();
    _durationController.dispose();

    super.dispose();
  }

  // 字段变更回调
  void _onFieldChanged() {
    if (!_hasUnsavedChanges) {
      setState(() {
        _hasUnsavedChanges = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (!_hasUnsavedChanges) return true;

        final result = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('确认放弃更改'),
            content: const Text('您有未保存的更改，确定要放弃吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.error,
                ),
                child: const Text('放弃'),
              ),
            ],
          ),
        );

        return result ?? false;
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('编辑模拟考试'),
          backgroundColor: Colors.transparent,
          elevation: 0,
          foregroundColor: AppColors.text,
          actions: [
            // 保存按钮
            TextButton.icon(
              onPressed: _isSaving ? null : _saveTemplate,
              icon: _isSaving
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                      ),
                    )
                  : const Icon(Icons.save),
              label: const Text('保存'),
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primary,
              ),
            ),
          ],
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppSizes.paddingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 基本信息卡片
                Card(
                  margin: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(AppSizes.paddingMedium),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('基本信息', style: AppTextStyles.headline3),
                        const SizedBox(height: AppSizes.paddingMedium),

                        // 名称输入框
                        TextField(
                          controller: _nameController,
                          decoration: const InputDecoration(
                            labelText: '考试名称',
                            hintText: '例如：考研英语一',
                            border: OutlineInputBorder(),
                          ),
                        ),
                        const SizedBox(height: AppSizes.paddingMedium),

                        // 总分输入框
                        TextField(
                          controller: _totalScoreController,
                          decoration: const InputDecoration(
                            labelText: '总分值',
                            hintText: '例如：100',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                        const SizedBox(height: AppSizes.paddingMedium),

                        // 时长输入框
                        TextField(
                          controller: _durationController,
                          decoration: const InputDecoration(
                            labelText: '考试时长（分钟）',
                            hintText: '例如：120',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                      ],
                    ),
                  ),
                ),

                // 题型模块卡片
                Card(
                  margin: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(AppSizes.paddingMedium),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 标题行
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('题型模块', style: AppTextStyles.headline3),
                            TextButton.icon(
                              onPressed: _showAddSectionDialog,
                              icon: const Icon(Icons.add),
                              label: const Text('添加'),
                              style: TextButton.styleFrom(
                                foregroundColor: AppColors.primary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: AppSizes.paddingMedium),

                        // 题型模块列表
                        if (_currentTemplate.sections.isEmpty)
                          const Center(
                            child: Padding(
                              padding: EdgeInsets.symmetric(vertical: AppSizes.paddingLarge),
                              child: Text(
                                '暂无题型模块，点击"添加"按钮创建',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: AppColors.textTertiary,
                                ),
                              ),
                            ),
                          )
                        else
                          ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: _currentTemplate.sections.length,
                            itemBuilder: (context, index) {
                              final section = _currentTemplate.sections[index];
                              return Card(
                                margin: const EdgeInsets.only(bottom: AppSizes.paddingSmall),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                                ),
                                child: ListTile(
                                  title: Text(section.name),
                                  subtitle: Text('分值: ${section.score} | 预计用时: ${section.estimatedTime}分钟'),
                                  trailing: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: const Icon(Icons.edit_outlined, size: 20),
                                        onPressed: () {
                                          _showEditSectionDialog(section);
                                        },
                                      ),
                                      IconButton(
                                        icon: const Icon(Icons.delete_outline, size: 20, color: AppColors.error),
                                        onPressed: () {
                                          _showDeleteSectionConfirmDialog(section);
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                      ],
                    ),
                  ),
                ),

                // 考试实例卡片
                Card(
                  margin: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(AppSizes.paddingMedium),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 标题行
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('考试实例', style: AppTextStyles.headline3),
                            TextButton.icon(
                              onPressed: () {
                                _showAddInstanceDialog();
                              },
                              icon: const Icon(Icons.add),
                              label: const Text('添加'),
                              style: TextButton.styleFrom(
                                foregroundColor: AppColors.primary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: AppSizes.paddingMedium),

                        // 考试实例列表
                        if (_currentTemplate.instances.isEmpty)
                          const Center(
                            child: Padding(
                              padding: EdgeInsets.symmetric(vertical: AppSizes.paddingLarge),
                              child: Text(
                                '暂无考试实例，点击"添加"按钮创建',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: AppColors.textTertiary,
                                ),
                              ),
                            ),
                          )
                        else
                          ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: _currentTemplate.instances.length,
                            itemBuilder: (context, index) {
                              final instance = _currentTemplate.instances[index];
                              return Card(
                                margin: const EdgeInsets.only(bottom: AppSizes.paddingSmall),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                                ),
                                child: ListTile(
                                  title: Text('编号: ${instance.number}'),
                                  subtitle: Text(
                                    '日期: ${instance.formattedDate} | 时间: ${instance.formattedTimeRange}',
                                  ),
                                  trailing: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: const Icon(Icons.edit_outlined, size: 20),
                                        onPressed: () {
                                          _showEditInstanceDialog(instance);
                                        },
                                      ),
                                      IconButton(
                                        icon: const Icon(Icons.delete_outline, size: 20, color: AppColors.error),
                                        onPressed: () {
                                          _showDeleteInstanceConfirmDialog(instance);
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }



  // 显示删除题型模块确认对话框
  void _showDeleteSectionConfirmDialog(ExamSection section) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除题型模块'),
        content: Text('确定要删除"${section.name}"吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteSection(section);
            },
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  // 删除题型模块
  Future<void> _deleteSection(ExamSection section) async {
    setState(() {
      _hasUnsavedChanges = true;
      _isSaving = true; // 显示保存中状态
    });

    try {
      // 从模板中移除题型模块
      final updatedSections = _currentTemplate.sections
          .where((s) => s.id != section.id)
          .toList();

      final updatedTemplate = _currentTemplate.copyWith(
        sections: updatedSections,
        updatedAt: DateTime.now(),
      );

      // 保存到数据库
      await _templateService.deleteSection(section.id);
      await _templateService.updateTemplate(updatedTemplate);

      // 更新状态
      if (mounted) {
        setState(() {
          _currentTemplate = updatedTemplate;
          _isSaving = false;
        });

        // 显示成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('题型模块已删除')),
        );
      }
    } catch (e) {
      // 显示错误提示
      if (mounted) {
        setState(() {
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('删除失败: $e')),
        );
      }
    }
  }

  // 显示编辑题型模块对话框
  void _showEditSectionDialog(ExamSection section) {
    // 使用一个安全的方式来处理对话框
    // 先创建临时数据对象，而不直接使用控制器
    String name = section.name;
    String scoreText = section.score.toString();
    String timeText = section.estimatedTime.toString();

    // 显示对话框
    showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false, // 防止点击外部关闭对话框
      builder: (dialogContext) {
        // 在对话框内部创建控制器，这样它们的生命周期与对话框绑定
        final nameController = TextEditingController(text: name);
        final scoreController = TextEditingController(text: scoreText);
        final timeController = TextEditingController(text: timeText);

        return StatefulBuilder(
          builder: (context, setDialogState) {
            // 验证并保存题型
            void validateAndSave() {
              // 获取当前值
              name = nameController.text.trim();
              scoreText = scoreController.text.trim();
              timeText = timeController.text.trim();

              // 验证名称
              if (name.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入题型名称')),
                );
                return;
              }

              // 验证分值
              final score = int.tryParse(scoreText);
              if (score == null || score <= 0) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入有效的分值')),
                );
                return;
              }

              // 验证预计用时
              final estimatedTime = int.tryParse(timeText);
              if (estimatedTime == null || estimatedTime <= 0) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入有效的预计用时')),
                );
                return;
              }

              // 关闭对话框并返回数据
              Navigator.of(dialogContext).pop({
                'name': name,
                'score': score,
                'estimatedTime': estimatedTime,
              });
            }

            return AlertDialog(
              title: const Text('编辑题型模块'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 题型名称
                    const Text('题型名称', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    TextField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        hintText: '输入题型名称，如"阅读理解"',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                      ),
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 分值
                    const Text('分值', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    TextField(
                      controller: scoreController,
                      decoration: const InputDecoration(
                        hintText: '输入分值，如"20"',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 预计用时
                    const Text('预计用时（分钟）', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    TextField(
                      controller: timeController,
                      decoration: const InputDecoration(
                        hintText: '输入预计用时，如"30"',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ],
                ),
              ),
              actions: [
                // 取消按钮
                TextButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                  child: const Text('取消'),
                ),
                // 确定按钮
                ElevatedButton(
                  onPressed: validateAndSave,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('保存'),
                ),
              ],
            );
          },
        );
      },
    ).then((result) async {
      // 如果用户点击了保存按钮并返回了数据
      if (result != null) {
        setState(() {
          _hasUnsavedChanges = true;
          _isSaving = true; // 显示保存中状态
        });

        try {
          // 更新题型模块
          final updatedSection = section.copyWith(
            name: result['name'],
            score: result['score'],
            estimatedTime: result['estimatedTime'],
          );

          // 更新模板中的题型模块
          final updatedSections = _currentTemplate.sections.map((s) {
            if (s.id == section.id) {
              return updatedSection;
            }
            return s;
          }).toList();

          final updatedTemplate = _currentTemplate.copyWith(
            sections: updatedSections,
            updatedAt: DateTime.now(),
          );

          // 保存到数据库
          await _templateService.updateSection(updatedSection);
          await _templateService.updateTemplate(updatedTemplate);

          // 更新状态
          if (mounted) {
            setState(() {
              _currentTemplate = updatedTemplate;
              _isSaving = false;
            });

            // 显示成功提示
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('题型模块更新成功')),
            );
          }
        } catch (e) {
          // 显示错误提示
          if (mounted) {
            setState(() {
              _isSaving = false;
            });

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('更新失败: $e')),
            );
          }
        }
      }
    });
  }

  // 显示删除考试实例确认对话框
  void _showDeleteInstanceConfirmDialog(ExamInstance instance) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除考试实例'),
        content: Text('确定要删除编号为"${instance.number}"的考试实例吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteInstance(instance);
            },
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  // 删除考试实例
  Future<void> _deleteInstance(ExamInstance instance) async {
    setState(() {
      _hasUnsavedChanges = true;
      _isSaving = true; // 显示保存中状态
    });

    try {
      // 从模板中移除考试实例
      final updatedInstances = _currentTemplate.instances
          .where((i) => i.id != instance.id)
          .toList();

      final updatedTemplate = _currentTemplate.copyWith(
        instances: updatedInstances,
        updatedAt: DateTime.now(),
      );

      // 保存到数据库
      await _templateService.deleteInstance(instance.id);
      await _templateService.updateTemplate(updatedTemplate);

      // 更新状态
      if (mounted) {
        setState(() {
          _currentTemplate = updatedTemplate;
          _isSaving = false;
        });

        // 显示成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('考试实例已删除')),
        );
      }
    } catch (e) {
      // 显示错误提示
      if (mounted) {
        setState(() {
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('删除失败: $e')),
        );
      }
    }
  }

  // 显示编辑考试实例对话框
  void _showEditInstanceDialog(ExamInstance instance) {
    // 使用一个安全的方式来处理对话框
    // 初始化数据
    String number = instance.number;
    ExamNumberingMode numberingMode = instance.numberingMode;
    int difficulty = instance.customSettings?['difficulty'] ?? 0;
    String notes = instance.customSettings?['notes'] ?? '';
    DateTime? examDate = instance.examDate;
    TimeOfDay? startTime = instance.startTime;
    TimeOfDay? endTime = instance.endTime;

    // 显示对话框
    showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false, // 防止点击外部关闭对话框
      builder: (dialogContext) {
        // 在对话框内部创建控制器
        final numberController = TextEditingController(text: number);
        final notesController = TextEditingController(text: notes);

        return StatefulBuilder(
          builder: (context, setDialogState) {
            // 选择日期
            Future<void> selectDate() async {
              final DateTime? picked = await showDatePicker(
                context: context,
                initialDate: examDate ?? DateTime.now(),
                firstDate: DateTime(2000),
                lastDate: DateTime(2100),
              );
              if (picked != null && picked != examDate) {
                setDialogState(() {
                  examDate = picked;
                });
              }
            }

            // 选择开始时间
            Future<void> selectStartTime() async {
              final TimeOfDay? picked = await showTimePicker(
                context: context,
                initialTime: startTime ?? const TimeOfDay(hour: 9, minute: 0),
              );
              if (picked != null && picked != startTime) {
                setDialogState(() {
                  startTime = picked;
                });
              }
            }

            // 选择结束时间
            Future<void> selectEndTime() async {
              final TimeOfDay? picked = await showTimePicker(
                context: context,
                initialTime: endTime ?? const TimeOfDay(hour: 12, minute: 0),
              );
              if (picked != null && picked != endTime) {
                setDialogState(() {
                  endTime = picked;
                });
              }
            }

            // 格式化时间
            String formatTime(TimeOfDay? time) {
              if (time == null) return '未设置';
              final hour = time.hour.toString().padLeft(2, '0');
              final minute = time.minute.toString().padLeft(2, '0');
              return '$hour:$minute';
            }

            // 格式化日期
            String formatDate(DateTime? date) {
              if (date == null) return '未设置';
              final year = date.year;
              final month = date.month.toString().padLeft(2, '0');
              final day = date.day.toString().padLeft(2, '0');
              return '$year-$month-$day';
            }

            // 验证并保存实例
            void validateAndSave() {
              // 获取当前值
              number = numberController.text.trim();
              notes = notesController.text.trim();

              // 验证编号
              if (number.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入考试编号')),
                );
                return;
              }

              // 检查编号是否已存在（排除当前实例）
              final isNumberExists = _currentTemplate.instances.any((i) =>
                  i.id != instance.id &&
                  i.number == number &&
                  i.numberingMode == numberingMode);

              if (isNumberExists) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('编号已存在')),
                );
                return;
              }

              // 关闭对话框并返回数据
              Navigator.of(dialogContext).pop({
                'number': number,
                'numberingMode': numberingMode,
                'difficulty': difficulty,
                'notes': notes,
                'examDate': examDate,
                'startTime': startTime,
                'endTime': endTime,
              });
            }

            return AlertDialog(
              title: const Text('编辑考试实例'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 编号模式选择（禁用，不允许更改）
                    const Text('编号模式', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    SegmentedButton<ExamNumberingMode>(
                      segments: const [
                        ButtonSegment<ExamNumberingMode>(
                          value: ExamNumberingMode.year,
                          label: Text('年份'),
                          icon: Icon(Icons.calendar_today),
                        ),
                        ButtonSegment<ExamNumberingMode>(
                          value: ExamNumberingMode.custom,
                          label: Text('自定义'),
                          icon: Icon(Icons.edit),
                        ),
                      ],
                      selected: {numberingMode},
                      onSelectionChanged: (Set<ExamNumberingMode> selection) {
                        // 不允许更改编号模式
                      },
                      showSelectedIcon: false,
                      emptySelectionAllowed: false,
                      // 禁用编号模式选择
                      style: ButtonStyle(
                        backgroundColor: WidgetStateProperty.resolveWith<Color>(
                          (Set<WidgetState> states) {
                            if (states.contains(WidgetState.disabled)) {
                              return AppColors.background;
                            }
                            return Colors.transparent;
                          },
                        ),
                      ),
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 编号输入框
                    const Text('编号', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    TextField(
                      controller: numberController,
                      decoration: InputDecoration(
                        hintText: numberingMode == ExamNumberingMode.year
                            ? '输入年份，如"2023"'
                            : '输入自定义编号，如"A01"',
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                      ),
                      keyboardType: numberingMode == ExamNumberingMode.year
                          ? TextInputType.number
                          : TextInputType.text,
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 考试日期
                    const Text('考试日期（可选）', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    InkWell(
                      onTap: selectDate,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.border),
                          borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              formatDate(examDate),
                              style: examDate == null
                                  ? AppTextStyles.hint
                                  : AppTextStyles.bodyMedium,
                            ),
                            const Icon(Icons.calendar_today, size: 20),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 考试时间
                    Row(
                      children: [
                        // 开始时间
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('开始时间（可选）', style: AppTextStyles.labelMedium),
                              const SizedBox(height: AppSizes.paddingSmall),
                              InkWell(
                                onTap: selectStartTime,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: AppSizes.paddingMedium,
                                    vertical: AppSizes.paddingSmall,
                                  ),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: AppColors.border),
                                    borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        formatTime(startTime),
                                        style: startTime == null
                                            ? AppTextStyles.hint
                                            : AppTextStyles.bodyMedium,
                                      ),
                                      const Icon(Icons.access_time, size: 20),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: AppSizes.paddingMedium),
                        // 结束时间
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('结束时间（可选）', style: AppTextStyles.labelMedium),
                              const SizedBox(height: AppSizes.paddingSmall),
                              InkWell(
                                onTap: selectEndTime,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: AppSizes.paddingMedium,
                                    vertical: AppSizes.paddingSmall,
                                  ),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: AppColors.border),
                                    borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        formatTime(endTime),
                                        style: endTime == null
                                            ? AppTextStyles.hint
                                            : AppTextStyles.bodyMedium,
                                      ),
                                      const Icon(Icons.access_time, size: 20),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 难度评级
                    const Text('难度评级（可选）', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    Row(
                      children: List.generate(5, (index) {
                        return IconButton(
                          icon: Icon(
                            index < difficulty ? Icons.star : Icons.star_border,
                            color: index < difficulty ? AppColors.warning : AppColors.textTertiary,
                          ),
                          onPressed: () {
                            setDialogState(() {
                              difficulty = index + 1;
                            });
                          },
                        );
                      }),
                    ),
                    if (difficulty > 0)
                      TextButton.icon(
                        onPressed: () {
                          setDialogState(() {
                            difficulty = 0;
                          });
                        },
                        icon: const Icon(Icons.close, size: 16),
                        label: const Text('重置'),
                        style: TextButton.styleFrom(
                          foregroundColor: AppColors.textTertiary,
                          padding: EdgeInsets.zero,
                          minimumSize: Size.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                      ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 备注
                    const Text('备注（可选）', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    TextField(
                      controller: notesController,
                      decoration: const InputDecoration(
                        hintText: '输入备注信息',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                      ),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
              actions: [
                // 取消按钮
                TextButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                  child: const Text('取消'),
                ),
                // 确定按钮
                ElevatedButton(
                  onPressed: validateAndSave,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('保存'),
                ),
              ],
            );
          },
        );
      },
    ).then((result) async {
      // 如果用户点击了保存按钮并返回了数据
      if (result != null) {
        setState(() {
          _hasUnsavedChanges = true;
          _isSaving = true; // 显示保存中状态
        });

        try {
          // 更新考试实例
          final updatedInstance = instance.copyWith(
            number: result['number'],
            examDate: result['examDate'],
            startTime: result['startTime'],
            endTime: result['endTime'],
            customSettings: {
              'difficulty': result['difficulty'],
              'notes': result['notes'],
            },
          );

          // 更新模板中的考试实例
          final updatedInstances = _currentTemplate.instances.map((i) {
            if (i.id == instance.id) {
              return updatedInstance;
            }
            return i;
          }).toList();

          final updatedTemplate = _currentTemplate.copyWith(
            instances: updatedInstances,
            updatedAt: DateTime.now(),
          );

          // 保存到数据库
          await _templateService.updateInstance(updatedInstance);
          await _templateService.updateTemplate(updatedTemplate);

          // 更新状态
          if (mounted) {
            setState(() {
              _currentTemplate = updatedTemplate;
              _isSaving = false;
            });

            // 显示成功提示
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('考试实例更新成功')),
            );
          }
        } catch (e) {
          // 显示错误提示
          if (mounted) {
            setState(() {
              _isSaving = false;
            });

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('更新失败: $e')),
            );
          }
        }
      }
    });
  }

  // 显示添加考试实例对话框
  void _showAddInstanceDialog() {
    // 使用一个安全的方式来处理对话框
    // 初始化数据
    String number = '';
    ExamNumberingMode numberingMode = ExamNumberingMode.year;
    int difficulty = 0;
    String notes = '';
    DateTime? examDate;
    TimeOfDay? startTime;
    TimeOfDay? endTime;

    // 显示对话框
    showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false, // 防止点击外部关闭对话框
      builder: (dialogContext) {
        // 在对话框内部创建控制器
        final numberController = TextEditingController();
        final notesController = TextEditingController();

        return StatefulBuilder(
          builder: (context, setDialogState) {
            // 选择日期
            Future<void> selectDate() async {
              final DateTime? picked = await showDatePicker(
                context: context,
                initialDate: examDate ?? DateTime.now(),
                firstDate: DateTime(2000),
                lastDate: DateTime(2100),
              );
              if (picked != null && picked != examDate) {
                setDialogState(() {
                  examDate = picked;
                });
              }
            }

            // 选择开始时间
            Future<void> selectStartTime() async {
              final TimeOfDay? picked = await showTimePicker(
                context: context,
                initialTime: startTime ?? const TimeOfDay(hour: 9, minute: 0),
              );
              if (picked != null && picked != startTime) {
                setDialogState(() {
                  startTime = picked;
                });
              }
            }

            // 选择结束时间
            Future<void> selectEndTime() async {
              final TimeOfDay? picked = await showTimePicker(
                context: context,
                initialTime: endTime ?? const TimeOfDay(hour: 12, minute: 0),
              );
              if (picked != null && picked != endTime) {
                setDialogState(() {
                  endTime = picked;
                });
              }
            }

            // 格式化时间
            String formatTime(TimeOfDay? time) {
              if (time == null) return '未设置';
              final hour = time.hour.toString().padLeft(2, '0');
              final minute = time.minute.toString().padLeft(2, '0');
              return '$hour:$minute';
            }

            // 格式化日期
            String formatDate(DateTime? date) {
              if (date == null) return '未设置';
              final year = date.year;
              final month = date.month.toString().padLeft(2, '0');
              final day = date.day.toString().padLeft(2, '0');
              return '$year-$month-$day';
            }

            // 验证并添加实例
            void validateAndAdd() {
              // 获取当前值
              number = numberController.text.trim();
              notes = notesController.text.trim();

              // 验证编号
              if (number.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入考试编号')),
                );
                return;
              }

              // 检查编号是否已存在
              final isNumberExists = _currentTemplate.instances.any((instance) =>
                  instance.number == number && instance.numberingMode == numberingMode);

              if (isNumberExists) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('编号已存在')),
                );
                return;
              }

              // 关闭对话框并返回数据
              Navigator.of(dialogContext).pop({
                'number': number,
                'numberingMode': numberingMode,
                'difficulty': difficulty,
                'notes': notes,
                'examDate': examDate,
                'startTime': startTime,
                'endTime': endTime,
              });
            }

            return AlertDialog(
              title: const Text('添加考试实例'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 编号模式选择
                    const Text('编号模式', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    SegmentedButton<ExamNumberingMode>(
                      segments: const [
                        ButtonSegment<ExamNumberingMode>(
                          value: ExamNumberingMode.year,
                          label: Text('年份'),
                          icon: Icon(Icons.calendar_today),
                        ),
                        ButtonSegment<ExamNumberingMode>(
                          value: ExamNumberingMode.custom,
                          label: Text('自定义'),
                          icon: Icon(Icons.edit),
                        ),
                      ],
                      selected: {numberingMode},
                      onSelectionChanged: (Set<ExamNumberingMode> selection) {
                        setDialogState(() {
                          numberingMode = selection.first;
                        });
                      },
                      showSelectedIcon: false,
                      emptySelectionAllowed: false,
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 编号输入框
                    const Text('编号', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    TextField(
                      controller: numberController,
                      decoration: InputDecoration(
                        hintText: numberingMode == ExamNumberingMode.year
                            ? '输入年份，如"2023"'
                            : '输入自定义编号，如"A01"',
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                      ),
                      keyboardType: numberingMode == ExamNumberingMode.year
                          ? TextInputType.number
                          : TextInputType.text,
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 考试日期
                    const Text('考试日期（可选）', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    InkWell(
                      onTap: selectDate,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.border),
                          borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              formatDate(examDate),
                              style: examDate == null
                                  ? AppTextStyles.hint
                                  : AppTextStyles.bodyMedium,
                            ),
                            const Icon(Icons.calendar_today, size: 20),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 考试时间
                    Row(
                      children: [
                        // 开始时间
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('开始时间（可选）', style: AppTextStyles.labelMedium),
                              const SizedBox(height: AppSizes.paddingSmall),
                              InkWell(
                                onTap: selectStartTime,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: AppSizes.paddingMedium,
                                    vertical: AppSizes.paddingSmall,
                                  ),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: AppColors.border),
                                    borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        formatTime(startTime),
                                        style: startTime == null
                                            ? AppTextStyles.hint
                                            : AppTextStyles.bodyMedium,
                                      ),
                                      const Icon(Icons.access_time, size: 20),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: AppSizes.paddingMedium),
                        // 结束时间
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('结束时间（可选）', style: AppTextStyles.labelMedium),
                              const SizedBox(height: AppSizes.paddingSmall),
                              InkWell(
                                onTap: selectEndTime,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: AppSizes.paddingMedium,
                                    vertical: AppSizes.paddingSmall,
                                  ),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: AppColors.border),
                                    borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        formatTime(endTime),
                                        style: endTime == null
                                            ? AppTextStyles.hint
                                            : AppTextStyles.bodyMedium,
                                      ),
                                      const Icon(Icons.access_time, size: 20),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 难度评级
                    const Text('难度评级（可选）', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    Row(
                      children: List.generate(5, (index) {
                        return IconButton(
                          icon: Icon(
                            index < difficulty ? Icons.star : Icons.star_border,
                            color: index < difficulty ? AppColors.warning : AppColors.textTertiary,
                          ),
                          onPressed: () {
                            setDialogState(() {
                              difficulty = index + 1;
                            });
                          },
                        );
                      }),
                    ),
                    if (difficulty > 0)
                      TextButton.icon(
                        onPressed: () {
                          setDialogState(() {
                            difficulty = 0;
                          });
                        },
                        icon: const Icon(Icons.close, size: 16),
                        label: const Text('重置'),
                        style: TextButton.styleFrom(
                          foregroundColor: AppColors.textTertiary,
                          padding: EdgeInsets.zero,
                          minimumSize: Size.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                      ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 备注
                    const Text('备注（可选）', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    TextField(
                      controller: notesController,
                      decoration: const InputDecoration(
                        hintText: '输入备注信息',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                      ),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
              actions: [
                // 取消按钮
                TextButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                  child: const Text('取消'),
                ),
                // 确定按钮
                ElevatedButton(
                  onPressed: validateAndAdd,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('添加'),
                ),
              ],
            );
          },
        );
      },
    ).then((result) async {
      // 如果用户点击了添加按钮并返回了数据
      if (result != null) {
        setState(() {
          _hasUnsavedChanges = true;
          _isSaving = true; // 显示保存中状态
        });

        try {
          // 创建新的考试实例
          final newInstance = await _templateService.createInstance(
            templateId: _currentTemplate.id,
            number: result['number'],
            numberingMode: result['numberingMode'],
            examDate: result['examDate'],
            startTime: result['startTime'],
            endTime: result['endTime'],
          );

          // 如果有自定义设置，更新实例
          if (result['difficulty'] > 0 || result['notes'].isNotEmpty) {
            final updatedInstance = newInstance.copyWith(
              customSettings: {
                'difficulty': result['difficulty'],
                'notes': result['notes'],
              },
            );
            await _templateService.updateInstance(updatedInstance);
          }

          // 更新当前模板
          final updatedInstances = [..._currentTemplate.instances, newInstance];
          final updatedTemplate = _currentTemplate.copyWith(
            instances: updatedInstances,
            updatedAt: DateTime.now(),
          );

          // 保存到数据库
          await _templateService.updateTemplate(updatedTemplate);

          // 更新状态
          if (mounted) {
            setState(() {
              _currentTemplate = updatedTemplate;
              _isSaving = false;
            });

            // 显示成功提示
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('考试实例添加成功')),
            );
          }
        } catch (e) {
          // 显示错误提示
          if (mounted) {
            setState(() {
              _isSaving = false;
            });

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('添加失败: $e')),
            );
          }
        }
      }
    });
  }

  // 显示添加题型模块对话框
  void _showAddSectionDialog() {
    // 使用一个安全的方式来处理对话框
    // 先创建临时数据对象，而不直接使用控制器
    String name = '';
    String scoreText = '10';
    String timeText = '30';

    // 显示对话框
    showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false, // 防止点击外部关闭对话框
      builder: (dialogContext) {
        // 在对话框内部创建控制器，这样它们的生命周期与对话框绑定
        final nameController = TextEditingController();
        final scoreController = TextEditingController(text: scoreText);
        final timeController = TextEditingController(text: timeText);

        return StatefulBuilder(
          builder: (context, setDialogState) {
            // 验证并添加题型
            void validateAndAdd() {
              // 获取当前值
              name = nameController.text.trim();
              scoreText = scoreController.text.trim();
              timeText = timeController.text.trim();

              // 验证名称
              if (name.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入题型名称')),
                );
                return;
              }

              // 验证分值
              final score = int.tryParse(scoreText);
              if (score == null || score <= 0) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入有效的分值')),
                );
                return;
              }

              // 验证预计用时
              final estimatedTime = int.tryParse(timeText);
              if (estimatedTime == null || estimatedTime <= 0) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入有效的预计用时')),
                );
                return;
              }

              // 关闭对话框并返回数据
              Navigator.of(dialogContext).pop({
                'name': name,
                'score': score,
                'estimatedTime': estimatedTime,
              });
            }

            return AlertDialog(
              title: const Text('添加题型模块'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 题型名称
                    const Text('题型名称', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    TextField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        hintText: '输入题型名称，如"阅读理解"',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                      ),
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 分值
                    const Text('分值', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    TextField(
                      controller: scoreController,
                      decoration: const InputDecoration(
                        hintText: '输入分值，如"20"',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: AppSizes.paddingMedium),

                    // 预计用时
                    const Text('预计用时（分钟）', style: AppTextStyles.labelMedium),
                    const SizedBox(height: AppSizes.paddingSmall),
                    TextField(
                      controller: timeController,
                      decoration: const InputDecoration(
                        hintText: '输入预计用时，如"30"',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingMedium,
                          vertical: AppSizes.paddingSmall,
                        ),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ],
                ),
              ),
              actions: [
                // 取消按钮
                TextButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                  child: const Text('取消'),
                ),
                // 确定按钮
                ElevatedButton(
                  onPressed: validateAndAdd,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('添加'),
                ),
              ],
            );
          },
        );
      },
    ).then((result) async {
      // 如果用户点击了添加按钮并返回了数据
      if (result != null) {
        setState(() {
          _hasUnsavedChanges = true;
          _isSaving = true; // 显示保存中状态
        });

        try {
          // 创建新的题型模块
          final newSection = await _templateService.createSection(
            templateId: _currentTemplate.id,
            name: result['name'],
            score: result['score'],
            estimatedTime: result['estimatedTime'],
          );

          // 更新当前模板
          final updatedSections = [..._currentTemplate.sections, newSection];
          final updatedTemplate = _currentTemplate.copyWith(
            sections: updatedSections,
            updatedAt: DateTime.now(),
          );

          // 保存到数据库
          await _templateService.updateTemplate(updatedTemplate);

          // 更新状态
          if (mounted) {
            setState(() {
              _currentTemplate = updatedTemplate;
              _isSaving = false;
            });

            // 显示成功提示
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('题型模块添加成功')),
            );
          }
        } catch (e) {
          // 显示错误提示
          if (mounted) {
            setState(() {
              _isSaving = false;
            });

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('添加失败: $e')),
            );
          }
        }
      }
    });
  }

  // 保存模板
  Future<void> _saveTemplate() async {
    // 验证输入
    final name = _nameController.text.trim();
    if (name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入考试名称')),
      );
      return;
    }

    final totalScoreText = _totalScoreController.text.trim();
    if (totalScoreText.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入总分值')),
      );
      return;
    }

    final totalScore = int.tryParse(totalScoreText);
    if (totalScore == null || totalScore <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('总分值必须是大于0的整数')),
      );
      return;
    }

    final durationText = _durationController.text.trim();
    if (durationText.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入考试时长')),
      );
      return;
    }

    final duration = int.tryParse(durationText);
    if (duration == null || duration <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('考试时长必须是大于0的整数')),
      );
      return;
    }

    // 设置保存状态
    setState(() {
      _isSaving = true;
    });

    try {
      // 更新模板
      final updatedTemplate = _currentTemplate.copyWith(
        name: name,
        subjectId: _selectedSubjectId,
        totalScore: totalScore,
        duration: duration,
        updatedAt: DateTime.now(),
      );

      // 保存到数据库
      await _templateService.updateTemplate(updatedTemplate);

      // 更新当前模板
      setState(() {
        _currentTemplate = updatedTemplate;
        _isSaving = false;
        _hasUnsavedChanges = false;
      });

      // 显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('保存成功')),
        );
      }

      // 返回上一页并传递更新后的模板
      if (mounted) {
        Navigator.of(context).pop(_currentTemplate);
      }
    } catch (e) {
      // 显示错误提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存失败: $e')),
        );
      }

      // 重置保存状态
      setState(() {
        _isSaving = false;
      });
    }
  }
}
