import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import '../../../shared/widgets/app_card.dart';
import '../models/exam_template_models.dart';
import '../services/exam_template_service.dart';
import 'exam_template_detail_screen.dart';
import 'exam_template_create_new_screen.dart';

/// 模拟考试主页面
/// 提供模拟考试的入口和功能选择
class ExamHomeScreen extends StatefulWidget {
  const ExamHomeScreen({super.key});

  @override
  State<ExamHomeScreen> createState() => _ExamHomeScreenState();
}

class _ExamHomeScreenState extends State<ExamHomeScreen> {
  // 考试模板服务
  final ExamTemplateService _templateService = ExamTemplateService();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // 添加顶部栏，包含返回按钮
      appBar: AppBar(
        backgroundColor: Color.fromARGB(255, 230, 240, 255), // 透明背景，与渐变背景协调
        elevation: 0, // 移除阴影
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.text),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text('模拟考试', style: TextStyle(color: AppColors.text)),
      ),
      // 使用渐变背景
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color.fromARGB(255, 230, 240, 255), // 顶部颜色 - 浅蓝色
              Color.fromARGB(255, 255, 255, 255), // 底部颜色 - 白色
            ],
          ),
        ),
        // 使用SingleChildScrollView使整个页面可滚动
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(AppSizes.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 欢迎信息
                  const Text(
                    '模拟考试中心',
                    style: AppTextStyles.headline2,
                  ),
                  const SizedBox(height: AppSizes.paddingSmall),
                  const Text(
                    '选择一个功能模块开始你的学习之旅',
                    style: AppTextStyles.bodyMedium,
                  ),
                  const SizedBox(height: AppSizes.paddingLarge),

                  // 模拟考试列表区域
                  _buildMockExamListSection(),
                  const SizedBox(height: AppSizes.paddingLarge),

                  // 功能模块卡片

                  _buildExamModeCard(
                    title: '单次测验模式',
                    description: '快速创建自定义测验，选择题目类型、数量和时间',
                    icon: Icons.assignment,
                    color: AppColors.info,
                    onTap: () => _navigateToModule('single_test'),
                  ),
                  const SizedBox(height: AppSizes.paddingMedium),

                  _buildExamModeCard(
                    title: '数据分析中心',
                    description: '查看你的学习进展、弱点分析和成长趋势',
                    icon: Icons.analytics,
                    color: AppColors.warning,
                    onTap: () => _navigateToModule('analytics'),
                  ),
                  const SizedBox(height: AppSizes.paddingMedium),

                  _buildExamModeCard(
                    title: '考试历史',
                    description: '查看并复习你的考试记录和错题集',
                    icon: Icons.history,
                    color: AppColors.secondary,
                    onTap: () => _navigateToModule('history'),
                  ),

                  const SizedBox(height: AppSizes.paddingLarge),

                  // 最近活动
                  AppCard(
                    title: '最近活动',
                    action: TextButton(
                      onPressed: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('活动历史功能开发中')),
                        );
                      },
                      child: const Text('查看全部'),
                    ),
                    child: const Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: AppSizes.paddingLarge),
                        child: Text(
                          '暂无活动记录',
                          style: AppTextStyles.bodyMedium,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 构建考试模式卡片
  Widget _buildExamModeCard({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return AppCard(
      padding: EdgeInsets.zero,
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingMedium),
        child: Row(
          children: [
            // 图标
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: color.withAlpha(30),
                borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
              ),
              child: Icon(
                icon,
                color: color,
                size: 28,
              ),
            ),
            const SizedBox(width: AppSizes.paddingMedium),
            // 文本内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.headline3,
                  ),
                  const SizedBox(height: AppSizes.paddingSmall / 2),
                  Text(
                    description,
                    style: AppTextStyles.bodySmall,
                  ),
                ],
              ),
            ),
            // 箭头
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColors.textTertiary,
            ),
          ],
        ),
      ),
    );
  }

  // 导航到不同的功能模块
  void _navigateToModule(String module) {
    switch (module) {
      case 'template_library':
        _showTemplateLibrary();
        break;
      case 'single_test':
        _showSingleTestMode();
        break;
      case 'analytics':
        _showAnalyticsCenter();
        break;
      case 'history':
        _showExamHistory();
        break;
    }
  }

  // 显示考试模版库
  void _showTemplateLibrary() {
    // 调用导航方法
    _navigateToTemplateLibrary();
  }

  // 显示单次测验模式
  void _showSingleTestMode() {
        ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('功能开发中')),
    );
  }

  // 显示数据分析中心
  void _showAnalyticsCenter() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('数据分析中心功能开发中')),
    );
  }

  // 显示考试历史
  void _showExamHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('考试历史功能开发中')),
    );
  }

  // 模板列表
  List<ExamTemplate> _templates = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTemplates();
  }

  // 加载模板列表
  Future<void> _loadTemplates() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final templates = await _templateService.getAllTemplates();
      if (mounted) {
        setState(() {
          _templates = templates;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载模板失败: $e')),
        );
      }
    }
  }

  // 构建模拟考试列表区域
  Widget _buildMockExamListSection() {
    // 获取屏幕高度，计算卡片区域高度
    final screenHeight = MediaQuery.of(context).size.height;
    final cardHeight = screenHeight * 0.55; // 占屏幕高度的 55%

    return AppCard(
      title: '我的模拟考试',
      margin: EdgeInsets.zero, // 移除边距，与下方卡片保持一致
      action: TextButton.icon(
        onPressed: _navigateToTemplateLibrary,
        icon: const Icon(Icons.add_circle, size: 18),
        label: const Text('新建'),
        style: TextButton.styleFrom(
          foregroundColor: AppColors.primary,
          textStyle: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ),
      child: Container(
        height: cardHeight,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _templates.isEmpty
                ? _buildEmptyState()
                : _buildTemplateList(_templates),
      ),
    );
  }

  // 构建空状态提示
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: AppColors.primary.withAlpha(30),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.assignment_outlined,
              size: 50,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: AppSizes.paddingLarge),
          const Text(
            '暂无模拟考试',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.text,
            ),
          ),
          const SizedBox(height: AppSizes.paddingMedium),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppSizes.paddingLarge,
              vertical: AppSizes.paddingSmall,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
              boxShadow: [AppShadows.low],
            ),
            child: const Text(
              '点击右上角的“新建”按钮创建你的第一个模拟考试',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: AppSizes.paddingLarge),
          OutlinedButton.icon(
            onPressed: _navigateToTemplateLibrary,
            icon: const Icon(Icons.add),
            label: const Text('新建模拟考试'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primary,
              side: BorderSide(color: AppColors.primary),
              padding: const EdgeInsets.symmetric(
                horizontal: AppSizes.paddingLarge,
                vertical: AppSizes.paddingMedium,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建考试模板列表
  Widget _buildTemplateList(List<ExamTemplate> templates) {
    return ListView.builder(
      padding: EdgeInsets.zero,
      itemCount: templates.length,
      itemBuilder: (context, index) {
        // 常规模板卡片
        final template = templates[index];
        return _buildTemplateCard(template);
      },
    );
  }

  // 构建模板卡片
  Widget _buildTemplateCard(ExamTemplate template) {
    // 获取科目相关颜色
    final Color templateColor = _getTemplateColor(template.name);

    return Card(
      margin: const EdgeInsets.symmetric(
        vertical: AppSizes.paddingSmall / 2, // 减小卡片间距
        horizontal: AppSizes.paddingMedium,
      ),
      elevation: 1, // 减小阴影，使设计更简洁
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusSmall), // 使用更小的圆角
      ),
      child: InkWell(
        onTap: () => _navigateToTemplateDetail(template),
        borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
            border: Border.all(
              color: templateColor.withAlpha(30), // 减小边框颜色强度
              width: 1, // 减小边框宽度
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppSizes.paddingMedium,
              vertical: AppSizes.paddingSmall, // 减小垂直内边距
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 模板图标 - 减小尺寸
                Container(
                  width: 36, // 减小图标容器尺寸
                  height: 36,
                  decoration: BoxDecoration(
                    color: templateColor.withAlpha(20), // 减小背景颜色强度
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Icon(
                      _getTemplateIcon(template.name),
                      color: templateColor,
                      size: 18, // 减小图标尺寸
                    ),
                  ),
                ),
                const SizedBox(width: AppSizes.paddingSmall), // 减小间距

                // 模板信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        template.name,
                        style: const TextStyle(
                          fontSize: 15, // 减小字体大小
                          fontWeight: FontWeight.w600,
                          color: AppColors.text,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4), // 减小间距
                      // 只保留分值和时长信息，使用行而非Wrap
                      Row(
                        children: [
                          _buildInfoChip(
                            '${template.totalScore}分',
                            Icons.score,
                            templateColor,
                          ),
                          const SizedBox(width: AppSizes.paddingSmall),
                          _buildInfoChip(
                            '${template.duration}分钟',
                            Icons.timer,
                            templateColor,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // 箭头图标
                Icon(
                  Icons.arrow_forward_ios,
                  size: 14, // 减小图标尺寸
                  color: AppColors.textTertiary, // 使用统一的颜色
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 构建信息标签
  Widget _buildInfoChip(String label, IconData icon, [Color? color]) {
    // 使用统一的文本颜色，而不是每个卡片不同的颜色
    final chipColor = AppColors.textSecondary;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 12,
          color: chipColor,
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
            fontWeight: FontWeight.normal, // 使用正常字重
          ),
        ),
      ],
    );
  }

  // 根据模板名称获取颜色
  Color _getTemplateColor(String templateName) {
    if (templateName.contains('英语')) {
      return Colors.blue;
    } else if (templateName.contains('数学')) {
      return Colors.purple;
    } else if (templateName.contains('物理')) {
      return Colors.orange;
    } else if (templateName.contains('计算机')) {
      return Colors.teal;
    } else {
      return AppColors.primary;
    }
  }

  // 根据模板名称获取图标
  IconData _getTemplateIcon(String templateName) {
    if (templateName.contains('英语')) {
      return Icons.language;
    } else if (templateName.contains('数学')) {
      return Icons.calculate;
    } else if (templateName.contains('物理')) {
      return Icons.science;
    } else if (templateName.contains('计算机')) {
      return Icons.computer;
    } else {
      return Icons.book;
    }
  }

  // 导航到模板详情页面
  void _navigateToTemplateDetail(ExamTemplate template) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ExamTemplateDetailScreen(template: template),
      ),
    ).then((_) {
      // 返回时刷新模板列表
      _loadTemplates();
    });
  }

  // 导航到模板库页面
  void _navigateToTemplateLibrary() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ExamTemplateCreateNewScreen(),
      ),
    ).then((_) {
      // 返回时刷新模板列表
      _loadTemplates();
    });
  }
}
