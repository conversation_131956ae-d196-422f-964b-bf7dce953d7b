import 'package:flutter/material.dart';
import '../../../shared/theme/constants.dart';
import '../models/exam_template_models.dart';
import '../services/exam_template_service.dart';

/// 考试实例详情页面
/// 用于查看和编辑考试实例信息
class ExamInstanceDetailScreen extends StatefulWidget {
  /// 考试模板
  final ExamTemplate template;

  /// 考试实例
  final ExamInstance instance;

  const ExamInstanceDetailScreen({
    super.key,
    required this.template,
    required this.instance,
  });

  @override
  State<ExamInstanceDetailScreen> createState() => _ExamInstanceDetailScreenState();
}

class _ExamInstanceDetailScreenState extends State<ExamInstanceDetailScreen> {
  // 考试模板服务
  final ExamTemplateService _templateService = ExamTemplateService();

  // 当前实例
  late ExamInstance _currentInstance;

  // 是否编辑模式
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();

    // 初始化当前实例
    _currentInstance = widget.instance;
  }

  @override
  Widget build(BuildContext context) {
    final numberPrefix = _currentInstance.numberingMode == ExamNumberingMode.year
        ? '年份：'
        : '编号：';

    return Scaffold(
      appBar: AppBar(
        title: Text('$numberPrefix${_currentInstance.number}'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: AppColors.text,
        actions: [
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: _toggleEditMode,
            ),
        ],
      ),
      body: SafeArea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                '考试实例详情页面',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                '模板: ${widget.template.name}',
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 10),
              Text(
                '编号: ${_currentInstance.number}',
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 40),
              if (_isEditing)
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    OutlinedButton(
                      onPressed: _toggleEditMode,
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingLarge,
                          vertical: AppSizes.paddingMedium,
                        ),
                      ),
                      child: const Text('取消'),
                    ),
                    const SizedBox(width: AppSizes.paddingMedium),
                    ElevatedButton(
                      onPressed: _saveInstance,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingLarge,
                          vertical: AppSizes.paddingMedium,
                        ),
                      ),
                      child: const Text('保存'),
                    ),
                  ],
                ),
              if (!_isEditing)
                OutlinedButton.icon(
                  onPressed: _confirmDelete,
                  icon: const Icon(Icons.delete, color: AppColors.error),
                  label: const Text(
                    '删除此实例',
                    style: TextStyle(color: AppColors.error),
                  ),
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: AppColors.error),
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSizes.paddingLarge,
                      vertical: AppSizes.paddingMedium,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // 切换编辑模式
  void _toggleEditMode() {
    setState(() {
      _isEditing = !_isEditing;
    });
  }

  // 保存实例
  void _saveInstance() {
    // 简化版保存逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('实例更新成功')),
    );

    setState(() {
      _isEditing = false;
    });
  }

  // 确认删除
  void _confirmDelete() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除考试实例'),
        content: Text('确定要删除"${_currentInstance.number}"吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteInstance();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  // 删除实例
  Future<void> _deleteInstance() async {
    try {
      await _templateService.deleteInstance(_currentInstance.id);

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('实例已删除')),
      );

      Navigator.pop(context);
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('删除失败: $e')),
      );
    }
  }
}
