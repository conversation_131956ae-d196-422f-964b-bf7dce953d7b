import 'package:flutter/material.dart';
import '../models/exam_template_models.dart';
import '../../../core/services/hive_service.dart';

/// 考试模板服务
/// 负责管理考试模板和实例数据
class ExamTemplateService {
  // 单例模式
  static final ExamTemplateService _instance = ExamTemplateService._internal();

  factory ExamTemplateService() {
    return _instance;
  }

  ExamTemplateService._internal();

  // Hive服务
  final HiveService _hiveService = HiveService();

  // 模拟数据 - 考试模板列表 (已废弃，使用Hive存储)
  final List<ExamTemplate> _legacyTemplates = [
    // 考研英语一模板
    ExamTemplate(
      id: 'template_1',
      name: '考研英语一',
      subjectId: 'subject_english',
      totalScore: 100,
      duration: 180, // 3小时
      sections: [
        ExamSection(
          id: 'section_1_1',
          name: '完形填空',
          score: 20,
          estimatedTime: 30,
          templateId: 'template_1',
        ),
        ExamSection(
          id: 'section_1_2',
          name: '阅读理解',
          score: 40,
          estimatedTime: 60,
          templateId: 'template_1',
        ),
        ExamSection(
          id: 'section_1_3',
          name: '新题型',
          score: 10,
          estimatedTime: 20,
          templateId: 'template_1',
        ),
        ExamSection(
          id: 'section_1_4',
          name: '翻译',
          score: 15,
          estimatedTime: 30,
          templateId: 'template_1',
        ),
        ExamSection(
          id: 'section_1_5',
          name: '写作',
          score: 15,
          estimatedTime: 40,
          templateId: 'template_1',
        ),
      ],
      instances: [
        ExamInstance(
          id: 'instance_1_1',
          templateId: 'template_1',
          number: '2022',
          numberingMode: ExamNumberingMode.year,
          examDate: DateTime(2022, 12, 25),
          startTime: const TimeOfDay(hour: 9, minute: 0),
          endTime: const TimeOfDay(hour: 12, minute: 0),
        ),
        ExamInstance(
          id: 'instance_1_2',
          templateId: 'template_1',
          number: '2021',
          numberingMode: ExamNumberingMode.year,
          examDate: DateTime(2021, 12, 26),
          startTime: const TimeOfDay(hour: 9, minute: 0),
          endTime: const TimeOfDay(hour: 12, minute: 0),
        ),
        ExamInstance(
          id: 'instance_1_3',
          templateId: 'template_1',
          number: 'A01',
          numberingMode: ExamNumberingMode.custom,
          examDate: DateTime(2023, 6, 15),
          startTime: const TimeOfDay(hour: 14, minute: 0),
          endTime: const TimeOfDay(hour: 17, minute: 0),
        ),
      ],
      createdAt: DateTime.now().subtract(const Duration(days: 100)),
      updatedAt: DateTime.now().subtract(const Duration(days: 10)),
    ),

    // 考研数学模板
    ExamTemplate(
      id: 'template_2',
      name: '考研数学',
      subjectId: 'subject_math',
      totalScore: 150,
      duration: 180, // 3小时
      sections: [
        ExamSection(
          id: 'section_2_1',
          name: '选择题',
          score: 56,
          estimatedTime: 60,
          templateId: 'template_2',
        ),
        ExamSection(
          id: 'section_2_2',
          name: '填空题',
          score: 24,
          estimatedTime: 30,
          templateId: 'template_2',
        ),
        ExamSection(
          id: 'section_2_3',
          name: '解答题',
          score: 70,
          estimatedTime: 90,
          templateId: 'template_2',
        ),
      ],
      instances: [
        ExamInstance(
          id: 'instance_2_1',
          templateId: 'template_2',
          number: '2022',
          numberingMode: ExamNumberingMode.year,
          examDate: DateTime(2022, 12, 24),
          startTime: const TimeOfDay(hour: 14, minute: 0),
          endTime: const TimeOfDay(hour: 17, minute: 0),
        ),
      ],
      createdAt: DateTime.now().subtract(const Duration(days: 90)),
      updatedAt: DateTime.now().subtract(const Duration(days: 5)),
    ),

    // 四级英语模板
    ExamTemplate(
      id: 'template_3',
      name: '大学英语四级',
      subjectId: 'subject_english',
      totalScore: 710,
      duration: 120, // 2小时
      sections: [
        ExamSection(
          id: 'section_3_1',
          name: '写作',
          score: 107,
          estimatedTime: 30,
          templateId: 'template_3',
        ),
        ExamSection(
          id: 'section_3_2',
          name: '听力理解',
          score: 248,
          estimatedTime: 30,
          templateId: 'template_3',
        ),
        ExamSection(
          id: 'section_3_3',
          name: '阅读理解',
          score: 249,
          estimatedTime: 40,
          templateId: 'template_3',
        ),
        ExamSection(
          id: 'section_3_4',
          name: '翻译',
          score: 106,
          estimatedTime: 20,
          templateId: 'template_3',
        ),
      ],
      instances: [],
      createdAt: DateTime.now().subtract(const Duration(days: 80)),
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
    ),
  ];

  // 初始化服务
  Future<void> init() async {
    try {
      await _hiveService.initHive();
    } catch (e) {
      debugPrint('初始化ExamTemplateService时发生错误: $e');
    }
  }

  // 获取所有考试模板
  Future<List<ExamTemplate>> getAllTemplates() async {
    return await _hiveService.examTemplateRepository.getAllTemplates();
  }

  // 根据ID获取考试模板
  Future<ExamTemplate?> getTemplateById(String id) async {
    return await _hiveService.examTemplateRepository.getTemplateById(id);
  }

  // 创建新的考试模板
  Future<ExamTemplate> createTemplate({
    required String name,
    String? subjectId,
    required int totalScore,
    required int duration,
    List<ExamSection>? sections,
  }) async {
    final now = DateTime.now();
    final id = 'template_${now.millisecondsSinceEpoch}';

    final newTemplate = ExamTemplate(
      id: id,
      name: name,
      subjectId: subjectId,
      totalScore: totalScore,
      duration: duration,
      sections: sections ?? [],
      instances: [],
      createdAt: now,
      updatedAt: now,
    );

    await _hiveService.examTemplateRepository.saveTemplate(newTemplate);
    return newTemplate;
  }

  // 更新考试模板
  Future<void> updateTemplate(ExamTemplate template) async {
    final updatedTemplate = template.copyWith(
      updatedAt: DateTime.now(),
    );
    await _hiveService.examTemplateRepository.saveTemplate(updatedTemplate);
  }

  // 删除考试模板
  Future<void> deleteTemplate(String id) async {
    await _hiveService.examTemplateRepository.deleteTemplate(id);
  }

  // 创建新的题型模块
  Future<ExamSection> createSection({
    required String templateId,
    required String name,
    required int score,
    required int estimatedTime,
  }) async {
    final template = await getTemplateById(templateId);
    if (template == null) {
      throw Exception('模板不存在');
    }

    final id = 'section_${templateId}_${template.sections.length + 1}';

    final newSection = ExamSection(
      id: id,
      name: name,
      score: score,
      estimatedTime: estimatedTime,
      templateId: templateId,
    );

    await _hiveService.examTemplateRepository.saveSection(newSection);
    return newSection;
  }

  // 更新题型模块
  Future<void> updateSection(ExamSection section) async {
    await _hiveService.examTemplateRepository.saveSection(section);
  }

  // 删除题型模块
  Future<void> deleteSection(String sectionId) async {
    await _hiveService.examTemplateRepository.deleteSection(sectionId);
  }

  // 创建新的考试实例
  Future<ExamInstance> createInstance({
    required String templateId,
    required String number,
    required ExamNumberingMode numberingMode,
    DateTime? examDate,
    TimeOfDay? startTime,
    TimeOfDay? endTime,
  }) async {
    final template = await getTemplateById(templateId);
    if (template == null) {
      throw Exception('模板不存在');
    }

    // 检查编号是否已存在
    final isNumberExists = template.instances.any((instance) =>
        instance.number == number && instance.numberingMode == numberingMode);

    if (isNumberExists) {
      throw Exception('编号已存在');
    }

    final id = 'instance_${templateId}_${template.instances.length + 1}';

    final newInstance = ExamInstance(
      id: id,
      templateId: templateId,
      number: number,
      numberingMode: numberingMode,
      examDate: examDate,
      startTime: startTime,
      endTime: endTime,
    );

    await _hiveService.examTemplateRepository.saveInstance(newInstance);
    return newInstance;
  }

  // 更新考试实例
  Future<void> updateInstance(ExamInstance instance) async {
    final template = await getTemplateById(instance.templateId);
    if (template == null) {
      throw Exception('模板不存在');
    }

    // 检查编号是否与其他实例冲突
    final isNumberConflict = template.instances.any((i) =>
        i.id != instance.id &&
        i.number == instance.number &&
        i.numberingMode == instance.numberingMode);

    if (isNumberConflict) {
      throw Exception('编号已被其他实例使用');
    }

    await _hiveService.examTemplateRepository.saveInstance(instance);
  }

  // 删除考试实例
  Future<void> deleteInstance(String instanceId) async {
    await _hiveService.examTemplateRepository.deleteInstance(instanceId);
  }

  // 获取所有考试实例
  Future<List<ExamInstance>> getAllInstances() async {
    return await _hiveService.examTemplateRepository.getAllInstances();
  }

  // 根据ID获取考试实例
  Future<ExamInstance?> getInstanceById(String instanceId) async {
    return await _hiveService.examTemplateRepository.getInstanceById(instanceId);
  }

  // 获取模板的所有实例
  Future<List<ExamInstance>> getInstancesByTemplateId(String templateId) async {
    return await _hiveService.examTemplateRepository.getInstancesByTemplateId(templateId);
  }
}
