// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'exam_template_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ExamSectionAdapter extends TypeAdapter<ExamSection> {
  @override
  final int typeId = 24;

  @override
  ExamSection read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ExamSection(
      id: fields[0] as String,
      name: fields[1] as String,
      score: fields[2] as int,
      estimatedTime: fields[3] as int,
      templateId: fields[4] as String,
    );
  }

  @override
  void write(BinaryWriter writer, ExamSection obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.score)
      ..writeByte(3)
      ..write(obj.estimatedTime)
      ..writeByte(4)
      ..write(obj.templateId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ExamSectionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TimeOfDayHiveAdapter extends TypeAdapter<TimeOfDayHive> {
  @override
  final int typeId = 25;

  @override
  TimeOfDayHive read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TimeOfDayHive(
      hour: fields[0] as int,
      minute: fields[1] as int,
    );
  }

  @override
  void write(BinaryWriter writer, TimeOfDayHive obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.hour)
      ..writeByte(1)
      ..write(obj.minute);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TimeOfDayHiveAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ExamInstanceAdapter extends TypeAdapter<ExamInstance> {
  @override
  final int typeId = 26;

  @override
  ExamInstance read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ExamInstance(
      id: fields[0] as String,
      templateId: fields[1] as String,
      number: fields[2] as String,
      numberingMode: fields[3] as ExamNumberingMode,
      examDate: fields[4] as DateTime?,
      startTime: fields[5] as TimeOfDay?,
      endTime: fields[6] as TimeOfDay?,
      customSettings: (fields[7] as Map?)?.cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, ExamInstance obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.templateId)
      ..writeByte(2)
      ..write(obj.number)
      ..writeByte(3)
      ..write(obj.numberingMode)
      ..writeByte(4)
      ..write(obj.examDate)
      ..writeByte(5)
      ..write(obj.startTime)
      ..writeByte(6)
      ..write(obj.endTime)
      ..writeByte(7)
      ..write(obj.customSettings);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ExamInstanceAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ExamTemplateAdapter extends TypeAdapter<ExamTemplate> {
  @override
  final int typeId = 27;

  @override
  ExamTemplate read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ExamTemplate(
      id: fields[0] as String,
      name: fields[1] as String,
      subjectId: fields[2] as String?,
      totalScore: fields[3] as int,
      duration: fields[4] as int,
      sections: (fields[5] as List).cast<ExamSection>(),
      instances: (fields[6] as List).cast<ExamInstance>(),
      createdAt: fields[7] as DateTime,
      updatedAt: fields[8] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, ExamTemplate obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.subjectId)
      ..writeByte(3)
      ..write(obj.totalScore)
      ..writeByte(4)
      ..write(obj.duration)
      ..writeByte(5)
      ..write(obj.sections)
      ..writeByte(6)
      ..write(obj.instances)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ExamTemplateAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ExamNumberingModeAdapter extends TypeAdapter<ExamNumberingMode> {
  @override
  final int typeId = 23;

  @override
  ExamNumberingMode read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ExamNumberingMode.year;
      case 1:
        return ExamNumberingMode.custom;
      default:
        return ExamNumberingMode.year;
    }
  }

  @override
  void write(BinaryWriter writer, ExamNumberingMode obj) {
    switch (obj) {
      case ExamNumberingMode.year:
        writer.writeByte(0);
        break;
      case ExamNumberingMode.custom:
        writer.writeByte(1);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ExamNumberingModeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
