import 'package:flutter/material.dart';
import 'package:hive/hive.dart';

part 'exam_template_models.g.dart';

@HiveType(typeId: 23)
/// 编号模式枚举
enum ExamNumberingMode {
  @HiveField(0)
  /// 年份模式（如"2022"）
  year,

  @HiveField(1)
  /// 自定义编号模式
  custom,
}

@HiveType(typeId: 24)
/// 考试题型模块
class ExamSection {
  @HiveField(0)
  /// 唯一标识符
  final String id;

  @HiveField(1)
  /// 题型名称（如"阅读理解"、"完形填空"等）
  final String name;

  @HiveField(2)
  /// 题型分值
  final int score;

  @HiveField(3)
  /// 题型预计用时（分钟）
  final int estimatedTime;

  @HiveField(4)
  /// 所属模板ID
  final String templateId;

  ExamSection({
    required this.id,
    required this.name,
    required this.score,
    required this.estimatedTime,
    required this.templateId,
  });

  /// 从JSON创建
  factory ExamSection.fromJson(Map<String, dynamic> json) {
    return ExamSection(
      id: json['id'] as String,
      name: json['name'] as String,
      score: json['score'] as int,
      estimatedTime: json['estimatedTime'] as int,
      templateId: json['templateId'] as String,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'score': score,
      'estimatedTime': estimatedTime,
      'templateId': templateId,
    };
  }

  /// 创建副本并更新属性
  ExamSection copyWith({
    String? id,
    String? name,
    int? score,
    int? estimatedTime,
    String? templateId,
  }) {
    return ExamSection(
      id: id ?? this.id,
      name: name ?? this.name,
      score: score ?? this.score,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      templateId: templateId ?? this.templateId,
    );
  }
}

@HiveType(typeId: 25)
/// TimeOfDay Hive 适配器
class TimeOfDayHive {
  @HiveField(0)
  final int hour;

  @HiveField(1)
  final int minute;

  TimeOfDayHive({required this.hour, required this.minute});

  factory TimeOfDayHive.fromTimeOfDay(TimeOfDay time) {
    return TimeOfDayHive(hour: time.hour, minute: time.minute);
  }

  TimeOfDay toTimeOfDay() {
    return TimeOfDay(hour: hour, minute: minute);
  }
}

@HiveType(typeId: 26)
/// 考试实例
class ExamInstance {
  @HiveField(0)
  /// 唯一标识符
  final String id;

  @HiveField(1)
  /// 所属模板ID
  final String templateId;

  @HiveField(2)
  /// 编号（如"2022"或自定义编号）
  final String number;

  @HiveField(3)
  /// 编号模式
  final ExamNumberingMode numberingMode;

  @HiveField(4)
  /// 考试日期
  final DateTime? examDate;

  @HiveField(5)
  /// 考试开始时间
  final TimeOfDay? startTime;

  @HiveField(6)
  /// 考试结束时间
  final TimeOfDay? endTime;

  @HiveField(7)
  /// 自定义设置（可选）
  final Map<String, dynamic>? customSettings;

  ExamInstance({
    required this.id,
    required this.templateId,
    required this.number,
    required this.numberingMode,
    this.examDate,
    this.startTime,
    this.endTime,
    this.customSettings,
  });

  /// 从JSON创建
  factory ExamInstance.fromJson(Map<String, dynamic> json) {
    return ExamInstance(
      id: json['id'] as String,
      templateId: json['templateId'] as String,
      number: json['number'] as String,
      numberingMode: ExamNumberingMode.values[json['numberingMode'] as int],
      examDate: json['examDate'] != null
          ? DateTime.parse(json['examDate'] as String)
          : null,
      startTime: json['startTime'] != null
          ? TimeOfDay(
              hour: (json['startTime'] as Map<String, dynamic>)['hour'] as int,
              minute: (json['startTime'] as Map<String, dynamic>)['minute'] as int,
            )
          : null,
      endTime: json['endTime'] != null
          ? TimeOfDay(
              hour: (json['endTime'] as Map<String, dynamic>)['hour'] as int,
              minute: (json['endTime'] as Map<String, dynamic>)['minute'] as int,
            )
          : null,
      customSettings: json['customSettings'] as Map<String, dynamic>?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'templateId': templateId,
      'number': number,
      'numberingMode': numberingMode.index,
      'examDate': examDate?.toIso8601String(),
      'startTime': startTime != null
          ? {'hour': startTime!.hour, 'minute': startTime!.minute}
          : null,
      'endTime': endTime != null
          ? {'hour': endTime!.hour, 'minute': endTime!.minute}
          : null,
      'customSettings': customSettings,
    };
  }

  /// 创建副本并更新属性
  ExamInstance copyWith({
    String? id,
    String? templateId,
    String? number,
    ExamNumberingMode? numberingMode,
    DateTime? examDate,
    TimeOfDay? startTime,
    TimeOfDay? endTime,
    Map<String, dynamic>? customSettings,
  }) {
    return ExamInstance(
      id: id ?? this.id,
      templateId: templateId ?? this.templateId,
      number: number ?? this.number,
      numberingMode: numberingMode ?? this.numberingMode,
      examDate: examDate ?? this.examDate,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      customSettings: customSettings ?? this.customSettings,
    );
  }

  /// 格式化考试时间范围
  String get formattedTimeRange {
    if (startTime == null || endTime == null) {
      return '时间未设置';
    }

    String formatTime(TimeOfDay time) {
      final hour = time.hour.toString().padLeft(2, '0');
      final minute = time.minute.toString().padLeft(2, '0');
      return '$hour:$minute';
    }

    return '${formatTime(startTime!)} - ${formatTime(endTime!)}';
  }

  /// 格式化考试日期
  String get formattedDate {
    if (examDate == null) {
      return '日期未设置';
    }

    final year = examDate!.year;
    final month = examDate!.month.toString().padLeft(2, '0');
    final day = examDate!.day.toString().padLeft(2, '0');

    return '$year-$month-$day';
  }
}

@HiveType(typeId: 27)
/// 考试模板
class ExamTemplate {
  @HiveField(0)
  /// 唯一标识符
  final String id;

  @HiveField(1)
  /// 考试名称（如"考研英语一"）
  final String name;

  @HiveField(2)
  /// 关联科目ID（可选）
  final String? subjectId;

  @HiveField(3)
  /// 总分值
  final int totalScore;

  @HiveField(4)
  /// 考试时长（分钟）
  final int duration;

  @HiveField(5)
  /// 题型模块列表
  final List<ExamSection> sections;

  @HiveField(6)
  /// 考试实例列表
  final List<ExamInstance> instances;

  @HiveField(7)
  /// 创建时间
  final DateTime createdAt;

  @HiveField(8)
  /// 最后修改时间
  final DateTime updatedAt;

  ExamTemplate({
    required this.id,
    required this.name,
    this.subjectId,
    required this.totalScore,
    required this.duration,
    required this.sections,
    required this.instances,
    required this.createdAt,
    required this.updatedAt,
  });

  /// 从JSON创建
  factory ExamTemplate.fromJson(Map<String, dynamic> json) {
    return ExamTemplate(
      id: json['id'] as String,
      name: json['name'] as String,
      subjectId: json['subjectId'] as String?,
      totalScore: json['totalScore'] as int,
      duration: json['duration'] as int,
      sections: (json['sections'] as List)
          .map((e) => ExamSection.fromJson(e as Map<String, dynamic>))
          .toList(),
      instances: (json['instances'] as List)
          .map((e) => ExamInstance.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'subjectId': subjectId,
      'totalScore': totalScore,
      'duration': duration,
      'sections': sections.map((e) => e.toJson()).toList(),
      'instances': instances.map((e) => e.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// 创建副本并更新属性
  ExamTemplate copyWith({
    String? id,
    String? name,
    String? subjectId,
    int? totalScore,
    int? duration,
    List<ExamSection>? sections,
    List<ExamInstance>? instances,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ExamTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      subjectId: subjectId ?? this.subjectId,
      totalScore: totalScore ?? this.totalScore,
      duration: duration ?? this.duration,
      sections: sections ?? this.sections,
      instances: instances ?? this.instances,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// 计算所有题型的总分
  int get calculatedTotalScore {
    return sections.fold(0, (sum, section) => sum + section.score);
  }

  /// 计算所有题型的总预计用时
  int get calculatedTotalTime {
    return sections.fold(0, (sum, section) => sum + section.estimatedTime);
  }

  /// 检查总分是否与题型分值总和匹配
  bool get isScoreBalanced {
    return totalScore == calculatedTotalScore;
  }

  /// 检查考试时长是否与题型预计用时总和匹配
  bool get isTimeBalanced {
    return duration == calculatedTotalTime;
  }
}
