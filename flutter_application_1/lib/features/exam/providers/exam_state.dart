// 该文件定义了模拟考试相关的状态管理Provider

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../models/exam_template_models.dart';

part 'exam_state.freezed.dart';
part 'exam_state.g.dart';

// 模拟考试状态数据类
@freezed
class ExamState with _$ExamState {
  const factory ExamState({
    @Default([]) List<ExamTemplate> templates,
    ExamTemplate? currentTemplate,
    ExamInstance? currentInstance,
    @Default(false) bool isLoading,
    String? error,
  }) = _ExamState;

  factory ExamState.fromJson(Map<String, dynamic> json) =>
      _$ExamStateFromJson(json);
}

// 模拟考试状态Provider
final examStateProvider = StateNotifierProvider<ExamStateNotifier, ExamState>(
  (ref) => ExamStateNotifier(),
);

// 模拟考试状态管理类
class ExamStateNotifier extends StateNotifier<ExamState> {
  ExamStateNotifier() : super(const ExamState());

  // 设置加载状态
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  // 设置错误信息
  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  // 设置所有模板
  void setTemplates(List<ExamTemplate> templates) {
    state = state.copyWith(templates: templates);
  }

  // 设置当前模板
  void setCurrentTemplate(ExamTemplate? template) {
    state = state.copyWith(currentTemplate: template);
  }

  // 设置当前实例
  void setCurrentInstance(ExamInstance? instance) {
    state = state.copyWith(currentInstance: instance);
  }

  // 添加模板
  void addTemplate(ExamTemplate template) {
    final updatedTemplates = [...state.templates, template];
    state = state.copyWith(templates: updatedTemplates);
  }

  // 更新模板
  void updateTemplate(ExamTemplate template) {
    final updatedTemplates = state.templates.map((t) {
      if (t.id == template.id) {
        return template;
      }
      return t;
    }).toList();

    state = state.copyWith(
      templates: updatedTemplates,
      currentTemplate: state.currentTemplate?.id == template.id
          ? template
          : state.currentTemplate,
    );
  }

  // 删除模板
  void deleteTemplate(String id) {
    final updatedTemplates = state.templates
        .where((template) => template.id != id)
        .toList();

    state = state.copyWith(
      templates: updatedTemplates,
      currentTemplate: state.currentTemplate?.id == id
          ? null
          : state.currentTemplate,
    );
  }

  // 添加题型模块
  void addSection(ExamSection section) {
    if (state.currentTemplate == null) return;

    final updatedSections = [
      ...state.currentTemplate!.sections,
      section,
    ];

    final updatedTemplate = state.currentTemplate!.copyWith(
      sections: updatedSections,
      updatedAt: DateTime.now(),
    );

    updateTemplate(updatedTemplate);
  }

  // 更新题型模块
  void updateSection(ExamSection section) {
    if (state.currentTemplate == null) return;

    final updatedSections = state.currentTemplate!.sections.map((s) {
      if (s.id == section.id) {
        return section;
      }
      return s;
    }).toList();

    final updatedTemplate = state.currentTemplate!.copyWith(
      sections: updatedSections,
      updatedAt: DateTime.now(),
    );

    updateTemplate(updatedTemplate);
  }

  // 删除题型模块
  void deleteSection(String id) {
    if (state.currentTemplate == null) return;

    final updatedSections = state.currentTemplate!.sections
        .where((section) => section.id != id)
        .toList();

    final updatedTemplate = state.currentTemplate!.copyWith(
      sections: updatedSections,
      updatedAt: DateTime.now(),
    );

    updateTemplate(updatedTemplate);
  }

  // 添加考试实例
  void addInstance(ExamInstance instance) {
    if (state.currentTemplate == null) return;

    final updatedInstances = [
      ...state.currentTemplate!.instances,
      instance,
    ];

    final updatedTemplate = state.currentTemplate!.copyWith(
      instances: updatedInstances,
      updatedAt: DateTime.now(),
    );

    updateTemplate(updatedTemplate);
  }

  // 更新考试实例
  void updateInstance(ExamInstance instance) {
    if (state.currentTemplate == null) return;

    final updatedInstances = state.currentTemplate!.instances.map((i) {
      if (i.id == instance.id) {
        return instance;
      }
      return i;
    }).toList();

    final updatedTemplate = state.currentTemplate!.copyWith(
      instances: updatedInstances,
      updatedAt: DateTime.now(),
    );

    updateTemplate(updatedTemplate);
    
    // 如果当前实例是被更新的实例，也更新当前实例
    if (state.currentInstance?.id == instance.id) {
      state = state.copyWith(currentInstance: instance);
    }
  }

  // 删除考试实例
  void deleteInstance(String id) {
    if (state.currentTemplate == null) return;

    final updatedInstances = state.currentTemplate!.instances
        .where((instance) => instance.id != id)
        .toList();

    final updatedTemplate = state.currentTemplate!.copyWith(
      instances: updatedInstances,
      updatedAt: DateTime.now(),
    );

    updateTemplate(updatedTemplate);
    
    // 如果当前实例是被删除的实例，清除当前实例
    if (state.currentInstance?.id == id) {
      state = state.copyWith(currentInstance: null);
    }
  }
}
