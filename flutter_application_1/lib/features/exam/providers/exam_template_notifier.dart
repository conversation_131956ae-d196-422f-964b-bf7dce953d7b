import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/exam_template_models.dart';
import '../services/exam_template_service.dart';

/// 模板创建参数
class TemplateCreateParams {
  final String name;
  final String? subjectId;
  final int totalScore;
  final int duration;
  final List<ExamSection> sections;
  final List<ExamInstance> instances;

  TemplateCreateParams({
    required this.name,
    this.subjectId,
    required this.totalScore,
    required this.duration,
    this.sections = const [],
    this.instances = const [],
  });
}

/// 模板操作结果
class TemplateOperationResult {
  final bool success;
  final String? errorMessage;
  final ExamTemplate? template;

  TemplateOperationResult({
    required this.success,
    this.errorMessage,
    this.template,
  });

  factory TemplateOperationResult.success(ExamTemplate template) {
    return TemplateOperationResult(
      success: true,
      template: template,
    );
  }

  factory TemplateOperationResult.error(String message) {
    return TemplateOperationResult(
      success: false,
      errorMessage: message,
    );
  }
}

/// 模板创建 Notifier
class ExamTemplateCreateNotifier extends AsyncNotifier<TemplateOperationResult?> {
  final ExamTemplateService _service = ExamTemplateService();

  @override
  Future<TemplateOperationResult?> build() async {
    // 初始状态为 null
    return null;
  }

  /// 创建新模板
  Future<void> createTemplate(TemplateCreateParams params) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      try {
        // 创建模板
        final newTemplate = await _service.createTemplate(
          name: params.name,
          subjectId: params.subjectId,
          totalScore: params.totalScore,
          duration: params.duration,
        );

        // 添加题型模块
        for (final section in params.sections) {
          final newSection = section.copyWith(
            templateId: newTemplate.id,
          );
          await _service.updateSection(newSection);
        }

        // 添加考试实例
        for (final instance in params.instances) {
          final newInstance = instance.copyWith(
            templateId: newTemplate.id,
          );
          await _service.updateInstance(newInstance);
        }

        return TemplateOperationResult.success(newTemplate);
      } catch (e) {
        debugPrint('创建模板时发生错误: $e');
        return TemplateOperationResult.error(e.toString());
      }
    });
  }

  /// 重置状态
  void reset() {
    state = const AsyncValue.data(null);
  }
}

/// 模板创建 Provider
final examTemplateCreateProvider = AsyncNotifierProvider<ExamTemplateCreateNotifier, TemplateOperationResult?>(() {
  return ExamTemplateCreateNotifier();
});
