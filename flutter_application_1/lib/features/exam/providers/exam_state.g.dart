// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'exam_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ExamStateImpl _$$ExamStateImplFromJson(Map<String, dynamic> json) =>
    _$ExamStateImpl(
      templates: (json['templates'] as List<dynamic>?)
              ?.map((e) => ExamTemplate.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      currentTemplate: json['currentTemplate'] == null
          ? null
          : ExamTemplate.fromJson(
              json['currentTemplate'] as Map<String, dynamic>),
      currentInstance: json['currentInstance'] == null
          ? null
          : ExamInstance.fromJson(
              json['currentInstance'] as Map<String, dynamic>),
      isLoading: json['isLoading'] as bool? ?? false,
      error: json['error'] as String?,
    );

Map<String, dynamic> _$$ExamStateImplToJson(_$ExamStateImpl instance) =>
    <String, dynamic>{
      'templates': instance.templates.map((e) => e.toJson()).toList(),
      'currentTemplate': instance.currentTemplate?.toJson(),
      'currentInstance': instance.currentInstance?.toJson(),
      'isLoading': instance.isLoading,
      'error': instance.error,
    };
