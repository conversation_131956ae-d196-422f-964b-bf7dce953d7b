// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'exam_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ExamState _$ExamStateFromJson(Map<String, dynamic> json) {
  return _ExamState.fromJson(json);
}

/// @nodoc
mixin _$ExamState {
  List<ExamTemplate> get templates => throw _privateConstructorUsedError;
  ExamTemplate? get currentTemplate => throw _privateConstructorUsedError;
  ExamInstance? get currentInstance => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  /// Serializes this ExamState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ExamState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ExamStateCopyWith<ExamState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExamStateCopyWith<$Res> {
  factory $ExamStateCopyWith(ExamState value, $Res Function(ExamState) then) =
      _$ExamStateCopyWithImpl<$Res, ExamState>;
  @useResult
  $Res call(
      {List<ExamTemplate> templates,
      ExamTemplate? currentTemplate,
      ExamInstance? currentInstance,
      bool isLoading,
      String? error});
}

/// @nodoc
class _$ExamStateCopyWithImpl<$Res, $Val extends ExamState>
    implements $ExamStateCopyWith<$Res> {
  _$ExamStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ExamState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? templates = null,
    Object? currentTemplate = freezed,
    Object? currentInstance = freezed,
    Object? isLoading = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      templates: null == templates
          ? _value.templates
          : templates // ignore: cast_nullable_to_non_nullable
              as List<ExamTemplate>,
      currentTemplate: freezed == currentTemplate
          ? _value.currentTemplate
          : currentTemplate // ignore: cast_nullable_to_non_nullable
              as ExamTemplate?,
      currentInstance: freezed == currentInstance
          ? _value.currentInstance
          : currentInstance // ignore: cast_nullable_to_non_nullable
              as ExamInstance?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ExamStateImplCopyWith<$Res>
    implements $ExamStateCopyWith<$Res> {
  factory _$$ExamStateImplCopyWith(
          _$ExamStateImpl value, $Res Function(_$ExamStateImpl) then) =
      __$$ExamStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<ExamTemplate> templates,
      ExamTemplate? currentTemplate,
      ExamInstance? currentInstance,
      bool isLoading,
      String? error});
}

/// @nodoc
class __$$ExamStateImplCopyWithImpl<$Res>
    extends _$ExamStateCopyWithImpl<$Res, _$ExamStateImpl>
    implements _$$ExamStateImplCopyWith<$Res> {
  __$$ExamStateImplCopyWithImpl(
      _$ExamStateImpl _value, $Res Function(_$ExamStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ExamState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? templates = null,
    Object? currentTemplate = freezed,
    Object? currentInstance = freezed,
    Object? isLoading = null,
    Object? error = freezed,
  }) {
    return _then(_$ExamStateImpl(
      templates: null == templates
          ? _value._templates
          : templates // ignore: cast_nullable_to_non_nullable
              as List<ExamTemplate>,
      currentTemplate: freezed == currentTemplate
          ? _value.currentTemplate
          : currentTemplate // ignore: cast_nullable_to_non_nullable
              as ExamTemplate?,
      currentInstance: freezed == currentInstance
          ? _value.currentInstance
          : currentInstance // ignore: cast_nullable_to_non_nullable
              as ExamInstance?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ExamStateImpl with DiagnosticableTreeMixin implements _ExamState {
  const _$ExamStateImpl(
      {final List<ExamTemplate> templates = const [],
      this.currentTemplate,
      this.currentInstance,
      this.isLoading = false,
      this.error})
      : _templates = templates;

  factory _$ExamStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$ExamStateImplFromJson(json);

  final List<ExamTemplate> _templates;
  @override
  @JsonKey()
  List<ExamTemplate> get templates {
    if (_templates is EqualUnmodifiableListView) return _templates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_templates);
  }

  @override
  final ExamTemplate? currentTemplate;
  @override
  final ExamInstance? currentInstance;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? error;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ExamState(templates: $templates, currentTemplate: $currentTemplate, currentInstance: $currentInstance, isLoading: $isLoading, error: $error)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ExamState'))
      ..add(DiagnosticsProperty('templates', templates))
      ..add(DiagnosticsProperty('currentTemplate', currentTemplate))
      ..add(DiagnosticsProperty('currentInstance', currentInstance))
      ..add(DiagnosticsProperty('isLoading', isLoading))
      ..add(DiagnosticsProperty('error', error));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExamStateImpl &&
            const DeepCollectionEquality()
                .equals(other._templates, _templates) &&
            (identical(other.currentTemplate, currentTemplate) ||
                other.currentTemplate == currentTemplate) &&
            (identical(other.currentInstance, currentInstance) ||
                other.currentInstance == currentInstance) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_templates),
      currentTemplate,
      currentInstance,
      isLoading,
      error);

  /// Create a copy of ExamState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ExamStateImplCopyWith<_$ExamStateImpl> get copyWith =>
      __$$ExamStateImplCopyWithImpl<_$ExamStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ExamStateImplToJson(
      this,
    );
  }
}

abstract class _ExamState implements ExamState {
  const factory _ExamState(
      {final List<ExamTemplate> templates,
      final ExamTemplate? currentTemplate,
      final ExamInstance? currentInstance,
      final bool isLoading,
      final String? error}) = _$ExamStateImpl;

  factory _ExamState.fromJson(Map<String, dynamic> json) =
      _$ExamStateImpl.fromJson;

  @override
  List<ExamTemplate> get templates;
  @override
  ExamTemplate? get currentTemplate;
  @override
  ExamInstance? get currentInstance;
  @override
  bool get isLoading;
  @override
  String? get error;

  /// Create a copy of ExamState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ExamStateImplCopyWith<_$ExamStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
