import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:io';
import '../../../core/config/sandbox_config.dart';
import '../../../core/providers/subscription_provider.dart';

/// 沙盒测试诊断页面
/// 用于排查沙盒测试相关问题
class SandboxDiagnosticScreen extends ConsumerStatefulWidget {
  const SandboxDiagnosticScreen({super.key});

  @override
  ConsumerState<SandboxDiagnosticScreen> createState() => _SandboxDiagnosticScreenState();
}

class _SandboxDiagnosticScreenState extends ConsumerState<SandboxDiagnosticScreen> {
  Map<String, dynamic> _diagnosticResults = {};
  bool _isRunning = false;

  @override
  void initState() {
    super.initState();
    _runDiagnostics();
  }

  Future<void> _runDiagnostics() async {
    setState(() {
      _isRunning = true;
      _diagnosticResults = {};
    });

    try {
      final results = <String, dynamic>{};

      // 1. 基础环境检查
      results['platform'] = {
        'isIOS': Platform.isIOS,
        'isDebugMode': SandboxConfig.isSandboxMode,
        'environment': SandboxConfig.environmentDescription,
      };

      // 2. 沙盒配置检查
      results['sandboxConfig'] = SandboxConfig.checkSandboxConfiguration();

      // 3. Apple订阅服务状态
      try {
        final appleService = ref.read(appleSubscriptionServiceProvider);
        results['appleService'] = {
          'isInitialized': appleService.isInitialized,
          'availableProducts': appleService.availableProductsCount,
        };
      } catch (e) {
        results['appleService'] = {
          'error': 'Apple服务访问失败: $e',
          'isInitialized': false,
          'availableProducts': 0,
        };
      }

      // 4. 产品配置检查
      try {
        final appleService = ref.read(appleSubscriptionServiceProvider);
        final products = await appleService.getSubscriptionProducts().timeout(
          const Duration(seconds: 10),
          onTimeout: () => [],
        );
        results['products'] = {
          'count': products.length,
          'productIds': products.map((p) => p['id'] ?? 'unknown').toList(),
          'details': products,
        };
      } catch (e) {
        debugPrint('产品加载失败: $e');
        results['products'] = {
          'error': '产品加载失败: $e',
          'count': 0,
          'productIds': [],
        };
      }

      // 5. 网络连接检查
      results['network'] = await _checkNetworkConnectivity();

      // 6. 设备信息
      results['device'] = await _getDeviceInfo();

      setState(() {
        _diagnosticResults = results;
        _isRunning = false;
      });
    } catch (e) {
      setState(() {
        _diagnosticResults = {'error': e.toString()};
        _isRunning = false;
      });
    }
  }

  Future<Map<String, dynamic>> _checkNetworkConnectivity() async {
    try {
      final result = await Process.run('ping', ['-c', '1', 'apple.com']);
      return {
        'canReachApple': result.exitCode == 0,
        'details': result.exitCode == 0 ? 'Connected' : 'Failed to reach apple.com',
      };
    } catch (e) {
      return {
        'canReachApple': false,
        'details': 'Network check failed: $e',
      };
    }
  }

  Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      return {
        'isSimulator': await _isSimulator(),
        'bundleId': _getBundleId(),
        'platform': Platform.operatingSystem,
        'isIOS': Platform.isIOS,
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'bundleId': 'com.arborflame.limefocus',
        'platform': Platform.operatingSystem,
        'isIOS': Platform.isIOS,
      };
    }
  }

  Future<bool> _isSimulator() async {
    try {
      // 简化模拟器检测，避免使用可能导致闪退的MethodChannel
      if (!Platform.isIOS) return false;

      // 在真机上，这些环境变量通常不存在
      // 在模拟器上，可能存在特定的环境标识
      return Platform.environment.containsKey('SIMULATOR_DEVICE_NAME') ||
             Platform.environment.containsKey('SIMULATOR_ROOT');
    } catch (e) {
      debugPrint('检测模拟器失败: $e');
      return false;
    }
  }

  String _getBundleId() {
    // 直接返回已知的Bundle ID，避免调用可能不稳定的原生方法
    return 'com.arborflame.limefocus';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('沙盒测试诊断'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _runDiagnostics,
          ),
        ],
      ),
      body: _isRunning
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在运行诊断...'),
                ],
              ),
            )
          : _buildDiagnosticResults(),
    );
  }

  Widget _buildDiagnosticResults() {
    if (_diagnosticResults.isEmpty) {
      return const Center(
        child: Text('暂无诊断结果'),
      );
    }

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionCard('环境信息', _diagnosticResults['platform']),
        _buildSectionCard('沙盒配置', _diagnosticResults['sandboxConfig']),
        _buildSectionCard('Apple服务', _diagnosticResults['appleService']),
        _buildSectionCard('产品配置', _diagnosticResults['products']),
        _buildSectionCard('网络连接', _diagnosticResults['network']),
        _buildSectionCard('设备信息', _diagnosticResults['device']),
        const SizedBox(height: 16),
        _buildTroubleshootingCard(),
        const SizedBox(height: 16),
        _buildTestActionsCard(),
      ],
    );
  }

  Widget _buildSectionCard(String title, dynamic data) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            _buildDataDisplay(data),
          ],
        ),
      ),
    );
  }

  Widget _buildDataDisplay(dynamic data) {
    if (data == null) {
      return const Text('无数据');
    }

    if (data is Map<String, dynamic>) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: data.entries.map((entry) {
          final value = entry.value;
          final color = _getStatusColor(entry.key, value);

          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              children: [
                Icon(
                  _getStatusIcon(entry.key, value),
                  size: 16,
                  color: color,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '${entry.key}: ${_formatValue(value)}',
                    style: TextStyle(color: color),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      );
    }

    return Text(_formatValue(data));
  }

  Color _getStatusColor(String key, dynamic value) {
    if (value is bool) {
      return value ? Colors.green : Colors.red;
    }
    if (key.contains('error') || key.contains('Error')) {
      return Colors.red;
    }
    return Theme.of(context).textTheme.bodyMedium?.color ?? Colors.black;
  }

  IconData _getStatusIcon(String key, dynamic value) {
    if (value is bool) {
      return value ? Icons.check_circle : Icons.error;
    }
    if (key.contains('error') || key.contains('Error')) {
      return Icons.error;
    }
    return Icons.info;
  }

  String _formatValue(dynamic value) {
    if (value is List) {
      return value.join(', ');
    }
    return value.toString();
  }

  Widget _buildTroubleshootingCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '常见问题排查',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text('1. 确保在真机上测试（不是模拟器）'),
            const Text('2. 在设备设置中退出真实Apple ID'),
            const Text('3. 使用专门的沙盒测试账号'),
            const Text('4. 确保产品在App Store Connect中已配置'),
            const Text('5. 检查Bundle ID是否匹配'),
            const Text('6. 确保网络连接正常'),
          ],
        ),
      ),
    );
  }

  Widget _buildTestActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '测试操作',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _testProductLoading,
              child: const Text('测试产品加载'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _copyDiagnosticInfo,
              child: const Text('复制诊断信息'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testProductLoading() async {
    // 显示加载对话框
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          title: Text('测试中'),
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在加载产品...'),
            ],
          ),
        ),
      );
    }

    try {
      final appleService = ref.read(appleSubscriptionServiceProvider);

      // 添加超时保护，避免无限等待
      final products = await appleService.getSubscriptionProducts().timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          debugPrint('产品加载超时');
          return [];
        },
      );

      if (mounted) {
        Navigator.of(context).pop(); // 关闭加载对话框

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('产品加载测试结果'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('成功加载 ${products.length} 个产品'),
                if (products.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  const Text('产品列表:'),
                  ...products.map((p) => Text('• ${p['id']}: ${p['title']}')),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('确定'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      debugPrint('产品加载测试失败: $e');

      if (mounted) {
        Navigator.of(context).pop(); // 关闭加载对话框

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('产品加载失败'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('产品加载测试失败'),
                const SizedBox(height: 8),
                Text('错误信息: $e'),
                const SizedBox(height: 8),
                const Text('可能原因:'),
                const Text('• Apple服务未初始化'),
                const Text('• 网络连接问题'),
                const Text('• App Store Connect配置问题'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('确定'),
              ),
            ],
          ),
        );
      }
    }
  }

  void _copyDiagnosticInfo() {
    final info = _diagnosticResults.toString();
    Clipboard.setData(ClipboardData(text: info));

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('诊断信息已复制到剪贴板'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
