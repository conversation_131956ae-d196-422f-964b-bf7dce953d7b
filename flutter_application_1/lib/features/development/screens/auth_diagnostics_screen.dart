import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/utils/network_diagnostics.dart';
import '../../../core/services/api_client.dart';
import '../../../shared/theme/constants.dart';

/// 认证诊断页面
/// 用于诊断登录注册相关问题
class AuthDiagnosticsScreen extends StatefulWidget {
  const AuthDiagnosticsScreen({super.key});

  @override
  State<AuthDiagnosticsScreen> createState() => _AuthDiagnosticsScreenState();
}

class _AuthDiagnosticsScreenState extends State<AuthDiagnosticsScreen> {
  bool _isRunning = false;
  String _diagnosticReport = '';
  Map<String, dynamic>? _testResults;

  @override
  void initState() {
    super.initState();
    _runDiagnostics();
  }

  Future<void> _runDiagnostics() async {
    setState(() {
      _isRunning = true;
      _diagnosticReport = '正在运行诊断...';
    });

    try {
      // 运行网络诊断
      final results = await NetworkDiagnostics.testConnection();
      
      // 测试认证API
      final authResults = await NetworkDiagnostics.testLoginApi(ApiClient.baseUrl);
      
      setState(() {
        _testResults = {
          'network': results,
          'auth': authResults,
        };
      });

      // 生成诊断报告
      final report = await _generateDetailedReport(results, authResults);
      
      setState(() {
        _diagnosticReport = report;
        _isRunning = false;
      });
    } catch (e) {
      setState(() {
        _diagnosticReport = '诊断过程中发生错误: $e';
        _isRunning = false;
      });
    }
  }

  Future<String> _generateDetailedReport(
    Map<String, dynamic> networkResults,
    Map<String, dynamic> authResults,
  ) async {
    final buffer = StringBuffer();
    
    buffer.writeln('=== LimeFocus 认证问题诊断报告 ===');
    buffer.writeln('时间: ${DateTime.now()}');
    buffer.writeln('API地址: ${ApiClient.baseUrl}');
    buffer.writeln();

    // 网络连接状态
    buffer.writeln('📡 网络连接状态:');
    buffer.writeln('• 互联网连接: ${networkResults['internet'] ? '✅ 正常' : '❌ 异常'}');
    
    final production = networkResults['production'] as Map<String, dynamic>;
    buffer.writeln('• 生产服务器: ${production['connected'] ? '✅ 连接成功' : '❌ 连接失败'}');
    if (production['connected']) {
      buffer.writeln('  状态码: ${production['statusCode']}');
    } else {
      buffer.writeln('  错误: ${production['error']}');
    }
    buffer.writeln();

    // 认证API状态
    buffer.writeln('🔐 认证API状态:');
    final verifyCode = authResults['verifyCode'] as Map<String, dynamic>;
    buffer.writeln('• 验证码API: ${verifyCode['connected'] ? '✅ 可访问' : '❌ 不可访问'}');
    buffer.writeln('  状态码: ${verifyCode['statusCode']}');
    buffer.writeln('  消息: ${verifyCode['message']}');
    
    if (verifyCode['data'] != null) {
      final data = verifyCode['data'];
      buffer.writeln('  响应数据: $data');
    }
    
    final login = authResults['login'] as Map<String, dynamic>;
    buffer.writeln('• 登录API: ${login['connected'] ? '✅ 可访问' : '❌ 不可访问'}');
    buffer.writeln('  状态码: ${login['statusCode']}');
    buffer.writeln();

    // 问题分析
    buffer.writeln('🔍 问题分析:');
    
    if (!networkResults['internet']) {
      buffer.writeln('❌ 网络连接问题 - 请检查网络设置');
    } else if (!production['connected']) {
      buffer.writeln('❌ 服务器连接问题 - 服务器可能暂时不可用');
    } else if (verifyCode['statusCode'] == 400) {
      buffer.writeln('⚠️  邮件服务异常 - 后端邮件服务配置有问题');
      buffer.writeln('   这是导致注册失败的主要原因');
    } else if (verifyCode['statusCode'] == 429) {
      buffer.writeln('⚠️  请求频率限制 - 验证码发送过于频繁');
    } else {
      buffer.writeln('✅ 基础连接正常 - 可能是临时性问题');
    }
    buffer.writeln();

    // 解决建议
    buffer.writeln('💡 解决建议:');
    
    if (verifyCode['statusCode'] == 400) {
      buffer.writeln('1. 联系后端开发者检查邮件服务配置');
      buffer.writeln('2. 确认SMTP服务器设置正确');
      buffer.writeln('3. 检查邮件服务API密钥是否有效');
      buffer.writeln('4. 暂时可以使用测试模式进行开发');
    } else if (verifyCode['statusCode'] == 429) {
      buffer.writeln('1. 等待一段时间后重试');
      buffer.writeln('2. 避免频繁发送验证码');
    } else if (!production['connected']) {
      buffer.writeln('1. 检查服务器状态');
      buffer.writeln('2. 确认域名解析正常');
      buffer.writeln('3. 检查防火墙设置');
    } else {
      buffer.writeln('1. 尝试重新启动应用');
      buffer.writeln('2. 检查设备网络设置');
      buffer.writeln('3. 如问题持续，请联系技术支持');
    }
    buffer.writeln();

    // 模拟器特殊说明
    buffer.writeln('📱 模拟器注意事项:');
    buffer.writeln('• Apple登录在模拟器上可能无法正常工作');
    buffer.writeln('• 建议在真机上测试Apple登录功能');
    buffer.writeln('• 邮箱注册功能不受模拟器影响');

    return buffer.toString();
  }

  void _copyReport() {
    if (_diagnosticReport.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: _diagnosticReport));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('诊断报告已复制到剪贴板'),
          backgroundColor: AppColors.success,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('认证问题诊断'),
        actions: [
          IconButton(
            onPressed: _isRunning ? null : _runDiagnostics,
            icon: const Icon(Icons.refresh),
            tooltip: '重新诊断',
          ),
          IconButton(
            onPressed: _diagnosticReport.isNotEmpty ? _copyReport : null,
            icon: const Icon(Icons.copy),
            tooltip: '复制报告',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 状态指示器
            if (_isRunning)
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Row(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(width: 16),
                      Text('正在运行诊断，请稍候...'),
                    ],
                  ),
                ),
              ),

            // 快速状态概览
            if (_testResults != null && !_isRunning)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '快速状态概览',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildStatusItem(
                        '网络连接',
                        _testResults!['network']['internet'],
                      ),
                      _buildStatusItem(
                        '服务器连接',
                        _testResults!['network']['production']['connected'],
                      ),
                      _buildStatusItem(
                        '验证码API',
                        _testResults!['auth']['verifyCode']['statusCode'] == 200,
                        subtitle: _testResults!['auth']['verifyCode']['statusCode'] == 400
                            ? '邮件服务异常'
                            : null,
                      ),
                      _buildStatusItem(
                        '登录API',
                        _testResults!['auth']['login']['connected'],
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // 详细报告
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '详细诊断报告',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Expanded(
                        child: SingleChildScrollView(
                          child: SelectableText(
                            _diagnosticReport,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String title, bool isOk, {String? subtitle}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isOk ? Icons.check_circle : Icons.error,
            color: isOk ? AppColors.success : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title),
                if (subtitle != null)
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
