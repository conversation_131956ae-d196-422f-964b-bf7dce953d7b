import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:io';
import '../../../core/config/sandbox_config.dart';

/// 安全的沙盒测试诊断页面
/// 避免可能导致闪退的原生方法调用
class SafeSandboxDiagnosticScreen extends ConsumerStatefulWidget {
  const SafeSandboxDiagnosticScreen({super.key});

  @override
  ConsumerState<SafeSandboxDiagnosticScreen> createState() => _SafeSandboxDiagnosticScreenState();
}

class _SafeSandboxDiagnosticScreenState extends ConsumerState<SafeSandboxDiagnosticScreen> {
  Map<String, dynamic> _diagnosticResults = {};
  bool _isRunning = false;

  @override
  void initState() {
    super.initState();
    _runSafeDiagnostics();
  }

  Future<void> _runSafeDiagnostics() async {
    setState(() {
      _isRunning = true;
      _diagnosticResults = {};
    });

    try {
      final results = <String, dynamic>{};

      // 1. 基础环境检查（安全）
      results['platform'] = {
        'isIOS': Platform.isIOS,
        'operatingSystem': Platform.operatingSystem,
        'isDebugMode': SandboxConfig.isSandboxMode,
        'environment': SandboxConfig.environmentDescription,
      };

      // 2. 沙盒配置检查（安全）
      results['sandboxConfig'] = SandboxConfig.checkSandboxConfiguration();

      // 3. 设备信息（安全，不调用原生方法）
      results['device'] = {
        'bundleId': 'com.arborflame.limefocus',
        'platform': Platform.operatingSystem,
        'version': Platform.operatingSystemVersion,
        'isIOS': Platform.isIOS,
      };

      // 4. 网络连接检查（简化版）
      results['network'] = await _checkBasicConnectivity();

      // 5. 沙盒测试指导
      results['guidance'] = _getSandboxTestingGuidance();

      setState(() {
        _diagnosticResults = results;
        _isRunning = false;
      });
    } catch (e) {
      setState(() {
        _diagnosticResults = {'error': '诊断过程中发生错误: $e'};
        _isRunning = false;
      });
    }
  }

  Future<Map<String, dynamic>> _checkBasicConnectivity() async {
    try {
      // 简化的网络检查，避免复杂的原生调用
      return {
        'status': '网络检查已简化',
        'note': '请手动确认设备网络连接正常',
        'canReachInternet': true, // 假设网络正常
      };
    } catch (e) {
      return {
        'status': '网络检查失败',
        'error': e.toString(),
      };
    }
  }

  Map<String, dynamic> _getSandboxTestingGuidance() {
    return {
      'steps': [
        '1. 在App Store Connect中创建沙盒测试账号',
        '2. 在设备设置中完全退出真实Apple ID',
        '3. 确保在真机上测试（不是模拟器）',
        '4. 进入订阅界面尝试购买',
        '5. 使用沙盒测试账号登录',
      ],
      'commonIssues': [
        '账号或密码错误 → 检查沙盒账号是否正确创建',
        'No active account → 设备上没有登录沙盒账号',
        '产品加载失败 → 检查App Store Connect配置',
        '网络连接失败 → 检查设备网络连接',
      ],
      'troubleshooting': [
        '重启设备清除Apple ID缓存',
        '确认Bundle ID匹配',
        '等待24小时让产品配置生效',
        '使用全新的邮箱创建沙盒账号',
      ],
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('安全沙盒诊断'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _runSafeDiagnostics,
          ),
        ],
      ),
      body: _isRunning
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在运行安全诊断...'),
                ],
              ),
            )
          : _buildDiagnosticResults(),
    );
  }

  Widget _buildDiagnosticResults() {
    if (_diagnosticResults.isEmpty) {
      return const Center(
        child: Text('暂无诊断结果'),
      );
    }

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildInfoCard(),
        _buildSectionCard('环境信息', _diagnosticResults['platform']),
        _buildSectionCard('沙盒配置', _diagnosticResults['sandboxConfig']),
        _buildSectionCard('设备信息', _diagnosticResults['device']),
        _buildSectionCard('网络状态', _diagnosticResults['network']),
        _buildGuidanceCard(),
        const SizedBox(height: 16),
        _buildActionsCard(),
      ],
    );
  }

  Widget _buildInfoCard() {
    return Card(
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue.shade700),
                const SizedBox(width: 8),
                Text(
                  '安全诊断模式',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              '此诊断工具避免了可能导致应用闪退的原生方法调用，'
              '提供基础的环境检查和沙盒测试指导。',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard(String title, dynamic data) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            _buildDataDisplay(data),
          ],
        ),
      ),
    );
  }

  Widget _buildDataDisplay(dynamic data) {
    if (data == null) {
      return const Text('无数据');
    }

    if (data is Map<String, dynamic>) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: data.entries.map((entry) {
          final value = entry.value;
          final color = _getStatusColor(entry.key, value);
          
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              children: [
                Icon(
                  _getStatusIcon(entry.key, value),
                  size: 16,
                  color: color,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '${entry.key}: ${_formatValue(value)}',
                    style: TextStyle(color: color),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      );
    }

    return Text(_formatValue(data));
  }

  Widget _buildGuidanceCard() {
    final guidance = _diagnosticResults['guidance'] as Map<String, dynamic>?;
    if (guidance == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '沙盒测试指导',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            const Text('测试步骤:', style: TextStyle(fontWeight: FontWeight.bold)),
            ...((guidance['steps'] as List?) ?? []).map((step) => 
              Padding(
                padding: const EdgeInsets.only(left: 8, top: 2),
                child: Text(step.toString()),
              ),
            ),
            
            const SizedBox(height: 12),
            const Text('常见问题:', style: TextStyle(fontWeight: FontWeight.bold)),
            ...((guidance['commonIssues'] as List?) ?? []).map((issue) => 
              Padding(
                padding: const EdgeInsets.only(left: 8, top: 2),
                child: Text(issue.toString()),
              ),
            ),
            
            const SizedBox(height: 12),
            const Text('故障排除:', style: TextStyle(fontWeight: FontWeight.bold)),
            ...((guidance['troubleshooting'] as List?) ?? []).map((tip) => 
              Padding(
                padding: const EdgeInsets.only(left: 8, top: 2),
                child: Text(tip.toString()),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '操作',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _copyDiagnosticInfo,
              child: const Text('复制诊断信息'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _showDetailedHelp,
              child: const Text('查看详细帮助'),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String key, dynamic value) {
    if (value is bool) {
      return value ? Colors.green : Colors.red;
    }
    if (key.contains('error') || key.contains('Error')) {
      return Colors.red;
    }
    return Theme.of(context).textTheme.bodyMedium?.color ?? Colors.black;
  }

  IconData _getStatusIcon(String key, dynamic value) {
    if (value is bool) {
      return value ? Icons.check_circle : Icons.error;
    }
    if (key.contains('error') || key.contains('Error')) {
      return Icons.error;
    }
    return Icons.info;
  }

  String _formatValue(dynamic value) {
    if (value is List) {
      return value.join(', ');
    }
    return value.toString();
  }

  void _copyDiagnosticInfo() {
    final info = _diagnosticResults.toString();
    Clipboard.setData(ClipboardData(text: info));
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('诊断信息已复制到剪贴板'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showDetailedHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('沙盒测试详细帮助'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('如果遇到"账号或密码错误"：'),
              Text('1. 确认使用的是沙盒测试账号'),
              Text('2. 检查账号密码是否正确'),
              Text('3. 确保设备已退出真实Apple ID'),
              SizedBox(height: 12),
              Text('如果遇到"No active account"：'),
              Text('1. 在设备设置中完全退出Apple ID'),
              Text('2. 重启设备清除缓存'),
              Text('3. 在购买时登录沙盒账号'),
              SizedBox(height: 12),
              Text('如果产品加载失败：'),
              Text('1. 检查App Store Connect配置'),
              Text('2. 确认Bundle ID匹配'),
              Text('3. 等待24小时让配置生效'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('知道了'),
          ),
        ],
      ),
    );
  }
}
