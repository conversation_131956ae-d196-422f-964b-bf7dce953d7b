import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'dart:io';


/// 增强的Apple诊断页面
class EnhancedAppleDiagnosticScreen extends ConsumerStatefulWidget {
  const EnhancedAppleDiagnosticScreen({super.key});

  @override
  ConsumerState<EnhancedAppleDiagnosticScreen> createState() => _EnhancedAppleDiagnosticScreenState();
}

class _EnhancedAppleDiagnosticScreenState extends ConsumerState<EnhancedAppleDiagnosticScreen> {
  String _diagnosticInfo = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _runEnhancedDiagnostic();
  }

  Future<void> _runEnhancedDiagnostic() async {
    if (!mounted) return;
    
    setState(() {
      _isLoading = true;
      _diagnosticInfo = '';
    });

    try {
      final info = StringBuffer();
      info.writeln('=== 增强Apple诊断 ===\n');
      
      // 1. 基础环境检查
      info.writeln('🔍 基础环境检查:');
      info.writeln('- 当前平台: ${Platform.operatingSystem}');
      info.writeln('- 是否iOS: ${Platform.isIOS}');
      info.writeln('- 调试模式: $kDebugMode');
      info.writeln('- 发布模式: $kReleaseMode');
      
      // 检查是否在真机上运行
      if (Platform.isIOS) {
        try {
          // 尝试获取设备信息来判断是否为真机
          final result = await Process.run('uname', ['-m']);
          final architecture = result.stdout.toString().trim();
          info.writeln('- 设备架构: $architecture');
          
          if (architecture.contains('arm') || architecture.contains('iPhone')) {
            info.writeln('- ✅ 真机环境');
          } else {
            info.writeln('- ❌ 模拟器环境（StoreKit不可用）');
          }
        } catch (e) {
          info.writeln('- ⚠️ 无法检测设备类型: $e');
        }
      }
      info.writeln('');

      // 2. StoreKit可用性检查
      info.writeln('🏪 StoreKit可用性检查:');
      try {
        final inAppPurchase = InAppPurchase.instance;
        final isAvailable = await inAppPurchase.isAvailable().timeout(
          const Duration(seconds: 10),
          onTimeout: () {
            info.writeln('- ⏰ StoreKit检查超时');
            return false;
          },
        );
        
        if (isAvailable) {
          info.writeln('- ✅ StoreKit可用');
        } else {
          info.writeln('- ❌ StoreKit不可用');
          info.writeln('  可能原因:');
          info.writeln('  • 在模拟器上运行');
          info.writeln('  • 网络连接问题');
          info.writeln('  • Apple服务器不可用');
          info.writeln('  • 设备限制设置');
        }
      } catch (e) {
        info.writeln('- ❌ StoreKit检查失败: $e');
      }
      info.writeln('');

      // 3. 网络连接详细检查
      info.writeln('🌐 网络连接详细检查:');
      
      // 检查基础网络
      try {
        final result = await InternetAddress.lookup('google.com').timeout(
          const Duration(seconds: 5),
        );
        if (result.isNotEmpty) {
          info.writeln('- ✅ 基础网络连接正常');
        } else {
          info.writeln('- ❌ 基础网络连接失败');
        }
      } catch (e) {
        info.writeln('- ❌ 基础网络测试失败: $e');
      }

      // 检查Apple服务器连接
      final appleHosts = [
        'apple.com',
        'itunes.apple.com',
        'buy.itunes.apple.com',
        'sandbox.itunes.apple.com',
      ];

      for (final host in appleHosts) {
        try {
          final result = await InternetAddress.lookup(host).timeout(
            const Duration(seconds: 5),
          );
          if (result.isNotEmpty) {
            info.writeln('- ✅ $host 可访问');
          } else {
            info.writeln('- ❌ $host 不可访问');
          }
        } catch (e) {
          info.writeln('- ❌ $host 连接失败: $e');
        }
      }
      info.writeln('');

      // 4. 产品查询详细测试
      info.writeln('📦 产品查询详细测试:');
      if (Platform.isIOS) {
        try {
          final inAppPurchase = InAppPurchase.instance;
          final productIds = {
            'LemiVip001',
            'LimeVip_quarter', 
            'LimeVip_yearly'
          };
          
          info.writeln('- 查询产品ID: ${productIds.join(", ")}');
          
          final response = await inAppPurchase.queryProductDetails(productIds).timeout(
            const Duration(seconds: 15),
            onTimeout: () {
              info.writeln('- ⏰ 产品查询超时');
              return ProductDetailsResponse(
                productDetails: [],
                notFoundIDs: productIds.toList(),
                error: null,
              );
            },
          );
          
          if (response.error != null) {
            info.writeln('- ❌ 产品查询错误: ${response.error}');
            info.writeln('  错误代码: ${response.error!.code}');
            info.writeln('  错误来源: ${response.error!.source}');
            info.writeln('  错误消息: ${response.error!.message}');
            info.writeln('  错误详情: ${response.error!.details}');
          } else {
            info.writeln('- ✅ 产品查询无错误');
          }
          
          if (response.productDetails.isNotEmpty) {
            info.writeln('- ✅ 找到 ${response.productDetails.length} 个产品:');
            for (final product in response.productDetails) {
              info.writeln('  • ${product.id}: ${product.title} - ${product.price}');
            }
          } else {
            info.writeln('- ❌ 未找到任何产品');
          }
          
          if (response.notFoundIDs.isNotEmpty) {
            info.writeln('- ⚠️ 未找到的产品ID: ${response.notFoundIDs.join(", ")}');
            info.writeln('  可能原因:');
            info.writeln('  • App Store Connect中产品未配置');
            info.writeln('  • 产品状态不是"准备提交"');
            info.writeln('  • Bundle ID不匹配');
            info.writeln('  • 产品ID拼写错误');
          }
          
        } catch (e) {
          info.writeln('- ❌ 产品查询异常: $e');
        }
      } else {
        info.writeln('- ⚠️ 非iOS平台，跳过产品查询');
      }
      info.writeln('');

      // 5. 应用配置检查
      info.writeln('⚙️ 应用配置检查:');
      info.writeln('- Bundle ID: com.arborflame.limefocus');
      info.writeln('- 开发团队: G5N2P69H35');
      info.writeln('- 产品ID配置:');
      info.writeln('  • LemiVip001 (月度订阅)');
      info.writeln('  • LimeVip_quarter (季度订阅)');
      info.writeln('  • LimeVip_yearly (年度订阅)');
      info.writeln('');

      // 6. 系统设置检查
      info.writeln('📱 系统设置检查建议:');
      info.writeln('- 检查设置 → 屏幕使用时间 → 内容和隐私访问限制');
      info.writeln('- 确保"App内购买项目"已启用');
      info.writeln('- 检查网络设置，确保无VPN干扰');
      info.writeln('- 确保Apple ID已登录且有效');
      info.writeln('');

      // 7. 故障排除建议
      info.writeln('🔧 故障排除建议:');
      info.writeln('1. 确保在真机上测试（不是模拟器）');
      info.writeln('2. 检查网络连接，关闭VPN');
      info.writeln('3. 重启设备和应用');
      info.writeln('4. 检查App Store Connect产品配置');
      info.writeln('5. 等待24小时让配置生效');
      info.writeln('6. 联系Apple开发者支持');

      if (mounted) {
        setState(() {
          _diagnosticInfo = info.toString();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _diagnosticInfo = '诊断过程中发生错误: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _testDirectStoreKitConnection() async {
    if (!mounted) return;
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('测试StoreKit连接...'),
          ],
        ),
      ),
    );

    try {
      final inAppPurchase = InAppPurchase.instance;
      
      // 测试基础可用性
      final isAvailable = await inAppPurchase.isAvailable().timeout(
        const Duration(seconds: 10),
      );
      
      String result = '';
      if (isAvailable) {
        result += '✅ StoreKit基础服务可用\n';
        
        // 测试产品查询
        final productIds = {'LemiVip001'};
        final response = await inAppPurchase.queryProductDetails(productIds).timeout(
          const Duration(seconds: 15),
        );
        
        if (response.error != null) {
          result += '❌ 产品查询失败: ${response.error!.message}\n';
          result += '错误代码: ${response.error!.code}\n';
        } else {
          result += '✅ 产品查询成功\n';
          result += '找到产品数量: ${response.productDetails.length}\n';
        }
      } else {
        result += '❌ StoreKit服务不可用\n';
        result += '可能原因: 模拟器环境或网络问题\n';
      }
      
      if (mounted) {
        Navigator.of(context).pop();
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('StoreKit连接测试结果'),
            content: Text(result),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('确定'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop();
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('StoreKit连接测试失败'),
            content: Text('错误: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('确定'),
              ),
            ],
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('增强Apple诊断'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _runEnhancedDiagnostic,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在运行增强诊断...'),
                ],
              ),
            )
          : Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[300]!),
                          ),
                          child: Text(
                            _diagnosticInfo,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                        const SizedBox(height: 24),
                        const Text(
                          '高级测试',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: _testDirectStoreKitConnection,
                            icon: const Icon(Icons.wifi_tethering),
                            label: const Text('直接测试StoreKit连接'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
