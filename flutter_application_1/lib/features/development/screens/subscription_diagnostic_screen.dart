import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:io';

import '../../../core/providers/subscription_provider.dart';


/// 订阅功能诊断页面
class SubscriptionDiagnosticScreen extends ConsumerStatefulWidget {
  const SubscriptionDiagnosticScreen({super.key});

  @override
  ConsumerState<SubscriptionDiagnosticScreen> createState() => _SubscriptionDiagnosticScreenState();
}

class _SubscriptionDiagnosticScreenState extends ConsumerState<SubscriptionDiagnosticScreen> {
  String _diagnosticInfo = '';
  bool _isLoading = false;
  List<Map<String, dynamic>> _products = [];

  @override
  void initState() {
    super.initState();
    _runDiagnostic();
  }

  Future<void> _runDiagnostic() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _diagnosticInfo = '';
    });

    try {
      final service = ref.read(appleSubscriptionServiceProvider);
      final info = StringBuffer();

      info.writeln('=== Apple订阅功能诊断 ===\n');

      // 1. 平台检查
      info.writeln('📱 平台检查:');
      info.writeln('- 当前平台: ${Platform.operatingSystem}');
      info.writeln('- 是否iOS: ${Platform.isIOS}');
      info.writeln('- 调试模式: $kDebugMode');
      info.writeln('- 发布模式: $kReleaseMode');
      info.writeln('');

      // 2. 服务状态检查
      info.writeln('🔧 服务状态:');
      info.writeln('- 服务已初始化: ${service.isInitialized}');
      info.writeln('- 可用产品数量: ${service.availableProductsCount}');
      info.writeln('');

      // 3. 产品加载测试
      info.writeln('📦 产品加载测试:');
      try {
        info.writeln('正在加载产品...');
        final products = await service.getSubscriptionProducts().timeout(
          const Duration(seconds: 15),
          onTimeout: () {
            info.writeln('⏰ 产品加载超时（15秒）');
            return [];
          },
        );

        _products = products;

        if (products.isNotEmpty) {
          info.writeln('✅ 成功加载 ${products.length} 个产品:');
          for (int i = 0; i < products.length; i++) {
            final product = products[i];
            info.writeln('  ${i + 1}. ${product['title']}');
            info.writeln('     ID: ${product['id']}');
            info.writeln('     价格: ${product['price']}');
            info.writeln('     描述: ${product['description']}');
            info.writeln('');
          }
        } else {
          info.writeln('⚠️ 未加载到任何产品');
          info.writeln('可能原因:');
          info.writeln('- App Store Connect中产品未配置');
          info.writeln('- 产品ID不匹配');
          info.writeln('- 网络连接问题');
          info.writeln('- App Store服务不可用');
        }
      } catch (e) {
        info.writeln('❌ 产品加载失败: $e');
      }
      info.writeln('');

      // 4. 订阅状态检查
      info.writeln('⭐ 订阅状态检查:');
      try {
        final isPremium = await service.isPremiumUser().timeout(
          const Duration(seconds: 10),
          onTimeout: () {
            info.writeln('⏰ 状态检查超时');
            return false;
          },
        );
        info.writeln('- 当前是高级用户: $isPremium');
      } catch (e) {
        info.writeln('❌ 状态检查失败: $e');
      }
      info.writeln('');

      // 5. App Store Connect配置检查
      info.writeln('🏪 App Store Connect配置:');
      info.writeln('预期产品ID:');
      info.writeln('- LemiVip001 (月度订阅)');
      info.writeln('- LimeVip_quarter (季度订阅)');
      info.writeln('- LimeVip_yearly (年度订阅)');
      info.writeln('');
      info.writeln('检查清单:');
      info.writeln('□ 产品已在App Store Connect中创建');
      info.writeln('□ 产品状态为"准备提交"或"等待审核"');
      info.writeln('□ Bundle ID匹配: com.arborflame.limefocus');
      info.writeln('□ 沙盒测试账号已创建');
      info.writeln('□ 设备已退出Apple ID（用于沙盒测试）');
      info.writeln('');

      // 6. 网络连接测试
      info.writeln('🌐 网络连接测试:');
      try {
        final result = await Process.run('ping', ['-c', '1', 'apple.com']);
        if (result.exitCode == 0) {
          info.writeln('✅ 网络连接正常');
        } else {
          info.writeln('❌ 网络连接异常');
        }
      } catch (e) {
        info.writeln('⚠️ 网络测试失败: $e');
      }
      info.writeln('');

      // 7. 故障排除建议
      info.writeln('🔍 故障排除建议:');
      if (_products.isEmpty) {
        info.writeln('产品加载失败的解决方案:');
        info.writeln('1. 检查App Store Connect中产品配置');
        info.writeln('2. 确认产品ID拼写正确');
        info.writeln('3. 等待24小时让配置生效');
        info.writeln('4. 检查网络连接');
        info.writeln('5. 重启设备和应用');
      } else {
        info.writeln('✅ 产品加载正常，可以进行购买测试');
      }

      if (mounted) {
        setState(() {
          _diagnosticInfo = info.toString();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _diagnosticInfo = '诊断过程中发生错误: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _testPurchase(String productId) async {
    if (!mounted) return;

    if (!Platform.isIOS) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('购买测试仅在iOS设备上可用')),
      );
      return;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('正在测试购买...'),
          ],
        ),
      ),
    );

    try {
      final service = ref.read(appleSubscriptionServiceProvider);
      await service.purchaseSubscription(
        productId,
        callback: (success, error) {
          if (!mounted) return;

          Navigator.of(context).pop(); // 关闭加载对话框

          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text(success ? '购买成功' : '购买失败'),
              content: Text(success ? '订阅已激活' : '错误: ${error ?? "未知错误"}'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('确定'),
                ),
              ],
            ),
          );
        },
      );
    } catch (e) {
      if (!mounted) return;

      Navigator.of(context).pop(); // 关闭加载对话框

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('购买测试失败'),
          content: Text('错误: $e'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('订阅功能诊断'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _runDiagnostic,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在运行诊断...'),
                ],
              ),
            )
          : Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[300]!),
                          ),
                          child: Text(
                            _diagnosticInfo,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                        if (_products.isNotEmpty) ...[
                          const SizedBox(height: 24),
                          const Text(
                            '购买测试',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ..._products.map((product) => Card(
                            margin: const EdgeInsets.only(bottom: 8),
                            child: ListTile(
                              title: Text(product['title']),
                              subtitle: Text('${product['price']} - ${product['id']}'),
                              trailing: ElevatedButton(
                                onPressed: () => _testPurchase(product['id']),
                                child: const Text('测试购买'),
                              ),
                            ),
                          )),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
