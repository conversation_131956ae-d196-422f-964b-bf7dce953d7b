import 'package:flutter/material.dart';
import '../../../core/services/test_data_generator.dart';
import '../../../core/services/hive_service.dart';
import '../../../shared/theme/constants.dart';
import '../../../shared/widgets/app_button.dart';

/// 测试数据生成器屏幕
/// 用于生成模拟专注数据，以便测试数据分析页面
class TestDataGeneratorScreen extends StatefulWidget {
  const TestDataGeneratorScreen({super.key});

  @override
  State<TestDataGeneratorScreen> createState() => _TestDataGeneratorScreenState();
}

class _TestDataGeneratorScreenState extends State<TestDataGeneratorScreen> {
  final HiveService _hiveService = HiveService();
  late TestDataGenerator _dataGenerator;

  bool _isGenerating = false;
  String _statusMessage = '';

  // 今日数据参数
  int _todayCount = 8;
  int _todayMinDuration = 20;
  int _todayMaxDuration = 90;
  double _todayCompletionRate = 0.85;

  // 本周数据参数
  int _weekCountPerDay = 5;
  int _weekMinDuration = 15;
  int _weekMaxDuration = 120;
  double _weekCompletionRate = 0.8;

  // 本月数据参数
  final int _monthCountPerDay = 3;
  final int _monthMinDuration = 15;
  final int _monthMaxDuration = 150;
  final double _monthCompletionRate = 0.75;

  // 历史数据参数
  final int _historyMonths = 6;
  final int _historyCountPerDay = 2;
  final int _historyMinDuration = 10;
  final int _historyMaxDuration = 180;
  final double _historyCompletionRate = 0.7;

  @override
  void initState() {
    super.initState();
    _dataGenerator = TestDataGenerator(_hiveService);

    // 确保Hive服务已初始化
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        await _hiveService.initHive();

        // 检查是否有科目和项目
        final hasSubjectsAndProjects = await _checkSubjectsAndProjects();
        if (!hasSubjectsAndProjects) {
          _showCreateSubjectProjectHint();
        }

        if (mounted) {
          setState(() {
            _statusMessage = '数据服务已初始化，可以开始生成测试数据';
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _statusMessage = '数据服务初始化失败: $e';
          });
        }
      }
    });
  }

  // 生成今日数据
  Future<void> _generateTodayData() async {
    setState(() {
      _isGenerating = true;
      _statusMessage = '正在生成今日数据...';
    });

    try {
      // 确保Hive服务已初始化
      await _hiveService.initHive();

      // 检查是否有科目和项目
      final hasSubjects = _hiveService.subjectRepository.getAllSubjects().isNotEmpty;
      if (!hasSubjects) {
        setState(() {
          _statusMessage = '错误: 没有可用的科目，请先创建至少一个科目和项目';
        });
        return;
      }

      debugPrint('开始生成今日数据');
      final records = await _dataGenerator.generateTodayRecords(
        count: _todayCount,
        minDuration: _todayMinDuration,
        maxDuration: _todayMaxDuration,
        completionRate: _todayCompletionRate,
      );

      if (records.isEmpty) {
        setState(() {
          _statusMessage = '生成数据失败: 无法生成有效的专注记录';
        });
        return;
      }

      debugPrint('生成了 ${records.length} 条记录，开始保存');
      await _dataGenerator.saveRecords(records);

      // 验证今日数据
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final tomorrow = today.add(const Duration(days: 1));
      final todayRecords = _hiveService.focusRecordRepository.getFocusRecordsByDateRange(today, tomorrow);

      setState(() {
        _statusMessage = '成功生成 ${records.length} 条今日数据，验证查询到 ${todayRecords.length} 条今日记录';
      });

      // 显示提示，建议用户查看数据分析页面
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('数据生成成功，建议前往数据分析页面查看'),
            action: SnackBarAction(
              label: '查看',
              onPressed: () {
                Navigator.pushNamed(context, '/data');
              },
            ),
            duration: const Duration(seconds: 10),
          ),
        );
      }
    } catch (e, stackTrace) {
      debugPrint('生成数据失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      setState(() {
        _statusMessage = '生成数据失败: $e';
      });
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  // 生成本周数据
  Future<void> _generateWeekData() async {
    setState(() {
      _isGenerating = true;
      _statusMessage = '正在生成本周数据...';
    });

    try {
      // 确保Hive服务已初始化
      await _hiveService.initHive();

      // 检查是否有科目和项目
      final hasSubjects = _hiveService.subjectRepository.getAllSubjects().isNotEmpty;
      if (!hasSubjects) {
        setState(() {
          _statusMessage = '错误: 没有可用的科目，请先创建至少一个科目和项目';
        });
        return;
      }

      final records = await _dataGenerator.generateWeekRecords(
        countPerDay: _weekCountPerDay,
        minDuration: _weekMinDuration,
        maxDuration: _weekMaxDuration,
        completionRate: _weekCompletionRate,
      );

      if (records.isEmpty) {
        setState(() {
          _statusMessage = '生成数据失败: 无法生成有效的专注记录';
        });
        return;
      }

      await _dataGenerator.saveRecords(records);

      setState(() {
        _statusMessage = '成功生成 ${records.length} 条本周数据';
      });

      // 显示提示，建议用户查看数据分析页面
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('数据生成成功，建议前往数据分析页面查看'),
            action: SnackBarAction(
              label: '查看',
              onPressed: () {
                Navigator.pushNamed(context, '/data');
              },
            ),
            duration: const Duration(seconds: 10),
          ),
        );
      }
    } catch (e, stackTrace) {
      debugPrint('生成数据失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      setState(() {
        _statusMessage = '生成数据失败: $e';
      });
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  // 生成本月数据
  Future<void> _generateMonthData() async {
    setState(() {
      _isGenerating = true;
      _statusMessage = '正在生成本月数据...';
    });

    try {
      final records = await _dataGenerator.generateMonthRecords(
        countPerDay: _monthCountPerDay,
        minDuration: _monthMinDuration,
        maxDuration: _monthMaxDuration,
        completionRate: _monthCompletionRate,
      );

      await _dataGenerator.saveRecords(records);

      setState(() {
        _statusMessage = '成功生成 ${records.length} 条本月数据';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '生成数据失败: $e';
      });
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  // 生成历史数据
  Future<void> _generateHistoryData() async {
    setState(() {
      _isGenerating = true;
      _statusMessage = '正在生成历史数据...';
    });

    try {
      final records = await _dataGenerator.generateHistoryRecords(
        months: _historyMonths,
        countPerDay: _historyCountPerDay,
        minDuration: _historyMinDuration,
        maxDuration: _historyMaxDuration,
        completionRate: _historyCompletionRate,
      );

      await _dataGenerator.saveRecords(records);

      setState(() {
        _statusMessage = '成功生成 ${records.length} 条历史数据';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '生成数据失败: $e';
      });
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  // 一键生成完整数据集
  Future<void> _generateCompleteDataSet() async {
    setState(() {
      _isGenerating = true;
      _statusMessage = '正在生成完整数据集...';
    });

    try {
      // 确保Hive服务已初始化
      await _hiveService.initHive();

      // 检查是否有科目和项目
      final hasSubjects = _hiveService.subjectRepository.getAllSubjects().isNotEmpty;
      if (!hasSubjects) {
        setState(() {
          _statusMessage = '错误: 没有可用的科目，请先创建至少一个科目和项目';
        });
        return;
      }

      final success = await _dataGenerator.generateCompleteTestDataSet();

      if (success) {
        setState(() {
          _statusMessage = '成功生成完整数据集';
        });

        // 显示提示，建议用户查看数据分析页面
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('数据生成成功，建议前往数据分析页面查看'),
              action: SnackBarAction(
                label: '查看',
                onPressed: () {
                  Navigator.pushNamed(context, '/data');
                },
              ),
              duration: const Duration(seconds: 10),
            ),
          );
        }
      } else {
        setState(() {
          _statusMessage = '生成数据失败: 无法生成有效的专注记录';
        });
      }
    } catch (e, stackTrace) {
      debugPrint('生成数据失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      setState(() {
        _statusMessage = '生成数据失败: $e';
      });
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  // 清除所有测试数据
  Future<void> _clearAllTestData() async {
    setState(() {
      _isGenerating = true;
      _statusMessage = '正在清除测试数据...';
    });

    try {
      await _dataGenerator.clearAllTestData();

      setState(() {
        _statusMessage = '成功清除所有测试数据';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '清除数据失败: $e';
      });
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  // 构建参数调整滑块
  Widget _buildSlider({
    required String label,
    required double value,
    required double min,
    required double max,
    required Function(double) onChanged,
    String? suffix,
    int? divisions,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(label, style: AppTextStyles.bodySmall),
            Text(
              suffix != null ? '${value.toStringAsFixed(1)}$suffix' : value.toStringAsFixed(1),
              style: AppTextStyles.bodySmall.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: divisions,
          activeColor: AppColors.primary,
          onChanged: onChanged,
        ),
      ],
    );
  }

  // 构建今日数据设置卡片
  Widget _buildTodayDataCard() {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('今日数据设置', style: AppTextStyles.headline3),
            const SizedBox(height: 16),

            _buildSlider(
              label: '记录数量',
              value: _todayCount.toDouble(),
              min: 1,
              max: 20,
              divisions: 19,
              onChanged: (value) {
                setState(() {
                  _todayCount = value.round();
                });
              },
              suffix: '条',
            ),

            _buildSlider(
              label: '最小时长',
              value: _todayMinDuration.toDouble(),
              min: 5,
              max: 60,
              divisions: 11,
              onChanged: (value) {
                setState(() {
                  _todayMinDuration = value.round();
                  if (_todayMinDuration > _todayMaxDuration) {
                    _todayMaxDuration = _todayMinDuration;
                  }
                });
              },
              suffix: '分钟',
            ),

            _buildSlider(
              label: '最大时长',
              value: _todayMaxDuration.toDouble(),
              min: _todayMinDuration.toDouble(),
              max: 180,
              divisions: 35,
              onChanged: (value) {
                setState(() {
                  _todayMaxDuration = value.round();
                });
              },
              suffix: '分钟',
            ),

            _buildSlider(
              label: '完成率',
              value: _todayCompletionRate,
              min: 0,
              max: 1,
              divisions: 10,
              onChanged: (value) {
                setState(() {
                  _todayCompletionRate = value;
                });
              },
              suffix: '%',
            ),

            const SizedBox(height: 16),

            AppButton(
              text: '生成今日数据',
              onPressed: _isGenerating ? null : _generateTodayData,
              type: AppButtonType.primary,
              width: double.infinity,
            ),
          ],
        ),
      ),
    );
  }

  // 构建本周数据设置卡片
  Widget _buildWeekDataCard() {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('本周数据设置', style: AppTextStyles.headline3),
            const SizedBox(height: 16),

            _buildSlider(
              label: '每天记录数量',
              value: _weekCountPerDay.toDouble(),
              min: 1,
              max: 10,
              divisions: 9,
              onChanged: (value) {
                setState(() {
                  _weekCountPerDay = value.round();
                });
              },
              suffix: '条',
            ),

            _buildSlider(
              label: '最小时长',
              value: _weekMinDuration.toDouble(),
              min: 5,
              max: 60,
              divisions: 11,
              onChanged: (value) {
                setState(() {
                  _weekMinDuration = value.round();
                  if (_weekMinDuration > _weekMaxDuration) {
                    _weekMaxDuration = _weekMinDuration;
                  }
                });
              },
              suffix: '分钟',
            ),

            _buildSlider(
              label: '最大时长',
              value: _weekMaxDuration.toDouble(),
              min: _weekMinDuration.toDouble(),
              max: 180,
              divisions: 35,
              onChanged: (value) {
                setState(() {
                  _weekMaxDuration = value.round();
                });
              },
              suffix: '分钟',
            ),

            _buildSlider(
              label: '完成率',
              value: _weekCompletionRate,
              min: 0,
              max: 1,
              divisions: 10,
              onChanged: (value) {
                setState(() {
                  _weekCompletionRate = value;
                });
              },
              suffix: '%',
            ),

            const SizedBox(height: 16),

            AppButton(
              text: '生成本周数据',
              onPressed: _isGenerating ? null : _generateWeekData,
              type: AppButtonType.primary,
              width: double.infinity,
            ),
          ],
        ),
      ),
    );
  }

  // 构建快速操作卡片
  Widget _buildQuickActionsCard() {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('快速操作', style: AppTextStyles.headline3),
            const SizedBox(height: 16),

            AppButton(
              text: '一键生成完整数据集',
              onPressed: _isGenerating ? null : _generateCompleteDataSet,
              type: AppButtonType.primary,
              width: double.infinity,
            ),

            const SizedBox(height: 12),

            AppButton(
              text: '清除所有测试数据',
              onPressed: _isGenerating ? null : _clearAllTestData,
              type: AppButtonType.secondary,
              backgroundColor: Colors.red.shade400,
              width: double.infinity,
            ),
          ],
        ),
      ),
    );
  }

  // 检查是否有科目和项目
  Future<bool> _checkSubjectsAndProjects() async {
    // 确保Hive服务已初始化
    await _hiveService.initHive();

    // 检查是否有科目
    final subjects = _hiveService.subjectRepository.getAllSubjects();
    if (subjects.isEmpty) {
      return false;
    }

    // 检查是否有项目
    for (final subject in subjects) {
      final projects = _hiveService.subjectRepository.getProjectsBySubjectId(subject.id);
      if (projects.isNotEmpty) {
        return true;
      }
    }

    return false;
  }

  // 显示创建科目和项目的提示
  void _showCreateSubjectProjectHint() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('请先创建至少一个科目和项目，再使用测试数据生成器'),
          action: SnackBarAction(
            label: '去创建',
            onPressed: () {
              Navigator.pushNamed(context, '/task/goal');
            },
          ),
          duration: const Duration(seconds: 10),
        ),
      );
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('测试数据生成器'),
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 提示信息
                Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.amber.withAlpha(50),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.info_outline,
                        color: Colors.amber,
                      ),
                      const SizedBox(width: 8),
                      const Expanded(
                        child: Text(
                          '注意：测试数据生成器需要至少一个科目和项目才能正常工作。请先创建科目和项目，再使用此功能。',
                          style: TextStyle(
                            color: Colors.amber,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // 状态信息
                if (_statusMessage.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withAlpha(30),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          _isGenerating ? Icons.hourglass_top : Icons.info_outline,
                          color: AppColors.primary,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _statusMessage,
                            style: TextStyle(
                              color: AppColors.primary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                // 快速操作卡片
                _buildQuickActionsCard(),

                // 今日数据设置卡片
                _buildTodayDataCard(),

                // 本周数据设置卡片
                _buildWeekDataCard(),

                const SizedBox(height: 16),

                // 提示信息
                const Text(
                  '注意：生成的测试数据会被标记为"测试数据"，可以通过清除功能一键删除。',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),

                const SizedBox(height: 32),
              ],
            ),
          ),

          // 加载指示器
          if (_isGenerating)
            Container(
              color: Colors.black.withAlpha(76), // 0.3 透明度
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }
}
