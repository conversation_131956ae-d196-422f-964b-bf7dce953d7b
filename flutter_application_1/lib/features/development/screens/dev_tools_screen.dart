import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/services/app_settings_service.dart';
import '../../../core/utils/network_diagnostics.dart';
import '../../../core/services/api_client.dart';
import '../../../core/providers/subscription_provider.dart';
import 'apple_debug_screen.dart';
import 'subscription_diagnostic_screen.dart';
import 'enhanced_apple_diagnostic_screen.dart';
import 'safe_sandbox_diagnostic_screen.dart';

/// 开发工具屏幕
/// 提供各种开发和测试工具的入口
class DevToolsScreen extends ConsumerStatefulWidget {
  const DevToolsScreen({super.key});

  @override
  ConsumerState<DevToolsScreen> createState() => _DevToolsScreenState();
}

class _DevToolsScreenState extends ConsumerState<DevToolsScreen> {
  String _diagnosticReport = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
  }

  /// 运行网络诊断
  Future<void> _runDiagnostics() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final report = await NetworkDiagnostics.getDiagnosticReport();
      setState(() {
        _diagnosticReport = report;
      });
    } catch (e) {
      setState(() {
        _diagnosticReport = '诊断失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 复制诊断报告
  void _copyReport() {
    Clipboard.setData(ClipboardData(text: _diagnosticReport));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('诊断报告已复制到剪贴板')),
    );
  }

  /// 测试登录API
  Future<void> _testLoginApi() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await NetworkDiagnostics.testLoginApi(ApiClient.baseUrl);

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('登录API测试结果'),
            content: SingleChildScrollView(
              child: Text(result.toString()),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('确定'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('测试失败: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 显示网络诊断对话框
  void _showNetworkDiagnosticDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题栏
              Row(
                children: [
                  const Text(
                    '网络诊断',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // API配置信息
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'API配置',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text('当前API地址: ${ApiClient.baseUrl}'),
                      Text('连接超时: ${ApiClient.connectTimeout}ms'),
                      Text('接收超时: ${ApiClient.receiveTimeout}ms'),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // 操作按钮
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : () async {
                        await _runDiagnostics();
                        setState(() {}); // 刷新对话框
                      },
                      icon: const Icon(Icons.refresh),
                      label: const Text('运行诊断'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : _testLoginApi,
                      icon: const Icon(Icons.login),
                      label: const Text('测试登录'),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // 诊断报告
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Text(
                              '诊断报告',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const Spacer(),
                            IconButton(
                              onPressed: _diagnosticReport.isEmpty ? null : _copyReport,
                              icon: const Icon(Icons.copy),
                              tooltip: '复制报告',
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Expanded(
                          child: _isLoading
                              ? const Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      CircularProgressIndicator(),
                                      SizedBox(height: 16),
                                      Text('正在运行网络诊断...'),
                                    ],
                                  ),
                                )
                              : SingleChildScrollView(
                                  child: Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Colors.grey[100],
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(color: Colors.grey[300]!),
                                    ),
                                    child: Text(
                                      _diagnosticReport.isEmpty
                                          ? '点击"运行诊断"开始网络检测...'
                                          : _diagnosticReport,
                                      style: const TextStyle(
                                        fontFamily: 'monospace',
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                                ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示订阅测试对话框
  void _showSubscriptionTestDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('订阅状态测试'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('当前订阅状态:'),
            const SizedBox(height: 8),
            Consumer(
              builder: (context, ref, child) {
                final subscriptionStatus = ref.watch(appleSubscriptionStatusProvider);
                return subscriptionStatus.when(
                  data: (isPremium) => Text(
                    isPremium ? '✅ 已订阅高级版' : '❌ 未订阅',
                    style: TextStyle(
                      color: isPremium ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  loading: () => const Text('🔄 检查中...'),
                  error: (error, stack) => Text(
                    '❌ 检查失败: $error',
                    style: const TextStyle(color: Colors.red),
                  ),
                );
              },
            ),
            const SizedBox(height: 16),
            const Text('测试操作:'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              // 设置为已订阅状态（仅用于测试）
              ref.read(appleSubscriptionServiceProvider).setTestPremiumStatus(true);
              // 刷新订阅状态provider
              ref.invalidate(appleSubscriptionStatusProvider);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('已设置为订阅状态（测试）')),
              );
            },
            child: const Text('设为已订阅'),
          ),
          TextButton(
            onPressed: () {
              // 设置为未订阅状态（仅用于测试）
              ref.read(appleSubscriptionServiceProvider).setTestPremiumStatus(false);
              // 刷新订阅状态provider
              ref.invalidate(appleSubscriptionStatusProvider);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('已设置为未订阅状态（测试）')),
              );
            },
            child: const Text('设为未订阅'),
          ),
          TextButton(
            onPressed: () {
              // 刷新订阅状态
              ref.invalidate(appleSubscriptionStatusProvider);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('已刷新订阅状态')),
              );
            },
            child: const Text('刷新状态'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('开发工具'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildToolCard(
            context,
            title: '测试数据生成器',
            description: '生成模拟专注数据，用于测试数据分析页面',
            icon: Icons.data_usage,
            onTap: () {
              Navigator.pushNamed(context, '/dev/test-data-generator');
            },
          ),

          _buildToolCard(
            context,
            title: '集成测试',
            description: '运行前后端集成测试，检查API连接和数据同步',
            icon: Icons.integration_instructions,
            onTap: () {
              Navigator.pushNamed(context, '/test/integration');
            },
          ),

          _buildToolCard(
            context,
            title: '认证测试',
            description: '测试用户登录、认证和API调用',
            icon: Icons.security,
            onTap: () {
              Navigator.pushNamed(context, '/test/auth');
            },
          ),

          // 隐藏页面预览功能暂时移除
          // _buildToolCard(
          //   context,
          //   title: '查看隐藏页面',
          //   description: '预览各种需要特定条件才能触发的页面和弹窗',
          //   icon: Icons.visibility,
          //   onTap: () {
          //     // 功能暂时不可用
          //   },
          // ),

          _buildToolCard(
            context,
            title: '重置引导页状态',
            description: '将应用状态重置为首次启动状态，下次启动时会显示引导页',
            icon: Icons.restore,
            onTap: () {
              // 重置引导页状态
              final appSettingsService = AppSettingsService();
              appSettingsService.resetOnboardingStatus();

              // 显示提示
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('引导页状态已重置，下次启动时将显示引导页')),
              );
            },
          ),

          _buildToolCard(
            context,
            title: '网络诊断',
            description: '检查网络连接状态和API可用性',
            icon: Icons.network_check,
            onTap: () {
              _showNetworkDiagnosticDialog();
            },
          ),

          _buildToolCard(
            context,
            title: '订阅状态测试',
            description: '测试和切换订阅状态，用于开发调试',
            icon: Icons.subscriptions,
            onTap: () {
              _showSubscriptionTestDialog();
            },
          ),

          _buildToolCard(
            context,
            title: 'Apple功能调试',
            description: '排查Apple订阅和登录相关问题，查看详细调试信息',
            icon: Icons.apple,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AppleDebugScreen(),
                ),
              );
            },
          ),

          _buildToolCard(
            context,
            title: '订阅功能诊断',
            description: '全面诊断Apple订阅功能，包括产品加载、购买测试等',
            icon: Icons.medical_services,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SubscriptionDiagnosticScreen(),
                ),
              );
            },
          ),

          _buildToolCard(
            context,
            title: '增强Apple诊断',
            description: '深度诊断StoreKit连接、网络状态和产品查询问题',
            icon: Icons.bug_report,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const EnhancedAppleDiagnosticScreen(),
                ),
              );
            },
          ),

          _buildToolCard(
            context,
            title: '沙盒测试诊断',
            description: '安全版本，专门用于排查沙盒账号登录和购买测试问题',
            icon: Icons.science,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SafeSandboxDiagnosticScreen(),
                ),
              );
            },
          ),

          _buildToolCard(
            context,
            title: '新订阅界面测试',
            description: '测试重新设计的订阅界面和新的产品配置',
            icon: Icons.star,
            onTap: () {
              Navigator.pushNamed(context, '/subscription/premium');
            },
          ),

          // 可以在这里添加更多开发工具入口
        ],
      ),
    );
  }

  Widget _buildToolCard(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.primary.withAlpha(30),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: AppColors.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTextStyles.headline3,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppColors.textSecondary,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
