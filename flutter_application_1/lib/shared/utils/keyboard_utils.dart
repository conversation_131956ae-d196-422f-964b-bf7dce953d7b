import 'package:flutter/material.dart';

/// 键盘适配工具类
/// 提供键盘相关的实用方法和组件
class KeyboardUtils {
  /// 隐藏键盘
  static void hideKeyboard(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

  /// 获取键盘高度
  static double getKeyboardHeight(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom;
  }

  /// 检查键盘是否显示
  static bool isKeyboardVisible(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom > 0;
  }

  /// 监听键盘状态变化
  static Widget keyboardListener({
    required Widget child,
    VoidCallback? onKeyboardShow,
    VoidCallback? onKeyboardHide,
  }) {
    return Builder(
      builder: (context) {
        return MediaQuery(
          data: MediaQuery.of(context),
          child: Builder(
            builder: (context) {
              final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

              // 使用WidgetsBinding来延迟回调执行，避免在build过程中调用setState
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (keyboardHeight > 0) {
                  onKeyboardShow?.call();
                } else {
                  onKeyboardHide?.call();
                }
              });

              return child;
            },
          ),
        );
      },
    );
  }

  /// 自动滚动到输入框位置
  static void scrollToInput({
    required ScrollController scrollController,
    required BuildContext context,
    double? extraOffset,
  }) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
      if (keyboardHeight > 0) {
        final renderBox = context.findRenderObject() as RenderBox?;
        if (renderBox != null) {
          final position = renderBox.localToGlobal(Offset.zero);
          final screenHeight = MediaQuery.of(context).size.height;
          final targetPosition = position.dy + renderBox.size.height;
          final visibleHeight = screenHeight - keyboardHeight;

          if (targetPosition > visibleHeight) {
            final scrollOffset = targetPosition - visibleHeight + (extraOffset ?? 50);
            scrollController.animateTo(
              scrollController.offset + scrollOffset,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          }
        }
      }
    });
  }
}

/// 键盘适配的Scaffold包装器
class KeyboardAdaptiveScaffold extends StatelessWidget {
  final Widget? appBar;
  final Widget body;
  final Widget? bottomNavigationBar;
  final Widget? floatingActionButton;
  final Color? backgroundColor;
  final bool resizeToAvoidBottomInset;
  final bool enableTapToHideKeyboard;
  final bool extendBodyBehindAppBar;

  const KeyboardAdaptiveScaffold({
    super.key,
    this.appBar,
    required this.body,
    this.bottomNavigationBar,
    this.floatingActionButton,
    this.backgroundColor,
    this.resizeToAvoidBottomInset = true,
    this.enableTapToHideKeyboard = true,
    this.extendBodyBehindAppBar = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget scaffoldBody = body;

    // 如果启用点击隐藏键盘功能
    if (enableTapToHideKeyboard) {
      scaffoldBody = GestureDetector(
        onTap: () => KeyboardUtils.hideKeyboard(context),
        behavior: HitTestBehavior.translucent,
        child: scaffoldBody,
      );
    }

    return Scaffold(
      appBar: appBar as PreferredSizeWidget?,
      body: scaffoldBody,
      bottomNavigationBar: bottomNavigationBar,
      floatingActionButton: floatingActionButton,
      backgroundColor: backgroundColor,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      extendBodyBehindAppBar: extendBodyBehindAppBar,
    );
  }
}

/// 键盘适配的滚动视图
class KeyboardAdaptiveScrollView extends StatefulWidget {
  final List<Widget> children;
  final EdgeInsetsGeometry? padding;
  final ScrollController? controller;
  final bool enableAutoScroll;
  final double autoScrollOffset;

  const KeyboardAdaptiveScrollView({
    super.key,
    required this.children,
    this.padding,
    this.controller,
    this.enableAutoScroll = true,
    this.autoScrollOffset = 50.0,
  });

  @override
  State<KeyboardAdaptiveScrollView> createState() => _KeyboardAdaptiveScrollViewState();
}

class _KeyboardAdaptiveScrollViewState extends State<KeyboardAdaptiveScrollView> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      controller: _scrollController,
      padding: widget.padding,
      child: Column(
        children: widget.children.map((child) {
          // 如果是输入框，包装键盘监听器
          if (widget.enableAutoScroll && _isInputWidget(child)) {
            return Focus(
              onFocusChange: (hasFocus) {
                if (hasFocus) {
                  KeyboardUtils.scrollToInput(
                    scrollController: _scrollController,
                    context: context,
                    extraOffset: widget.autoScrollOffset,
                  );
                }
              },
              child: child,
            );
          }
          return child;
        }).toList(),
      ),
    );
  }

  bool _isInputWidget(Widget widget) {
    return widget is TextField ||
           widget is TextFormField ||
           widget.runtimeType.toString().contains('TextField') ||
           widget.runtimeType.toString().contains('AppTextField');
  }
}
