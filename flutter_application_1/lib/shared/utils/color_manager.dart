import 'package:flutter/material.dart';
import '../theme/constants.dart';

/// 智能颜色管理器
/// 负责管理科目颜色的分配、排序和可用性
class ColorManager {
  /// 获取排序后的颜色列表
  /// 已使用的颜色会被排到最后并标记为禁用状态
  /// [usedColors] 已被使用的颜色列表
  /// [excludeColor] 需要排除的颜色（如当前编辑的科目颜色）
  static List<ColorOption> getSortedColors({
    List<Color> usedColors = const [],
    Color? excludeColor,
  }) {
    final List<ColorOption> colorOptions = [];
    
    // 创建所有颜色选项
    for (int i = 0; i < AppColors.subjectColors.length; i++) {
      final color = AppColors.subjectColors[i];
      final isUsed = usedColors.any((usedColor) => usedColor.value == color.value);
      final isExcluded = excludeColor != null && excludeColor.value == color.value;
      
      colorOptions.add(ColorOption(
        color: color,
        index: i,
        isUsed: isUsed && !isExcluded, // 如果是被排除的颜色，不标记为已使用
        isAvailable: !isUsed || isExcluded, // 如果是被排除的颜色，标记为可用
      ));
    }
    
    // 排序：可用的颜色在前，已使用的颜色在后
    colorOptions.sort((a, b) {
      if (a.isAvailable && !b.isAvailable) return -1;
      if (!a.isAvailable && b.isAvailable) return 1;
      return a.index.compareTo(b.index); // 保持原有顺序
    });
    
    return colorOptions;
  }
  
  /// 获取下一个可用的颜色
  /// 返回第一个未被使用的颜色
  static Color? getNextAvailableColor(List<Color> usedColors) {
    final sortedColors = getSortedColors(usedColors: usedColors);
    final availableColor = sortedColors.firstWhere(
      (option) => option.isAvailable,
      orElse: () => sortedColors.first, // 如果没有可用颜色，返回第一个
    );
    return availableColor.color;
  }
  
  /// 检查是否还有可用颜色
  static bool hasAvailableColors(List<Color> usedColors) {
    return usedColors.length < AppColors.subjectColors.length;
  }
  
  /// 获取颜色使用统计信息
  static ColorUsageStats getUsageStats(List<Color> usedColors) {
    final totalColors = AppColors.subjectColors.length;
    final usedCount = usedColors.length;
    final availableCount = totalColors - usedCount;
    
    return ColorUsageStats(
      totalColors: totalColors,
      usedCount: usedCount,
      availableCount: availableCount,
      usagePercentage: (usedCount / totalColors * 100).round(),
    );
  }
}

/// 颜色选项数据类
class ColorOption {
  final Color color;
  final int index;
  final bool isUsed;
  final bool isAvailable;
  
  const ColorOption({
    required this.color,
    required this.index,
    required this.isUsed,
    required this.isAvailable,
  });
}

/// 颜色使用统计数据类
class ColorUsageStats {
  final int totalColors;
  final int usedCount;
  final int availableCount;
  final int usagePercentage;
  
  const ColorUsageStats({
    required this.totalColors,
    required this.usedCount,
    required this.availableCount,
    required this.usagePercentage,
  });
  
  /// 是否接近用完
  bool get isNearlyFull => usagePercentage >= 90;
  
  /// 是否已用完
  bool get isFull => availableCount == 0;
}
