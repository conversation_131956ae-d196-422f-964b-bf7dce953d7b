import 'package:flutter/material.dart';

/// 键盘避让工具类
/// 提供键盘相关的实用方法
class KeyboardAvoidanceUtils {
  /// 获取键盘高度
  /// 增强第三方键盘兼容性
  static double getKeyboardHeight(BuildContext context) {
    final height = MediaQuery.of(context).viewInsets.bottom;

    // 对于第三方键盘，确保最小高度检测
    // 某些第三方键盘可能报告很小的高度值
    if (height > 0 && height < 100) {
      // 如果检测到异常小的键盘高度，使用默认最小高度
      return 250; // 常见键盘的最小高度
    }

    return height;
  }

  /// 检查键盘是否显示
  static bool isKeyboardVisible(BuildContext context) {
    return getKeyboardHeight(context) > 0;
  }

  /// 获取安全的弹窗高度
  /// [context] 上下文
  /// [maxHeightRatio] 最大高度比例（相对于屏幕高度）
  /// [minHeight] 最小高度
  /// [topOffset] 顶部偏移量
  static double getSafeBottomSheetHeight(
    BuildContext context, {
    double maxHeightRatio = 0.9,
    double minHeight = 200,
    double topOffset = 100,
  }) {
    final mediaQuery = MediaQuery.of(context);
    final screenHeight = mediaQuery.size.height;
    final rawKeyboardHeight = mediaQuery.viewInsets.bottom;
    final safeAreaBottom = mediaQuery.padding.bottom;

    // 使用增强的键盘高度检测
    final keyboardHeight = getKeyboardHeight(context);

    // 对于第三方键盘，添加额外的安全边距
    final adjustedKeyboardHeight = rawKeyboardHeight != keyboardHeight
        ? keyboardHeight + 20 // 第三方键盘额外边距
        : keyboardHeight;

    // 计算可用高度
    final availableHeight = screenHeight - adjustedKeyboardHeight - safeAreaBottom - topOffset;
    final maxHeight = screenHeight * maxHeightRatio;

    // 返回约束后的高度
    return availableHeight < maxHeight
        ? availableHeight.clamp(minHeight, maxHeight)
        : maxHeight;
  }

  /// 计算输入框是否被键盘遮挡
  /// [inputContext] 输入框的上下文
  /// [keyboardHeight] 键盘高度
  static bool isInputObscuredByKeyboard(
    BuildContext inputContext,
    double keyboardHeight,
  ) {
    if (keyboardHeight <= 0) return false;

    final RenderObject? renderObject = inputContext.findRenderObject();
    if (renderObject is! RenderBox) return false;

    final Offset globalPosition = renderObject.localToGlobal(Offset.zero);
    final double inputBottom = globalPosition.dy + renderObject.size.height;
    final double screenHeight = MediaQuery.of(inputContext).size.height;
    final double keyboardTop = screenHeight - keyboardHeight;

    return inputBottom > keyboardTop;
  }

  /// 滚动到指定的输入框
  /// [scrollController] 滚动控制器
  /// [inputContext] 输入框上下文
  /// [containerContext] 容器上下文
  /// [margin] 边距
  static Future<void> scrollToInput(
    ScrollController scrollController,
    BuildContext inputContext,
    BuildContext containerContext, {
    double margin = 20,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) async {
    if (!scrollController.hasClients) return;

    final RenderObject? inputRenderObject = inputContext.findRenderObject();
    final RenderObject? containerRenderObject = containerContext.findRenderObject();

    if (inputRenderObject is! RenderBox || containerRenderObject is! RenderBox) {
      return;
    }

    try {
      // 计算输入框相对于容器的位置
      final Offset inputPosition = inputRenderObject.localToGlobal(Offset.zero);
      final Offset containerPosition = containerRenderObject.localToGlobal(Offset.zero);
      final double relativePosition = inputPosition.dy - containerPosition.dy;

      // 计算需要滚动的距离
      final double inputHeight = inputRenderObject.size.height;
      final double viewportHeight = scrollController.position.viewportDimension;
      final double currentScroll = scrollController.offset;

      // 目标滚动位置：确保输入框在可视区域内
      final double targetScroll = relativePosition + currentScroll - viewportHeight + inputHeight + margin;

      if (targetScroll > currentScroll) {
        await scrollController.animateTo(
          targetScroll.clamp(0, scrollController.position.maxScrollExtent),
          duration: duration,
          curve: curve,
        );
      }
    } catch (e) {
      // 如果计算失败，滚动到底部
      await scrollController.animateTo(
        scrollController.position.maxScrollExtent,
        duration: duration,
        curve: curve,
      );
    }
  }

  /// 创建键盘感知的滚动视图
  /// 自动处理键盘弹出时的滚动
  static Widget createKeyboardAwareScrollView({
    required Widget child,
    ScrollController? controller,
    EdgeInsetsGeometry? padding,
    ScrollPhysics? physics,
  }) {
    return Builder(
      builder: (context) {
        return SingleChildScrollView(
          controller: controller,
          padding: padding,
          physics: physics ?? const ClampingScrollPhysics(),
          child: child,
        );
      },
    );
  }

  /// 为输入框添加键盘避让功能
  /// 返回一个包装后的输入框，自动处理键盘避让
  static Widget wrapWithKeyboardAvoidance({
    required Widget inputField,
    required ScrollController scrollController,
    required BuildContext containerContext,
    double margin = 20,
  }) {
    return Builder(
      builder: (context) {
        return Focus(
          onFocusChange: (hasFocus) {
            if (hasFocus) {
              // 延迟执行，确保键盘已经弹出
              Future.delayed(const Duration(milliseconds: 300), () {
                if (context.mounted) {
                  scrollToInput(
                    scrollController,
                    context,
                    containerContext,
                    margin: margin,
                  );
                }
              });
            }
          },
          child: inputField,
        );
      },
    );
  }

  /// 获取键盘动画持续时间
  /// iOS和Android的键盘动画时间不同
  static Duration getKeyboardAnimationDuration() {
    return const Duration(milliseconds: 250);
  }

  /// 创建键盘感知的动画容器
  /// 自动根据键盘状态调整高度
  static Widget createKeyboardAwareContainer({
    required BuildContext context,
    required Widget child,
    double maxHeightRatio = 0.9,
    double minHeight = 200,
    double topOffset = 100,
    Duration? animationDuration,
    Curve animationCurve = Curves.easeInOut,
  }) {
    final height = getSafeBottomSheetHeight(
      context,
      maxHeightRatio: maxHeightRatio,
      minHeight: minHeight,
      topOffset: topOffset,
    );

    return AnimatedContainer(
      duration: animationDuration ?? getKeyboardAnimationDuration(),
      curve: animationCurve,
      constraints: BoxConstraints(
        maxHeight: height,
        minHeight: minHeight,
      ),
      child: child,
    );
  }

  /// 隐藏键盘
  static void hideKeyboard(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

  /// 检查是否有输入框获得焦点
  static bool hasInputFocus(BuildContext context) {
    return FocusScope.of(context).hasFocus;
  }

  /// 获取当前聚焦的节点
  static FocusNode? getCurrentFocusedNode(BuildContext context) {
    return FocusScope.of(context).focusedChild;
  }
}
