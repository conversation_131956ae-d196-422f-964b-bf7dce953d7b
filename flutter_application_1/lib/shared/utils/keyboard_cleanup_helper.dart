import 'package:flutter/material.dart';
import '../widgets/keyboard_input_overlay.dart';

/// 键盘清理助手
/// 用于处理键盘输入条卡住的紧急情况
class KeyboardCleanupHelper {
  static final KeyboardCleanupHelper _instance = KeyboardCleanupHelper._internal();
  factory KeyboardCleanupHelper() => _instance;
  KeyboardCleanupHelper._internal();

  /// 强制清理所有键盘输入条
  static void forceCleanupAll() {
    try {
      // 清理键盘输入条管理器
      KeyboardInputOverlayManager().forceCleanup();
      
      // 强制收起系统键盘
      FocusManager.instance.primaryFocus?.unfocus();
      
      // 清理所有焦点
      WidgetsBinding.instance.focusManager.primaryFocus?.unfocus();
      
    } catch (e) {
      // 静默处理清理错误
    }
  }

  /// 检查是否有键盘输入条正在显示
  static bool hasActiveInputBar() {
    return KeyboardInputOverlayManager().isShowing;
  }

  /// 安全地隐藏键盘输入条
  static void safeHideInputBar() {
    try {
      if (hasActiveInputBar()) {
        KeyboardInputOverlayManager().hideInputBar();
      }
    } catch (e) {
      // 如果正常隐藏失败，使用强制清理
      forceCleanupAll();
    }
  }
}

/// 键盘清理按钮组件
/// 用于开发和调试时手动清理卡住的键盘
class KeyboardCleanupButton extends StatelessWidget {
  final bool showInProduction;
  
  const KeyboardCleanupButton({
    super.key,
    this.showInProduction = false,
  });

  @override
  Widget build(BuildContext context) {
    // 在生产环境中默认不显示，除非明确指定
    if (!showInProduction && const bool.fromEnvironment('dart.vm.product')) {
      return const SizedBox.shrink();
    }

    return FloatingActionButton(
      mini: true,
      backgroundColor: Colors.red,
      onPressed: () {
        KeyboardCleanupHelper.forceCleanupAll();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('已强制清理所有键盘输入条'),
            duration: Duration(seconds: 1),
          ),
        );
      },
      child: const Icon(
        Icons.keyboard_hide,
        color: Colors.white,
        size: 20,
      ),
    );
  }
}
