// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppStateImpl _$$AppStateImplFromJson(Map<String, dynamic> json) =>
    _$AppStateImpl(
      isLoading: json['isLoading'] as bool? ?? false,
      isPlaying: json['isPlaying'] as bool? ?? false,
      currentDuration: (json['currentDuration'] as num?)?.toInt() ?? 0,
      totalDuration: (json['totalDuration'] as num?)?.toInt() ?? 0,
      error: json['error'] as String?,
    );

Map<String, dynamic> _$$AppStateImplToJson(_$AppStateImpl instance) =>
    <String, dynamic>{
      'isLoading': instance.isLoading,
      'isPlaying': instance.isPlaying,
      'currentDuration': instance.currentDuration,
      'totalDuration': instance.totalDuration,
      'error': instance.error,
    };
