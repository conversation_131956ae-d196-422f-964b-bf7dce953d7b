// 该文件定义了应用的全局状态管理Provider

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_state.freezed.dart';
part 'app_state.g.dart';

// 应用全局状态数据类
@freezed
class AppState with _$AppState {
  const factory AppState({
    @Default(false) bool isLoading,
    @Default(false) bool isPlaying,
    @Default(0) int currentDuration,
    @Default(0) int totalDuration,
    String? error,
  }) = _AppState;

  factory AppState.fromJson(Map<String, dynamic> json) =>
      _$AppStateFromJson(json);
}

// 全局状态Provider
final appStateProvider = StateNotifierProvider<AppStateNotifier, AppState>(
  (ref) => AppStateNotifier(),
);

// 状态管理类
class AppStateNotifier extends StateNotifier<AppState> {
  AppStateNotifier() : super(const AppState());

  // 设置加载状态
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  // 设置播放状态
  void setPlaying(bool isPlaying) {
    state = state.copyWith(isPlaying: isPlaying);
  }

  // 更新播放进度
  void updateProgress(int current, int total) {
    state = state.copyWith(
      currentDuration: current,
      totalDuration: total,
    );
  }

  // 设置错误信息
  void setError(String? error) {
    state = state.copyWith(error: error);
  }
}