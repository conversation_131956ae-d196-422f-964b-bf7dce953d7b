import 'package:flutter/material.dart';

/// 应用颜色规范
/// 定义应用中使用的所有颜色常量
class AppColors {
  // 页面渐变背景 - 用于主要页面背景
  static const LinearGradient pageBackground = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color.fromRGBO(204, 255, 229, 1), // 顶部颜色
      Color.fromARGB(255, 255, 250, 240), // 底部颜色
    ],
  );
  // 保留原有色彩方案（注释处理）
  //static const primary = Color.fromARGB(255, 146, 153, 224);
  static const primary = Color(0xFF4CAF50);    // 主色调，用于主要按钮、重要信息强调 ！！！当前使用greenLight400 替代了原先的primary
  //static const secondary = Color(0xFF2196F3);   // 次要色调，用于次要按钮、链接等
  //static const accent = Color(0xFFFFA726);      // 强调色，用于特殊元素、标记等

  // 基础颜色 - 用于替代直接使用Colors类的颜色
  static const white = Color(0xFFFFFFFF);
  static const black = Color(0xFF000000);
  static const transparent = Color(0x00000000);
  static const grey = Color(0xFF9E9E9E);
  static const red = Color(0xFFE53935);
  static const green = Color(0xFF4CAF50);
  static const blue = Color(0xFF2196F3);
  static const amber = Color(0xFFFFC107);
  static const purple = Color(0xFF9C27B0);
  static const orange = Color(0xFFFF9800);

  // 主题色系 - 基于色彩图调整
  // 主色调 - 紫色系（用于主要按钮、重要信息强调）- 保留但已注释，当前使用绿色系
  // static const primary = Color(0xFF563BFF);      // 主色调基准色（500）
  static const primaryLight100 = Color(0xFFECE8FF); // 主色调浅色（100）- 用于背景、轻度强调
  static const primaryLight200 = Color(0xFFD4CCFF); // 主色调浅色（200）- 用于次要背景
  static const primaryLight300 = Color(0xFFB8A6FF); // 主色调浅色（300）- 用于轻度强调元素
  static const primaryLight400 = Color(0xFF9C80FF); // 主色调浅色（400）- 用于次要元素
  static const primaryDark600 = Color(0xFF4730CC); // 主色调深色（600）- 用于悬停状态
  static const primaryDark700 = Color(0xFF3A2799); // 主色调深色（700）- 用于按下状态
  static const primaryDark800 = Color(0xFF2D1E66); // 主色调深色（800）- 用于深色背景
  static const primaryDark900 = Color(0xFF1F1433); // 主色调深色（900）- 用于极深色背景

  // 绿色主题色系 - 基于当前主色调 Color(0xFF4CAF50) 构建的阶梯色系
  // 使用相同的色彩构建原则，确保色彩系统的一致性
  static const greenLight50 = Color(0xFFE8F5E9);  // 绿色浅色（50）- 用于背景、极轻度强调
  static const greenLight100 = Color(0xFFC8E6C9); // 绿色浅色（100）- 用于背景、轻度强调
  static const greenLight200 = Color(0xFFA5D6A7); // 绿色浅色（200）- 用于次要背景
  static const greenLight300 = Color(0xFF81C784); // 绿色浅色（300）- 用于轻度强调元素
  static const greenLight400 = Color(0xFF66BB6A); // 绿色浅色（400）- 用于次要元素
  static const green500 = Color(0xFF4CAF50);      // 绿色基准色（500）- 当前主色调
  static const greenDark600 = Color(0xFF43A047); // 绿色深色（600）- 用于悬停状态
  static const greenDark700 = Color(0xFF388E3C); // 绿色深色（700）- 用于按下状态
  static const greenDark800 = Color(0xFF2E7D32); // 绿色深色（800）- 用于深色背景
  static const greenDark900 = Color(0xFF1B5E20); // 绿色深色（900）- 用于极深色背景

  // 绿色主题色系的低饱和度变体 - 适合按钮等UI元素
  static const greenMuted50 = Color(0xFFF1F8F1);  // 低饱和度绿色（50）
  static const greenMuted100 = Color(0xFFDDECDD); // 低饱和度绿色（100）
  static const greenMuted200 = Color(0xFFC1D7C2); // 低饱和度绿色（200）
  static const greenMuted300 = Color(0xFFA5C2A6); // 低饱和度绿色（300）
  static const greenMuted400 = Color(0xFF8AAD8B); // 低饱和度绿色（400）
  static const greenMuted500 = Color(0xFF6F986F); // 低饱和度绿色（500）
  static const greenMuted600 = Color(0xFF5C8A5D); // 低饱和度绿色（600）
  static const greenMuted700 = Color(0xFF4A7C4B); // 低饱和度绿色（700）
  static const greenMuted800 = Color(0xFF396E3A); // 低饱和度绿色（800）
  static const greenMuted900 = Color(0xFF275F28); // 低饱和度绿色（900）

  // 成功色系 - 绿色（用于成功状态、完成操作）
  static const success = Color(0xFF98D02A);      // 成功色基准色（500）
  static const successLight100 = Color(0xFFF2FADC); // 成功色浅色（100）- 用于背景、轻度强调
  static const successLight200 = Color(0xFFE5F5B9); // 成功色浅色（200）- 用于次要背景
  static const successLight300 = Color(0xFFCCE77A); // 成功色浅色（300）- 用于轻度强调元素
  static const successLight400 = Color(0xFFB2DC52); // 成功色浅色（400）- 用于次要元素
  static const successDark600 = Color(0xFF7AA622); // 成功色深色（600）- 用于悬停状态
  static const successDark700 = Color(0xFF5C7D1A); // 成功色深色（700）- 用于按下状态
  static const successDark800 = Color(0xFF3E5311); // 成功色深色（800）- 用于深色背景
  static const successDark900 = Color(0xFF1F2A09); // 成功色深色（900）- 用于极深色背景

  // 信息色系 - 蓝色（用于信息提示、链接）
  static const info = Color(0xFF4CABFF);      // 信息色基准色（500）
  static const infoLight100 = Color(0xFFE8F4FF); // 信息色浅色（100）- 用于背景、轻度强调
  static const infoLight200 = Color(0xFFD1E9FF); // 信息色浅色（200）- 用于次要背景
  static const infoLight300 = Color(0xFFA3D3FF); // 信息色浅色（300）- 用于轻度强调元素
  static const infoLight400 = Color(0xFF76BFFF); // 信息色浅色（400）- 用于次要元素
  static const infoDark600 = Color(0xFF3D89CC); // 信息色深色（600）- 用于悬停状态
  static const infoDark700 = Color(0xFF2E6799); // 信息色深色（700）- 用于按下状态
  static const infoDark800 = Color(0xFF1F4466); // 信息色深色（800）- 用于深色背景
  static const infoDark900 = Color(0xFF102233); // 信息色深色（900）- 用于极深色背景

  // 警告色系 - 黄色（用于警告、提醒）
  static const warning = Color(0xFFFFD344);      // 警告色基准色（500）
  static const warningLight100 = Color(0xFFFFF9E6); // 警告色浅色（100）- 用于背景、轻度强调
  static const warningLight200 = Color(0xFFFFF3CC); // 警告色浅色（200）- 用于次要背景
  static const warningLight300 = Color(0xFFFFE699); // 警告色浅色（300）- 用于轻度强调元素
  static const warningLight400 = Color(0xFFFFDC66); // 警告色浅色（400）- 用于次要元素
  static const warningDark600 = Color(0xFFCCA936); // 警告色深色（600）- 用于悬停状态
  static const warningDark700 = Color(0xFF997F29); // 警告色深色（700）- 用于按下状态
  static const warningDark800 = Color(0xFF66551B); // 警告色深色（800）- 用于深色背景
  static const warningDark900 = Color(0xFF332A0D); // 警告色深色（900）- 用于极深色背景

  // 错误色系 - 红色（用于错误、危险操作）
  static const error = Color(0xFFFF5432);      // 错误色基准色（500）
  static const errorLight100 = Color(0xFFFFF0ED); // 错误色浅色（100）- 用于背景、轻度强调
  static const errorLight200 = Color(0xFFFFE0DA); // 错误色浅色（200）- 用于次要背景
  static const errorLight300 = Color(0xFFFFC1B5); // 错误色浅色（300）- 用于轻度强调元素
  static const errorLight400 = Color(0xFFFF8A73); // 错误色浅色（400）- 用于次要元素
  static const errorDark600 = Color(0xFFCC4328); // 错误色深色（600）- 用于悬停状态
  static const errorDark700 = Color(0xFF99321E); // 错误色深色（700）- 用于按下状态
  static const errorDark800 = Color(0xFF662114); // 错误色深色（800）- 用于深色背景
  static const errorDark900 = Color(0xFF33110A); // 错误色深色（900）- 用于极深色背景

  // 次要色调 - 用于次要按钮、链接等
  static const secondary = info;   // 使用信息色作为次要色调

  // 强调色 - 用于特殊元素、标记等
  static const accent = warning;   // 使用警告色作为强调色

  // 背景色系
  static const background = Color(0xFFF5F5F5);  // 页面背景色
  static const cardBackground = Color(0xFFFFFFFF); // 卡片背景色
  static const modalBackground = Color(0xFFFAFAFA); // 模态框背景色

  // 文字色系 - 基于Material Design 3规范优化
  static const text = Color(0xFF212121);        // 主文本色 (Gray 900) - 主标题/关键数据 - 对比度15.8:1
  static const textSecondary = Color(0xFF424242); // 次要文本色 (Gray 800) - 正文文本/重要按钮文字 - 对比度12.3:1
  static const textTertiary = Color(0xFF616161);  // 三级文本色 (Gray 700) - 次级说明文字 - 对比度8.9:1
  static const textAssist = Color(0xFF757575);    // 辅助文本色 (Gray 600) - 辅助信息/禁用态文字 - 对比度6.7:1
  static const textPlaceholder = Color(0xFF9E9E9E); // 占位文本色 (Gray 500) - 占位符/分割线 - 对比度4.2:1
  static const textHint = Color(0xFFBDBDBD);     // 提示文本色 (Gray 400) - 极次要装饰线条 - 对比度2.9:1
  static const textDisabled = Color(0xFFE0E0E0);  // 禁用文本色 (Gray 300) - 浅色背景的分割线 - 对比度1.9:1

  // 边框和分割线
  static const border = Color(0xFFE0E0E0);      // 边框颜色 (Gray 300)
  static const divider = Color(0xFFEEEEEE);      // 分割线颜色 (Gray 200)

  // 标准透明度值 - 用于统一透明度使用（使用Alpha值，范围0-255）
  static const alpha10 = 26;    // 约等于10%不透明度
  static const alpha20 = 51;    // 约等于20%不透明度
  static const alpha30 = 77;    // 约等于30%不透明度
  static const alpha40 = 102;   // 约等于40%不透明度
  static const alpha50 = 128;   // 约等于50%不透明度
  static const alpha60 = 153;   // 约等于60%不透明度
  static const alpha70 = 179;   // 约等于70%不透明度
  static const alpha80 = 204;   // 约等于80%不透明度
  static const alpha90 = 230;   // 约等于90%不透明度

  // 科目颜色系统 - 用于科目颜色选择
  // 这些颜色参考了应用中Light400级别的颜色标准，具有适中的饱和度和亮度
  // 避免使用主色调绿色，并确保各颜色之间有足够的区分度
  // 所有颜色都满足WCAG 2.1 AA标准的对比度要求，确保在白色卡片背景下清晰可见
  // 共12个颜色，支持最多12个科目的创建
  static const List<Color> subjectColors = [
    Color(0xFF5C9CFF),  // 蓝色 - 明亮蓝
    Color(0xFFE57373),  // 红色 - 玫瑰红
    Color(0xFF8D6E63),  // 棕色 - 温暖棕
    Color(0xFFFFB74D),  // 橙色 - 温暖橙
    Color(0xFF4FC3F7),  // 浅蓝色 - 天空蓝
    Color(0xFFAB47BC),  // 紫色 - 梅红紫
    Color(0xFF4DD0E1),  // 青色 - 浅青色
    Color(0xFF78909C),  // 蓝灰色 - 冷灰
    Color(0xFFFFD54F),  // 琥珀色 - 金黄色
    Color(0xFFF06292),  // 粉色 - 浅粉红
    Color(0xFF81C784),  // 浅绿色 - 薄荷绿
    Color(0xFFBA68C8),  // 淡紫色 - 薰衣草紫
  ];

  // 透明度颜色 - 常用的带透明度颜色
  static Color whiteWithAlpha(int alpha) => white.withAlpha(alpha);
  static Color blackWithAlpha(int alpha) => black.withAlpha(alpha);
  static Color primaryWithAlpha(int alpha) => primary.withAlpha(alpha);
  static Color secondaryWithAlpha(int alpha) => Color(0xFF2196F3).withAlpha(alpha); // 使用次要色调
  static Color redWithAlpha(int alpha) => red.withAlpha(alpha);
  static Color greenWithAlpha(int alpha) => green.withAlpha(alpha);
  static Color blueWithAlpha(int alpha) => blue.withAlpha(alpha);

  // 常用的固定透明度颜色
  static final white10 = white.withAlpha(alpha10);
  static final white20 = white.withAlpha(alpha20);
  static final white50 = white.withAlpha(alpha50);
  static final white70 = white.withAlpha(alpha70);

  static final black10 = black.withAlpha(alpha10);
  static final black20 = black.withAlpha(alpha20);
  static final black50 = black.withAlpha(alpha50);
  static final black70 = black.withAlpha(alpha70);

  static final primary10 = primary.withAlpha(alpha10);
  static final primary20 = primary.withAlpha(alpha20);
  static final primary50 = primary.withAlpha(alpha50);
}

/// 应用文字样式规范
/// 定义应用中使用的所有文字样式常量
class AppTextStyles {
  // 标题文字样式
  static const headline1 = TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: AppColors.text);       // 主标题
  static const headline2 = TextStyle(fontSize: 20, fontWeight: FontWeight.w600, color: AppColors.text);       // 次级标题
  static const headline3 = TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: AppColors.text);       // 三级标题

  // 正文文字样式
  static const bodyLarge = TextStyle(fontSize: 16, fontWeight: FontWeight.normal, color: AppColors.textSecondary);    // 大号正文
  static const bodyMedium = TextStyle(fontSize: 14, fontWeight: FontWeight.normal, color: AppColors.textSecondary);   // 中号正文
  static const bodySmall = TextStyle(fontSize: 12, fontWeight: FontWeight.normal, color: AppColors.textSecondary);    // 小号正文

  // 按钮文字样式
  static const buttonLarge = TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Colors.white);     // 大号按钮文字
  static const buttonMedium = TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: Colors.white);    // 中号按钮文字
  static const buttonSmall = TextStyle(fontSize: 12, fontWeight: FontWeight.w600, color: Colors.white);     // 小号按钮文字

  // 辅助文字样式
  static const caption = TextStyle(fontSize: 12, fontWeight: FontWeight.normal, color: AppColors.textTertiary); // 标签文字
  static const overline = TextStyle(fontSize: 10, fontWeight: FontWeight.normal, color: AppColors.textAssist);     // 上标文字
  static const labelMedium = TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: AppColors.textSecondary);      // 表单标签文字

  // 提示和禁用文字样式
  static const hint = TextStyle(fontSize: 14, fontWeight: FontWeight.normal, color: AppColors.textPlaceholder);     // 提示文字
  static const disabled = TextStyle(fontSize: 14, fontWeight: FontWeight.normal, color: AppColors.textAssist);     // 禁用文字
}

/// 应用尺寸规范
/// 定义应用中使用的所有间距、圆角等尺寸常量
class AppSizes {
  // 内容边距
  static const double paddingXSmall = 4.0;     // 超小间距
  static const double paddingSmall = 8.0;      // 小间距
  static const double paddingMedium = 16.0;    // 中等间距
  static const double paddingLarge = 24.0;     // 大间距
  static const double paddingXLarge = 32.0;    // 超大间距

  // 圆角大小
  static const double radiusSmall = 4.0;       // 小圆角
  static const double radiusMedium = 8.0;      // 中等圆角
  static const double radiusLarge = 16.0;      // 大圆角
  static const double radiusXLarge = 24.0;     // 超大圆角

  // 图标尺寸
  static const double iconSmall = 16.0;        // 小图标
  static const double iconMedium = 24.0;       // 中等图标
  static const double iconLarge = 32.0;        // 大图标

  // 按钮高度
  static const double buttonHeightSmall = 32.0;  // 小按钮高度
  static const double buttonHeightMedium = 44.0; // 中等按钮高度
  static const double buttonHeightLarge = 52.0;  // 大按钮高度
}

/// 应用阴影规范
/// 定义应用中使用的所有阴影效果常量
class AppShadows {
  static const low = BoxShadow(
    color: Color(0x14212121),
    offset: Offset(0, 2),
    blurRadius: 4,
    spreadRadius: 0,
  );  // 浅阴影，用于卡片等轻量级元素

  static const medium = BoxShadow(
    color: Color(0x1A212121),
    offset: Offset(0, 4),
    blurRadius: 8,
    spreadRadius: 0,
  );  // 中等阴影，用于悬浮按钮等

  static const high = BoxShadow(
    color: Color(0x1F212121),
    offset: Offset(0, 8),
    blurRadius: 16,
    spreadRadius: 0,
  );  // 深阴影，用于模态框等
}

/// 应用常量
/// 定义应用中使用的其他常量
class AppConstants {
  static const String appName = '专注场景';
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);  // 默认动画时长
  static const Duration longAnimationDuration = Duration(milliseconds: 500);    // 较长动画时长
  static const Duration shortAnimationDuration = Duration(milliseconds: 150);   // 较短动画时长
}

/// 应用装饰样式
/// 定义应用中使用的常用装饰样式
class AppDecorations {
  // 标准卡片装饰
  static BoxDecoration card({
    Color? color,
    Color? borderColor,
    double borderRadius = AppSizes.radiusLarge,
    List<BoxShadow>? boxShadow,
  }) {
    return BoxDecoration(
      color: color ?? AppColors.cardBackground,
      borderRadius: BorderRadius.circular(borderRadius),
      border: borderColor != null
          ? Border.all(color: borderColor)
          : Border.all(color: AppColors.border),
      boxShadow: boxShadow ?? [AppShadows.low],
    );
  }

  // 无边框卡片装饰
  static BoxDecoration cardNoBorder({
    Color? color,
    double borderRadius = AppSizes.radiusLarge,
    List<BoxShadow>? boxShadow,
  }) {
    return BoxDecoration(
      color: color ?? AppColors.cardBackground,
      borderRadius: BorderRadius.circular(borderRadius),
      boxShadow: boxShadow ?? [AppShadows.low],
    );
  }

  // 标准内容卡片装饰 - 用于页面中的内容卡片
  static BoxDecoration standardCard({
    Color? color,
    double borderRadius = AppSizes.radiusLarge,
    List<BoxShadow>? boxShadow,
  }) {
    return BoxDecoration(
      color: color ?? Colors.white,
      borderRadius: BorderRadius.circular(borderRadius),
      boxShadow: boxShadow ?? [AppShadows.low],
    );
  }

  // 圆形装饰
  static BoxDecoration circle({
    Color? color,
    Color? borderColor,
    double? borderWidth,
    List<BoxShadow>? boxShadow,
  }) {
    return BoxDecoration(
      color: color ?? AppColors.cardBackground,
      shape: BoxShape.circle,
      border: borderColor != null
          ? Border.all(color: borderColor, width: borderWidth ?? 1.0)
          : null,
      boxShadow: boxShadow,
    );
  }

  // 标签装饰
  static BoxDecoration tag({
    Color? color,
    Color? borderColor,
    double borderRadius = AppSizes.radiusSmall,
  }) {
    return BoxDecoration(
      color: color ?? AppColors.primaryLight100,
      borderRadius: BorderRadius.circular(borderRadius),
      border: borderColor != null
          ? Border.all(color: borderColor, width: 1.0)
          : null,
    );
  }

  // 底部弹窗装饰
  static BoxDecoration bottomSheet({
    Color? color,
  }) {
    return BoxDecoration(
      color: color ?? AppColors.cardBackground,
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(AppSizes.radiusLarge),
        topRight: Radius.circular(AppSizes.radiusLarge),
      ),
    );
  }
}

/// 应用输入框样式规范
/// 定义应用中使用的所有输入框和表单元素的样式常量
class AppInputStyles {
  // 标准输入框高度
  static const double inputHeightLarge = 48.0;   // 大型输入框高度
  static const double inputHeightMedium = 40.0;  // 中型输入框高度
  static const double inputHeightSmall = 36.0;   // 小型输入框高度
  static const double inputHeightCompact = 32.0; // 紧凑型输入框高度

  // 标准输入框宽度
  static const double inputWidthLarge = 240.0;   // 大型输入框宽度
  static const double inputWidthMedium = 180.0;  // 中型输入框宽度
  static const double inputWidthSmall = 120.0;   // 小型输入框宽度
  static const double inputWidthCompact = 80.0;  // 紧凑型输入框宽度

  // 标准输入框内边距
  static const EdgeInsets inputPaddingLarge = EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0);
  static const EdgeInsets inputPaddingMedium = EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
  static const EdgeInsets inputPaddingSmall = EdgeInsets.symmetric(horizontal: 8.0, vertical: 6.0);

  // 标准输入框圆角
  static const double inputRadiusLarge = 12.0;   // 大型输入框圆角
  static const double inputRadiusMedium = 8.0;   // 中型输入框圆角
  static const double inputRadiusSmall = 4.0;    // 小型输入框圆角

  // 标准输入框边框
  static const BorderSide inputBorderDefault = BorderSide(color: AppColors.border, width: 1.0);
  static const BorderSide inputBorderFocused = BorderSide(color: AppColors.primary, width: 1.5);
  static const BorderSide inputBorderError = BorderSide(color: AppColors.error, width: 1.0);

  // 标准输入框文本样式
  static const TextStyle inputTextStyle = TextStyle(
    fontSize: 14.0,
    fontWeight: FontWeight.w500,
    color: AppColors.text,
  );
  static const TextStyle inputHintStyle = TextStyle(
    fontSize: 14.0,
    fontWeight: FontWeight.normal,
    color: AppColors.textPlaceholder,
  );
  static const TextStyle inputLabelStyle = TextStyle(
    fontSize: 14.0,
    fontWeight: FontWeight.w500,
    color: AppColors.textSecondary,
  );
  static const TextStyle inputHelperStyle = TextStyle(
    fontSize: 12.0,
    fontWeight: FontWeight.normal,
    color: AppColors.textTertiary,
  );
  static const TextStyle inputErrorStyle = TextStyle(
    fontSize: 12.0,
    fontWeight: FontWeight.normal,
    color: AppColors.error,
  );

  // 标准输入框装饰
  static InputDecoration inputDecoration({
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    String? helperText,
    String? errorText,
    bool filled = true,
    Color? fillColor,
    double borderRadius = inputRadiusMedium,
  }) {
    return InputDecoration(
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      helperText: helperText,
      errorText: errorText,
      filled: filled,
      fillColor: fillColor ?? Colors.white,
      contentPadding: inputPaddingMedium,
      hintStyle: inputHintStyle,
      helperStyle: inputHelperStyle,
      errorStyle: inputErrorStyle,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        borderSide: inputBorderDefault,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        borderSide: inputBorderDefault,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        borderSide: inputBorderFocused,
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        borderSide: inputBorderError,
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        borderSide: inputBorderError,
      ),
    );
  }

  // 无边框输入框装饰
  static InputDecoration inputDecorationNoBorder({
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool filled = true,
    Color? fillColor,
    EdgeInsets? contentPadding,
    bool isDense = false, // 添加 isDense 参数，使输入框更加紧凑
  }) {
    return InputDecoration(
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      filled: filled,
      fillColor: fillColor ?? Colors.white,
      contentPadding: contentPadding ?? inputPaddingSmall,
      hintStyle: inputHintStyle,
      isDense: isDense, // 使用 isDense 参数
      border: InputBorder.none,
      enabledBorder: InputBorder.none,
      focusedBorder: InputBorder.none,
      errorBorder: InputBorder.none,
      focusedErrorBorder: InputBorder.none,
    );
  }

  // 带单位的输入框容器装饰
  static BoxDecoration inputWithUnitDecoration({
    double borderRadius = inputRadiusMedium,
    Color? borderColor,
    Color? backgroundColor,
  }) {
    return BoxDecoration(
      color: backgroundColor ?? Colors.white,
      borderRadius: BorderRadius.circular(borderRadius),
      border: Border.all(
        color: borderColor ?? AppColors.border,
        width: 1.0,
      ),
    );
  }

  // 选择器样式
  static BoxDecoration selectorDecoration({
    double borderRadius = inputRadiusMedium,
    Color? borderColor,
    Color? backgroundColor,
  }) {
    return BoxDecoration(
      color: backgroundColor ?? Colors.white,
      borderRadius: BorderRadius.circular(borderRadius),
      border: Border.all(
        color: borderColor ?? AppColors.border,
        width: 1.0,
      ),
    );
  }

  // 选择芯片样式
  static ChipThemeData chipThemeData() {
    return ChipThemeData(
      backgroundColor: Colors.transparent,
      disabledColor: AppColors.background,
      selectedColor: AppColors.primary10,
      secondarySelectedColor: AppColors.primary,
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 0),
      labelStyle: const TextStyle(
        fontSize: 13.0,
        color: AppColors.textSecondary,
      ),
      secondaryLabelStyle: const TextStyle(
        fontSize: 13.0,
        fontWeight: FontWeight.w500,
        color: AppColors.primary,
      ),
      brightness: Brightness.light,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
        side: const BorderSide(color: AppColors.border),
      ),
    );
  }
}

/// 应用布局规范
/// 定义应用中使用的常用布局常量
class AppLayouts {
  // 常用内边距组合
  static const EdgeInsets contentPadding = EdgeInsets.all(AppSizes.paddingMedium);
  static const EdgeInsets horizontalPadding = EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium);
  static const EdgeInsets verticalPadding = EdgeInsets.symmetric(vertical: AppSizes.paddingMedium);
  static const EdgeInsets cardPadding = EdgeInsets.all(AppSizes.paddingMedium);
  static const EdgeInsets listItemPadding = EdgeInsets.symmetric(
    horizontal: AppSizes.paddingMedium,
    vertical: AppSizes.paddingSmall,
  );

  // 常用间距
  static const double sectionSpacing = AppSizes.paddingLarge;
  static const double itemSpacing = AppSizes.paddingMedium;
  static const double elementSpacing = AppSizes.paddingSmall;

  // 常用外边距
  static const EdgeInsets cardMargin = EdgeInsets.only(bottom: AppSizes.paddingMedium);
  static const EdgeInsets sectionMargin = EdgeInsets.only(bottom: AppSizes.paddingLarge);
  static const EdgeInsets itemMargin = EdgeInsets.only(bottom: AppSizes.paddingMedium);
}

/// 色彩使用指南
/// 提供不同色阶的使用建议，帮助开发者正确使用色彩
class ColorUsageGuide {
  /// 主色调使用指南
  static const Map<String, String> primaryUsage = {
    '100': '轻度背景、浅色卡片、非活跃状态',
    '200': '次要背景、轻度强调元素',
    '300': '轻度强调元素、进度条背景',
    '400': '次要元素、轻度按钮',
    '500': '主要按钮、重要元素、标准状态',
    '600': '悬停状态、活跃元素',
    '700': '按下状态、选中状态',
    '800': '深色背景、重要强调',
    '900': '极深色背景、最高强调',
  };

  /// 功能色使用指南（成功、信息、警告、错误）
  static const Map<String, String> functionalUsage = {
    '100': '提示背景、轻度提示、浅色通知',
    '200': '次要提示背景、轻度强调',
    '300': '轻度强调元素、进度指示',
    '400': '次要提示元素、轻度按钮',
    '500': '主要提示元素、标准状态、图标',
    '600': '悬停状态、活跃元素',
    '700': '按下状态、选中状态',
    '800': '深色提示、重要强调',
    '900': '极深色提示、最高强调',
  };

  /// 常见界面元素色彩使用建议
  static const Map<String, String> elementUsage = {
    '主要按钮': 'AppColors.primary',
    '次要按钮': 'AppColors.secondary',
    '文字按钮': 'AppColors.primary',
    '悬停状态': 'AppColors.primaryDark600',
    '按下状态': 'AppColors.primaryDark700',
    '禁用状态': 'AppColors.textDisabled',
    '进度条': 'AppColors.primary',
    '进度条背景': 'AppColors.primaryLight300',
    '卡片边框': 'AppColors.border',
    '卡片阴影': 'AppShadows.low',
    '成功提示': 'AppColors.success',
    '信息提示': 'AppColors.info',
    '警告提示': 'AppColors.warning',
    '错误提示': 'AppColors.error',
    '链接文字': 'AppColors.info',
    '选中状态': 'AppColors.primary',
    '未选中状态': 'AppColors.textTertiary',
    '轻度背景': 'AppColors.primaryLight100',
    '标签背景': 'AppColors.primaryLight200',
    '数据可视化图表': '不同色阶的主色调和功能色',
  };

  /// 文字颜色使用建议
  static const Map<String, String> textUsage = {
    '主标题': 'AppColors.text',
    '次级标题': 'AppColors.text',
    '正文文本': 'AppColors.textSecondary',
    '次要文本': 'AppColors.textTertiary',
    '辅助文本': 'AppColors.textAssist',
    '提示文本': 'AppColors.textPlaceholder',
    '禁用文本': 'AppColors.textAssist',
    '链接文本': 'AppColors.info',
    '错误文本': 'AppColors.error',
    '成功文本': 'AppColors.success',
    '警告文本': 'AppColors.warning',
    '按钮文本': 'Colors.white 或 AppColors.primary',
  };

  /// 表单元素使用指南
  static const Map<String, String> formElementUsage = {
    // 输入框尺寸
    '大型输入框': 'AppInputStyles.inputHeightLarge (48px) - 用于表单中的主要输入框',
    '中型输入框': 'AppInputStyles.inputHeightMedium (40px) - 用于一般场景的输入框',
    '小型输入框': 'AppInputStyles.inputHeightSmall (36px) - 用于紧凑布局中的输入框',
    '紧凑型输入框': 'AppInputStyles.inputHeightCompact (32px) - 用于空间有限的场景',

    // 输入框宽度
    '大型输入框宽度': 'AppInputStyles.inputWidthLarge (240px) - 用于长文本输入',
    '中型输入框宽度': 'AppInputStyles.inputWidthMedium (180px) - 用于一般文本输入',
    '小型输入框宽度': 'AppInputStyles.inputWidthSmall (120px) - 用于短文本或数字输入',
    '紧凑型输入框宽度': 'AppInputStyles.inputWidthCompact (80px) - 用于极短文本或数字输入',

    // 输入框圆角
    '大型圆角': 'AppInputStyles.inputRadiusLarge (12px) - 用于强调的输入框',
    '中型圆角': 'AppInputStyles.inputRadiusMedium (8px) - 用于标准输入框',
    '小型圆角': 'AppInputStyles.inputRadiusSmall (4px) - 用于紧凑型输入框',

    // 输入框样式
    '标准输入框': 'AppInputStyles.inputDecoration() - 带边框的标准输入框',
    '无边框输入框': 'AppInputStyles.inputDecorationNoBorder() - 无边框的输入框，用于内嵌场景',
    '带单位输入框': 'AppInputStyles.inputWithUnitDecoration() - 带单位的输入框，如时间、数量等',

    // 选择器样式
    '标准选择器': 'AppInputStyles.selectorDecoration() - 用于下拉选择、日期选择等',
    '选择芯片': 'AppInputStyles.chipThemeData() - 用于多选场景',

    // 文本样式
    '输入框文本': 'AppInputStyles.inputTextStyle - 输入框中的文本样式',
    '输入框提示文本': 'AppInputStyles.inputHintStyle - 输入框中的提示文本样式',
    '输入框标签': 'AppInputStyles.inputLabelStyle - 输入框上方的标签文本样式',
    '输入框辅助文本': 'AppInputStyles.inputHelperStyle - 输入框下方的辅助文本样式',
    '输入框错误文本': 'AppInputStyles.inputErrorStyle - 输入框错误提示文本样式',
  };
}
