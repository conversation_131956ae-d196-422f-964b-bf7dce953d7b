import 'package:flutter/material.dart';
import '../theme/constants.dart';

/// 卡片类型
enum AppCardType {
  /// 标准卡片 - 带边框和阴影
  standard,

  /// 简单卡片 - 无边框，只有阴影
  simple,

  /// 扁平卡片 - 无边框无阴影
  flat,

  /// 选中卡片 - 带主题色边框
  selected,

  /// 强调卡片 - 带主题色背景
  accent,
}

/// 应用卡片组件
/// 提供统一的卡片样式，用于展示内容
class AppCard extends StatelessWidget {
  /// 卡片标题
  final String? title;

  /// 卡片内容
  final Widget child;

  /// 卡片右上角操作按钮
  final Widget? action;

  /// 卡片类型
  final AppCardType type;

  /// 卡片内边距
  final EdgeInsetsGeometry padding;

  /// 卡片外边距
  final EdgeInsetsGeometry margin;

  /// 卡片高度
  final double? height;

  /// 卡片宽度
  final double? width;

  /// 卡片背景色
  final Color? backgroundColor;

  /// 卡片边框颜色
  final Color? borderColor;

  /// 卡片圆角大小
  final double borderRadius;

  /// 卡片阴影
  final List<BoxShadow>? boxShadow;

  /// 点击事件
  final VoidCallback? onTap;

  /// 长按事件
  final VoidCallback? onLongPress;

  /// 是否使用墨水效果
  final bool useInkWell;

  /// 墨水效果颜色
  final Color? splashColor;

  const AppCard({
    super.key,
    this.title,
    required this.child,
    this.action,
    this.type = AppCardType.standard,
    this.padding = const EdgeInsets.all(16),
    this.margin = const EdgeInsets.all(0),
    this.height,
    this.width,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius = AppSizes.radiusLarge,
    this.boxShadow,
    this.onTap,
    this.onLongPress,
    this.useInkWell = true,
    this.splashColor,
  });

  @override
  Widget build(BuildContext context) {
    // 根据卡片类型设置样式
    final BoxDecoration decoration = _getCardDecoration();

    final content = Container(
      padding: padding,
      width: width,
      height: height,
      decoration: decoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题和操作按钮
          if (title != null || action != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (title != null)
                    Text(
                      title!,
                      style: AppTextStyles.headline3,
                    )
                  else
                    const SizedBox.shrink(),
                  if (action != null) action! else const SizedBox.shrink(),
                ],
              ),
            ),

          // 内容
          child,
        ],
      ),
    );

    // 如果有点击事件，包装在InkWell中
    if ((onTap != null || onLongPress != null) && useInkWell) {
      return Padding(
        padding: margin,
        child: Material(
          color: AppColors.transparent,
          child: InkWell(
            onTap: onTap,
            onLongPress: onLongPress,
            borderRadius: BorderRadius.circular(borderRadius),
            splashColor: splashColor,
            child: content,
          ),
        ),
      );
    }

    // 如果有点击事件但不使用墨水效果，包装在GestureDetector中
    if ((onTap != null || onLongPress != null) && !useInkWell) {
      return Padding(
        padding: margin,
        child: GestureDetector(
          onTap: onTap,
          onLongPress: onLongPress,
          child: content,
        ),
      );
    }

    // 否则直接返回内容
    return Padding(
      padding: margin,
      child: content,
    );
  }

  /// 根据卡片类型获取装饰样式
  BoxDecoration _getCardDecoration() {
    switch (type) {
      case AppCardType.standard:
        return AppDecorations.card(
          color: backgroundColor,
          borderColor: borderColor,
          borderRadius: borderRadius,
          boxShadow: boxShadow,
        );

      case AppCardType.simple:
        return AppDecorations.cardNoBorder(
          color: backgroundColor,
          borderRadius: borderRadius,
          boxShadow: boxShadow,
        );

      case AppCardType.flat:
        return BoxDecoration(
          color: backgroundColor ?? AppColors.white,
          borderRadius: BorderRadius.circular(borderRadius),
        );

      case AppCardType.selected:
        return BoxDecoration(
          color: backgroundColor ?? AppColors.primary10,
          borderRadius: BorderRadius.circular(borderRadius),
          border: Border.all(color: borderColor ?? AppColors.primary),
        );

      case AppCardType.accent:
        return BoxDecoration(
          color: backgroundColor ?? AppColors.primary20,
          borderRadius: BorderRadius.circular(borderRadius),
        );
    }
  }
}

/// 卡片组工厂类
/// 提供创建不同类型卡片的便捷方法
class AppCardFactory {
  /// 创建标准卡片
  static AppCard standard({
    String? title,
    required Widget child,
    Widget? action,
    EdgeInsetsGeometry padding = const EdgeInsets.all(16),
    EdgeInsetsGeometry margin = const EdgeInsets.all(0),
    double? height,
    double? width,
    Color? backgroundColor,
    Color? borderColor,
    double borderRadius = AppSizes.radiusLarge,
    List<BoxShadow>? boxShadow,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
  }) {
    return AppCard(
      title: title,
      action: action,
      type: AppCardType.standard,
      padding: padding,
      margin: margin,
      height: height,
      width: width,
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      borderRadius: borderRadius,
      boxShadow: boxShadow,
      onTap: onTap,
      onLongPress: onLongPress,
      child: child,
    );
  }

  /// 创建简单卡片
  static AppCard simple({
    String? title,
    required Widget child,
    Widget? action,
    EdgeInsetsGeometry padding = const EdgeInsets.all(16),
    EdgeInsetsGeometry margin = const EdgeInsets.all(0),
    double? height,
    double? width,
    Color? backgroundColor,
    double borderRadius = AppSizes.radiusLarge,
    List<BoxShadow>? boxShadow,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
  }) {
    return AppCard(
      title: title,
      action: action,
      type: AppCardType.simple,
      padding: padding,
      margin: margin,
      height: height,
      width: width,
      backgroundColor: backgroundColor,
      borderRadius: borderRadius,
      boxShadow: boxShadow,
      onTap: onTap,
      onLongPress: onLongPress,
      child: child,
    );
  }

  /// 创建扁平卡片
  static AppCard flat({
    String? title,
    required Widget child,
    Widget? action,
    EdgeInsetsGeometry padding = const EdgeInsets.all(16),
    EdgeInsetsGeometry margin = const EdgeInsets.all(0),
    double? height,
    double? width,
    Color? backgroundColor,
    double borderRadius = AppSizes.radiusLarge,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
  }) {
    return AppCard(
      title: title,
      action: action,
      type: AppCardType.flat,
      padding: padding,
      margin: margin,
      height: height,
      width: width,
      backgroundColor: backgroundColor,
      borderRadius: borderRadius,
      onTap: onTap,
      onLongPress: onLongPress,
      child: child,
    );
  }

  /// 创建选中卡片
  static AppCard selected({
    String? title,
    required Widget child,
    Widget? action,
    EdgeInsetsGeometry padding = const EdgeInsets.all(16),
    EdgeInsetsGeometry margin = const EdgeInsets.all(0),
    double? height,
    double? width,
    Color? backgroundColor,
    Color? borderColor,
    double borderRadius = AppSizes.radiusLarge,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
  }) {
    return AppCard(
      title: title,
      action: action,
      type: AppCardType.selected,
      padding: padding,
      margin: margin,
      height: height,
      width: width,
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      borderRadius: borderRadius,
      onTap: onTap,
      onLongPress: onLongPress,
      child: child,
    );
  }

  /// 创建强调卡片
  static AppCard accent({
    String? title,
    required Widget child,
    Widget? action,
    EdgeInsetsGeometry padding = const EdgeInsets.all(16),
    EdgeInsetsGeometry margin = const EdgeInsets.all(0),
    double? height,
    double? width,
    Color? backgroundColor,
    double borderRadius = AppSizes.radiusLarge,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
  }) {
    return AppCard(
      title: title,
      action: action,
      type: AppCardType.accent,
      padding: padding,
      margin: margin,
      height: height,
      width: width,
      backgroundColor: backgroundColor,
      borderRadius: borderRadius,
      onTap: onTap,
      onLongPress: onLongPress,
      child: child,
    );
  }
}
