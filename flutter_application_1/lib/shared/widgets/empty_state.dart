import 'package:flutter/material.dart';
import '../theme/constants.dart';

/// 空状态组件
/// 用于显示列表或页面的空状态
class EmptyState extends StatelessWidget {
  /// 图标
  final IconData? icon;
  
  /// 图片
  final String? image;
  
  /// 标题
  final String title;
  
  /// 描述
  final String? description;
  
  /// 操作按钮
  final Widget? action;
  
  /// 图标尺寸
  final double iconSize;
  
  /// 图片宽度
  final double? imageWidth;
  
  /// 图片高度
  final double? imageHeight;
  
  /// 内边距
  final EdgeInsetsGeometry padding;
  
  const EmptyState({
    super.key,
    this.icon,
    this.image,
    required this.title,
    this.description,
    this.action,
    this.iconSize = 64,
    this.imageWidth,
    this.imageHeight,
    this.padding = const EdgeInsets.all(24),
  }) : assert(icon != null || image != null, '图标和图片不能同时为空');

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 图标或图片
            if (icon != null)
              Icon(
                icon,
                size: iconSize,
                color: AppColors.textHint,
              )
            else if (image != null)
              Image.asset(
                image!,
                width: imageWidth,
                height: imageHeight,
              ),
            
            const SizedBox(height: 16),
            
            // 标题
            Text(
              title,
              style: AppTextStyles.headline3.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            
            // 描述
            if (description != null) ...[
              const SizedBox(height: 8),
              Text(
                description!,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textHint,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            
            // 操作按钮
            if (action != null) ...[
              const SizedBox(height: 24),
              action!,
            ],
          ],
        ),
      ),
    );
  }
}
