import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../theme/constants.dart';
import '../../core/models/goal_milestone.dart';
import '../../features/task/providers/goal_state.dart';

/// 里程碑日期选择器
/// 在标准日期选择器的基础上，添加了快速选择里程碑日期的功能
class MilestoneDatePicker extends ConsumerWidget {
  final DateTime? initialDate;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final String title;
  final Function(DateTime?) onDateSelected;
  final bool useRootNavigator;
  final String buttonText;

  const MilestoneDatePicker({
    super.key,
    this.initialDate,
    this.firstDate,
    this.lastDate,
    this.title = '选择日期',
    required this.onDateSelected,
    this.useRootNavigator = true,
    this.buttonText = '选择日期',
  });

  /// 显示里程碑选择对话框
  Future<void> _showMilestoneSelector(BuildContext context, WidgetRef ref) async {
    // 获取当前目标和里程碑
    final goalState = ref.read(goalStateProvider);
    final currentGoal = goalState.currentGoal;
    final milestones = goalState.milestones;

    // 如果没有目标，直接显示标准日期选择器
    if (currentGoal == null) {
      _showStandardDatePicker(context);
      return;
    }

    // 如果没有里程碑，但有目标结束日期，显示简化版选择器
    if (milestones.isEmpty) {
      _showSimplifiedSelector(context, currentGoal);
      return;
    }

    // 按日期排序里程碑
    final sortedMilestones = List<Milestone>.from(milestones)
      ..sort((a, b) => a.date.compareTo(b.date));

    // 过滤掉已过期的里程碑
    final futureMilestones = sortedMilestones
        .where((m) => m.date.isAfter(DateTime.now()))
        .toList();

    // 显示里程碑选择对话框
    showDialog(
      context: context,
      builder: (BuildContext context) {
        final screenHeight = MediaQuery.of(context).size.height;
        final maxDialogHeight = screenHeight * 0.6; // 最大高度为屏幕高度的60%

        return AlertDialog(
          title: const Text('选择截止日期'),
          contentPadding: const EdgeInsets.fromLTRB(0, 16, 0, 0),
          content: Container(
            width: double.maxFinite,
            constraints: BoxConstraints(maxHeight: maxDialogHeight),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 里程碑标题
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Text('目标里程碑', style: AppTextStyles.labelMedium),
                ),
                const SizedBox(height: 8),

                // 里程碑列表 - 可滚动区域
                Flexible(
                  child: futureMilestones.isEmpty
                    ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        child: Text('没有未来的里程碑日期可选', style: AppTextStyles.bodySmall),
                      )
                    : ListView.builder(
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        itemCount: futureMilestones.length,
                        itemBuilder: (context, index) => _buildMilestoneItem(context, futureMilestones[index]),
                      ),
                ),

                // 分隔线
                const Divider(height: 1),

                // 底部固定选项 - 压缩设计
                if (currentGoal.endDate.isAfter(DateTime.now()))
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                      onDateSelected(currentGoal.endDate);
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      child: Row(
                        children: [
                          const Icon(Icons.event_available, size: 18, color: AppColors.primary),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('目标结束日期', style: AppTextStyles.bodyMedium),
                                Text(
                                  _formatDate(currentGoal.endDate),
                                  style: AppTextStyles.bodySmall.copyWith(color: AppColors.textTertiary),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                const Divider(height: 1),

                // 自定义日期选项
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                    _showStandardDatePicker(context);
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    child: Row(
                      children: [
                        const Icon(Icons.calendar_today, size: 18, color: AppColors.primary),
                        const SizedBox(width: 12),
                        Text('选择其他日期', style: AppTextStyles.bodyMedium),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
          ],
        );
      },
    );
  }

  /// 构建里程碑项
  Widget _buildMilestoneItem(BuildContext context, Milestone milestone) {
    return InkWell(
      onTap: () {
        Navigator.pop(context);
        onDateSelected(milestone.date);
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 24),
        child: Row(
          children: [
            const Icon(Icons.flag, size: 18, color: AppColors.primary),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    milestone.name,
                    style: AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    _formatDate(milestone.date),
                    style: AppTextStyles.bodySmall.copyWith(color: AppColors.textTertiary),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示简化版选择器（只有目标结束日期和自定义日期选项）
  Future<void> _showSimplifiedSelector(BuildContext context, Goal currentGoal) async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('选择截止日期'),
          contentPadding: const EdgeInsets.fromLTRB(0, 16, 0, 0),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 目标结束日期标题
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Text('目标日期', style: AppTextStyles.labelMedium),
                ),
                const SizedBox(height: 8),

                // 目标结束日期选项
                if (currentGoal.endDate.isAfter(DateTime.now()))
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                      onDateSelected(currentGoal.endDate);
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      child: Row(
                        children: [
                          const Icon(Icons.event_available, size: 18, color: AppColors.primary),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('目标结束日期', style: AppTextStyles.bodyMedium),
                                Text(
                                  _formatDate(currentGoal.endDate),
                                  style: AppTextStyles.bodySmall.copyWith(color: AppColors.textTertiary),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                const Divider(height: 1),

                // 自定义日期选项
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                    _showStandardDatePicker(context);
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    child: Row(
                      children: [
                        const Icon(Icons.calendar_today, size: 18, color: AppColors.primary),
                        const SizedBox(width: 12),
                        Text('选择其他日期', style: AppTextStyles.bodyMedium),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
          ],
        );
      },
    );
  }

  /// 显示标准日期选择器
  Future<void> _showStandardDatePicker(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate ?? DateTime.now(),
      firstDate: firstDate ?? DateTime(2000),
      lastDate: lastDate ?? DateTime(2100),
      locale: const Locale('zh', 'CN'),
      initialEntryMode: DatePickerEntryMode.calendarOnly,
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: AppColors.text,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      onDateSelected(pickedDate);
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      onTap: () => _showMilestoneSelector(context, ref),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.border),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              initialDate != null
                  ? _formatDate(initialDate!)
                  : buttonText,
              style: AppTextStyles.bodyMedium,
            ),
            const Icon(Icons.calendar_today, color: AppColors.textHint, size: 20),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final DateFormat formatter = DateFormat('yyyy-MM-dd', 'zh_CN');
    return formatter.format(date);
  }
}
