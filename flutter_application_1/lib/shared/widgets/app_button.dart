import 'package:flutter/material.dart';
import '../theme/constants.dart';

/// 按钮类型
enum AppButtonType {
  /// 主要按钮
  primary,

  /// 次要按钮
  secondary,

  /// 文本按钮
  text,

  /// 轮廓按钮
  outline,

  /// 图标按钮
  icon,
}

/// 按钮尺寸
enum AppButtonSize {
  /// 小按钮
  small,

  /// 中等按钮
  medium,

  /// 大按钮
  large,
}

/// 应用按钮组件
/// 提供统一的按钮样式
class AppButton extends StatelessWidget {
  /// 按钮文本
  final String? text;

  /// 按钮图标
  final IconData? icon;

  /// 按钮类型
  final AppButtonType type;

  /// 按钮尺寸
  final AppButtonSize size;

  /// 按钮宽度
  final double? width;

  /// 按钮高度
  final double? height;

  /// 按钮内边距
  final EdgeInsetsGeometry? padding;

  /// 按钮圆角
  final double? borderRadius;

  /// 按钮前景色
  final Color? foregroundColor;

  /// 按钮背景色
  final Color? backgroundColor;

  /// 按钮边框颜色
  final Color? borderColor;

  /// 按钮是否禁用
  final bool isDisabled;

  /// 按钮是否加载中
  final bool isLoading;

  /// 点击事件
  final VoidCallback? onPressed;

  const AppButton({
    super.key,
    this.text,
    this.icon,
    this.type = AppButtonType.primary,
    this.size = AppButtonSize.medium,
    this.width,
    this.height,
    this.padding,
    this.borderRadius,
    this.foregroundColor,
    this.backgroundColor,
    this.borderColor,
    this.isDisabled = false,
    this.isLoading = false,
    this.onPressed,
  }) : assert(text != null || icon != null, '文本和图标不能同时为空');

  @override
  Widget build(BuildContext context) {
    // 根据按钮类型和尺寸设置样式
    final buttonStyle = _getButtonStyle(context);

    // 构建按钮内容
    Widget buttonContent = _buildButtonContent();

    // 如果按钮正在加载，显示加载指示器
    if (isLoading) {
      buttonContent = Stack(
        alignment: Alignment.center,
        children: [
          Opacity(
            opacity: 0.0,
            child: buttonContent,
          ),
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                _getForegroundColor(),
              ),
            ),
          ),
        ],
      );
    }

    // 根据按钮类型构建不同的按钮
    switch (type) {
      case AppButtonType.primary:
      case AppButtonType.secondary:
      case AppButtonType.outline:
        return SizedBox(
          width: width,
          height: height,
          child: ElevatedButton(
            onPressed: (isDisabled || isLoading) ? null : onPressed,
            style: buttonStyle,
            child: buttonContent,
          ),
        );

      case AppButtonType.text:
        return SizedBox(
          width: width,
          height: height,
          child: TextButton(
            onPressed: (isDisabled || isLoading) ? null : onPressed,
            style: buttonStyle,
            child: buttonContent,
          ),
        );

      case AppButtonType.icon:
        return SizedBox(
          width: width ?? _getIconSize() + 16,
          height: height ?? _getIconSize() + 16,
          child: IconButton(
            onPressed: (isDisabled || isLoading) ? null : onPressed,
            icon: buttonContent,
            style: buttonStyle,
            padding: EdgeInsets.zero,
          ),
        );
    }
  }

  /// 构建按钮内容
  Widget _buildButtonContent() {
    // 只有图标
    if (text == null && icon != null) {
      return Icon(
        icon,
        size: _getIconSize(),
        color: _getForegroundColor(),
      );
    }

    // 只有文本
    if (text != null && icon == null) {
      return Text(
        text!,
        style: _getTextStyle(),
      );
    }

    // 图标和文本
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: _getIconSize(),
          color: _getForegroundColor(),
        ),
        const SizedBox(width: 8),
        Text(
          text!,
          style: _getTextStyle(),
        ),
      ],
    );
  }

  /// 获取按钮样式
  ButtonStyle _getButtonStyle(BuildContext context) {
    switch (type) {
      case AppButtonType.primary:
        return ElevatedButton.styleFrom(
          foregroundColor: _getForegroundColor(),
          backgroundColor: _getBackgroundColor(),
          padding: _getPadding(),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius()),
          ),
          elevation: 0,
          disabledForegroundColor: AppColors.whiteWithAlpha(AppColors.alpha50),
          disabledBackgroundColor: AppColors.primaryWithAlpha(AppColors.alpha50),
        );

      case AppButtonType.secondary:
        return ElevatedButton.styleFrom(
          foregroundColor: _getForegroundColor(),
          backgroundColor: _getBackgroundColor(),
          padding: _getPadding(),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius()),
          ),
          elevation: 0,
          disabledForegroundColor: AppColors.whiteWithAlpha(AppColors.alpha50),
          disabledBackgroundColor: AppColors.secondaryWithAlpha(AppColors.alpha50),
        );

      case AppButtonType.outline:
        return ElevatedButton.styleFrom(
          foregroundColor: _getForegroundColor(),
          backgroundColor: AppColors.transparent,
          padding: _getPadding(),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius()),
            side: BorderSide(
              color: isDisabled
                  ? _getBorderColor().withOpacity(0.5)
                  : _getBorderColor(),
            ),
          ),
          elevation: 0,
          shadowColor: AppColors.transparent,
        );

      case AppButtonType.text:
        return TextButton.styleFrom(
          foregroundColor: _getForegroundColor(),
          backgroundColor: AppColors.transparent,
          padding: _getPadding(),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius()),
          ),
          disabledForegroundColor: AppColors.textHint,
        );

      case AppButtonType.icon:
        return IconButton.styleFrom(
          foregroundColor: _getForegroundColor(),
          backgroundColor: _getBackgroundColor(),
          padding: _getPadding(),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius()),
          ),
          disabledForegroundColor: AppColors.textHint,
        );
    }
  }

  /// 获取按钮前景色
  Color _getForegroundColor() {
    if (foregroundColor != null) {
      return foregroundColor!;
    }

    switch (type) {
      case AppButtonType.primary:
        return AppColors.white;
      case AppButtonType.secondary:
        return AppColors.white;
      case AppButtonType.outline:
        return AppColors.primary;
      case AppButtonType.text:
        return AppColors.primary;
      case AppButtonType.icon:
        return AppColors.primary;
    }
  }

  /// 获取按钮背景色
  Color _getBackgroundColor() {
    if (backgroundColor != null) {
      return backgroundColor!;
    }

    switch (type) {
      case AppButtonType.primary:
        return AppColors.primary;
      case AppButtonType.secondary:
        return AppColors.secondary;
      case AppButtonType.outline:
        return AppColors.transparent;
      case AppButtonType.text:
        return AppColors.transparent;
      case AppButtonType.icon:
        return AppColors.transparent;
    }
  }

  /// 获取按钮边框颜色
  Color _getBorderColor() {
    if (borderColor != null) {
      return borderColor!;
    }

    switch (type) {
      case AppButtonType.outline:
        return AppColors.primary;
      default:
        return AppColors.transparent;
    }
  }

  /// 获取按钮内边距
  EdgeInsetsGeometry _getPadding() {
    if (padding != null) {
      return padding!;
    }

    switch (size) {
      case AppButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: AppSizes.paddingSmall, vertical: AppSizes.paddingXSmall);
      case AppButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium, vertical: AppSizes.paddingSmall);
      case AppButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: AppSizes.paddingLarge, vertical: AppSizes.paddingMedium);
    }
  }

  /// 获取按钮圆角
  double _getBorderRadius() {
    if (borderRadius != null) {
      return borderRadius!;
    }

    switch (size) {
      case AppButtonSize.small:
        return AppSizes.radiusSmall;
      case AppButtonSize.medium:
        return AppSizes.radiusMedium;
      case AppButtonSize.large:
        return AppSizes.radiusLarge;
    }
  }

  /// 获取按钮文本样式
  TextStyle _getTextStyle() {
    switch (size) {
      case AppButtonSize.small:
        return AppTextStyles.buttonSmall.copyWith(
          color: _getForegroundColor(),
        );
      case AppButtonSize.medium:
        return AppTextStyles.buttonMedium.copyWith(
          color: _getForegroundColor(),
        );
      case AppButtonSize.large:
        return AppTextStyles.buttonLarge.copyWith(
          color: _getForegroundColor(),
        );
    }
  }

  /// 获取图标尺寸
  double _getIconSize() {
    switch (size) {
      case AppButtonSize.small:
        return AppSizes.iconSmall;
      case AppButtonSize.medium:
        return AppSizes.iconMedium - 4; // 20px
      case AppButtonSize.large:
        return AppSizes.iconMedium;
    }
  }
}
