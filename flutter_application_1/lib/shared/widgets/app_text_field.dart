import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/constants.dart';

/// 应用输入框组件
/// 提供统一的输入框样式
class AppTextField extends StatelessWidget {
  /// 控制器
  final TextEditingController? controller;
  
  /// 焦点节点
  final FocusNode? focusNode;
  
  /// 输入框标签
  final String? label;
  
  /// 输入框提示文本
  final String? hint;
  
  /// 输入框前缀图标
  final IconData? prefixIcon;
  
  /// 输入框后缀图标
  final IconData? suffixIcon;
  
  /// 输入框后缀图标点击事件
  final VoidCallback? onSuffixIconPressed;
  
  /// 输入框前缀组件
  final Widget? prefix;
  
  /// 输入框后缀组件
  final Widget? suffix;
  
  /// 输入框是否密码
  final bool isPassword;
  
  /// 输入框是否只读
  final bool readOnly;
  
  /// 输入框是否禁用
  final bool disabled;
  
  /// 输入框是否必填
  final bool required;
  
  /// 输入框错误文本
  final String? errorText;
  
  /// 输入框帮助文本
  final String? helperText;
  
  /// 输入框最大行数
  final int? maxLines;
  
  /// 输入框最小行数
  final int? minLines;
  
  /// 输入框最大长度
  final int? maxLength;
  
  /// 输入框键盘类型
  final TextInputType? keyboardType;
  
  /// 输入框文本对齐方式
  final TextAlign textAlign;
  
  /// 输入框文本样式
  final TextStyle? textStyle;
  
  /// 输入框内边距
  final EdgeInsetsGeometry? contentPadding;
  
  /// 输入框边框圆角
  final double borderRadius;
  
  /// 输入框填充颜色
  final Color? fillColor;
  
  /// 输入框边框颜色
  final Color? borderColor;
  
  /// 输入框聚焦边框颜色
  final Color? focusBorderColor;
  
  /// 输入框错误边框颜色
  final Color? errorBorderColor;
  
  /// 输入框输入格式化器
  final List<TextInputFormatter>? inputFormatters;
  
  /// 输入框内容变化回调
  final ValueChanged<String>? onChanged;
  
  /// 输入框提交回调
  final ValueChanged<String>? onSubmitted;
  
  /// 输入框点击回调
  final VoidCallback? onTap;
  
  /// 输入框完成回调
  final VoidCallback? onEditingComplete;
  
  const AppTextField({
    super.key,
    this.controller,
    this.focusNode,
    this.label,
    this.hint,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconPressed,
    this.prefix,
    this.suffix,
    this.isPassword = false,
    this.readOnly = false,
    this.disabled = false,
    this.required = false,
    this.errorText,
    this.helperText,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.keyboardType,
    this.textAlign = TextAlign.start,
    this.textStyle,
    this.contentPadding,
    this.borderRadius = 12,
    this.fillColor,
    this.borderColor,
    this.focusBorderColor,
    this.errorBorderColor,
    this.inputFormatters,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.onEditingComplete,
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      focusNode: focusNode,
      obscureText: isPassword,
      readOnly: readOnly,
      enabled: !disabled,
      maxLines: maxLines,
      minLines: minLines,
      maxLength: maxLength,
      keyboardType: keyboardType,
      textAlign: textAlign,
      style: textStyle ?? AppTextStyles.bodyMedium,
      inputFormatters: inputFormatters,
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      onTap: onTap,
      onEditingComplete: onEditingComplete,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        errorText: errorText,
        helperText: helperText,
        filled: true,
        fillColor: disabled
            ? (fillColor ?? Colors.grey.shade100)
            : (fillColor ?? Colors.white),
        contentPadding: contentPadding ??
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        prefixIcon: prefixIcon != null
            ? Icon(prefixIcon, color: AppColors.textSecondary)
            : prefix,
        suffixIcon: suffixIcon != null
            ? IconButton(
                icon: Icon(suffixIcon, color: AppColors.textSecondary),
                onPressed: disabled ? null : onSuffixIconPressed,
              )
            : suffix,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          borderSide: BorderSide(
            color: borderColor ?? AppColors.border,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          borderSide: BorderSide(
            color: borderColor ?? AppColors.border,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          borderSide: BorderSide(
            color: focusBorderColor ?? AppColors.primary,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          borderSide: BorderSide(
            color: errorBorderColor ?? AppColors.error,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          borderSide: BorderSide(
            color: errorBorderColor ?? AppColors.error,
          ),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          borderSide: BorderSide(
            color: Colors.grey.shade300,
          ),
        ),
        labelStyle: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textSecondary,
        ),
        hintStyle: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textHint,
        ),
        errorStyle: AppTextStyles.caption.copyWith(
          color: AppColors.error,
        ),
        helperStyle: AppTextStyles.caption.copyWith(
          color: AppColors.textSecondary,
        ),
      ),
    );
  }
}
