import 'package:flutter/material.dart';
import '../theme/constants.dart';

/// 消息类型枚举
enum MessageType {
  success,
  error,
  warning,
  info,
}

/// 上方悬浮提示消息管理器
/// 替代SnackBar，在屏幕上方显示提示消息，不被底部弹窗遮挡
class TopMessageOverlayManager {
  static final TopMessageOverlayManager _instance = TopMessageOverlayManager._internal();
  factory TopMessageOverlayManager() => _instance;
  TopMessageOverlayManager._internal();

  OverlayEntry? _overlayEntry;

  /// 是否正在显示消息
  bool get isShowing => _overlayEntry != null;

  /// 显示提示消息
  void showMessage({
    required BuildContext context,
    required String message,
    MessageType type = MessageType.info,
    Duration duration = const Duration(seconds: 3),
    VoidCallback? onTap,
  }) {
    // 如果已经有消息在显示，先移除
    hideMessage();

    _overlayEntry = OverlayEntry(
      builder: (context) => _TopMessageWidget(
        message: message,
        type: type,
        duration: duration,
        onTap: onTap,
        onDismiss: hideMessage,
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  /// 显示成功消息
  void showSuccess({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 3),
    VoidCallback? onTap,
  }) {
    showMessage(
      context: context,
      message: message,
      type: MessageType.success,
      duration: duration,
      onTap: onTap,
    );
  }

  /// 显示错误消息
  void showError({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 4),
    VoidCallback? onTap,
  }) {
    showMessage(
      context: context,
      message: message,
      type: MessageType.error,
      duration: duration,
      onTap: onTap,
    );
  }

  /// 显示警告消息
  void showWarning({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 3),
    VoidCallback? onTap,
  }) {
    showMessage(
      context: context,
      message: message,
      type: MessageType.warning,
      duration: duration,
      onTap: onTap,
    );
  }

  /// 显示信息消息
  void showInfo({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 3),
    VoidCallback? onTap,
  }) {
    showMessage(
      context: context,
      message: message,
      type: MessageType.info,
      duration: duration,
      onTap: onTap,
    );
  }

  /// 隐藏提示消息
  void hideMessage() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}

/// 上方提示消息组件
class _TopMessageWidget extends StatefulWidget {
  final String message;
  final MessageType type;
  final Duration duration;
  final VoidCallback? onTap;
  final VoidCallback onDismiss;

  const _TopMessageWidget({
    required this.message,
    required this.type,
    required this.duration,
    this.onTap,
    required this.onDismiss,
  });

  @override
  State<_TopMessageWidget> createState() => _TopMessageWidgetState();
}

class _TopMessageWidgetState extends State<_TopMessageWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    // 开始显示动画
    _animationController.forward();

    // 自动隐藏
    Future.delayed(widget.duration, () {
      if (mounted) {
        _dismiss();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _dismiss() async {
    await _animationController.reverse();
    widget.onDismiss();
  }

  Color _getBackgroundColor() {
    switch (widget.type) {
      case MessageType.success:
        return AppColors.success;
      case MessageType.error:
        return AppColors.error;
      case MessageType.warning:
        return Colors.orange;
      case MessageType.info:
        return AppColors.primary;
    }
  }

  IconData _getIcon() {
    switch (widget.type) {
      case MessageType.success:
        return Icons.check_circle;
      case MessageType.error:
        return Icons.error;
      case MessageType.warning:
        return Icons.warning;
      case MessageType.info:
        return Icons.info;
    }
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final topPadding = mediaQuery.padding.top;

    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return SlideTransition(
            position: _slideAnimation,
            child: FadeTransition(
              opacity: _opacityAnimation,
              child: Material(
                color: Colors.transparent,
                child: Container(
                  margin: EdgeInsets.only(
                    top: topPadding + 8,
                    left: 16,
                    right: 16,
                  ),
                  decoration: BoxDecoration(
                    color: _getBackgroundColor(),
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: InkWell(
                    onTap: widget.onTap ?? _dismiss,
                    borderRadius: BorderRadius.circular(8),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      child: Row(
                        children: [
                          Icon(
                            _getIcon(),
                            color: Colors.white,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              widget.message,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          GestureDetector(
                            onTap: _dismiss,
                            child: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
