import 'package:flutter/material.dart';
import '../theme/constants.dart';

/// 自定义时间选择器
/// 提供一个简洁直接的时间选择界面
class CustomTimePicker extends StatefulWidget {
  final TimeOfDay? initialTime;
  final Function(TimeOfDay?) onTimeSelected;
  final String placeholder;
  final bool allowClear;

  const CustomTimePicker({
    super.key,
    this.initialTime,
    required this.onTimeSelected,
    this.placeholder = '选择时间',
    this.allowClear = true,
  });

  /// 显示时间选择器对话框
  static Future<TimeOfDay?> show({
    required BuildContext context,
    TimeOfDay? initialTime,
  }) async {
    return showDialog<TimeOfDay?>(
      context: context,
      builder: (context) {
        TimeOfDay selectedTime = initialTime ?? TimeOfDay.now();
        bool hasSelectedTime = initialTime != null;

        return AlertDialog(
          contentPadding: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: StatefulBuilder(
            builder: (context, setState) {
              return SizedBox(
                width: 280,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 标题
                    const Center(
                      child: Text('选择时间', style: AppTextStyles.headline3),
                    ),
                    const SizedBox(height: 24),

                    // 时间选择器
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // 小时选择
                        SizedBox(
                          width: 60,
                          height: 120,
                          child: ListWheelScrollView.useDelegate(
                            itemExtent: 40,
                            perspective: 0.005,
                            diameterRatio: 1.5,
                            physics: const FixedExtentScrollPhysics(),
                            controller: FixedExtentScrollController(
                              initialItem: selectedTime.hour,
                            ),
                            onSelectedItemChanged: (index) {
                              setState(() {
                                selectedTime = TimeOfDay(hour: index % 24, minute: selectedTime.minute);
                                hasSelectedTime = true;
                              });
                            },
                            childDelegate: ListWheelChildLoopingListDelegate(
                              children: List.generate(24, (index) {
                                final isSelected = index == selectedTime.hour && hasSelectedTime;

                                return Center(
                                  child: Text(
                                    index.toString().padLeft(2, '0'),
                                    style: isSelected
                                        ? AppTextStyles.headline3.copyWith(
                                            color: AppColors.primary,
                                            fontWeight: FontWeight.bold,
                                          )
                                        : AppTextStyles.bodyLarge,
                                  ),
                                );
                              }),
                            ),
                          ),
                        ),

                        // 分隔符
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          child: Text(':', style: AppTextStyles.headline1.copyWith(
                            fontWeight: FontWeight.w500,
                          )),
                        ),

                        // 分钟选择
                        SizedBox(
                          width: 60,
                          height: 120,
                          child: ListWheelScrollView.useDelegate(
                            itemExtent: 40,
                            perspective: 0.005,
                            diameterRatio: 1.5,
                            physics: const FixedExtentScrollPhysics(),
                            controller: FixedExtentScrollController(
                              initialItem: selectedTime.minute,
                            ),
                            onSelectedItemChanged: (index) {
                              setState(() {
                                selectedTime = TimeOfDay(hour: selectedTime.hour, minute: index % 60);
                                hasSelectedTime = true;
                              });
                            },
                            childDelegate: ListWheelChildLoopingListDelegate(
                              children: List.generate(60, (index) {
                                final isSelected = index == selectedTime.minute && hasSelectedTime;

                                return Center(
                                  child: Text(
                                    index.toString().padLeft(2, '0'),
                                    style: isSelected
                                        ? AppTextStyles.headline3.copyWith(
                                            color: AppColors.primary,
                                            fontWeight: FontWeight.bold,
                                          )
                                        : AppTextStyles.bodyLarge,
                                  ),
                                );
                              }),
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // 操作按钮
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        // 取消按钮
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('取消', style: TextStyle(color: AppColors.textSecondary)),
                        ),

                        const SizedBox(width: 16),

                        // 确认按钮
                        TextButton(
                          onPressed: () => Navigator.pop(context, hasSelectedTime ? selectedTime : null),
                          child: const Text('确定', style: TextStyle(color: AppColors.primary)),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  @override
  State<CustomTimePicker> createState() => _CustomTimePickerState();
}

class _CustomTimePickerState extends State<CustomTimePicker> {
  TimeOfDay? _selectedTime;
  bool _isPickerVisible = false;

  @override
  void initState() {
    super.initState();
    _selectedTime = widget.initialTime;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 时间选择器触发器
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _isPickerVisible = !_isPickerVisible;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.border),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _selectedTime != null
                            ? '${_selectedTime!.hour.toString().padLeft(2, '0')}:${_selectedTime!.minute.toString().padLeft(2, '0')}'
                            : widget.placeholder,
                        style: AppTextStyles.bodyMedium,
                      ),
                      const Icon(Icons.access_time, color: AppColors.textHint, size: 20),
                    ],
                  ),
                ),
              ),
            ),

            // 清除按钮
            if (_selectedTime != null && widget.allowClear)
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: IconButton(
                  onPressed: () {
                    setState(() {
                      _selectedTime = null;
                    });
                    widget.onTimeSelected(null);
                  },
                  icon: const Icon(Icons.clear, color: AppColors.error),
                  tooltip: '清除时间',
                  constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
                  padding: EdgeInsets.zero,
                  iconSize: 20,
                ),
              ),
          ],
        ),

        // 自定义时间选择器面板
        if (_isPickerVisible)
          Container(
            margin: const EdgeInsets.only(top: 8),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.cardBackground,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [AppShadows.low],
              border: Border.all(color: AppColors.border),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 时间选择器
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 小时选择
                    _buildNumberPicker(
                      value: _selectedTime?.hour ?? TimeOfDay.now().hour,
                      maxValue: 23,
                      onChanged: (value) {
                        setState(() {
                          _selectedTime = TimeOfDay(
                            hour: value,
                            minute: _selectedTime?.minute ?? TimeOfDay.now().minute
                          );
                        });
                      },
                      isLooping: true,
                    ),

                    // 分隔符
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      child: Text(':', style: AppTextStyles.headline1.copyWith(
                        fontWeight: FontWeight.w500,
                      )),
                    ),

                    // 分钟选择
                    _buildNumberPicker(
                      value: _selectedTime?.minute ?? TimeOfDay.now().minute,
                      maxValue: 59,
                      onChanged: (value) {
                        setState(() {
                          _selectedTime = TimeOfDay(
                            hour: _selectedTime?.hour ?? TimeOfDay.now().hour,
                            minute: value
                          );
                        });
                      },
                      isLooping: true,
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // 操作按钮
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // 清除按钮
                    if (widget.allowClear)
                      TextButton(
                        onPressed: () {
                          setState(() {
                            _selectedTime = null;
                            _isPickerVisible = false;
                          });
                          widget.onTimeSelected(null);
                        },
                        child: const Text('清除', style: TextStyle(color: AppColors.error)),
                      ),

                    Row(
                      children: [
                        // 取消按钮
                        TextButton(
                          onPressed: () {
                            setState(() {
                              _isPickerVisible = false;
                            });
                          },
                          child: const Text('取消', style: TextStyle(color: AppColors.textSecondary)),
                        ),

                        const SizedBox(width: 16),

                        // 确认按钮
                        TextButton(
                          onPressed: () {
                            if (_selectedTime != null) {
                              widget.onTimeSelected(_selectedTime);
                            }
                            setState(() {
                              _isPickerVisible = false;
                            });
                          },
                          child: const Text('确定', style: TextStyle(color: AppColors.primary)),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
      ],
    );
  }

  // 构建数字选择器
  Widget _buildNumberPicker({
    required int value,
    required int maxValue,
    required Function(int) onChanged,
    int minValue = 0,
    bool isLooping = false,
  }) {
    return SizedBox(
      width: 60,
      height: 120,
      child: isLooping
          ? ListWheelScrollView.useDelegate(
              itemExtent: 40,
              perspective: 0.005,
              diameterRatio: 1.5,
              physics: const FixedExtentScrollPhysics(),
              controller: FixedExtentScrollController(
                initialItem: value,
              ),
              onSelectedItemChanged: (index) {
                onChanged(index % (maxValue + 1));
              },
              childDelegate: ListWheelChildLoopingListDelegate(
                children: List.generate(maxValue + 1, (index) {
                  final isSelected = index == value;

                  return Center(
                    child: Text(
                      index.toString().padLeft(2, '0'),
                      style: isSelected
                          ? AppTextStyles.headline3.copyWith(
                              color: AppColors.primary,
                              fontWeight: FontWeight.bold,
                            )
                          : AppTextStyles.bodyLarge,
                    ),
                  );
                }),
              ),
            )
          : ListWheelScrollView.useDelegate(
              itemExtent: 40,
              perspective: 0.005,
              diameterRatio: 1.5,
              physics: const FixedExtentScrollPhysics(),
              controller: FixedExtentScrollController(
                initialItem: value - minValue,
              ),
              onSelectedItemChanged: (index) {
                onChanged(index + minValue);
              },
              childDelegate: ListWheelChildBuilderDelegate(
                childCount: maxValue - minValue + 1,
                builder: (context, index) {
                  final actualValue = index + minValue;
                  final isSelected = actualValue == value;

                  return Center(
                    child: Text(
                      actualValue.toString().padLeft(2, '0'),
                      style: isSelected
                          ? AppTextStyles.headline3.copyWith(
                              color: AppColors.primary,
                              fontWeight: FontWeight.bold,
                            )
                          : AppTextStyles.bodyLarge,
                    ),
                  );
                },
              ),
            ),
    );
  }
}
