import 'package:flutter/material.dart';
import '../utils/color_manager.dart';

/// 颜色选择器组件
/// 用于选择颜色，支持智能排序和禁用已使用的颜色
class ColorSelector extends StatelessWidget {
  /// 当前选中的颜色
  final Color selectedColor;

  /// 已被使用的颜色列表（除当前选中的颜色外，这些颜色将显示为禁用状态）
  final List<Color> usedColors;

  /// 颜色选择回调
  final Function(Color) onColorSelected;

  /// 颜色选择器的列数
  final int crossAxisCount;

  /// 颜色选择器的间距
  final double spacing;

  /// 颜色选择器的子项宽高比
  final double childAspectRatio;

  /// 是否显示提示信息
  final bool showHint;

  const ColorSelector({
    super.key,
    required this.selectedColor,
    required this.onColorSelected,
    this.usedColors = const [],
    this.crossAxisCount = 6,  // 改为一排6个，适配12个颜色
    this.spacing = 20,         // 调整间距
    this.childAspectRatio = 1,
    this.showHint = true,
  });

  @override
  Widget build(BuildContext context) {
    // 使用智能颜色管理器获取排序后的颜色
    final sortedColorOptions = ColorManager.getSortedColors(
      usedColors: usedColors,
      excludeColor: selectedColor,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 颜色网格
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            mainAxisSpacing: spacing,
            crossAxisSpacing: spacing,
            childAspectRatio: childAspectRatio,
          ),
          itemCount: sortedColorOptions.length,
          itemBuilder: (context, index) {
            final colorOption = sortedColorOptions[index];
            final color = colorOption.color;
            final isSelected = color == selectedColor;
            final isDisabled = !colorOption.isAvailable;

            return GestureDetector(
              onTap: isDisabled ? null : () => onColorSelected(color),
              child: SizedBox(
                width: 32,
                height: 32,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  decoration: BoxDecoration(
                    color: isDisabled
                        ? color.withAlpha((0.3 * 255).round())
                        : color,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected
                          ? Colors.white
                          : color.withAlpha((0.7 * 255).round()),
                      width: isSelected ? 2.5 : 1,
                    ),
                    boxShadow: isSelected
                        ? [
                            BoxShadow(
                              color: color.withAlpha((0.6 * 255).round()),
                              blurRadius: 6,
                              spreadRadius: 1,
                              offset: const Offset(0, 2),
                            )
                          ]
                        : isDisabled
                            ? null
                            : [
                                BoxShadow(
                                  color: Colors.black.withAlpha(20),
                                  blurRadius: 2,
                                  offset: const Offset(0, 1),
                                )
                              ],
                  ),
                  child: Stack(
                    children: [
                      // 选中状态的勾选图标
                      if (isSelected)
                        const Center(
                          child: Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      // 禁用状态的斜线
                      if (isDisabled)
                        Center(
                          child: Container(
                            width: 20,
                            height: 1,
                            decoration: BoxDecoration(
                              color: Colors.white.withAlpha(180),
                              borderRadius: BorderRadius.circular(0.5),
                            ),
                            transform: Matrix4.rotationZ(-0.785398), // -45度
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),

        // 提示信息
        // if (showHint) ...[
        //   const SizedBox(height: 12),
        //   _buildHintText(context),
        // ],
      ],
    );
  }

  /// 构建提示文本
  // Widget _buildHintText(BuildContext context) {
  //   final stats = ColorManager.getUsageStats(usedColors);

  //   String hintText;
  //   Color hintColor;

  //   if (stats.isFull) {
  //     hintText = '已达到科目创建数量上限';
  //     hintColor = AppColors.error;
  //   } else if (stats.isNearlyFull) {
  //     hintText = '还可创建 ${stats.availableCount} 个科目';
  //     hintColor = AppColors.warning;
  //   } else {
  //     hintText = '已使用的颜色会自动排到最后并禁用，避免数据分析时颜色冲突';
  //     hintColor = AppColors.textTertiary;
  //   }

  //   return Row(
  //     children: [
  //       Icon(
  //         Icons.info_outline,
  //         size: 14,
  //         color: hintColor,
  //       ),
  //       const SizedBox(width: 6),
  //       Expanded(
  //         child: Text(
  //           hintText,
  //           style: TextStyle(
  //             fontSize: 12,
  //             color: hintColor,
  //             height: 1.3,
  //           ),
  //         ),
  //       ),
  //     ],
  //   );
  // }
}
