import 'package:flutter/material.dart';
import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import '../theme/constants.dart';

enum DatePickerMode {
  single,
  range,
}

class CustomDatePicker extends StatelessWidget {
  final DatePickerMode mode;
  final DateTime? initialDate;
  final DateTime? initialEndDate;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final String title;
  final Function(List<DateTime?>) onDateSelected;
  final bool useRootNavigator;

  const CustomDatePicker({
    super.key,
    this.mode = DatePickerMode.single,
    this.initialDate,
    this.initialEndDate,
    this.firstDate,
    this.lastDate,
    this.title = '选择日期',
    required this.onDateSelected,
    this.useRootNavigator = true,
  });

  /// 显示日期选择器对话框
  static Future<void> show({
    required BuildContext context,
    DatePickerMode mode = DatePickerMode.single,
    DateTime? initialDate,
    DateTime? initialEndDate,
    DateTime? firstDate,
    DateTime? lastDate,
    String title = '选择日期',
    required Function(List<DateTime?>) onDateSelected,
    bool useRootNavigator = true,
  }) async {
    final results = await showCalendarDatePicker2Dialog(
      context: context,
      config: CalendarDatePicker2WithActionButtonsConfig(
        calendarType: mode == DatePickerMode.single
            ? CalendarDatePicker2Type.single
            : CalendarDatePicker2Type.range,
        selectedDayHighlightColor: AppColors.primary,
        todayTextStyle: AppTextStyles.bodyMedium.copyWith(color: AppColors.primary),
        dayTextStyle: AppTextStyles.bodyMedium,
        selectedDayTextStyle: AppTextStyles.bodyMedium.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
        weekdayLabelTextStyle: AppTextStyles.labelMedium,
        controlsTextStyle: AppTextStyles.labelMedium,
        controlsHeight: 50,
        centerAlignModePicker: true,
        customModePickerIcon: const SizedBox(),
        firstDate: firstDate ?? DateTime(2000),
        lastDate: lastDate ?? DateTime(2100),
        currentDate: initialDate ?? DateTime.now(),
        yearTextStyle: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.primary,
          fontWeight: FontWeight.bold,
        ),
        dayBorderRadius: BorderRadius.circular(8),
        // buttonStyle: CalendarDatePicker2ButtonStyle(
        //   backgroundColor: Colors.white,
        //   textStyle: AppTextStyles.bodyMedium,
        //   elevation: 0,
        // ),
        gapBetweenCalendarAndButtons: 10,
        cancelButton: const Text('取消', style: TextStyle(color: AppColors.textSecondary)),
        okButton: const Text('确定', style: TextStyle(color: AppColors.primary)),
        // titleTextStyle: AppTextStyles.headline3,
        // title: title,
      ),
      dialogSize: const Size(325, 400),
      borderRadius: BorderRadius.circular(16),
      value: mode == DatePickerMode.single
          ? [initialDate ?? DateTime.now()]
          : [initialDate ?? DateTime.now(), initialEndDate],
      useRootNavigator: useRootNavigator,
    );

    if (results != null) {
      onDateSelected(results);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => show(
        context: context,
        mode: mode,
        initialDate: initialDate,
        initialEndDate: initialEndDate,
        firstDate: firstDate,
        lastDate: lastDate,
        title: title,
        onDateSelected: onDateSelected,
        useRootNavigator: useRootNavigator,
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.border),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              initialDate != null
                  ? mode == DatePickerMode.single
                      ? _formatDate(initialDate!)
                      : '${_formatDate(initialDate!)} - ${initialEndDate != null ? _formatDate(initialEndDate!) : "选择结束日期"}'
                  : '选择日期',
              style: AppTextStyles.bodyMedium,
            ),
            const Icon(Icons.calendar_today, color: AppColors.textHint, size: 20),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}