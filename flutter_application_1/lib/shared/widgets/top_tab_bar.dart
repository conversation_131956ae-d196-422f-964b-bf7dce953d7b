// 该文件定义了一个 TopTabBar 组件，用于显示应用的顶部选项卡栏。
// 它接受一个选项卡列表和一个 TabController 对象作为参数，并根据这些参数显示相应的选项卡。

import 'package:flutter/material.dart';

// TopTabBar 是一个无状态组件，用于显示应用的顶部选项卡栏。
class TopTabBar extends StatelessWidget implements PreferredSizeWidget {
  // 选项卡列表，包含每个选项卡的标题。
  final List<String> tabs;
  // 用于控制选项卡切换的 TabController 对象。
  final TabController controller;

  // 构造函数，接收选项卡列表和 TabController 对象作为参数。
  const TopTabBar({
    super.key,
    required this.tabs,
    required this.controller,
  });

  // 构建方法，返回一个 TabBar 组件。
  @override
  Widget build(BuildContext context) {
    return TabBar(
      // 用于控制选项卡切换的 TabController 对象。
      controller: controller,
      // 选项卡列表，使用 map 方法将每个标题转换为 Tab 组件。
      tabs: tabs.map((tab) => Tab(text: tab)).toList(),
    );
  }

  // 返回选项卡栏的首选大小。
  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
