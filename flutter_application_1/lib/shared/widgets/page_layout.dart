import 'package:flutter/material.dart';
import '../theme/constants.dart';
import 'page_header.dart';
import 'loading_state.dart';

/// 页面布局组件
/// 提供统一的页面布局，包括头部、内容和底部
class PageLayout extends StatelessWidget {
  /// 页面标题
  final String? title;
  
  /// 页面副标题
  final String? subtitle;
  
  /// 页面内容
  final Widget? body;
  
  /// 页面头部
  final Widget? header;
  
  /// 页面底部
  final Widget? footer;
  
  /// 页面悬浮按钮
  final Widget? floatingActionButton;
  
  /// 页面背景色
  final Color? backgroundColor;
  
  /// 是否显示返回按钮
  final bool showBackButton;
  
  /// 返回按钮点击事件
  final VoidCallback? onBackPressed;
  
  /// 页面头部操作按钮
  final List<Widget>? actions;
  
  /// 页面头部底部组件
  final Widget? headerBottom;
  
  /// 页面内边距
  final EdgeInsetsGeometry padding;
  
  /// 是否安全区域
  final bool safeArea;
  
  /// 是否可滚动
  final bool scrollable;
  
  /// 是否正在加载
  final bool isLoading;
  
  /// 加载文本
  final String? loadingText;
  
  /// 是否调整大小以避免键盘
  final bool resizeToAvoidBottomInset;
  
  const PageLayout({
    super.key,
    this.title,
    this.subtitle,
    this.body,
    this.header,
    this.footer,
    this.floatingActionButton,
    this.backgroundColor,
    this.showBackButton = false,
    this.onBackPressed,
    this.actions,
    this.headerBottom,
    this.padding = const EdgeInsets.all(16),
    this.safeArea = true,
    this.scrollable = true,
    this.isLoading = false,
    this.loadingText,
    this.resizeToAvoidBottomInset = true,
  });

  @override
  Widget build(BuildContext context) {
    // 构建页面内容
    Widget content = _buildContent();
    
    // 如果需要安全区域，包装在SafeArea中
    if (safeArea) {
      content = SafeArea(child: content);
    }
    
    // 构建页面脚手架
    return Scaffold(
      backgroundColor: backgroundColor ?? AppColors.background,
      body: Stack(
        children: [
          // 页面内容
          content,
          
          // 加载状态
          if (isLoading)
            LoadingState.overlay(text: loadingText),
        ],
      ),
      floatingActionButton: floatingActionButton,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
    );
  }
  
  /// 构建页面内容
  Widget _buildContent() {
    // 构建主体内容
    Widget mainContent = body ?? const SizedBox.shrink();
    
    // 如果需要滚动，包装在SingleChildScrollView中
    if (scrollable) {
      mainContent = SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: padding,
        child: mainContent,
      );
    } else {
      mainContent = Padding(
        padding: padding,
        child: mainContent,
      );
    }
    
    // 构建完整内容
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 头部
        if (header != null)
          header!
        else if (title != null)
          PageHeader(
            title: title!,
            subtitle: subtitle,
            showBackButton: showBackButton,
            onBackPressed: onBackPressed,
            actions: actions,
            bottom: headerBottom,
          ),
        
        // 主体内容
        Expanded(child: mainContent),
        
        // 底部
        if (footer != null) footer!,
      ],
    );
  }
}
