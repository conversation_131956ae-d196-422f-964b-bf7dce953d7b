import 'package:flutter/material.dart';
import '../theme/constants.dart';

/// 页面头部组件
/// 用于显示页面的标题和操作按钮
class PageHeader extends StatelessWidget {
  /// 标题
  final String title;
  
  /// 副标题
  final String? subtitle;
  
  /// 左侧操作按钮
  final Widget? leading;
  
  /// 右侧操作按钮
  final List<Widget>? actions;
  
  /// 底部组件
  final Widget? bottom;
  
  /// 背景颜色
  final Color? backgroundColor;
  
  /// 是否显示返回按钮
  final bool showBackButton;
  
  /// 返回按钮点击事件
  final VoidCallback? onBackPressed;
  
  /// 内边距
  final EdgeInsetsGeometry padding;
  
  /// 高度
  final double? height;
  
  /// 底部边框
  final Border? border;
  
  /// 底部阴影
  final List<BoxShadow>? boxShadow;
  
  const PageHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.leading,
    this.actions,
    this.bottom,
    this.backgroundColor,
    this.showBackButton = false,
    this.onBackPressed,
    this.padding = const EdgeInsets.all(16),
    this.height,
    this.border,
    this.boxShadow,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        border: border,
        boxShadow: boxShadow,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题栏
          Row(
            children: [
              // 左侧按钮或返回按钮
              if (showBackButton)
                IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                )
              else if (leading != null)
                leading!,
              
              const SizedBox(width: 16),
              
              // 标题和副标题
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 标题
                    Text(
                      title,
                      style: AppTextStyles.headline2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    // 副标题
                    if (subtitle != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        subtitle!,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              
              // 右侧操作按钮
              if (actions != null && actions!.isNotEmpty) ...[
                const SizedBox(width: 16),
                ...actions!,
              ],
            ],
          ),
          
          // 底部组件
          if (bottom != null) ...[
            const SizedBox(height: 16),
            bottom!,
          ],
        ],
      ),
    );
  }
}
