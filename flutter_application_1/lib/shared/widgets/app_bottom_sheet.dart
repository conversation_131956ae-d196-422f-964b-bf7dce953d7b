import 'package:flutter/material.dart';
import '../theme/constants.dart';

/// 应用底部弹出框组件
/// 提供统一的底部弹出框样式
class AppBottomSheet extends StatelessWidget {
  /// 弹出框标题
  final String? title;

  /// 弹出框内容
  final Widget child;

  /// 弹出框右上角操作按钮
  final Widget? action;

  /// 弹出框内边距
  final EdgeInsetsGeometry padding;

  /// 弹出框高度
  final double? height;

  /// 是否可拖动关闭
  final bool isDismissible;

  /// 是否可拖动
  final bool enableDrag;

  /// 是否显示顶部拖动条
  final bool showDragHandle;

  const AppBottomSheet({
    super.key,
    this.title,
    required this.child,
    this.action,
    this.padding = const EdgeInsets.all(16),
    this.height,
    this.isDismissible = true,
    this.enableDrag = true,
    this.showDragHandle = true,
  });

  /// 显示底部弹出框
  static Future<T?> show<T>({
    required BuildContext context,
    String? title,
    required Widget child,
    Widget? action,
    EdgeInsetsGeometry padding = const EdgeInsets.all(16),
    double? height,
    bool isDismissible = true,
    bool enableDrag = true,
    bool showDragHandle = true,
    bool isScrollControlled = true,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: isScrollControlled,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: AppColors.transparent,
      builder: (context) => AppBottomSheet(
        title: title,
        action: action,
        padding: padding,
        height: height,
        isDismissible: isDismissible,
        enableDrag: enableDrag,
        showDragHandle: showDragHandle,
        child: child,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      constraints: BoxConstraints(
        maxHeight: height ?? MediaQuery.of(context).size.height * 0.9,
      ),
      decoration: AppDecorations.bottomSheet(),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 拖动条
          if (showDragHandle)
            Center(
              child: Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppColors.grey.withAlpha(AppColors.alpha30),
                  borderRadius: BorderRadius.circular(AppSizes.radiusSmall / 2),
                ),
              ),
            ),

          // 标题和操作按钮
          if (title != null || action != null)
            Padding(
              padding: EdgeInsets.only(
                left: padding.horizontal / 2,
                right: padding.horizontal / 2,
                top: showDragHandle ? 16 : padding.vertical / 2,
                bottom: 8,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (title != null)
                    Text(
                      title!,
                      style: AppTextStyles.headline3,
                    )
                  else
                    const SizedBox.shrink(),
                  if (action != null) action! else const SizedBox.shrink(),
                ],
              ),
            ),

          // 内容
          Flexible(
            child: Padding(
              padding: EdgeInsets.only(
                left: padding.horizontal / 2,
                right: padding.horizontal / 2,
                top: (title != null || action != null) ? 0 : padding.vertical / 2,
                bottom: padding.vertical / 2,
              ),
              child: child,
            ),
          ),
        ],
      ),
    );
  }
}

extension EdgeInsetsGeometryExtension on EdgeInsetsGeometry {
  double get horizontal {
    if (this is EdgeInsets) {
      final edgeInsets = this as EdgeInsets;
      return edgeInsets.left + edgeInsets.right;
    }
    return 32; // 默认值
  }

  double get vertical {
    if (this is EdgeInsets) {
      final edgeInsets = this as EdgeInsets;
      return edgeInsets.top + edgeInsets.bottom;
    }
    return 32; // 默认值
  }
}
