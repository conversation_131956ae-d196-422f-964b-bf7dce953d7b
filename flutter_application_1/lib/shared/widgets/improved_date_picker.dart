import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../theme/constants.dart';

/// 改进的日期选择器，解决本地化和按钮点击问题
class ImprovedDatePicker extends StatelessWidget {
  final DateTime? initialDate;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final String title;
  final Function(DateTime?) onDateSelected;
  final bool useRootNavigator;

  const ImprovedDatePicker({
    super.key,
    this.initialDate,
    this.firstDate,
    this.lastDate,
    this.title = '选择日期',
    required this.onDateSelected,
    this.useRootNavigator = true,
  });

  /// 显示日期选择器对话框
  Future<void> _showDatePicker(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate ?? DateTime.now(),
      firstDate: firstDate ?? DateTime(2000),
      lastDate: lastDate ?? DateTime(2100),
      locale: const Locale('zh', 'CN'),
      initialEntryMode: DatePickerEntryMode.calendarOnly, // 只显示日历，不显示输入框
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: AppColors.text,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      onDateSelected(pickedDate);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showDatePicker(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.border),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              initialDate != null
                  ? _formatDate(initialDate!)
                  : '选择日期',
              style: AppTextStyles.bodyMedium,
            ),
            const Icon(Icons.calendar_today, color: AppColors.textHint, size: 20),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final DateFormat formatter = DateFormat('yyyy-MM-dd', 'zh_CN');
    return formatter.format(date);
  }
}

/// 改进的日期范围选择器
class ImprovedDateRangePicker extends StatelessWidget {
  final DateTimeRange? initialDateRange;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final String title;
  final Function(DateTimeRange?) onDateRangeSelected;
  final bool useRootNavigator;

  const ImprovedDateRangePicker({
    super.key,
    this.initialDateRange,
    this.firstDate,
    this.lastDate,
    this.title = '选择日期范围',
    required this.onDateRangeSelected,
    this.useRootNavigator = true,
  });

  /// 显示日期范围选择器对话框
  Future<void> _showDateRangePicker(BuildContext context) async {
    final DateTimeRange? pickedDateRange = await showDateRangePicker(
      context: context,
      initialDateRange: initialDateRange ?? DateTimeRange(
        start: DateTime.now(),
        end: DateTime.now().add(const Duration(days: 7)),
      ),
      firstDate: firstDate ?? DateTime(2000),
      lastDate: lastDate ?? DateTime(2100),
      locale: const Locale('zh', 'CN'),
      initialEntryMode: DatePickerEntryMode.calendarOnly, // 只显示日历，不显示输入框
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: AppColors.text,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDateRange != null) {
      onDateRangeSelected(pickedDateRange);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showDateRangePicker(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.border),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              initialDateRange != null
                  ? '${_formatDate(initialDateRange!.start)} - ${_formatDate(initialDateRange!.end)}'
                  : '选择日期范围',
              style: AppTextStyles.bodyMedium,
            ),
            const Icon(Icons.date_range, color: AppColors.textHint, size: 20),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final DateFormat formatter = DateFormat('yyyy-MM-dd', 'zh_CN');
    return formatter.format(date);
  }
}
