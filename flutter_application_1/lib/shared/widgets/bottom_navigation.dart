import 'package:flutter/material.dart';
import '../theme/constants.dart';

class BottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const BottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // 使用Material组件包装，确保底部区域颜色一致
    return Material(
      color: Color.fromARGB(255, 255, 250, 240),
      // color: AppColors.cardBackground, // 使用卡片背景色，确保与导航栏颜色一致
      elevation: 8, // 添加阴影，增强视觉层次感
      child: SafeArea(
        child: Container(
          height: 56,
          decoration: BoxDecoration(
            color: Color.fromARGB(255, 255, 250, 240),
            // color: AppColors.cardBackground, // 使用卡片背景色
            // boxShadow: [
            //   AppShadows.low, // 使用应用定义的阴影
            // ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: List.generate(
              5,
              (index) => _buildNavItem(
                index,
                _getIcon(index),
                _getActiveIcon(index),
                _getLabel(index),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(int index, IconData icon, IconData activeIcon, String label) {
    final isSelected = index == currentIndex;
    return InkWell(
      onTap: () => onTap(index),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          // 选中时使用浅色背景，增强视觉效果
          color: isSelected ? AppColors.successLight100 : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isSelected ? activeIcon : icon,
              size: 22,
              // 选中时使用主题色，未选中时使用浅色文本色
              color: isSelected ? AppColors.primary : AppColors.textPlaceholder,
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                // 选中时使用主题色，未选中时使用浅色文本色
                color: isSelected ? AppColors.primary : AppColors.textPlaceholder,
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIcon(int index) {
    switch (index) {
      case 0:
        return Icons.home_outlined; // 首页图标
      case 1:
        return Icons.timer_outlined; // 专注图标
      case 2:
        return Icons.flag_outlined; // 目标图标
      case 3:
        return Icons.calendar_today_outlined; // 日程图标
      case 4:
        return Icons.person_outline; // 我的图标
      default:
        return Icons.home_outlined;
    }
  }

  IconData _getActiveIcon(int index) {
    switch (index) {
      case 0:
        return Icons.home; // 首页图标
      case 1:
        return Icons.timer; // 专注图标
      case 2:
        return Icons.flag; // 目标图标
      case 3:
        return Icons.calendar_today; // 日程图标
      case 4:
        return Icons.person; // 我的图标
      default:
        return Icons.home;
    }
  }

  String _getLabel(int index) {
    switch (index) {
      case 0:
        return '首页';
      case 1:
        return '专注';
      case 2:
        return '目标';
      case 3:
        return '日程';
      case 4:
        return '我的';
      default:
        return '首页';
    }
  }
}

