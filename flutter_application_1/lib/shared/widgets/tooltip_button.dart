import 'package:flutter/material.dart';
import '../theme/constants.dart';

/// 通用提示按钮组件
///
/// 一个小型的提示按钮，长按时显示提示文本
/// 可以在应用的多个位置复用
class TooltipButton extends StatelessWidget {
  /// 提示文本
  final String message;

  /// 图标
  final IconData icon;

  /// 图标大小
  final double iconSize;

  /// 图标颜色
  final Color? iconColor;

  /// 提示文本样式
  final TextStyle? tooltipTextStyle;

  /// 提示背景颜色
  final Color? tooltipBackgroundColor;

  /// 提示显示时长
  final Duration? showDuration;

  /// 提示等待时长
  final Duration? waitDuration;

  /// 提示文本位置
  final TooltipTriggerMode triggerMode;

  /// 提示文本位置
  final bool preferBelow;

  const TooltipButton({
    super.key,
    required this.message,
    this.icon = Icons.info_outline,
    this.iconSize = 16.0,
    this.iconColor,
    this.tooltipTextStyle,
    this.tooltipBackgroundColor,
    this.showDuration,
    this.waitDuration,
    this.triggerMode = TooltipTriggerMode.longPress,
    this.preferBelow = false,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveIconColor = iconColor ?? AppColors.textSecondary;

    return Tooltip(
      message: message,
      textStyle: tooltipTextStyle ?? const TextStyle(
        color: Colors.white,
        fontSize: 12,
      ),
      decoration: BoxDecoration(
        color: tooltipBackgroundColor ?? AppColors.textTertiary.withAlpha(230),
        borderRadius: BorderRadius.circular(4),
      ),
      showDuration: showDuration ?? const Duration(seconds: 2),
      waitDuration: waitDuration ?? const Duration(milliseconds: 500),
      triggerMode: triggerMode,
      preferBelow: preferBelow,
      child: Icon(
        icon,
        size: iconSize,
        color: effectiveIconColor,
      ),
    );
  }
}
