import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 中文输入法友好的长度限制格式化器
/// 解决中文输入法在拼音阶段被意外截断的问题
class ChineseInputFormatter extends TextInputFormatter {
  final int maxLength;
  final bool allowOverflow;

  const ChineseInputFormatter({
    required this.maxLength,
    this.allowOverflow = true,
  });

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // 如果允许溢出（用于中文输入法），在composing状态下不限制长度
    if (allowOverflow && newValue.composing.isValid) {
      // 在拼音输入过程中，允许临时超过长度限制
      return newValue;
    }

    // 如果不在composing状态，或者不允许溢出，则严格限制长度
    if (newValue.text.length <= maxLength) {
      return newValue;
    }

    // 超过长度限制时，截断到最大长度
    final truncated = newValue.text.substring(0, maxLength);
    return TextEditingValue(
      text: truncated,
      selection: TextSelection.collapsed(offset: truncated.length),
    );
  }
}

/// 数字输入格式化器
/// 只允许输入数字和小数点
class NumberInputFormatter extends TextInputFormatter {
  final bool allowDecimal;
  final int? maxLength;

  const NumberInputFormatter({
    this.allowDecimal = true,
    this.maxLength,
  });

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // 如果是删除操作，直接返回
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // 检查长度限制
    if (maxLength != null && newValue.text.length > maxLength!) {
      return oldValue;
    }

    // 构建正则表达式
    String pattern = allowDecimal ? r'^\d*\.?\d*$' : r'^\d*$';
    RegExp regex = RegExp(pattern);

    // 如果新值符合数字格式，返回新值
    if (regex.hasMatch(newValue.text)) {
      // 防止多个小数点
      if (allowDecimal && newValue.text.split('.').length > 2) {
        return oldValue;
      }
      return newValue;
    }

    // 否则返回旧值
    return oldValue;
  }
}

/// 整数输入格式化器
/// 只允许输入正整数
class IntegerInputFormatter extends TextInputFormatter {
  final int? maxLength;
  final int? maxValue;

  const IntegerInputFormatter({
    this.maxLength,
    this.maxValue,
  });

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // 如果是删除操作，直接返回
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // 检查长度限制
    if (maxLength != null && newValue.text.length > maxLength!) {
      return oldValue;
    }

    // 只允许数字
    if (!RegExp(r'^\d+$').hasMatch(newValue.text)) {
      return oldValue;
    }

    // 检查数值限制
    if (maxValue != null) {
      final value = int.tryParse(newValue.text);
      if (value != null && value > maxValue!) {
        return oldValue;
      }
    }

    return newValue;
  }
}

/// 中文输入法友好的文本输入框
/// 解决中文输入法长度限制和键盘遮挡问题
class ChineseFriendlyTextField extends StatefulWidget {
  final TextEditingController? controller;
  final String? hintText;
  final String? labelText;
  final int? maxLength;
  final TextInputType? keyboardType;
  final InputDecoration? decoration;
  final FormFieldValidator<String>? validator;
  final bool enabled;
  final VoidCallback? onTap;
  final ValueChanged<String>? onChanged;
  final int maxLines;
  final bool showCounter;

  const ChineseFriendlyTextField({
    super.key,
    this.controller,
    this.hintText,
    this.labelText,
    this.maxLength,
    this.keyboardType,
    this.decoration,
    this.validator,
    this.enabled = true,
    this.onTap,
    this.onChanged,
    this.maxLines = 1,
    this.showCounter = true,
  });

  @override
  State<ChineseFriendlyTextField> createState() => _ChineseFriendlyTextFieldState();
}

class _ChineseFriendlyTextFieldState extends State<ChineseFriendlyTextField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _isControllerOwned = false;
  bool _isComposing = false;
  int _currentLength = 0;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _isControllerOwned = widget.controller == null;
    _focusNode = FocusNode();
    _currentLength = _controller.text.length;

    // 监听输入变化
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    if (_isControllerOwned) {
      _controller.dispose();
    }
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    setState(() {
      _currentLength = _controller.text.length;
    });
    widget.onChanged?.call(_controller.text);
  }

  void _handleTap() {
    if (!widget.enabled) return;
    widget.onTap?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: _handleTap,
          child: AbsorbPointer(
            absorbing: widget.onTap != null,
            child: TextFormField(
              controller: _controller,
              focusNode: _focusNode,
              decoration: widget.decoration ?? InputDecoration(
                hintText: widget.hintText,
                labelText: widget.labelText,
                border: const OutlineInputBorder(),
                // 不显示默认的计数器，我们自定义
                counterText: '',
              ),
              maxLines: widget.maxLines,
              keyboardType: widget.keyboardType,
              validator: widget.validator,
              enabled: widget.enabled,
              // 关键：使用none模式，不在输入时强制限制长度
              maxLengthEnforcement: MaxLengthEnforcement.none,
              // 使用自定义格式化器
              inputFormatters: widget.maxLength != null
                  ? [ChineseInputFormatter(maxLength: widget.maxLength!)]
                  : null,
              onChanged: (value) {
                // 检测是否在composing状态
                final composing = _controller.value.composing;
                setState(() {
                  _isComposing = composing.isValid;
                });
              },
            ),
          ),
        ),

        // 自定义计数器
        if (widget.maxLength != null && widget.showCounter)
          Padding(
            padding: const EdgeInsets.only(top: 4, right: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  '$_currentLength/${widget.maxLength}',
                  style: TextStyle(
                    fontSize: 12,
                    color: _currentLength > widget.maxLength!
                        ? Colors.red
                        : _isComposing
                            ? Colors.orange
                            : Colors.grey[600],
                  ),
                ),
                if (_isComposing)
                  Padding(
                    padding: const EdgeInsets.only(left: 4),
                    child: Icon(
                      Icons.edit,
                      size: 12,
                      color: Colors.orange,
                    ),
                  ),
              ],
            ),
          ),
      ],
    );
  }
}

/// 中文输入法友好的键盘感知输入框
/// 结合键盘上方输入条和中文输入法优化
class ChineseFriendlyKeyboardTextField extends StatefulWidget {
  final TextEditingController? controller;
  final String? hintText;
  final String? labelText;
  final int? maxLength;
  final TextInputType? keyboardType;
  final InputDecoration? decoration;
  final FormFieldValidator<String>? validator;
  final bool enabled;
  final ValueChanged<String>? onChanged;
  final int maxLines;
  final bool showCounter;

  const ChineseFriendlyKeyboardTextField({
    super.key,
    this.controller,
    this.hintText,
    this.labelText,
    this.maxLength,
    this.keyboardType,
    this.decoration,
    this.validator,
    this.enabled = true,
    this.onChanged,
    this.maxLines = 1,
    this.showCounter = true,
  });

  @override
  State<ChineseFriendlyKeyboardTextField> createState() => _ChineseFriendlyKeyboardTextFieldState();
}

class _ChineseFriendlyKeyboardTextFieldState extends State<ChineseFriendlyKeyboardTextField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _isControllerOwned = false;
  int _currentLength = 0;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _isControllerOwned = widget.controller == null;
    _focusNode = FocusNode();
    _currentLength = _controller.text.length;

    // 监听输入变化
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    if (_isControllerOwned) {
      _controller.dispose();
    }
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    setState(() {
      _currentLength = _controller.text.length;
    });
    widget.onChanged?.call(_controller.text);
  }

  void _handleTap() {
    if (!widget.enabled) return;

    // 显示键盘输入覆盖层，使用中文友好的输入方式
    _showChineseFriendlyKeyboard();
  }

  void _showChineseFriendlyKeyboard() {
    // 这里可以扩展为显示自定义的中文友好键盘输入条
    // 暂时使用现有的键盘输入覆盖层
    showDialog(
      context: context,
      builder: (context) => _ChineseFriendlyInputDialog(
        controller: _controller,
        hintText: widget.hintText,
        labelText: widget.labelText,
        maxLength: widget.maxLength,
        keyboardType: widget.keyboardType,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: _handleTap,
          child: AbsorbPointer(
            child: TextFormField(
              controller: _controller,
              focusNode: _focusNode,
              decoration: widget.decoration ?? InputDecoration(
                hintText: widget.hintText,
                labelText: widget.labelText,
                border: const OutlineInputBorder(),
                counterText: '',
              ),
              maxLines: widget.maxLines,
              keyboardType: widget.keyboardType,
              validator: widget.validator,
              enabled: widget.enabled,
            ),
          ),
        ),

        // 自定义计数器
        if (widget.maxLength != null && widget.showCounter)
          Padding(
            padding: const EdgeInsets.only(top: 4, right: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  '$_currentLength/${widget.maxLength}',
                  style: TextStyle(
                    fontSize: 12,
                    color: _currentLength > widget.maxLength!
                        ? Colors.red
                        : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}

/// 中文友好的输入对话框
class _ChineseFriendlyInputDialog extends StatefulWidget {
  final TextEditingController controller;
  final String? hintText;
  final String? labelText;
  final int? maxLength;
  final TextInputType? keyboardType;

  const _ChineseFriendlyInputDialog({
    required this.controller,
    this.hintText,
    this.labelText,
    this.maxLength,
    this.keyboardType,
  });

  @override
  State<_ChineseFriendlyInputDialog> createState() => _ChineseFriendlyInputDialogState();
}

class _ChineseFriendlyInputDialogState extends State<_ChineseFriendlyInputDialog> {
  late TextEditingController _dialogController;

  @override
  void initState() {
    super.initState();
    _dialogController = TextEditingController(text: widget.controller.text);
  }

  @override
  void dispose() {
    _dialogController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.labelText ?? '输入'),
      content: ChineseFriendlyTextField(
        controller: _dialogController,
        hintText: widget.hintText,
        maxLength: widget.maxLength,
        keyboardType: widget.keyboardType,
        decoration: const InputDecoration(
          border: OutlineInputBorder(),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('取消'),
        ),
        TextButton(
          onPressed: () {
            widget.controller.text = _dialogController.text;
            Navigator.pop(context);
          },
          child: const Text('确定'),
        ),
      ],
    );
  }
}
