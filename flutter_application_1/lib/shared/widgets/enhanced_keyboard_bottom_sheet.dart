import 'package:flutter/material.dart';
import '../theme/constants.dart';

/// 增强的键盘感知底部弹窗
/// 使用最佳实践解决键盘遮挡问题
class EnhancedKeyboardBottomSheet extends StatelessWidget {
  /// 弹窗标题
  final String title;

  /// 弹窗内容
  final Widget child;

  /// 右上角操作按钮
  final List<Widget>? actions;

  /// 内容内边距
  final EdgeInsetsGeometry? contentPadding;

  /// 最大高度比例
  final double maxHeightRatio;

  /// 最小高度
  final double minHeight;

  const EnhancedKeyboardBottomSheet({
    super.key,
    required this.title,
    required this.child,
    this.actions,
    this.contentPadding,
    this.maxHeightRatio = 0.9,
    this.minHeight = 200,
  });

  /// 显示增强的键盘感知底部弹窗
  static Future<T?> show<T>({
    required BuildContext context,
    required String title,
    required Widget child,
    List<Widget>? actions,
    EdgeInsetsGeometry? contentPadding,
    double maxHeightRatio = 0.9,
    double minHeight = 200,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: true, // 关键：允许弹窗控制自己的高度
      isDismissible: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) => EnhancedKeyboardBottomSheet(
        title: title,
        actions: actions,
        contentPadding: contentPadding,
        maxHeightRatio: maxHeightRatio,
        minHeight: minHeight,
        child: child,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenHeight = mediaQuery.size.height;
    final safeAreaBottom = mediaQuery.padding.bottom;

    // 计算最大可用高度（恢复原有逻辑，不考虑键盘）
    final maxHeight = screenHeight * maxHeightRatio;

    return Container(
      constraints: BoxConstraints(
        maxHeight: maxHeight,
        minHeight: minHeight,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 拖动指示器
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 8),
            decoration: BoxDecoration(
              color: AppColors.divider,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // 标题栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(bottom: BorderSide(color: AppColors.divider)),
            ),
            child: Row(
              children: [
                Text(title, style: AppTextStyles.headline3),
                const Spacer(),
                if (actions != null) ...actions!,
                if (actions == null)
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
              ],
            ),
          ),

          // 内容区域 - 使用Flexible确保可以滚动
          Flexible(
            child: SingleChildScrollView(
              padding: contentPadding ?? const EdgeInsets.all(16),
              physics: const ClampingScrollPhysics(),
              child: child,
            ),
          ),

          // 底部安全区域
          SizedBox(height: safeAreaBottom),
        ],
      ),
    );
  }
}

/// 增强的键盘感知表单底部弹窗
/// 专门用于包含表单输入的弹窗，提供更好的用户体验
class EnhancedKeyboardFormSheet extends StatelessWidget {
  /// 弹窗标题
  final String title;

  /// 表单内容
  final Widget child;

  /// 提交按钮文本
  final String submitText;

  /// 提交回调
  final VoidCallback? onSubmit;

  /// 是否正在提交
  final bool isSubmitting;

  /// 内容内边距
  final EdgeInsetsGeometry? contentPadding;

  /// 最大高度比例
  final double maxHeightRatio;

  const EnhancedKeyboardFormSheet({
    super.key,
    required this.title,
    required this.child,
    this.submitText = '保存',
    this.onSubmit,
    this.isSubmitting = false,
    this.contentPadding,
    this.maxHeightRatio = 0.9,
  });

  /// 显示增强的键盘感知表单底部弹窗
  static Future<T?> show<T>({
    required BuildContext context,
    required String title,
    required Widget child,
    String submitText = '保存',
    VoidCallback? onSubmit,
    bool isSubmitting = false,
    EdgeInsetsGeometry? contentPadding,
    double maxHeightRatio = 0.9,
  }) {
    return EnhancedKeyboardBottomSheet.show<T>(
      context: context,
      title: title,
      maxHeightRatio: maxHeightRatio,
      contentPadding: contentPadding,
      // 移除标题栏中的保存按钮，只保留关闭按钮
      actions: [
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.pop(context),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      ],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 表单内容
          child,

          const SizedBox(height: 24),

          // 底部保存按钮
          if (onSubmit != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ElevatedButton(
                onPressed: isSubmitting ? null : onSubmit,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: isSubmitting
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(submitText),
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return EnhancedKeyboardBottomSheet(
      title: title,
      maxHeightRatio: maxHeightRatio,
      contentPadding: contentPadding,
      // 移除标题栏中的保存按钮，只保留关闭按钮
      actions: [
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.pop(context),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      ],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 表单内容
          child,

          const SizedBox(height: 24),

          // 底部保存按钮
          if (onSubmit != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ElevatedButton(
                onPressed: isSubmitting ? null : onSubmit,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: isSubmitting
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(submitText),
              ),
            ),
        ],
      ),
    );
  }
}
