import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/constants.dart';
import 'chinese_input_formatter.dart';

/// 键盘输入覆盖层管理器
/// 全局管理键盘上方的输入条
class KeyboardInputOverlayManager {
  static final KeyboardInputOverlayManager _instance = KeyboardInputOverlayManager._internal();
  factory KeyboardInputOverlayManager() => _instance;
  KeyboardInputOverlayManager._internal();

  OverlayEntry? _overlayEntry;

  /// 是否正在显示输入条
  bool get isShowing => _overlayEntry != null;

  /// 显示键盘输入条
  void showInputBar({
    required BuildContext context,
    required TextEditingController controller,
    String? hint,
    String? label,
    int? maxLength,
    TextInputType? keyboardType,
    FormFieldValidator<String>? validator,
  }) {
    // 如果已经有输入条在显示，先移除
    hideInputBar();

    _overlayEntry = OverlayEntry(
      builder: (context) => _KeyboardInputBar(
        controller: controller,
        hint: hint,
        label: label,
        maxLength: maxLength,
        keyboardType: keyboardType,
        onComplete: hideInputBar,
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  /// 隐藏键盘输入条
  void hideInputBar() {
    try {
      _overlayEntry?.remove();
    } catch (e) {
      // 忽略移除时的错误，确保_overlayEntry被设置为null
      // 静默处理移除错误
    } finally {
      _overlayEntry = null;
    }
  }

  /// 强制清理所有输入条（用于紧急情况）
  void forceCleanup() {
    hideInputBar();
  }
}

/// 键盘上方的输入条
class _KeyboardInputBar extends StatefulWidget {
  final TextEditingController controller;
  final String? hint;
  final String? label;
  final int? maxLength;
  final TextInputType? keyboardType;
  final VoidCallback onComplete;

  const _KeyboardInputBar({
    required this.controller,
    this.hint,
    this.label,
    this.maxLength,
    this.keyboardType,
    required this.onComplete,
  });

  @override
  State<_KeyboardInputBar> createState() => _KeyboardInputBarState();
}

class _KeyboardInputBarState extends State<_KeyboardInputBar>
    with WidgetsBindingObserver {
  late TextEditingController _overlayController;
  late FocusNode _overlayFocusNode;
  double _keyboardHeight = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // 创建覆盖层专用的控制器，初始值为原控制器的值
    _overlayController = TextEditingController(text: widget.controller.text);
    _overlayFocusNode = FocusNode();

    // 监听覆盖层输入的变化，同步到原控制器
    _overlayController.addListener(_syncToOriginal);

    // 监听焦点变化
    _overlayFocusNode.addListener(_onFocusChange);

    // 获取当前键盘高度
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateKeyboardHeight();
      _overlayFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _overlayController.removeListener(_syncToOriginal);
    _overlayFocusNode.removeListener(_onFocusChange);
    _overlayController.dispose();
    _overlayFocusNode.dispose();
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateKeyboardHeight();
    });
  }

  void _updateKeyboardHeight() {
    if (!mounted) return;

    final mediaQuery = MediaQuery.of(context);
    final keyboardHeight = mediaQuery.viewInsets.bottom;

    // 增强第三方键盘兼容性：添加延迟和多次检测
    if (_keyboardHeight != keyboardHeight) {
      setState(() {
        _keyboardHeight = keyboardHeight;
      });

      // 对于第三方键盘，添加额外的延迟检测
      // 某些第三方键盘的高度变化可能有延迟
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          final updatedHeight = MediaQuery.of(context).viewInsets.bottom;
          if (_keyboardHeight != updatedHeight) {
            setState(() {
              _keyboardHeight = updatedHeight;
            });
          }
        }
      });

      // 注意：不要在键盘消失时自动调用onComplete，这会导致循环调用
      // 让用户主动点击完成按钮或点击外部区域来完成输入
    }
  }

  void _syncToOriginal() {
    widget.controller.text = _overlayController.text;
    widget.controller.selection = _overlayController.selection;
  }

  void _onFocusChange() {
    // 当焦点失去时，延迟一段时间后检查是否需要关闭输入条
    if (!_overlayFocusNode.hasFocus) {
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted && !_overlayFocusNode.hasFocus) {
          // 如果焦点确实失去了，关闭输入条
          widget.onComplete();
        }
      });
    }
  }

  bool _isNumberKeyboard() {
    return widget.keyboardType == TextInputType.number ||
           widget.keyboardType == const TextInputType.numberWithOptions(decimal: true) ||
           widget.keyboardType == const TextInputType.numberWithOptions(decimal: false);
  }

  List<TextInputFormatter>? _getInputFormatters() {
    if (_isNumberKeyboard()) {
      // 数字键盘使用数字格式化器
      bool allowDecimal = widget.keyboardType == const TextInputType.numberWithOptions(decimal: true) ||
                         widget.keyboardType == TextInputType.number;
      return [
        NumberInputFormatter(allowDecimal: allowDecimal, maxLength: widget.maxLength),
      ];
    } else if (widget.maxLength != null) {
      // 其他键盘使用中文友好格式化器
      return [
        ChineseInputFormatter(maxLength: widget.maxLength!),
      ];
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final safeAreaBottom = mediaQuery.padding.bottom;

    return Stack(
      children: [
        // 背景遮罩，点击可收起键盘
        Positioned.fill(
          child: GestureDetector(
            onTap: widget.onComplete,
            child: Container(
              color: Colors.transparent,
            ),
          ),
        ),

        // 键盘输入条
        Positioned(
          left: 0,
          right: 0,
          bottom: _keyboardHeight,
          child: GestureDetector(
            onTap: () {}, // 阻止事件冒泡
            child: Material(
              elevation: 8,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    top: BorderSide(color: AppColors.divider, width: 0.5),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                padding: EdgeInsets.only(
                  bottom: _keyboardHeight > 0 ? 0 : safeAreaBottom,
                ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 对于数字键盘，添加顶部工具栏
              if (_isNumberKeyboard())
                Container(
                  height: 44,
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    border: Border(
                      bottom: BorderSide(color: AppColors.divider, width: 0.5),
                    ),
                  ),
                  child: Row(
                    children: [
                      const SizedBox(width: 16),
                      if (widget.label != null)
                        Expanded(
                          child: Text(
                            widget.label!,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: AppColors.text,
                            ),
                          ),
                        ),

                      TextButton(
                        onPressed: widget.onComplete,
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          minimumSize: Size.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                        child: const Text(
                          '完成',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.primary,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],
                  ),
                ),

              // 输入框
              Container(
                padding: const EdgeInsets.all(16),
                child: TextField(
                  controller: _overlayController,
                  focusNode: _overlayFocusNode,
                  decoration: InputDecoration(
                    hintText: widget.hint,
                    labelText: _isNumberKeyboard() ? null : widget.label,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    counterText: widget.maxLength != null ? null : '',
                  ),
                  // 关键：使用none模式，支持中文输入法
                  maxLengthEnforcement: MaxLengthEnforcement.none,
                  // 根据键盘类型使用不同的格式化器
                  inputFormatters: _getInputFormatters(),
                  keyboardType: widget.keyboardType,
                  textInputAction: TextInputAction.done,
                  onSubmitted: (_) => widget.onComplete(),
                ),
              ),
            ],
          ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// 键盘感知输入框
/// 点击时显示键盘上方的输入条
class KeyboardAwareTextField extends StatefulWidget {
  final TextEditingController? controller;
  final String? hintText;
  final String? labelText;
  final int? maxLength;
  final TextInputType? keyboardType;
  final InputDecoration? decoration;
  final FormFieldValidator<String>? validator;
  final bool enabled;
  final VoidCallback? onTap;

  const KeyboardAwareTextField({
    super.key,
    this.controller,
    this.hintText,
    this.labelText,
    this.maxLength,
    this.keyboardType,
    this.decoration,
    this.validator,
    this.enabled = true,
    this.onTap,
  });

  @override
  State<KeyboardAwareTextField> createState() => _KeyboardAwareTextFieldState();
}

class _KeyboardAwareTextFieldState extends State<KeyboardAwareTextField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _isControllerOwned = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _isControllerOwned = widget.controller == null;
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    if (_isControllerOwned) {
      _controller.dispose();
    }
    _focusNode.dispose();
    super.dispose();
  }

  void _handleTap() {
    if (!widget.enabled) return;

    widget.onTap?.call();

    // 显示键盘输入覆盖层
    KeyboardInputOverlayManager().showInputBar(
      context: context,
      controller: _controller,
      hint: widget.hintText,
      label: widget.labelText,
      maxLength: widget.maxLength,
      keyboardType: widget.keyboardType,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTap,
      child: AbsorbPointer(
        child: TextFormField(
          controller: _controller,
          focusNode: _focusNode,
          decoration: widget.decoration ?? InputDecoration(
            hintText: widget.hintText,
            labelText: widget.labelText,
            border: const OutlineInputBorder(),
          ),
          maxLength: widget.maxLength,
          keyboardType: widget.keyboardType,
          validator: widget.validator,
          enabled: widget.enabled,
        ),
      ),
    );
  }
}
