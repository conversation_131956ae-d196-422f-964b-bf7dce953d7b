import 'package:flutter/material.dart';
import '../theme/constants.dart';

/// 加载状态组件
/// 用于显示页面或操作的加载状态
class LoadingState extends StatelessWidget {
  /// 加载文本
  final String? text;
  
  /// 是否显示背景
  final bool showBackground;
  
  /// 加载指示器尺寸
  final double size;
  
  /// 加载指示器线宽
  final double strokeWidth;
  
  /// 加载指示器颜色
  final Color? color;
  
  const LoadingState({
    super.key,
    this.text,
    this.showBackground = false,
    this.size = 40,
    this.strokeWidth = 4,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final content = Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 加载指示器
        SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: strokeWidth,
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? AppColors.primary,
            ),
          ),
        ),
        
        // 加载文本
        if (text != null) ...[
          const SizedBox(height: 16),
          Text(
            text!,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
    
    if (showBackground) {
      return Container(
        color: Colors.black.withOpacity(0.1),
        child: Center(child: content),
      );
    }
    
    return Center(child: content);
  }
  
  /// 创建全屏加载状态
  static Widget fullScreen({
    String? text,
    Color? backgroundColor,
    double size = 40,
    double strokeWidth = 4,
    Color? color,
  }) {
    return Container(
      color: backgroundColor ?? Colors.white,
      child: LoadingState(
        text: text,
        size: size,
        strokeWidth: strokeWidth,
        color: color,
      ),
    );
  }
  
  /// 创建覆盖加载状态
  static Widget overlay({
    String? text,
    double size = 40,
    double strokeWidth = 4,
    Color? color,
  }) {
    return LoadingState(
      text: text,
      showBackground: true,
      size: size,
      strokeWidth: strokeWidth,
      color: color,
    );
  }
}
