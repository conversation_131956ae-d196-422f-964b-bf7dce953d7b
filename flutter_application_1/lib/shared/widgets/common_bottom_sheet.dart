import 'package:flutter/material.dart';
import '../theme/constants.dart';
import '../utils/keyboard_utils.dart';

/// 通用底部弹窗组件
/// 提供统一的样式和交互方式
class CommonBottomSheet extends StatelessWidget {
  final String title;
  final Widget child;
  final List<Widget>? actions;
  final double? maxHeight;
  final EdgeInsets contentPadding;

  const CommonBottomSheet({
    super.key,
    required this.title,
    required this.child,
    this.actions,
    this.maxHeight,
    this.contentPadding = const EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 16.0),
  });

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenHeight = mediaQuery.size.height;
    final keyboardHeight = mediaQuery.viewInsets.bottom;
    final safeAreaBottom = mediaQuery.padding.bottom;

    // 计算可用高度
    final availableHeight = screenHeight - keyboardHeight - safeAreaBottom;
    final defaultMaxHeight = screenHeight * 0.75;
    final constrainedHeight = availableHeight < defaultMaxHeight ? availableHeight : (maxHeight ?? defaultMaxHeight);

    return GestureDetector(
      onTap: () => KeyboardUtils.hideKeyboard(context),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: constrainedHeight,
        ),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
          // 顶部拖动条
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          // 标题
          Padding(
            padding: const EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: AppTextStyles.headline3,
                ),
                if (actions != null) ...actions!,
              ],
            ),
          ),
          // 内容区域 - 使用Flexible让内容可以滚动
          Flexible(
            child: Container(
              padding: contentPadding,
              child: child,
            ),
          ),
          // 底部安全区域
          SizedBox(height: safeAreaBottom),
          ],
        ),
      ),
    );
  }

  /// 显示底部弹窗的静态方法
  static Future<T?> show<T>(
    BuildContext context, {
    required String title,
    required Widget child,
    List<Widget>? actions,
    double? maxHeight,
    EdgeInsets? contentPadding,
    bool isDismissible = true,
    bool enableDrag = true,
    Color? backgroundColor,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: true,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: backgroundColor ?? Colors.transparent,
      builder: (context) => CommonBottomSheet(
        title: title,
        actions: actions,
        maxHeight: maxHeight,
        contentPadding: contentPadding ?? const EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 16.0),
        child: child,
      ),
    );
  }
}