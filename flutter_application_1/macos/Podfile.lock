PODS:
  - audio_session (0.0.1):
    - FlutterMacOS
  - connectivity_plus (0.0.1):
    - FlutterMacOS
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - flutter_local_notifications (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - just_audio (0.0.1):
    - FlutterMacOS
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - ReachabilitySwift (5.2.4)
  - share_plus (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - audio_session (from `Flutter/ephemeral/.symlinks/plugins/audio_session/macos`)
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - flutter_local_notifications (from `Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - in_app_purchase_storekit (from `Flutter/ephemeral/.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - just_audio (from `Flutter/ephemeral/.symlinks/plugins/just_audio/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `Flutter/ephemeral/.symlinks/plugins/sign_in_with_apple/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - wakelock_plus (from `Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos`)

SPEC REPOS:
  trunk:
    - ReachabilitySwift

EXTERNAL SOURCES:
  audio_session:
    :path: Flutter/ephemeral/.symlinks/plugins/audio_session/macos
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  flutter_local_notifications:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  in_app_purchase_storekit:
    :path: Flutter/ephemeral/.symlinks/plugins/in_app_purchase_storekit/darwin
  just_audio:
    :path: Flutter/ephemeral/.symlinks/plugins/just_audio/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sign_in_with_apple:
    :path: Flutter/ephemeral/.symlinks/plugins/sign_in_with_apple/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  wakelock_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos

SPEC CHECKSUMS:
  audio_session: 48ab6500f7a5e7c64363e206565a5dfe5a0c1441
  connectivity_plus: e74b9f74717d2d99d45751750e266e55912baeb5
  device_info_plus: b0fafc687fb901e2af612763340f1b0d4352f8e5
  file_selector_macos: 6280b52b459ae6c590af5d78fc35c7267a3c4b31
  flutter_local_notifications: 4bf37a31afde695b56091b4ae3e4d9c7a7e6cda0
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  in_app_purchase_storekit: d1a48cb0f8b29dbf5f85f782f5dd79b21b90a5e6
  just_audio: eb8b016ac4493159ab24db4f7215e55303b39a84
  package_info_plus: f0052d280d17aa382b932f399edf32507174e870
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  share_plus: 3c787998077d6b31e839225a282e9e27edf99274
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sign_in_with_apple: 6673c03c9e3643f6c8d33601943fbfa9ae99f94e
  url_launcher_macos: 0fba8ddabfc33ce0a9afe7c5fef5aab3d8d2d673
  wakelock_plus: 21ddc249ac4b8d018838dbdabd65c5976c308497

PODFILE CHECKSUM: 9ebaf0ce3d369aaa26a9ea0e159195ed94724cf3

COCOAPODS: 1.16.2
