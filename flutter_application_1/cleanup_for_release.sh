#!/bin/bash

# 预发布版本清理脚本
echo "🧹 开始预发布版本清理..."
echo "=========================="

# 检查当前目录
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ 请在Flutter项目根目录运行此脚本"
    exit 1
fi

echo "📋 清理测试和开发文件..."

# 1. 清理测试相关文件
echo ""
echo "🧪 清理测试文件..."

# 删除集成测试目录（保留基本的widget_test.dart）
if [ -d "test/integration_test" ]; then
    rm -rf test/integration_test
    echo "✅ 已删除集成测试目录"
fi

if [ -f "test/auth_integration_test.dart" ]; then
    rm test/auth_integration_test.dart
    echo "✅ 已删除认证集成测试文件"
fi

# 2. 清理开发功能模块
echo ""
echo "🛠️ 清理开发功能模块..."

if [ -d "lib/features/test" ]; then
    rm -rf lib/features/test
    echo "✅ 已删除测试功能模块"
fi

if [ -d "lib/features/dev" ]; then
    rm -rf lib/features/dev
    echo "✅ 已删除开发工具模块"
fi

if [ -d "lib/features/development" ]; then
    rm -rf lib/features/development
    echo "✅ 已删除开发相关功能模块"
fi

# 3. 清理备份和归档文件
echo ""
echo "🗂️ 清理备份和归档文件..."

if [ -d "archived_features" ]; then
    rm -rf archived_features
    echo "✅ 已删除归档功能目录"
fi

if [ -d "backup_mvp_removed_features" ]; then
    rm -rf backup_mvp_removed_features
    echo "✅ 已删除备份功能目录"
fi

if [ -d "flutter_application_1/backup_mvp_removed_features" ]; then
    rm -rf flutter_application_1/backup_mvp_removed_features
    echo "✅ 已删除重复备份目录"
fi

# 删除重复的flutter_application_1目录
if [ -d "flutter_application_1/flutter_application_1" ]; then
    rm -rf flutter_application_1/flutter_application_1
    echo "✅ 已删除重复的项目目录"
fi

# 4. 清理构建和临时文件
echo ""
echo "🔨 清理构建和临时文件..."

if [ -d "build" ]; then
    rm -rf build
    echo "✅ 已删除构建目录"
fi

# 5. 清理开发脚本
echo ""
echo "📜 清理开发脚本..."

rm -f test_apple_subscription.sh
rm -f final_test_verification.sh
rm -f configure_ios_project.sh
rm -f generate_ios_icons.sh
echo "✅ 已删除开发脚本文件"

# 删除脚本目录
if [ -d "scripts" ]; then
    rm -rf scripts
    echo "✅ 已删除脚本目录"
fi

# 6. 清理开发配置文件
echo ""
echo "⚙️ 清理开发配置文件..."

rm -f build.yaml
rm -f integration_test.yaml
rm -f flutter_ignore_platform_warnings.yaml
rm -f devtools_options.yaml
echo "✅ 已删除开发配置文件"

# 7. 清理不必要的文档
echo ""
echo "📚 整理文档目录..."

# 保留重要文档，删除开发过程文档
if [ -d "docs/development" ]; then
    rm -rf docs/development
    echo "✅ 已删除开发过程文档"
fi

# 删除一些具体的开发文档
rm -f docs/bug修复记录.md
rm -f docs/fixes_summary.md
rm -f docs/final_fixes_summary.md
rm -f docs/开发记录.md
rm -f docs/问题解决方案总结.md
echo "✅ 已删除开发过程记录文档"

# 8. 清理Flutter构建缓存
echo ""
echo "🧽 清理Flutter缓存..."
flutter clean > /dev/null 2>&1
echo "✅ 已清理Flutter构建缓存"

# 9. 重新获取依赖
echo ""
echo "📦 重新获取依赖..."
flutter pub get > /dev/null 2>&1
echo "✅ 已重新获取依赖"

# 10. 运行代码分析
echo ""
echo "🔍 运行代码分析..."
if flutter analyze --no-fatal-infos > /dev/null 2>&1; then
    echo "✅ 代码分析通过"
else
    echo "⚠️ 代码分析发现问题，请检查"
    flutter analyze
fi

echo ""
echo "🎉 预发布清理完成！"
echo "==================="
echo ""
echo "📋 清理总结："
echo "- ✅ 删除了测试和开发功能模块"
echo "- ✅ 删除了备份和归档文件"
echo "- ✅ 删除了构建和临时文件"
echo "- ✅ 删除了开发脚本和配置"
echo "- ✅ 整理了文档目录"
echo "- ✅ 清理了Flutter缓存"
echo ""
echo "🚀 下一步操作："
echo "1. 检查代码是否正常运行"
echo "2. 创建发布分支 (release/v1.0)"
echo "3. 进行最终的构建和测试"
echo "4. 准备提交App Store审核"
