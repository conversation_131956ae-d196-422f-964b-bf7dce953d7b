# 专注场景应用

这是一个帮助用户通过不同场景进行专注的 Flutter 应用。

## 项目结构

项目采用清晰的分层架构，包含以下主要模块：

### API 层 (`lib/api/`)
- `scene_api.dart`: 处理场景相关的网络请求
- `user_api.dart`: 处理用户相关的网络请求

### 组件层 (`lib/components/`)
- `bottom_navigation.dart`: 自定义底部导航栏
- `scene_card.dart`: 场景展示卡片
- `top_tab_bar.dart`: 场景页面顶部标签栏

### 数据模型层 (`lib/models/`)
- `scene.dart`: 场景数据模型
- `user.dart`: 用户数据模型

### 页面层 (`lib/screens/`)
- `home_screen.dart`: 应用首页
- `scene_selection_screen.dart`: 场景选择页面
- `scene_screen.dart`: 场景详情页面
- `profile_screen.dart`: 用户个人信息页面

### 服务层 (`lib/services/`)
- `audio_service.dart`: 音频播放服务
- `timer_service.dart`: 专注计时服务

### 工具层 (`lib/utils/`)
- `constants.dart`: 全局常量定义
- `theme.dart`: 应用主题配置

## 功能特性
- 场景选择和播放
- 专注计时
- 用户数据管理
- 音频播放控制

## 开发环境
- Flutter 3.0+
- Dart 2.17+

 生成详细的代码注释，并在开头解释该文件代码的作用​，注意不要修改代码内容。