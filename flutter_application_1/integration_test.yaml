# 集成测试配置
# 用于配置测试环境

# 测试服务器配置
server:
  url: http://localhost:3000/api
  timeout: 10000

# 测试账号配置
test_account:
  username: tester
  email: <EMAIL>
  password: test123

# 测试数据配置
test_data:
  # 是否在测试前清除数据
  clear_before_test: true
  # 是否在测试后清除数据
  clear_after_test: true
  # 是否使用模拟数据
  use_mock_data: true

# 测试报告配置
report:
  # 是否生成测试报告
  generate: true
  # 测试报告输出路径
  output_path: test/reports
  # 测试报告格式
  format: html
