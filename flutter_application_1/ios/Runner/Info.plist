<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Limefocus</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>limefocus</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<!-- 隐私权限描述 - Apple审核要求 -->

	<!-- 麦克风权限 - just_audio包可能需要 -->
	<key>NSMicrophoneUsageDescription</key>
	<string>LimeFocus需要访问麦克风以播放专注提醒音效，提升您的专注体验。我们不会录制或存储任何音频内容。</string>

	<!-- 相机权限 - image_picker包需要 -->
	<key>NSCameraUsageDescription</key>
	<string>LimeFocus需要访问相机以便您拍摄和上传头像照片，个性化您的个人资料。</string>

	<!-- 照片库权限 - image_picker包需要 -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>LimeFocus需要访问照片库以便您选择和上传头像照片，个性化您的个人资料。</string>

	<!-- 通知权限 - flutter_local_notifications包需要 -->
	<key>NSUserNotificationUsageDescription</key>
	<string>LimeFocus需要发送通知以提醒您专注时间结束、目标达成等重要事件，帮助您更好地管理时间。</string>

	<!-- 网络状态权限 - connectivity_plus包需要 -->
	<key>NSNetworkUsageDescription</key>
	<string>LimeFocus需要检查网络连接状态以同步您的专注数据和目标进度，确保数据安全备份。</string>

	<!-- 出口合规声明 - Export Compliance -->
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>

	<!-- 通知权限配置 -->
	<key>UIUserNotificationSettings</key>
	<dict>
		<key>UIUserNotificationTypeAlert</key>
		<true/>
		<key>UIUserNotificationTypeBadge</key>
		<true/>
		<key>UIUserNotificationTypeSound</key>
		<true/>
	</dict>

	<!-- 后台模式配置 - 移除无效的后台模式 -->
	<!-- LimeFocus暂时不需要后台模式，如需要可以添加有效的模式如：
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>background-app-refresh</string>
	</array>
	-->
</dict>
</plist>
