#!/bin/bash

# LimeFocus TestFlight 构建和上传脚本
# 使用方法: ./scripts/build_and_upload.sh [version] [build_number]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -eq 0 ]; then
    log_info "使用当前版本号进行构建"
    VERSION=""
    BUILD_NUMBER=""
elif [ $# -eq 2 ]; then
    VERSION=$1
    BUILD_NUMBER=$2
    log_info "使用指定版本号: $VERSION+$BUILD_NUMBER"
else
    log_error "用法: $0 [version] [build_number]"
    log_error "示例: $0 1.0.1 2"
    exit 1
fi

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

log_info "开始 LimeFocus TestFlight 构建流程..."

# 1. 环境检查
log_info "检查开发环境..."

if ! command -v flutter &> /dev/null; then
    log_error "Flutter 未安装或不在 PATH 中"
    exit 1
fi

if ! command -v xcodebuild &> /dev/null; then
    log_error "Xcode 命令行工具未安装"
    exit 1
fi

# 检查Flutter环境
log_info "检查 Flutter 环境..."
flutter doctor --android-licenses > /dev/null 2>&1 || true
flutter doctor -v

# 2. 清理和准备
log_info "清理项目..."
flutter clean
rm -rf ios/build
rm -rf build

log_info "获取依赖..."
flutter pub get

# 2.1 修复iOS配置问题
log_info "修复iOS配置问题..."
cd ios
if [ -d "Pods" ]; then
    log_info "重新安装CocoaPods依赖..."
    pod deintegrate > /dev/null 2>&1 || true
    pod install
else
    log_info "安装CocoaPods依赖..."
    pod install
fi
cd ..

# 3. 更新版本号（如果提供）
if [ ! -z "$VERSION" ] && [ ! -z "$BUILD_NUMBER" ]; then
    log_info "更新版本号到 $VERSION+$BUILD_NUMBER..."

    # 备份原始文件
    cp pubspec.yaml pubspec.yaml.backup

    # 更新版本号
    sed -i '' "s/^version: .*/version: $VERSION+$BUILD_NUMBER/" pubspec.yaml

    log_success "版本号已更新"
fi

# 4. 代码质量检查
log_info "运行代码分析..."
if ! flutter analyze --no-fatal-infos; then
    log_warning "代码分析发现问题，但继续构建..."
fi

# 5. 运行测试
log_info "运行测试..."
if ! flutter test; then
    log_warning "测试失败，但继续构建..."
fi

# 6. 构建 iOS Release 版本
log_info "构建 iOS Release 版本..."
flutter build ios --release --no-codesign

if [ $? -ne 0 ]; then
    log_error "Flutter iOS 构建失败"
    exit 1
fi

log_success "Flutter 构建完成"

# 7. 使用 Xcode 进行 Archive
log_info "使用 Xcode 进行 Archive..."

# 检查 Xcode workspace 是否存在
if [ ! -f "ios/Runner.xcworkspace/contents.xcworkspacedata" ]; then
    log_error "Xcode workspace 不存在"
    exit 1
fi

# 获取当前版本信息
CURRENT_VERSION=$(grep "version:" pubspec.yaml | sed 's/version: //' | tr -d ' ')
log_info "当前版本: $CURRENT_VERSION"

# 创建 Archive
ARCHIVE_PATH="build/ios/archive/Runner_$(date +%Y%m%d_%H%M%S).xcarchive"
mkdir -p "$(dirname "$ARCHIVE_PATH")"

log_info "开始 Archive 构建..."
xcodebuild -workspace ios/Runner.xcworkspace \
    -scheme Runner \
    -configuration Release \
    -destination generic/platform=iOS \
    -archivePath "$ARCHIVE_PATH" \
    archive

if [ $? -ne 0 ]; then
    log_error "Xcode Archive 失败"
    exit 1
fi

log_success "Archive 构建完成: $ARCHIVE_PATH"

# 8. 导出 IPA（可选）
log_info "导出 IPA 文件..."

# 创建导出选项 plist
EXPORT_OPTIONS_PLIST="build/ios/ExportOptions.plist"
mkdir -p "$(dirname "$EXPORT_OPTIONS_PLIST")"

cat > "$EXPORT_OPTIONS_PLIST" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>method</key>
    <string>app-store</string>
    <key>uploadBitcode</key>
    <false/>
    <key>uploadSymbols</key>
    <true/>
    <key>compileBitcode</key>
    <false/>
</dict>
</plist>
EOF

IPA_PATH="build/ios/ipa"
mkdir -p "$IPA_PATH"

xcodebuild -exportArchive \
    -archivePath "$ARCHIVE_PATH" \
    -exportOptionsPlist "$EXPORT_OPTIONS_PLIST" \
    -exportPath "$IPA_PATH"

if [ $? -eq 0 ]; then
    log_success "IPA 导出完成: $IPA_PATH"
else
    log_warning "IPA 导出失败，但 Archive 已完成"
fi

# 9. 提示下一步操作
log_success "构建流程完成！"
echo ""
log_info "下一步操作："
echo "1. 打开 Xcode Organizer:"
echo "   Xcode -> Window -> Organizer"
echo ""
echo "2. 选择刚才创建的 Archive:"
echo "   $ARCHIVE_PATH"
echo ""
echo "3. 点击 'Distribute App' 按钮"
echo ""
echo "4. 选择 'App Store Connect' -> 'Upload'"
echo ""
echo "5. 按照向导完成上传"
echo ""

# 10. 自动打开 Xcode Organizer（可选）
read -p "是否自动打开 Xcode Organizer? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "打开 Xcode Organizer..."
    open -a Xcode
    sleep 2
    osascript -e 'tell application "Xcode" to activate'
    osascript -e 'tell application "System Events" to keystroke "9" using {command down, shift down}'
fi

# 11. 清理临时文件
log_info "清理临时文件..."
rm -f "$EXPORT_OPTIONS_PLIST"

# 恢复版本号文件（如果有备份）
if [ -f "pubspec.yaml.backup" ]; then
    log_info "是否恢复原始版本号? (y/n)"
    read -p "> " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        mv pubspec.yaml.backup pubspec.yaml
        log_info "版本号已恢复"
    else
        rm pubspec.yaml.backup
    fi
fi

log_success "所有操作完成！"
echo ""
log_info "构建信息:"
echo "- 版本: $CURRENT_VERSION"
echo "- Archive: $ARCHIVE_PATH"
echo "- 时间: $(date)"
echo ""
log_info "请在 App Store Connect 中检查上传状态"
