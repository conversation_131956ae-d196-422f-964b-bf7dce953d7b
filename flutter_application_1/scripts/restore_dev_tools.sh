#!/bin/bash

# v1.0.1 开发工具恢复脚本
# 从旧的develop分支选择性恢复开发工具

echo "🔧 开始恢复开发工具..."

# 确保在正确的分支上
current_branch=$(git branch --show-current)
if [ "$current_branch" != "develop-v1.0.1" ]; then
    echo "❌ 错误：请先切换到 develop-v1.0.1 分支"
    exit 1
fi

# 创建备份
echo "📦 创建当前状态备份..."
git stash push -m "backup before restoring dev tools"

# 恢复核心开发工具
echo "🛠️ 恢复核心开发工具..."

# 开发者工具页面
git checkout develop -- lib/features/dev/screens/developer_tools_screen.dart 2>/dev/null || echo "⚠️  developer_tools_screen.dart 不存在"
git checkout develop -- lib/features/development/screens/dev_tools_screen.dart 2>/dev/null || echo "✅ dev_tools_screen.dart 已恢复"

# 调试和诊断工具
git checkout develop -- lib/features/development/screens/apple_debug_screen.dart 2>/dev/null || echo "✅ apple_debug_screen.dart 已恢复"
git checkout develop -- lib/features/development/screens/auth_diagnostics_screen.dart 2>/dev/null || echo "✅ auth_diagnostics_screen.dart 已恢复"
git checkout develop -- lib/features/development/screens/enhanced_apple_diagnostic_screen.dart 2>/dev/null || echo "⚠️  enhanced_apple_diagnostic_screen.dart 不存在"
git checkout develop -- lib/features/development/screens/safe_sandbox_diagnostic_screen.dart 2>/dev/null || echo "⚠️  safe_sandbox_diagnostic_screen.dart 不存在"
git checkout develop -- lib/features/development/screens/sandbox_diagnostic_screen.dart 2>/dev/null || echo "⚠️  sandbox_diagnostic_screen.dart 不存在"
git checkout develop -- lib/features/development/screens/subscription_diagnostic_screen.dart 2>/dev/null || echo "⚠️  subscription_diagnostic_screen.dart 不存在"

# 测试工具
git checkout develop -- lib/features/development/screens/test_data_generator_screen.dart 2>/dev/null || echo "⚠️  test_data_generator_screen.dart 不存在"
git checkout develop -- lib/features/test/screens/auth_test_screen.dart 2>/dev/null || echo "⚠️  auth_test_screen.dart 不存在"
git checkout develop -- lib/features/test/screens/integration_test_screen.dart 2>/dev/null || echo "⚠️  integration_test_screen.dart 不存在"

# 测试配置和服务
git checkout develop -- lib/core/config/test_config.dart 2>/dev/null || echo "✅ test_config.dart 已恢复"
git checkout develop -- lib/core/services/test_data_generator.dart 2>/dev/null || echo "⚠️  test_data_generator.dart 不存在"
git checkout develop -- lib/core/services/test_account_service.dart 2>/dev/null || echo "⚠️  test_account_service.dart 不存在"

# 集成测试相关
git checkout develop -- integration_test.yaml 2>/dev/null || echo "✅ integration_test.yaml 已恢复"
git checkout develop -- lib/core/providers/integration_test_provider.dart 2>/dev/null || echo "⚠️  integration_test_provider.dart 不存在"
git checkout develop -- lib/core/utils/integration_test_helper.dart 2>/dev/null || echo "⚠️  integration_test_helper.dart 不存在"

# 测试脚本
git checkout develop -- final_test_verification.sh 2>/dev/null || echo "✅ final_test_verification.sh 已恢复"

# 开发文档
echo "📚 恢复开发文档..."
git checkout develop -- docs/development/ 2>/dev/null || echo "⚠️  development 目录不存在"

echo "✅ 开发工具恢复完成！"

# 检查恢复的文件
echo "📋 已恢复的文件列表："
git status --porcelain | grep "^A" | cut -c4-

echo ""
echo "🔍 请检查恢复的文件是否正确，然后运行："
echo "   git add ."
echo "   git commit -m '🔧 恢复开发工具和调试功能'"
echo ""
echo "⚠️  注意：某些文件可能不存在于旧的develop分支中，这是正常的。"
